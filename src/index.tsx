import React, { Suspense, Component, ComponentProps } from 'react';
import ReactDOM from 'react-dom';
import { BrowserRouter as Router, Route } from 'react-router-dom';
import { corsImport, getLocale } from '@weapp/utils';

import './style/index.less';
import { needLayout } from './utils';
import reportWebVitals from './reportWebVitals';
import { menuConfig } from './mock';
import { root } from './constants';
import { RouterMain } from './lib';
import ebdcoms from './utils/ebdcoms';

function noop () {}
function Main(props: any) {
  let path = window.location.href.indexOf('sp') <= -1 ? root : `${root}/sp/`;
  // if (isMobile()) {
  //   path = `${root}/mobile/`;
  // }
  return (
    <Route weId={`${props.weId || ''}_34khin`} path={path}>
      <Suspense weId={`${props.weId || ''}_fzeols`}
        fallback={noop}
      >
        <RouterMain weId={`${props.weId || ''}_z87pd3`} />
      </Suspense>
    </Route>
  )
}
class WithLayout extends Component<ComponentProps<any>, any> {

  state = {
    module: <Main weId={`${this.props.weId || ''}_7twox1`} key={`msrrf0`} />
  }

  componentDidMount(){
    if (needLayout()) {
      this.loadLayout();
    }
  }

  loadLayout() {
    corsImport(`@weapp/layout`).then((app) => {
      if (app.SpComs) {
        this.setState({
          module: React.createElement(
            app.SpComs,
            {},
            <Main weId={`${this.props.weId || ''}_se3980`}
              key={`msrrf0`}
              needLayout
            />
          )
        });
        app?.mainStore?.updateAside(menuConfig, needLayout());
      }
    }, (error) => {
      console.error(error);
    });
  }

  render(){
    return this.state.module;
  }
}

Promise.all([getLocale('@weapp/ebdboard'), ebdcoms.load()]).then(() => {
  ReactDOM.render(
    <React.StrictMode weId={`_u3n3tk`}>
      <Router weId={`_7oza3o`}>
        <WithLayout weId={`_6s088f`} />
      </Router>
    </React.StrictMode>,
    document.getElementById('root')
  );
});

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
