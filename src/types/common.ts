import { DataSet as EbdcomsDataSet } from '@weapp/ebdcoms';
import {EtComponentKey} from '../constants/EtComponent'
import {ShowPosition} from '../constants/common'
export enum CommonSearchType {
  Advanced = 0,
  TopSearch = 1,
}
export enum SearchType {
  QuickSearch = '1',
  AdvanceSearch = '2',
  FilterSearch = '3',
}
/** *
 * 抽象类公用types
 */

export type ResponseError = {
  status: string;
  message: string;
  description?: string;
};
export interface DataSetPermission {
  /** 数据源id */
  objId?: string;
  /** 是否包含下属 */
  containSub?: boolean;
  /** 所有数据、有权限数据、指定权限数据 */
  type?: string;
  /** 参与人、共享人、负责人 */
  shareTypes?: string[];
}
export interface DataSet {
  id: string;
  text: string;
  type: string;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: DataSetPermission[];
  servicePath?: string;
  params?: string[];
  dataId?: string;
  ebuilderDataConfig?: any;
  data?: any[];
  /** 关联的组件id */
  refComId?: string;
  /** 数字面板，关联的数据项id */
  refPanelId?: string;
  isPhysical?: boolean;
}
/**
 * 继承@weapp/ebdcoms DataSet 数据源类型
 * @filePath \\weapp-ebdcoms\src\common\dataset\types.ts
 * */
export interface DataSet extends EbdcomsDataSet {
  id: string;
  text: string;
  type: string;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: DataSetPermission[];
}
/**
 * 表单字段
 * */
export type FormFieldItem = {
  componentKey: EtComponentKey;
  config: string;
  fieldId: string;
  fieldName: string;
  fieldShowName: string;
  fieldType: string;
  multiSelect?: boolean;
  canNotLink?: boolean;
  type?: string;
  parentId?: string;
};

/* 任意属性对象 */
export type AnyObj = {
  [_: string]: any;
};
export type ConditionSourceTypes = {
  // 关联、按钮等数据源的条件参数信息
  targetConditionId?: string; // 关联id或者按钮id
  openSource?: 'associated' | 'button';
  openSourceId?: string;
};
/**
 * 分组搜索字段
 */
export type GroupSearchField = {
  groupField: string;
  id: string;
  listFilterDefaultSets: AnyObj;
  multiSelect: boolean;
  showName: string;
  showPosition: ShowPosition;
  fieldName: string; // 后端搜索key值
  componentKey: EtComponentKey;
  tabStyle?: string; // "select" | tab
};
/**
 * 右键按钮
 */
export type RightMenu = {
  actions: Array<any>;
  buttonKey: string;
  id: string;
  name: string;
  showPosition: Array<any>;
  hidden?: boolean;
  iconPath?: string;
  iconSelectedPath?: string; // 选中后的图标
  onlyIcon?: boolean; // 1 0
  conditionEnable?: number; // 1 0
  conditionId?: string;
  likeInfo?: -1 | 0 | 1;
  likeNum?: number;
  operateType?: string; // 1 0 执行方式 是否需要勾选数据
  subButtonKeys?: string[]; // 子按钮信息，batchEditValue大按钮，
  customTips?: any; // 自定义按钮提示信息
  // 对应batchEditValue和batchEdit两个子按钮，通过subButtonKeys返回
   buttonType: string; // 按钮类型 COMCUSTOM
   clickFn?: string; // 视图自定义按钮触发名称
   scope?: string; // 支持的设备PC,MOBILE
};
export interface FilterData {
  componentKey: EtComponentKey;
  config: string;
  fieldId: string;
  fieldName: string;
  filterId: string;
  isQuick: string;
  number: number; // Number 0 NumberComponent 3  Money 16  ，0作为文本字段
  showName: string;
  // 重命名显示名称
  configNameJson?: any;
  type: EtComponentKey;

  value?: any; // 值
  conditionType?: any[]; // 条件options 包含不包含，等于不等于
  conValue?: string; // 条件值
  conditionTypeValue?: string; // 默认条件值

  custom?: boolean; // 是否是 其他条件字段
  disableCustomConditions?: boolean; // 是否隐藏其他条件
  [_: string]: any;
}

// 看板插件包形式key 新的都存这
export enum EbdBPlKeys {
  onDidUpdate = 'onDidUpdate', //  更新时的回调
  onBeforeMount = 'onBeforeMount',
  onBoardInit = 'onBoardInit',
  renderContentTop = 'renderContentTop', // 看板自定义渲染头部区域（插件包）
  getData = 'getData', // 自定义拦截卡片列表数据并返回新处理的数据（插件包）
  renderCardBefore = 'renderCardBefore', // 看板自定义渲染卡片左侧区域（插件包）
  renderCardAfter = 'renderCardAfter', // 看板自定义渲染卡片右侧区域（插件包）
  renderCardContent = 'renderCardContent', // 看板自定义渲染单个卡片内容（插件包）
  getGroupData = 'getGroupData', // 自定义拦截看板分组数据（插件包）
  // 最新加的
  onCustomUpdate = 'onCustomUpdate', // 自定义判断是否要更新
  getBoardData = 'getBoardData', // 获取单个看板数据
  renderCard = 'renderCard', // 看板自定义渲染单个卡片（插件包）
  renderMain = 'renderMain', // 看板自定义整体内容区
  onCustomGetExtraCardInfo = 'onCustomGetExtraCardInfo', // 自定义获取单个卡片下拉菜单
  onCustomSetExtraCardInfo = 'onCustomSetExtraCardInfo', // 自定义设置单个卡片下拉菜单
  getCardDraggable = 'getCardDraggable', // 自定义卡片是否可以拖拽
  onLoadMore = 'onLoadMore', // 看板分组点击加载更多事件
  onFilter = 'onFilter', // 看板自定义处理筛选事件
  onPageChange = 'onPageChange', // 看板自定义处理分页事件
  getBoardAddParams = 'getBoardAddParams', // 看板自定义新建数据传参
  initBoardStore = 'initBoardStore', // 看板自定义初始化store方法
  getGroupEditDlgParams = 'getGroupEditDlgParams', // 看板自定义新建/编辑分组弹窗参数
  getCardEditDlgParams = 'getCardEditDlgParams', // 看板自定义新建/编辑数据弹窗参数
  getRefreshCompIsMulti = 'getRefreshCompIsMulti', // 看板自定义刷新组件是否开启多选
  getRefreshCompParams = 'getRefreshCompParams', // 看板自定义刷新组件参数
  onCustomDidMount = 'onCustomDidMount', // 看板自定义初始化方法组件第一次进入方法
  getShowLoading = 'getShowLoading', // 看板开启显示loading
  getGroupMenu = 'getGroupMenu', // 看板自定义列表菜单
  onCardDel = 'onCardDel', // 看板自定义卡片删除事件
  onCardFollow = 'onCardFollow', // 看板自定义卡片关注事件
  getGroupMenus = 'getGroupMenus', // 看板自定义获取列表菜单
  getPageSize = 'getPageSize', // 看板自定义获取页码
  getPageMode = 'getPageMode', // 看板自定义获取页码
  renderCardAddBtn = 'renderCardAddBtn', // 看板自定义新建数据按钮
  getCardAddVisible = 'getCardAddVisible', // 看板自定义新建数据按钮是否显示
  getShowEmptyVisible = 'getShowEmptyVisible', // 看板自定义是否显示为空占位
  onCustomReload = 'onCustomReload', // 看板自定义刷新事件
  renderGroupTitle = 'renderGroupTitle', // 看板自定义分组标题
  renderGroupMenus = 'renderGroupMenus', // 看板自定义分组菜单
  // ***************二维分组相关**************
  getIsLaneBoard = 'getIsLaneBoard', // 看板自定义是否为二维分组
  getIsLaneLoading = 'getIsLaneLoading', // 整体二维看板加载
  getLaneTitleConfig = 'getLaneTitleConfig', // 看板自定义二维展开收起标题卡片配置
  renderLaneBoardTitle = 'renderLaneBoardTitle', // 看板自定义二维分组标题
  renderLaneBoardTitleContent = 'renderLaneBoardTitleContent', // 看板自定义二维分组标题内容
  renderLaneBoardStat = 'renderLaneBoardStat', // 看板自定义二维分组统计
  getCustomLaneTitle = 'getCustomLaneTitle', // 看板自定义二维分组头部标题（左右侧）
  // ***************拖拽卡片相关相关**************
  afterDragEvent = 'afterDragEvent', // 拖拽卡片后事件
  beforeDragEvent = 'beforeDragEvent', // 拖拽卡片前事件
}
export enum EbdBoardEventName {
  onDidMount = 'onDidMount', // 加载完成
  onDestroy = 'onDestroy', // 销毁
  onGroupSave = 'onGroupSave', // 分组设置保存分组触发
  onGroupDataLoaded = 'onGroupDataLoaded', // 看板分组数据加载完毕
  onGroupDataUpdate = 'onGroupDataUpdate', // 看板分组数据更新回调
  onCardDataUpdate = 'onCardDataUpdate', // 看板卡片数据更新回调(add/del/update/follow/unfollow)
  onCardDragEvt = 'onCardDragEvt', // 看板卡片拖拽回调
  onCardAddEvt = 'onCardAddEvt', // 看板新建数据事件
  onBoardReload = 'onBoardReload', // 看板重新加载（只是看板分组区域）
  onLaneRightBoardReload = 'onLaneRightBoardReload', // 泳道看板重新右侧重新加载
  onReload = 'onReload', // 看板整体重新加载
  renderContentBottom = 'renderContentBottom', // 看板自定义渲染底部区域（插件包）
  renderContentBefore = 'renderContentBefore', // 看板自定义渲染左侧区域（插件包）
  renderContentAfter = 'renderContentAfter', // 看板自定义渲染右侧区域（插件包）
  onSetData = 'onSetData', // 看板自定义数据(分组和分组数据)（插件包）
  onCardClickEvt = 'onCardClick', // 看板单个卡片点击事件
  scrollToLeft = 'scrollToLeft', // 看板内容回到第一个看板
  setStoreParams = 'setStoreParams', // 看板自定义新建数据按钮
  onLaneBoardCollapsed = 'onLaneBoardCollapsed', // 泳道看板自定义展开收起事件
  onChangeLaneBoardCollapsed = 'onChangeLaneBoardCollapsed', // 看板自定义展开收起事件
  onLaneBoardPageChange = 'onLaneBoardPageChange', // 看板自定义泳道看板分页变化
}