import React, { Component } from 'react';
import { Route, with<PERSON>outer, RouteComponentProps } from 'react-router-dom';
import { formatParentPath } from '../../utils';
import { EbDesigner, EbDesignerViewPage, EbFormDesignerViewPage, EbFormEngine, EbDesignerMpreview  } from '../../pages/designer';
import EbdDoc from '../../pages/doc';

class MainRoute extends Component<RouteComponentProps> {
  render() {
    const parentPath: string = formatParentPath(this.props);
    return (
      <>
        <Route weId={`${this.props.weId || ''}_aqeolb`} path={`${parentPath}/ebddesigner(/)?:pageId?`} component={EbDesigner} exact />
        {/* 预览 */}
        <Route weId={`${this.props.weId || ''}_v4anu2`} path={`${parentPath}/ebddesigner/preview/pc/:pageId`} component={EbDesignerViewPage} />
        <Route weId={`${this.props.weId || ''}_v4anu2`} path={`${parentPath}/ebdfpage/viewport/:pageId`} component={EbFormDesignerViewPage} />
        <Route weId={`${this.props.weId || ''}_vbva8a`} path={`${parentPath}/ebFormEngine`} component={EbFormEngine} exact />
        <Route weId={`${this.props.weId || ''}_vbva8a`} path={`${parentPath}/ebFormEngine/:apid/form/view/:objId/`} component={EbFormEngine} />
        <Route weId={`${this.props.weId || ''}_3glfmr`} path={`${parentPath}/doc`} component={EbdDoc} />
        <Route weId={`${this.props.weId || ''}_vbds8a`} path={`${parentPath}/ebddesigner/preview/mobile/:pageId`} component={EbDesignerMpreview} />
      </>
    );
  }
}

export default withRouter(MainRoute);
