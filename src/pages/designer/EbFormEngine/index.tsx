import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import FormEngine from './formEngine';

const mockJson = () => {
  const appId = '929357371621318657';
  const objId = '929462795949170688';
  const id = '939804218206388225';
  const pageId = '939804218206388226';
  return {
    appId,
    id,
    objId,
    pageId,
  };
};
@observer
class EbFormEngine extends PureComponent<any, any> {
  render() {
    return <>
      <FormEngine weId={`${this.props.weId || ''}_7f9a42`}
        type='Kanban'
        advancedView='kanban'
        warehouse='@weapp/ebdboard'
        customJson={mockJson()}
      />
    </>
  }
}

export default withRouter(EbFormEngine);
