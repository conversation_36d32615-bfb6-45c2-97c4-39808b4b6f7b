import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import { Spin } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { getExternalEngine } from './utils';
import { getFormViews } from '../../../api/common';
import { ebdBClsPrefix } from '../../../constants/index';
import './index.less';

const mockJson = () => {
  const appId = '929357371621318657';
  const objId = '929462795949170688';
  const id = '939804218206388225';
  const pageId = '939804218206388226';
  return {
    appId,
    id,
    objId,
    pageId,
  };
};
const cls = `${ebdBClsPrefix}-ebFormEngine`;
interface IProps extends RouteComponentProps {
  type: string; // ebdfpage视图名
  advancedView?: string; // 高级视图名
  warehouse: string; // 仓库名
  customJson?: any; // 自自定义参数配置
}
@observer
class EbFormEngine extends PureComponent<IProps, any> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      Com: null,
      viewInfo: {},
      views: [],
      viewLoading: false,
      errInfo: '',
    };
  }
  localConfigName = `Local_EbFormEngineViewInfo_${this.props.advancedView || '-'}`;
  componentDidMount() {
    this.mounted();
  }
  mounted = () => {
    const { warehouse } = this.props;
    if (!warehouse) {
      this.setState({ errInfo: 'Please set the warehouse name' });
      return;
    }
    // 此处type只支持engine源码在ebdfpage项目的
    getExternalEngine({ type: this.props.type, warehouse }).then((Com: any) => {
      this.setState({ Com });
      this.init();
    });
  };
  init = async () => {
    const localItem = sessionStorage.getItem(this.localConfigName);
    if (localItem) {
      const viewInfo = JSON.parse(localItem);
      this.setState({ viewInfo: { ...viewInfo, isLocal: true }, views: [] });
    }
    if (window.location.href.indexOf('/form/view') > -1) {
      const { advancedView, match } = this.props;
      const { apid, objId } = match?.params as any;
      if (objId && apid) {
        const views = await this.getFormViews({ objId, apid }, advancedView || '');
        if (isEmpty(views)) {
          this.setState({ errInfo: `The current view (${advancedView}) has no data` });
        } else {
          this.setState({ views });
        }
      }
    } else {
      const { customJson } = this.props;
      if (isEmpty(customJson)) {
        this.setState({ errInfo: 'Please configure parameters：{customJson}' });
        return;
      }
      this.setViewInfo(customJson);
    }
  };
  getFormViews = async (params: any, advancedView: string) => {
    if (!advancedView) {
      throw new Error(`Please configure parameters：{advancedView}`);
    }
    try {
      this.setState({ viewLoading: true });
      const response = await getFormViews(params);
      return response?.advancedView?.[advancedView]?.detail || [];
    } catch (error) {
    } finally {
      this.setState({ viewLoading: false });
    }
  };
  resetViewInfo = () => {
    sessionStorage.removeItem(this.localConfigName);
    this.setState({ viewInfo: {}, views: [], errInfo: '' }, this.init);
  };
  setViewInfo = (view: any) => {
    const { appId, id, objId, pageId, name = '' } = view;
    const { apid } = this.props.match?.params as any;
    let _apId = apid || appId;
    let json = {
      appId: _apId,
      id,
      name,
      objId,
      pageId,
      dataset: {
        id: objId,
        text: '',
        type: 'FORM',
        groupId: appId,
      },
    };
    sessionStorage.setItem(this.localConfigName, JSON.stringify(json));
    this.setState({ viewInfo: json });
  };
  renderViews = () => {
    const { views } = this.state;
    return (
      <div className={`${cls}-views`}>
        {views.map((item: any) => {
          return (
            <div onClick={() => this.setViewInfo(item)} key={item.id} className={`${cls}-views-item`}>
              <div className={`${cls}-views-item-line`}>ViewId：{item.id}</div>
              <div className={`${cls}-views-item-line`}>ViewName：{item.name}</div>
              <div className={`${cls}-views-item-line`}>Viewdesc：{item.describe || '-'}</div>
            </div>
          );
        })}
      </div>
    );
  };

  render() {
    const { Com, viewInfo, views, viewLoading, errInfo } = this.state;
    if (viewLoading) {
      return (
        <div className={`${cls}-spin`}>
          <Spin weId={`${this.props.weId || ''}_yksnu3`} size="large" />
        </div>
      );
    }
    if (errInfo) {
      return <div className={`${cls}-errTip`}>{errInfo}</div>;
    }
    if (!isEmpty(views) && isEmpty(viewInfo)) {
      return this.renderViews();
    }
    if (!Com || isEmpty(viewInfo)) return null;
    return (
      <div className={cls}>
        <div className={`${cls}-localTip`}>
          <div>
            {viewInfo.isLocal && <span>The local configuration is currently used；（{`id：${viewInfo.id}，name：${viewInfo.name}`}）</span>}
            <span onClick={this.resetViewInfo} className={`${cls}-localTip-change`}>
              Click to switch
            </span>
          </div>
        </div>
        <Com weId={`${this.props.weId || ''}_xfp5x4`} data={viewInfo} />
      </div>
    );
  }
}

export default withRouter(EbFormEngine);
