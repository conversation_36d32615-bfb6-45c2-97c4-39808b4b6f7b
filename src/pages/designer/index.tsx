import React from 'react';
import {LazyType} from '../../types/public';

export const EbDesigner = React.lazy(() => import('./flow-designer')) as LazyType;
export const EbDesignerViewPage = React.lazy(() => import('./flow-designer/preview')) as LazyType;
export const EbFormDesignerViewPage = React.lazy(() => import('./flow-designer/fromPreview')) as LazyType;
export const EbFormEngine = React.lazy(() => import('./EbFormEngine')) as LazyType;
export const EbDesignerMpreview = React.lazy(() => import('./flow-designer/Mpreview')) as LazyType;


export default {};
