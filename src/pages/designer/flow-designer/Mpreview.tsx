import React from 'react';
import { CorsComponent } from '@weapp/ui';
import { withRouter } from 'react-router-dom';

interface FlowDesignerDemoStates {
}
class Mpreview extends React.PureComponent<any, FlowDesignerDemoStates> {
    render() {
        const { pageId } = this.props.match.params;
        if (!pageId) return
        return <CorsComponent weId={`${this.props.weId || ''}_owhl79`}
            app="@weapp/ebdpage"
            compName="MPageView"
            pageId={pageId}
            // type={'EB_FORM_VIEW'}
        />
    }
}

export default withRouter(Mpreview);
