import React from 'react';
import { CorsComponent } from '@weapp/ui';
import { withRouter } from 'react-router-dom';

interface FlowDesignerDemoStates {}
class FlowDesignViewDemo extends React.PureComponent<any, FlowDesignerDemoStates> {
  render() {
    const { pageId } = this.props.match.params;
    return (
      <CorsComponent weId={`${this.props.weId || ''}_r0u096`}
        app="@weapp/ebdpage"
        compName="PageView"
        pageId={pageId}
        // type={'EB_FORM_VIEW'}
        // type={'EB_PAGE'}
      />
    );
  }
}

export default withRouter(FlowDesignViewDemo);
