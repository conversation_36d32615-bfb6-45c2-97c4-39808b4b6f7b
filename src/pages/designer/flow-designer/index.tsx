import React from 'react';
import {CorsComponent} from '@weapp/ui';
import {withRouter} from 'react-router-dom';

interface FlowDesignerDemoStates {
}
class FlowDesignDemo extends React.PureComponent<any, FlowDesignerDemoStates> {
  render() {
    const {pageId} = this.props.match.params;
    if (!pageId) return
    return <CorsComponent
      weId={`${this.props.weId || ''}_qoyb1k`}
      app="@weapp/ebddesigner"
      compName="Design"
      pageId={pageId}
    />
  }
}

export default withRouter(FlowDesignDemo);
