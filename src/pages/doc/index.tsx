import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';

const config = [
  {
    typeName: get<PERSON><PERSON><PERSON>('55830', '数据展示类'),
    name: get<PERSON><PERSON><PERSON>('208387', '看板视图'),
    component: "Kanban",
    type: "dataPresentation",
    package: "weapp-ebdboard",
    id: "weapp-ebdboard_Kanban"
  },
];

const EbdDoc = (props: any) => {
  // const config = [{
  //   package: "@weapp/ui-props-design", // 组件库名称
  //   component: "MenuDesign",  // 抛出组件名称
  //   name: "菜单配置", // 组件中文名称
  //   type: "basis", // 组件分类
  //   typeName: "基础类", // 分类名称
  //   id: "ui-props-design_MenuDesign",  //id，唯一即可
  // }]
  return <CorsComponent weId={`${props.weId || ''}_8j848s`} app="@weapp/ebddoc" compName="LayoutCom" config={config} />;
};

export default EbdDoc;
