import { ajax } from '../utils/ajax'
import { BATCH_URL } from "../constants";
/**
 * eb数据关注、取关接口。pc&h5
 * @param params
 * @param isApp
 * @returns
 */
export const followApi = (params: any, isApp: boolean = false) => ajax({
  url: `/api${isApp ? '/app' : ''}/ebuilder/form/follow/followDatas`,
  method: 'POST',
  data: params,
  ebBusinessId: params.objId,
});

/**
 * rpc方式调用的转化,服务内部转化
 * @param typeId  objId | dataId |requestId   代表不同的类型的id
 * @param type  app(应用)  obj(表单) data(数据) flow(流程)
 * @returns
 */
export const getAppDocDiridRpc = (typeId: string, type: 'app' | 'obj' | 'data' | 'flow') => ajax({
  url: `/api/ebuilder/form/data/getDocDirectoryId?typeId=${typeId}&type=${type}`,
  method: 'get',
  ebBusinessId: typeId,
});

/**
 * 获取关联eb浏览按钮数据
 * @param params
 * @returns
 */
export const getEbBrowserData = (params: {
  fieldId: string;
  formId: string;
  current?: number;
  pageSize?: number;
}) => ajax({
  url: '/api/ebuilder/form/common/browser/data/ebuilder',
  method: 'post',
  data: {
    browserMultiple: false,
    controlId: params.fieldId,
    current: 1,
    pageSize: 20,
    formDatas: {},
    formParam: JSON.stringify({
      formId: params.formId,
      layoutId: '',
      fieldId: params.fieldId,
      filterItems: [],
      module: 'ebuilderform',
      dataDetails: [],
    }),
    openScroll: true,
    quickSearchDatas: {},
    showTabs: '',
    type: 'and',
    ...params,
  },
  ebBusinessId: params.formId,
});

/**
 * 获取关联浏览按钮数据 (类型为eb数据)
 * @param params
 * @param browserId
 * @returns
 */
export const getEbRelationBrowserData = (
  params: {
    fieldId: string;
    formId: string;
    current?: number;
    pageSize?: number
  },
  browserType: string,
) => ajax({
  url: `/api/bcw/common/browser/data/${browserType}`, // customBrowser_ljc_0110
  method: 'post',
  data: {
    browserMultiple: false,
    controlId: params.fieldId,
    current: 1,
    pageSize: 20,
    formParam: JSON.stringify({
      formId: params.formId,
      layoutId: '',
      fieldId: params.fieldId,
      filterItems: [],
      module: 'ebuilderform',
      dataDetails: [],
    }),
    ...params,
  },
  ebBusinessId: params.formId,
});
export const getImpTemplate = (listId: any) => ajax({
  url: BATCH_URL('getImpTemplate'),
  params: { listId },
  ebBusinessId: listId,
});
export const getActionAllParams = (
  params: { dataIds: string; actionId: string; type?: string },
  ebBusinessId: string,
) => ajax({
  method: 'get',
  url: '/api/ebuilder/form/list/getActionAllParams',
  params,
  compatibleData: {},
  ebBusinessId,
});

export const getActionParams = (
  params: {
    dataId: string;
    actionId: string;
    type?: string;
    listId?: string;
    formTableId?: string;
  },
  ebBusinessId: string,
) => ajax({
  method: 'get',
  url: '/api/ebuilder/form/list/getActionParams',
  params,
  compatibleData: {},
  ebBusinessId,
});
// 获取组件所有的表单高级视图
export const getFormViews = (
  params: {
    apid: string,
    objId: string,
  }
) => ajax({
  method: 'get',
  url: `/api/bs/ebuilder/form/viewlist/getViewlist/${params.objId}?apid=${params.apid}`,
  ebBusinessId: ''
});