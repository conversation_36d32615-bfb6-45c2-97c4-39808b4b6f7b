/**
 * <AUTHOR>
 * @createTime 2022-12-05
 * @desc   : 页面列表同步过去的数据源自定义和数据源默认按钮事件触发
 */
import { AnyObj, Dialog, Icon } from '@weapp/ui';
import { getLabel, ua, forIn } from '@weapp/utils';
import { action, observable, runInAction, toJS } from 'mobx';
import { getActionAllParams, getActionParams } from '../api/common';
import { ListButtonType } from '../constants';
import { DataSet } from '../types/common';
import { ActionException, ActionRecord } from '../utils/action/types';
import { triggerActions } from '../utils/action/useAction';
import { openConfirmBox, runJsCode } from '../utils/action/utils';
import { ajax } from '../utils/ajax';
import { ActionPositionType, ActionType, OpenMode } from '../constants/common';
import ebdcoms from '../utils/ebdcoms';
import { arrayParamsUnique, setLinkParams } from '../utils/jumpLink';

const LinePos = [ActionPositionType.LineEnd, ActionPositionType.LineEndDropDown, ActionPositionType.Link, ActionPositionType.ListLine];
/**
 * 事件调用getActionParams传递得type判断
 * @returns
 */
const getActionParamsType = (type: ActionPositionType) => {
  const _type = type === ActionPositionType.Link ? 'field' : 'button';
  return type === ActionPositionType.ListLine ? 'listline' : _type;
};
enum OperateType {
  NoCheck = '0', // 无需勾选数据
  NeedCheck = '1', // 需要勾选数据
}

interface ConfigParams {
  btnConfig: AnyObj;
  actions: ActionRecord[];
  btnType: string;
  client: 'PC' | 'MOBILE';
}

interface CacheDataParams {
  ids?: string[];
  objId?: string;
  compId?: string;
  pageId?: string;
  formDataIds?: string[];
  dataset?: DataSet;
}

export default class BtnActionCommonStore {
  /**
   * 缓存当前按钮动作传进来的相关参数
   */

  @observable config: ConfigParams | null = null;

  @observable cacheCurrentParams: CacheDataParams = {};

  @action
  setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };

  /**
   * 对应按钮点击触发
   */
  @action
  triggerButtonClick = (config: ConfigParams, dataParams: CacheDataParams, routeProps: any, endCallBack?: Function) => {
    runInAction(() => {
      this.cacheCurrentParams = dataParams;
      this.config = config;
    });
    triggerActions(toJS(config.actions), config.btnType, this.btnHandleRegister, routeProps, endCallBack).catch((e: any) => {
      if (e !== ActionException.Break) {
        // eslint-disable-next-line no-console
        console && console.error(e);
      }
    });
  };

  @action
  btnHandleRegister = (key: string, routerProps?: any) => {
    const isMobile = this.config?.client === 'MOBILE';
    switch (key) {
      case ActionType.OpenPage:
        return (config: any) =>
          new Promise((resolve: any) => {
            const page = JSON.parse(config.url);
            this.openPage(page, routerProps, resolve);
            resolve();
          });
      case ActionType.Interface:
        return (config: any) =>
          new Promise((resolve: any) => {
            this.toTriggerInterface(config, resolve);
          });
      case ActionType.Esb:
        return (config: any) =>
          new Promise((resolve: any) => {
            this.toTriggerEsbEvent(config, resolve);
          });
      case ActionType.ConfirmBox: {
        return (config: any) =>
          new Promise((resolve: any) => {
            openConfirmBox(config, isMobile, resolve);
          });
      }
      case ActionType.JsCode: {
        return (config: any) =>
          new Promise((resolve: any) => {
            runJsCode(config, false, routerProps, resolve);
          });
      }
      // 批量删除看板暂时不需要 后续如果要 deleteList可以参考ebdfpage里去搜
      // case ListButtonType.TableDelete:
      //   return () =>
      //     new Promise((resolve: any) => {
      //       this.deleteList(resolve);
      //     });
      case ListButtonType.TableAdd:
        return (config: any, buttonKey: string) =>
          new Promise((resolve: any) => {
            // 新建按钮无动作，直接转为打开页面新建
            const { dataset } = this.cacheCurrentParams;
            const { openType } = config || {};
            let params: any = [];
            if (buttonKey && this.config) {
              const { btnConfig = {} } = this.config;
              const { sysParams } = btnConfig;
              const sysParamsArr = sysParams ? JSON.parse(sysParams) : [];
              params = params.concat(sysParamsArr);
            }
            let otherProps = config?.windowSize && { winSizeNew: `${config?.windowSize}%` };
            if (openType === OpenMode.Modal && otherProps?.winSizeNew) {
              otherProps = {
                ...otherProps,
                modalWidth: otherProps?.modalWidth || otherProps?.winSizeNew,
              };
            }
            this.openPage(
              {
                ...otherProps,
                openMode: openType || '1',
                name: '',
                isRelationCustomPage: false,
                isNew: true,
                page: {
                  pageId: `1/${dataset?.id}`,
                  openMode: openType || '0',
                  pageName: '',
                  pageType: 'LAYOUT',
                  appid: `${dataset?.groupId}`,
                  pageViewType: '1',
                },
                params,
              },
              routerProps,
              resolve
            );
          });
      // 点赞和踩项目看板暂时不需要 后续如果要 setLikeOrDislike可以参考ebdfpage里去搜
      // case ListButtonType.LikeBtn:
      // case ListButtonType.DislikeBtn: {
      //   return (config: any) => new Promise((resolve: any) => {
      //     this.setLikeOrDislike(key, config, resolve, routerProps);
      //   });
      // }
      default:
        return () =>
          new Promise((resolve: any) => {
            resolve();
          });
    }
  };

  @action
  openPage = async (page: any, routerProps: any, resolve: any) => {
    const { pos = '' } = routerProps;
    const { ownParams, params = [], ...restPage } = page;
    const isLine = LinePos.includes(pos);
    let _params: any[] = [];
    if (ownParams) {
      // 复写openSourceId
      const openSourceId = toJS(this.cacheCurrentParams.ids || []).join(',') || routerProps?.data?.id || '-1';
      ownParams.openSourceId = openSourceId;
      // 请求（固定值 -- 表单字段）_params
      const sourcreObj = {
        type: 'button',
        actionId: ownParams.targetConditionId,
        objId: this.cacheCurrentParams?.objId,
      };
      let result: AnyObj = {};
      if (isLine) {
        result = await getActionParams(
          { ...sourcreObj, dataId: openSourceId, type: getActionParamsType(routerProps.pos) },
          this.cacheCurrentParams.pageId || ''
        );
      } else if (openSourceId && restPage.page.pageType === 'LAYOUT' && restPage.page.pageViewType === '1') {
        result = await getActionAllParams({ ...sourcreObj, dataIds: openSourceId }, this.cacheCurrentParams.pageId || '');
      }
      _params = Object.keys(result).map(name => ({ name, value: result[name] }));
    }
    // 缓存当前列表url参数等
    // ls.setItem(EbdfUrlParamsCacheKey, JSON.stringify(this.searchParams));
    const { JumpLink, PageType } = await ebdcoms.get();
    /** 参数配置处理 */
    const _linkInfo = {
      ...restPage,
      ownParams,
      params: arrayParamsUnique([...params, ..._params]),
    };
    const info = setLinkParams(_linkInfo, PageType);
    JumpLink(info, {
      ...routerProps,
      compId: this.cacheCurrentParams.compId, // 没有compId使用viewId代替
      clientType: this.config?.client,
    });
    resolve();
  };

  /**
   * 判断是否开启【无需构建数据开关】
   * @returns true  没开启，必须勾选数据，
   *          false   开启了，不用勾选数据
   */
  @action
  needDataIds = () => {
    // 执行方式为无需勾选数据
    const needDataIds = this.config?.btnConfig?.operateType !== OperateType.NoCheck || false;
    return needDataIds;
  };

  /**
   * 规则、自定义接口、esb动作等获取数据id公共方法
   * @param config
   * @param registerActionResolve
   * @returns
   */
  getDataIds = (config: any, registerActionResolve?: Function) => {
    const dataIds = this.cacheCurrentParams.ids || [];

    if (dataIds.length === 0 && this.needDataIds()) {
      // 如果没有id阻止后续动作执行
      Dialog.message({
        type: 'info',
        content: getLabel('235055', '请至少选择一条数据'),
      });
      return;
    }

    config = config.filter(({ enable }: any) => enable === 1); // 过滤掉所有未开启的动作
    /**
     * 1、config代表当前动作，如果当前动作没开启，则跳过继续往下执行
     * 2、没选中任何数据，跳过执行下一个动作
     */
    if (config.length === 0) {
      if (registerActionResolve) {
        // 后续action继续执行
        registerActionResolve();
      }
      return null;
    }
    return `${dataIds}`;
  };

  @action
  toTriggerInterface = async (config: any, registerActionResolve?: Function) => {
    if (!Array.isArray(config)) {
      config = [config];
    }
    /**
     * 1、无需要勾选数据情况下，跳过数据获取、校验，直接走
     * 2、需要勾选数据时，正常走
     */
    const dataIds: any = this.getDataIds(config, registerActionResolve);

    if (!dataIds && this.needDataIds()) {
      return;
    }

    const res: any = await ajax({
      url: '/api/ebuilder/form/action/doCustomAction',
      method: 'POST',
      data: {
        objId: this.cacheCurrentParams.objId,
        dataIds,
        formDataId: `${this.cacheCurrentParams.formDataIds}` || '',
        interfacePath: config[0]?.interfacePath,
        interfaceType: config[0]?.interfaceType,
      },
      ebBusinessId: this.cacheCurrentParams.pageId || '',
    });
    runInAction(() => {
      this.afterAsyncCallBack(res, registerActionResolve);
    });
  };

  @action
  toTriggerEsbEvent = async (config: any, registerActionResolve?: Function) => {
    if (!Array.isArray(config)) {
      config = [config];
    }
    const dataIds: any = this.getDataIds(config, registerActionResolve);

    if (!dataIds && this.needDataIds()) {
      return;
    }
    const params: AnyObj = {
      dataIds: dataIds || '',
      esbActionIds: `${config[0].recordIds}`,
    };

    const res: any = await ajax({
      url: '/api/ebuilder/form/esb/runEsb',
      method: 'POST',
      data: params,
      ebBusinessId: this.cacheCurrentParams.pageId || '',
    });
    runInAction(() => {
      this.afterAsyncCallBack(res, registerActionResolve);
    });
  };

  /**
   * 按钮动作触发公用，暂时公用自定义接口、esb按钮
   * @param config
   * @param url
   * @param data
   * @param registerActionResolve
   * @returns
   */
  @action
  triggerAsyncFn = async (config: any, url: string, data: any, registerActionResolve?: Function) => {
    config = config.filter(({ enable }: any) => enable === 1); // 过滤开启按钮
    const { objId } = this.cacheCurrentParams!;
    // 接口动作执行
    const res: any = await ajax({
      url,
      method: 'POST',
      data,
      ebBusinessId: objId || '',
    });
    this.afterAsyncCallBack(res, registerActionResolve);
  };

  @action
  afterAsyncCallBack = (res: any, registerActionResolve?: Function) => {
    const { status = true } = res;
    if (registerActionResolve) {
      registerActionResolve({
        status,
        isMobile: ua.device === 'Mobile',
        ...res,
        ebBusinessId: this.cacheCurrentParams?.objId || '',
      });
    }
  };
}
