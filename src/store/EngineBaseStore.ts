import { AnyObj } from '@weapp/ui';
import { ListData } from '@weapp/ui/lib/components/list';
import { action, observable, runInAction } from 'mobx';
import { ModuleType } from '../constants';
import { ajax } from '../utils/ajax';

/** **
 * 考虑到视图编辑页面的公用性，抽取公共类
 */
class BaseStore {
  // 当前编辑视图的一些基本信息存储
  @observable currentCardData: any = { id: '', name: '', type: ModuleType.Table };

  @observable selectedFormId: string = ''; // 表单id

  @observable appId: string = ''; // 应用id

  @observable pageId: string = ''; // 设计器页面id

  @observable isLoading: boolean = false;

  @observable formFields: any = {};

  @observable allFields: ListData[] = [];

  @observable conditionId: string = '';

  @observable isVirtualForm: boolean = false; // 是否是虚拟表单

  @observable checkedTypes: string[] = []; // 已有数据的字段设置项

  @observable visible: boolean = false;

  @action
  updateBaseCurrentData = (params: AnyObj) => {
    this.currentCardData = { ...this.currentCardData, ...params, isModify: true };
  };

  @action
  onConditionSetSave = (data: string, conditionId: string) => {
    this.conditionId = conditionId;
  };

  @action
  onConditionSetClear = () => {
    this.conditionId = '';
  };

  @action('获取所有字段')
  getAllFields = async (type?: string) => {
    const res = await ajax({
      url: `/api/ebuilder/form/list/getMainFieldsByObjId?apid=${this.appId}`,
      method: 'GET',
      params: { objId: this.selectedFormId, type },
      ebBusinessId: this.appId,
    });
    runInAction(() => {
      this.allFields = res;
    });
  };

  @action
  setCheckedTypes = (checked: boolean, datas: any[], checkKey: string) => {
    const newTypes = [...this.checkedTypes];
    if (checked) {
      if (datas.length === 0) {
        this.checkedTypes = newTypes.filter((key) => key !== checkKey);
      }
    } else if (datas.length > 0) {
      this.checkedTypes = newTypes.concat([checkKey]);
    }
  };
}

export default BaseStore;
