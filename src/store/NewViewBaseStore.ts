import { AnyObj, SearchAdvancedStore } from '@weapp/ui';
import { action, observable, toJS } from 'mobx';
import { EbdfDfValueParamsCacheKey, EbuilderFormModule, ListSearchCacheParams } from '../constants';
import { CommonSearchType, ResponseError, SearchType } from '../types/common';
import ls from '../utils/ls';
export type WidthParams = {
  width: string;
  resize?: boolean;
  minResizeWidth?: number;
  maxResizeWidth?: number;
};
// 多条件后缀
export const conditionSuffix = 'condition_type';
export interface ViewComProps {
  compId: string;
  pageId: string;
  config: any;
  isMobile: boolean;
  isDesign: boolean;
  ebParams?: AnyObj; // eb参数
  comServicePath?: string;
  page?: AnyObj;

  // 是否是表单后台引擎
  isEngine?: boolean;
  onRef?: (ref: any) => void;
}

/** *
 * 新抽象类公用store
 * 作用范围：视图日历，思维导图等表单&页面设计器模式
 * 组件化
 */
export class NewViewBaseStore {
  @observable responseError: ResponseError | null = null;

  @observable viewComInfo: ViewComProps | null = null;

  // 筛选、搜索相关
  // 高级搜索(看板除外,看板有单独的baseSearchStore)
  @observable searchAdvancedStore: SearchAdvancedStore = new SearchAdvancedStore();

  @observable searchParams: AnyObj = {}; // url参数:关联参数hideTop也通过这里传

  @observable dfSearchValues: AnyObj = {}; // getFilterDefault 接口返回的默认值(1筛选条件默认值;2数据源配置的条件参数)

  dfFieldsValues: AnyObj = {}; // 初始化筛选条件默认值

  @observable commonSearch: CommonSearchType = CommonSearchType.Advanced; // 记录高级搜索配置信息

  @observable advancedParams: AnyObj = {}; // 快捷/高级

  @observable filterSearchStore: any = null; // 顶部筛选store实例

  @observable conditionId: string = ''; // 顶部搜索的常用筛选所需的condition，其实就是高级搜索的conditionid

  @observable appManager: boolean = false; // 是否是管理员

  @observable comServicePath: string = 'ebuilder/coms'; // ebuilder/coms映射路径

  @action
  setComServicePath = (comServicePath: string) => {
    this.comServicePath = comServicePath || 'ebuilder/coms';
  };

  cardPageRef: any = null;

  @action
  editOrShowListData = (
    type: string,
    objid: string,
    dataid: string,
    dialogWidthParams: WidthParams,
  ) => {
    const { cardPageRef } = this;
    cardPageRef?.openFormCardPage(type, objid, dataid, dialogWidthParams);
  };

  /**
   * 格式化默认值:
   * @returns { type,searchType,字段信息 }
   */
  formatDfData = (dfValues: AnyObj) => {
    const { searchParamData = {} } = dfValues;
    const { groupFields, ...restData } = searchParamData;
    /** searchType */
    let searchType = SearchType.AdvanceSearch;
    if (this.commonSearch === CommonSearchType.TopSearch) {
      searchType = SearchType.FilterSearch;
    }
    /** type: 初始化 默认是所有 */
    const type = 'and';
    /** params */
    const params: AnyObj = {};
    Object.keys(restData).forEach((key: string) => {
      if (key.indexOf(conditionSuffix) < 0) {
        /** 处理其他条件 */
        const [newKey, suffix] = key.split('_customField');
        const datas = { [newKey]: restData[key] };
        /** 处理多条件 */
        const conValue = restData[`${newKey}_${conditionSuffix}${suffix ? `_customField${suffix}` : ''}`];
        if (conValue) {
          datas[`${newKey}_${conditionSuffix}`] = conValue;
        }
        params[newKey] = [...(params[newKey] || []), datas];
      }
    });
    return { type, searchType, ...params };
  };

  /**
   * 给顶部筛选、高级搜索设置默认值，
   * */
  @action
  setFilterDefault = (dfValues: AnyObj, formatValues?: AnyObj) => {
    const { searchType, type, ...params } = formatValues || this.formatDfData(dfValues);
    const { groupFields, ...restData } = dfValues?.searchParamData || {};
    this.dfFieldsValues = restData;
    this.dfSearchValues = {
      ...dfValues,
      searchParamData: { ...params, groupFields },
    };
    if (this.commonSearch === CommonSearchType.Advanced) {
      this.advancedParams = { formDatas: params, type };
    }
    ls.setItem(ListSearchCacheParams.CurrentSearchState, searchType);
    // 缓存默认值
    ls.setItem(EbdfDfValueParamsCacheKey, JSON.stringify(dfValues));
  };

  @action
  getAdPanelParams = (isAppManager: boolean) => {
    if (isAppManager) {
      return {
        saveAsPublic: true,
        currentModule: EbuilderFormModule,
      };
    }
    return {};
  };

  /**
   * 顶部搜索非注入之后处理回调
   */
  filterMount = () => {
    const newValues: AnyObj = {};
    /** 删除非筛选字段中的字段 */
    // @ts-ignore
    const fieldKeys = this.filterSearchStore?.filterData?.map(({ fieldName }) => fieldName) || [];
    Object.keys(this.dfSearchValues).forEach((key: string) => {
      const [name] = key.split(`_${conditionSuffix}`);
      if (!fieldKeys.includes(name)) {
        newValues[key] = this.dfSearchValues[key];
      }
    });
    this.dfSearchValues = newValues;
    /** 赋默认值 */
    this.filterSearchStore?.setDfaultValues(this.dfFieldsValues);
  };

  @action('筛选store实例获取')
  getFilterSearchInstance = (store: any) => {
    this.filterSearchStore = store;
  };
}

export default new NewViewBaseStore();
