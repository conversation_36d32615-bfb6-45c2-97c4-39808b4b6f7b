import { AnyObj, Dialog } from '@weapp/ui';
import { dayjs, getLabel, request, RequestResult } from '@weapp/utils';
import { AxiosRequestConfig } from 'axios';
import ebdcoms from './ebdcoms';

const { message } = Dialog;

export enum Status {
  Failed = -1,
  Success = 0,
}

type Callback = (data: any) => void | string;
type SuccessHandler = (data: any) => void | string;
type Error = boolean | string | Callback;
export interface AjaxRequestConfig extends AxiosRequestConfig {
  /** 请求成功后，显示的自定义成功信息，常用于操作成功后的提示 */
  success?: string | SuccessHandler;
  /**
   * 请求失败
   * boolean: 是否显示后端返回的错误信息
   * string: 显示自定义的错误信息
   * Function: 自行控制错误
   */
  error?: Error;
  /**
   * 副作用函数，在请求成功后失败时执行，可用于清除状态，如loading
   */
  effect?: (status: Status) => void;

  /** 用于处理后端抛错，前端组件内部未处理数据，导致页面报错空白的问题 */
  compatibleData?: any;
  /** eb的业务id，带有区分应用独立部署的部署服务标识等信息 */
  ebBusinessId?: string;
}

const handleError = (error: string | boolean | Callback | undefined, res: { code?: number; status?: boolean; msg: any; data: any }) => {
  if (typeof error === 'string') {
    message({
      type: 'error',
      content: error,
    });
  } else if (error === true || typeof error === 'undefined') {
    message({
      type: 'error',
      content: res?.msg,
    });
  } else if (typeof error === 'function') {
    const result = error(res);

    if (typeof result === 'string') {
      message({
        type: 'error',
        content: result,
      });
    }
  }

  return Promise.reject(res);
};
export const ajax = (config: AjaxRequestConfig): Promise<any> => ebdcoms.excu('ajax', config);
// 原始版本ajax
export function ajaxBf(config: AjaxRequestConfig): Promise<any> {
  const { error = true, success, effect, compatibleData, ebBusinessId = '', ...axiosConfig } = config;
  const req = request({
    ...axiosConfig,
    headers: {
      ebBusinessId: ebBusinessId || '10000000000000000',
      ...axiosConfig.headers
    }
  });
  return req
    .then((res: RequestResult) => {
      const { code, data, msg, status } = res;
      if (code === 200 && status) {
        effect?.(Status.Success);
        if (typeof success === 'string') {
          Dialog.message({ type: 'success', content: success });
        } else if (typeof success === 'function') {
          const successTip = success(data);
          if (typeof successTip === 'string') {
            Dialog.message({ type: 'success', content: successTip });
          }
        }
        return Promise.resolve(data);
      }
      if (!status || code < 200 || code >= 400) {
        effect?.(Status.Failed);
        if (code === 401) {
          const err: any = { msg: msg || (res as any)?.message, code };
          return Promise.reject(err);
        }
        // 499 临时鉴权方案提示用 。后续改回来，禁止提交
        if (code === 499) {
          Dialog.confirm({
            content: `appid${getLabel('221912', '鉴权返回异常')}，url:${axiosConfig.url},time:${dayjs(Date.now()).format(
              'YYYY-MM-DD HH:mm:ss'
            )},uerId:${window?.TEAMS?.currentUser?.id}`,
            onOk: () => {},
            onCancel: () => {},
          });
          return Promise.reject(msg);
        }
        return handleError(error, res);
      }
    })
    .catch((e: any) => {
      effect?.(Status.Failed);

      if (e?.message?.includes('Network')) {
        e.message = getLabel('184244', '网络出错，请检查网络连接是否正常');
      }

      if (typeof error === 'string') {
        Dialog.message({ type: 'error', content: error });
      } else if (typeof error === 'function') {
        const errorTip = error(e);
        if (typeof errorTip === 'string') {
          Dialog.message({ type: 'error', content: errorTip });
        }
        return Promise.resolve();
      }

      if (compatibleData !== undefined) {
        return Promise.resolve(compatibleData);
      }

      return Promise.reject(e);
    });
}

/**
 * @format2FormData
 * @param ajaxData [AnyObj]
 * @desc 将ajaxData转换为FormData格式
 * */
export const format2FormData = (ajaxData: AnyObj) => {
  const formData = new FormData();
  Object.keys(ajaxData).forEach((key: any) => {
    if (typeof ajaxData[key] === 'object') {
      ajaxData[key] = JSON.stringify(ajaxData[key]);
    }
    formData.append(`${key}`, ajaxData[key]);
  });
  return formData;
};

export enum LoadStatus {
  pending = 'pending',
  success = 'success',
  fail = 'fail',
}
