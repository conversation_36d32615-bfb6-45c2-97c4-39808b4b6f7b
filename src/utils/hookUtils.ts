/**
 * basically copied from ahooks
 */

import { DependencyList, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';

type noop = (this: any, ...args: any[]) => any;

type PickFunction<T extends noop> = (
    this: ThisParameterType<T>,
    ...args: Parameters<T>
) => ReturnType<T>;

function depsAreSame(oldDeps: DependencyList, deps: DependencyList): boolean {
    if (oldDeps === deps) return true;
    for (let i = 0; i < oldDeps.length; i++) {
        if (!Object.is(oldDeps[i], deps[i])) return false;
    }
    return true;
}

/**
 * @usage const a = useCreation(() => new Subject(), []);
 * @desc 代替useMemo / useRef使用
 *  */
export default function useCreation<T>(factory: () => T, deps: DependencyList) {
    const { current } = useRef({
        deps,
        obj: undefined as undefined | T,
        initialized: false,
    });

    if (current.initialized === false || !depsAreSame(current.deps, deps)) {
        current.deps = deps;
        current.obj = factory();
        current.initialized = true;
    }

    return current.obj as T;
}

/**
 * @usage const memoizedFn = useMemoizedFn<T>(fn: T): T;
 * @desc 持久化 function 的 Hook
 *  */
export function useMemoizedFn<T extends noop>(fn: T) {
    const fnRef = useRef<T>(fn);
    fnRef.current = useMemo<T>(() => fn, [fn]);

    const memoizedFn = useRef<PickFunction<T>>();
    if (!memoizedFn.current) {
        memoizedFn.current = function (this, ...args) {
            return fnRef.current.apply(this, args);
        };
    }

    return memoizedFn.current as T;
}

export const useMount = (_fn: () => void) => {
    const fn = useMemoizedFn(_fn);

    useEffect(() => {
        fn?.();
    }, [fn]);
};

export const useUnmount = (_fn: () => void) => {
    const fn = useMemoizedFn(_fn);

    useEffect(() => () => {
        fn?.();
    }, [fn]);
}

export const isFunction = (value: unknown): value is (...args: any) => any =>
    typeof value === 'function';

export type SetState<S extends Record<string, any>> = <K extends keyof S>(
    state: Pick<S, K> | null | ((prevState: Readonly<S>) => Pick<S, K> | S | null),
) => void;

/**
 * @usage const [state, setState] = useSetState<T>(initialState);
 */
export const useSetState = <S extends Record<string, any>>(
    initialState: S | (() => S),
): [S, SetState<S>] => {
    const [state, setState] = useState<S>(initialState);

    const setMergeState = useCallback((patch) => {
        setState((prevState) => {
            const newState = isFunction(patch) ? patch(prevState) : patch;
            return newState ? { ...prevState, ...newState } : prevState;
        });
    }, []);

    return [state, setMergeState];
};


type noop_boolean = (...args: any) => boolean;
type rt = [
    state: boolean,
    options: {
        setTrue: () => void,
        setFalse: () => void,
        toggle: () => void,
    }
];
/**
 * @usage const [bool, {setTrue, setFalse, toggle}] = useBoolean(initialState: boolean);
 */
export function useBoolean<T extends noop_boolean>(initBool?: boolean | T): rt {
    const [bool, setBool] = useState(initBool ?? false);

    const setTrue = useCallback(() => setBool(true), []);
    const setFalse = useCallback(() => setBool(false), []);
    const toggle = useCallback(() => setBool(preBool => !preBool), []);

    return [
        bool,
        { setTrue, setFalse, toggle },
    ]
};

type EffectHookType = typeof useEffect | typeof useLayoutEffect;
const createUpdateEffect: (hook: EffectHookType) => EffectHookType = (hook) => (effect, deps) => {
    const isMounted = useRef(false);

    // for react-refresh
    hook(() => {
        return () => {
            isMounted.current = false;
        };
    }, []);

    hook(() => {
        if (!isMounted.current) {
            isMounted.current = true;
        } else {
            return effect();
        }
    }, deps);
};
/**
 * @desc useEffect，但是忽略第一次渲染
 */
export const useUpdateEffect = createUpdateEffect(useEffect);