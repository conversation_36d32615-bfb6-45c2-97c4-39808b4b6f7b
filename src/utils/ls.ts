/**
 * 本地缓存
 */
const ls = {
  getItem(key: string, dftVal?: any) {
    const item = localStorage.getItem(key);
    try {
      if (item) {
        const datas = JSON.parse(item);
        // 有过期时间
        if (datas instanceof Object && datas.expires) {
          const { value, startTime, expires } = datas;
          const current = new Date().getTime();
          if (current - startTime > expires) {
            localStorage.removeItem(key);
            return undefined;
          }
          return JSON.parse(value);
        }
        return datas;
      }
      return dftVal;
    } catch {
      return item;
    }
  },

  setItem(key: string, value: any, expires?: number) {
    const val = typeof value === 'string' ? value : JSON.stringify(value);
    let item = val;

    // 设置过期时间
    if (expires) {
      item = JSON.stringify({
        startTime: new Date().getTime(),
        value: val,
        expires,
      });
    }

    localStorage.setItem(key, item);
  },

  removeItem(key: string) {
    localStorage.removeItem(key);
  },
};

export default ls;
