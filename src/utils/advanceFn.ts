import { FormStore, AnyObj } from '@weapp/ui';
import { SearchAdvancedInitAllDatas } from '@weapp/ui/lib/components/search-advanced/store/Store';
import { conditionSuffix } from '../constants';

/**
 * 高级搜索展示情况：只展示快捷/只展示高级/都展示/都不展示
 * @param param0
 * @returns
 */
export const getQuickParams = ({ layout }:SearchAdvancedInitAllDatas, customNoAd?:boolean) => {
  const params: AnyObj = {};
  const commonLys = layout?.[0] || [];
  /** 是否有快捷字段 */
  // eslint-disable-next-line max-len
  const noQuick = commonLys.filter((ly) => ly.groupId === 'commonGroup' && !!ly.needQuickSearch).length === 0;
  /** 是否有高级字段 */
  // eslint-disable-next-line max-len
  let noAd = commonLys.filter((ly) => ly.groupId === 'commonGroup' && !ly.colClassName).length === 0;
  if (customNoAd !== undefined) {
    noAd = customNoAd || noAd;
  }

  /** 返回null:不展示高级和快捷 */
  if (noAd && noQuick) {
    return null;
  }
  if (noAd) {
    /** 隐藏高级 */
    params.quickSearchInputProps = { suffix: null };
  } else if (noQuick) {
    /** 隐藏快捷 */
    params.onlyShowIcon = true;
  }
  return params;
};

/**
 * 如果不处理，默认值会有高亮问题
 * @param store 表单store
 * @param datas 表单的datas
 */
export const onAfterAdInit = (store: FormStore, datas: any) => {
  const comKeys = (store.layout?.[0] || [])
    .filter(({ colClassName, groupId }) => groupId === 'commonGroup' && !colClassName)
    .map(({ id }) => id);
  const newDatas:any = {};
  Object.keys(datas).forEach((key) => {
    const [_key] = key.split(conditionSuffix);
    if (comKeys.includes(_key)) {
      newDatas[key] = datas[key];
    }
  });
  store.updateDatas(newDatas);
};
