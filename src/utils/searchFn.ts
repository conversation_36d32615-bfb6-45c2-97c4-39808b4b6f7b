import { SearchDatas } from '@weapp/ui';
import { toJS } from 'mobx';
import { conditionSuffix } from '../constants';

export const parseAdData = (searchDatas: SearchDatas) => {
  const resData: any = { ...searchDatas };
  Object.keys(searchDatas).forEach((key: string) => {
    if (key === 'formDatas') {
      let formDatas: any = searchDatas[key];
      Object.keys(formDatas).forEach((formdataKey: any) => {
        const valItem = formDatas[formdataKey];
        if (Array.isArray(toJS(valItem))) {
          valItem.forEach((valObj: any) => {
            Object.keys(valObj).forEach((valKey: string) => {
              const val = valObj[valKey];
              if (val?.isComboSelect) {
                const children: any[] = val?.childValue;
                const conditionType = `${formdataKey}_${conditionSuffix}`;
                const conditions: any = {};
                children.forEach((obj: any) => {
                  if (conditionType in valObj) {
                    const conditionValue = valObj[conditionType];
                    const conditionName = `${obj.fieldName}_${conditionSuffix}`;
                    conditions[conditionName] = conditionValue;
                  }
                  formDatas = {
                    ...formDatas,
                    [obj.fieldName]: [{
                      ...obj.fieldValue,
                      ...conditions,
                    }],
                  };
                });
                delete formDatas[formdataKey];
                if (conditionType in valObj) {
                  delete formDatas[conditionType];
                }
              }
            });
          });
        }
      });
      resData[key] = formDatas as any;
    }
  });
  return { ...resData };
};

/**
 * 默认常用筛选值格式化特殊字段：日期
 */
export const parseDateValue = (datas: any, items: any) => {
  const newDatas = { ...datas };
  Object.keys(newDatas).forEach((key: string) => {
    const _value = newDatas[key];
    /** 日期特殊格式处理 */
    if (
      items[key]?.itemType === 'DATEPICKER'
      && Object.prototype.toString.call(_value) === '[object Object]'
    ) {
      newDatas[key] = _value?.value || [];
    }
  });
  return newDatas;
};

// 多级下拉
export const parseComboValue = (datas: any) => {
  let newDatas = { ...datas };
  Object.keys(newDatas).forEach((key: string) => {
    const _value = newDatas[key];
    if (Object.prototype.toString.call(_value) === '[object Object]' && _value.isComboSelect) {
      const children: any[] = _value?.childValue;
      newDatas = {
        ...newDatas,
        ...(children.reduce((pre, { fieldValue, fieldName }) => ({
          ...pre,
          ...fieldValue,
          [`${fieldName}_${conditionSuffix}`]: newDatas[`${key}_${conditionSuffix}`],
        }), {})),
      };
    }
  });
  return newDatas;
};

export default {
  parseAdData,
};
