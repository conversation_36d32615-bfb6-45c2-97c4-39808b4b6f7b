/**
 * <AUTHOR>
 * @createTime 2022 -03 -01
 */
import { EventEmitter } from 'events';
import { CustomEventName } from './types';

/** 事件注册器 */
export const customEvent = new EventEmitter();
/** 事件队列 */
let eventQueue: { [key: string]: any } = {};

/**
 * 全局事件监听注册器
 * */

const EE = {
  /**
   * @function on
   * @desc 绑定事件
   * @param eventName [CustomEventName] 注册的事件名称
   * @param fn [Function] 被监听函数，不能为匿名函数
   * @param id [?string]
   */
  on: (eventName: CustomEventName, fn: any, id?: string) => {
    let name: string = eventName;
    if (id) {
      name = `${eventName}_${id}`;
    }
    if (eventQueue[name]) {
      EE.off(name as CustomEventName);
    } else {
      customEvent.on(name, fn);
    }
    eventQueue[name] = fn;
  },
  /**
   * @function off
   * @desc 移除事件
   * @param eventName [CustomEventName] 解除注册的事件名称
   * @param fn [Function] 被监听函数，不能为匿名函数
   * @param id [?string]
   */
  off: (eventName: CustomEventName, fn?: any, id?: string) => {
    if (!eventName) return;
    let name: string = eventName;
    if (id) {
      name = `${eventName}_${id}`;
    }
    if (fn && fn === 'removeAll') {
      if (id) {
        // 移除某id下所有事件
        Object.keys(eventQueue).forEach((key: any) => {
          if (key.includes(id)) {
            customEvent.off(key, eventQueue[key]);
            delete eventQueue.key;
          }
        });
      } else {
        // 移除所有事件
        customEvent.removeAllListeners();
        eventQueue = {};
      }
    } else {
      fn = fn || eventQueue[name];
      customEvent.off(name, fn);
      delete eventQueue[name];
    }
  },
  /**
   * @function emit
   * @desc 触发事件
   * @param eventName [CustomEventName] 触发已注册的事件
   * @param args [any] 传递给被触发函数的参数
   * @param id [?string]
   */
  emit: (eventName: CustomEventName, args: any, id?: string) => {
    let name: string = eventName;
    if (id) {
      name = `${eventName}_${id}`;
    }
    customEvent.emit(name, args);
  },

  /**
   * @function show
   * @desc 查看注册事件队列
   * */
  show: () => eventQueue,
};

export default EE;
