export enum CustomEventName {
  cardAdd = 'card.add',
  flowAdd = 'flow.add',
  cardEdit = 'card.edit',
  cardClose = 'card.close',
  mCardAdd = 'mCard.add',
  mFlowAdd = 'mFlow.add',
  mCardEdit = 'mCard.edit',
  mCardClose = 'mCard.close',
  // 回收站列表
  recycleList = 'recycle.list',
  mRecycleList = 'mRecycleList.list',
  // 批量新增和修改
  batchEdit = 'table.batchAdd',
  batchAdd = 'table.batchEdit',
  // 表格关联数据
  ebListBrowser = 'open-entity-ebListBrowser',
  // 表格未读已读标识更新
  ebListReadStatus = 'table.updateReadStatus',
  // 列表自定义按钮数据更新
  ebUpdateGridDisplayButtons = 'table.gridDisplayButtons.update',
  ebGridDisplayButtonsClick = 'eb.gridDisplayButtons.click',
  mEbGridDisplayButtonsClick = 'mEb.gridDisplayButtons.click',
  // 查看占用详情
  occupyBrowser = 'viewPort.occupyBrowser',
}

const types = {
  CustomEventName,
};

export default types;
