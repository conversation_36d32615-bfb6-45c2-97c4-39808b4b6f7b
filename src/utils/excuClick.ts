import { AnyObj } from '@weapp/ui';

type clickProps = {
  data: any; // 行数据
  eventGroup: any[][]; // 事件动作配置
  allDatas?: any[]; // 当前所有数据
  dom?: Element; // 点击的dom元素
  eventAction: any; // 基础设计器内的触发函数， store.eventAction
  id?: string; // 组件id
  eventType?: 'CLICK' | 'CLICKDATA'; // 触发的事件类型
  plugin?: any;
  customOpts?: any; // 自定义链接参数
  customParams?: AnyObj;
  [key: string]: any;
};

/**
 * 获取点击事件默认第一个打开页面事件
 * @param eventGroup 事件动作组
 * @returns
 */
const getDftClickPageData = (eventGroup: any[][] = [], type: any = 'CLICK') => {
  const eventData: any[] = eventGroup[0];
  const clickIndex = eventData?.findIndex((el) => el.type === type);
  if (clickIndex > -1 && eventGroup[0][clickIndex].events[0]?.type === 'NewPage') {
    return {
      linkInfo: eventGroup[0][clickIndex].events[0].linkInfo,
      clickIndex,
    };
  }
  return {
    clickIndex,
  };
};

/**
 * 触发事件动作函数
 */
const excuClick = (opts: clickProps) => {
  const {
    data = {},
    eventGroup,
    dom,
    eventAction,
    plugin,
    eventType = 'CLICKDATA',
    customOpts,
    allDatas = [],
    customParams = {},
  } = opts;
  const pageData = getDftClickPageData(eventGroup, eventType);

  if (pageData.clickIndex > -1) {
    const DataPlugin = plugin?.use('Data') as any;
    const linkOpts: any = {
      fields: { ...data },
      ...(customOpts || {}),
    };
    const dynamicFieldsValue = Object.keys(data).map((k) => ({
      id: k,
      value: data[k],
    }));
    const allSubFormData = (DataPlugin?.data || allDatas)
      .map((el: any) => {
        if (el.id === data.id) {
          return el;
        }
        return null;
      })
      .filter(Boolean);
    const p = {
      dom,
      data,
      linkOpts,
      dynamicFieldsValue,
      allSubFormData,
      ...customParams,
    };
    const [params] = (plugin?.invoke('getExcuEventActionsParams', { args: [p] }) as any[]) || [p];
    plugin?.invoke('onBeforeExcuEventActions', { args: [eventGroup, p] });
    eventAction?.create(eventGroup).trigger(eventType, params);
  }
};

export default excuClick;
