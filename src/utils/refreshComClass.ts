import { toJS } from 'mobx';
import { isArray, isEmpty } from '@weapp/utils';
import ebdcoms from './ebdcoms';

const getPageFilter = (config: any, pageId: string = '', comId?: string) => {
  const { getEbParams } = ebdcoms.get();
  const pageParams = getEbParams('pageParams', pageId);

  // 1. 当前列表存在数据源，并且页面存在筛选条件的配置
  if (!isEmpty(toJS(config.dataset)) && !isEmpty(pageParams)) {
    const { id, groupId, type } = config.dataset!;
    const datasetStr1 = comId ? `${type}_${groupId}_${id}_${comId}` : `${type}_${groupId}_${id}`;
    const datasetStr2 = `${type}_${groupId}_${id}`;
    if (pageParams.condition) {
      const pageFilterOps = pageParams.condition[datasetStr1] || pageParams.condition[datasetStr2];
      return pageFilterOps;
    }
  }
};

/** 刷新组件动作都可以调用这个，基本是统一格式 */
class RefreshComFunc {
  pageFilter: any[] | undefined = undefined;
  filterVal: any[] | undefined = undefined;
  currentFilterId = '';

  constructor() {
    ebdcoms.load();
  }

  reset() {
    this.pageFilter = undefined;
    this.filterVal = undefined;
    this.currentFilterId = '';
  }

  setFilter(val: any[], filterId?: string) {
    if (filterId) {
      this.currentFilterId = filterId
    }
    if (val?.length && val[0].actionId) {
      // 刷新组件事件
      if (this.pageFilter) {
        this.pageFilter = this.pageFilter.filter((filter) => filter.comId !== filterId);
        this.pageFilter = [...this.pageFilter, ...val];
      } else {
        this.pageFilter = [...val];
      }
    } else if (this.filterVal && filterId) {
      this.filterVal = this.filterVal.filter((filter) => filter.conditionComId !== filterId);
      if (isArray(val)) {
        this.filterVal = this.filterVal.concat(val);
      }
    } else {
      this.filterVal = val;
    }
  }

  getFilter(pageJumpParams?: any) {
    let pageJumpFilter: any = {};
    let filterIsNull = true;
    let isOverrideFilter = false; /** 是否覆盖条件 */
    if (pageJumpParams?.config && pageJumpParams?.pageId && pageJumpParams?.comId) {
      pageJumpFilter = getPageFilter(
        pageJumpParams?.config,
        pageJumpParams?.pageId,
        pageJumpParams?.comId,
      );
    }

    let pageFilter = pageJumpFilter;
    let _pageFilter = this.currentFilterId ? (this.pageFilter || []).filter((i: any) => i.comId === this.currentFilterId) : this.pageFilter
    if (
      _pageFilter?.length
      && _pageFilter[0].conditionSet
      && (_pageFilter[0].conditionSet.relationship
        || _pageFilter[0].conditionSet.type === 'sql_where'
        || _pageFilter[0].conditionSet.type === 'http')
    ) {
      pageFilter = {
        isParent: true,
        key: Math.round(Math.random() * 10000000),
        parentNode: '',
        relationship: '1',
        datas: [],
      };

      filterIsNull = _pageFilter[0]?.filterIsNull;
      isOverrideFilter = !!_pageFilter[0]?.isOverrideFilter;

      if (pageJumpFilter) {
        pageFilter.datas.push(pageJumpFilter);
      }
      _pageFilter.forEach((el) => {
        pageFilter.datas.push(el.conditionSet);
      });
    } else if (
      _pageFilter?.length
      && _pageFilter[0].conditionSet
      && !_pageFilter[0].conditionSet.relationship
    ) {
      pageFilter = [];
      if (pageJumpFilter) {
        pageFilter.push(pageJumpFilter);
      }
      _pageFilter.forEach((el) => {
        pageFilter.push({ ...el.conditionSet });
      });
    }

    return {
      pageFilter: { filter: pageFilter, filterIsNull, isOverrideFilter },
      filter: this.filterVal || [],
    };
  }
}

export default RefreshComFunc;
