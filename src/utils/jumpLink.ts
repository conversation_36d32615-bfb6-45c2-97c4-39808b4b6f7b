import { AnyObj } from '../types/common';
import { LayoutType, OpenMode } from '../constants/common';
import ebdcoms from '../utils/ebdcoms';
import { toPageParams, getFixedObj } from './';

export interface PageInfo {
  pageType: any;
  pageId: string;
  pageName: string;
  appid?: string;
  pageViewType?: string; // 页面显示类型，针对pagetype做扩充处理
}
export type ConditionSourceTypes = {
  // 关联、按钮等数据源的条件参数信息
  targetConditionId?: string; // 关联id或者按钮id
  openSource?: 'associated' | 'button';
  openSourceId?: string;
};
export interface LinkInfo {
  name?: string;
  page: PageInfo;
  openMode: string;
  winSize?: string;
  winSizeNew?: string;
  params?: any[]; // 条件参数
  ownParams?: ConditionSourceTypes;
  /** 链接表单字段 */
  urlFieldId?: string; // "775400334755110912"
  urlFieldText?: string; // "单行文本"
  urlLinkType?: string; // "1"
  urlObjId?: string; // "775400356333502465"
  type?: any;
  fieldList?: any[];
  /** EB_202207256841改版，数据列表可配置打开方式（老数据打开方式默认值为弹出窗口-无效，需要此字段判断兼容下老数据） */
  isNew?: boolean;
  layoutType?: any;
  linkUrl?: string;
  isUnitFill?: boolean;
  /** 页面图标 */
  pageIcon?: string | any;
  isRelationCustomPage?: boolean; // 关联自定义页面组件
  /** 页面标题 */
  pageTitle?: string;
}
/**
 * @function jumpToCard
 * @param openType
 * @param cardInfo
 * @param params
 * @param routerProps
 * @param otherLinkInfo
 * @description 打开卡片
 * */
export const jumpToCard = async (
  openType: OpenMode,
  cardInfo: {
    appId: string;
    objId: string;
    type: LayoutType;
    name?: string;
    dataId?: string;
  },
  params: any[],
  routerProps: AnyObj,
  otherLinkInfo?: AnyObj,
  digType?: string
) => {
  const { appId, objId, type, name = '', dataId } = cardInfo;
  if (!window.weappEbdcoms) {
    await ebdcoms.load();
  }
  const { JumpLink } = await ebdcoms.get();
  if (dataId) {
    params = params.concat({
      name: 'id',
      nameType: 'dataId',
      value: dataId,
      type: 'fixed',
    });
  }
  let newRouterProps = { ...routerProps }
   // 表单下新建分组还是要刷新下当前看板
  if (digType !== 'group') {
    newRouterProps = {
      ...routerProps,
      onSave: () => {
        routerProps?.history?.go(-1)
      }
    }
  };
  JumpLink(
    {
      ...otherLinkInfo,
      openMode: openType,
      name,
      page: {
        pageViewType: '1',
        pageType: 'LAYOUT',
        appid: appId,
        pageId: `${type}/${objId}`,
        pageName: name,
      },
      params,
    },
    newRouterProps
  );
};

export const arrayParamsUnique = (params: any[]) => {
  if (!params || !params.length) return [];
  const nameHash: AnyObj = {};
  const uniqParams = params.reverse().filter((prm: any) => {
    if (!prm.name) return true;
    // 如果该值首次出现，则留下
    if (!nameHash[prm.name]) {
      nameHash[prm.name] = true;
      return true;
    }
    return false;
  });
  return uniqParams;
};

/**
 * 参数配置：routePage那边接收
 * 1 卡片：新窗口和当前窗口只取了ownParams;侧滑只取了params；因此卡片ownParams和params都赋相同的值
 * 2 列表、视图：参数都放到params中
 */
export const setLinkParams = (linkInfo: LinkInfo, PageType: any) => {
  const {
    page: { pageType },
    params = [],
    ownParams = {},
  } = linkInfo;
  const newParams = [...params, ...toPageParams(Object.keys(ownParams), cur => [cur, (ownParams as any)[cur]])];
  switch (pageType) {
    case PageType.Layout:
      return {
        ...linkInfo,
        ownParams: { ...ownParams, ...getFixedObj(params) },
        params: newParams,
      };
    case PageType.Search:
    case PageType.ViewPort:
      return { ...linkInfo, params: newParams };
    default:
      return linkInfo;
  }
};
