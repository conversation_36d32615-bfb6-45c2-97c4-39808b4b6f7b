import { qs } from '@weapp/utils';

/**
 * @function getSearchParams
 * @desc 获取url参数
 * @return object [key:value]
 */
export const getSearchParams = () => {
  const location = window.__location__ || window.location;
  if (!location.href.includes('?')) return;
  const hrefArr = location.href.split('?');
  const queryParams = hrefArr.length > 0 ? hrefArr[hrefArr.length - 1] : '';
  // 重置掉__location__ ,放置影响关联页面参数取值
  // ios下页面url参数取参存在问题
  window.__location__ = null;
  return qs.parse(queryParams);
};

const parseURL = {
  getSearchParams,
};

export type CutRule = (path: string, word: string) => boolean
/**
 * @cutPathBeforeRoutePush
 * @desc 去除复杂场景下的路由push时直接向后拼接导致的路由错误问题
 * @param parentPath 父路由
 * @param keywords 分割参数，去除父路由中的分割参数及其后面的字符串
 * @param cutRule 分割规则，可以在调用的地方自定义分割规则
 * */
export const cutPathBeforeRoutePush = (
  parentPath: string,
  keywords: string[] = ['/layoutdlg', '/card'],
  cutRule?: CutRule,
) => {
  let path = parentPath;
  keywords.forEach((word: string) => {
    const rule = !cutRule || cutRule(path, word);
    if (path.indexOf(word) >= 0 && rule) {
      path = path.slice(0, path.indexOf(word));
    }
  });
  return path;
};

export default parseURL;
