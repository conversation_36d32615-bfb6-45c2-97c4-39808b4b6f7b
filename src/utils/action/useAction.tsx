import { AnyObj, Dialog, MDialog } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { ListButtonType } from '../../constants';
import { ActionType } from '../../constants/common';
import { isMobile } from '../index';
import './index.less';
import MMessage from './message';
import { ActionException, ActionRecord } from './types';
import { markSpecialAction, mergeAction } from './utils';

// 删除前的成功执行规则不做提示，避免弹出后，在弹出成功的双提示问题
const validateToHideMsg = (buttonKey: string, actions: any[], index: number, hasFail: boolean) => {
  const isTableDelete = buttonKey === ListButtonType.TableDelete;
  const matchToHide = isTableDelete && actions[index - 1]?.actionType !== 'system';
  return matchToHide && !hasFail; // 表格删除，并且是全成功的情况,则隐藏信息提示
};

export interface ActionQueueProps {
  status?: boolean;
  callback?: Function /* 有条件执行的内容，如刷新，esb某些场景不执行 */;
  clearAction?: Function /* 用来清除变量和状态等无条件执行的内容 */;

  /** 是否移动端，不一定返回这个 */
  isMobile?: boolean;
  /** 失败 */
  failNum?: number | string;
  failMsg?: string;

  /** 成功 */
  successNum?: number | string;
  successMsg?: string;
  successedIds?: string;
  needRefresh?: boolean;
  /** 条件不满足-也算成功 */
  conditionFailMsg?: string;

  /** 如果不需要弹出提示，如规则执行时，该规则已被删除 */
  ignore?: boolean;
  extraEvent?: AnyObj;
  interrupt?: boolean; // 执行队列中断，终止执行
}

/**
 * 动作触发
 */
export function triggerActions(
  actions: ActionRecord[],
  buttonKey: string,
  emitFns: Function,
  routerProps?: any,
  endCallBack?: Function,
) {
  actions = markSpecialAction(buttonKey, actions);
  actions = mergeAction(actions);
  if (actions.length === 1) {
    // 因为默认有一个长度
    if (isMobile()) {
      MDialog.toast({ type: 'info', content: getLabel('61986', '该按钮未启用任何动作！') });
    } else {
      Dialog.message({ type: 'info', content: getLabel('61986', '该按钮未启用任何动作！') });
    }
  }
  const endActions: Function[] = [];
  const clearActions: Function[] = [];
  const len = actions.length - 1;
  /**
   * reduce
   * total必需。初始值, 或者计算结束后的返回值。
   * currentValue必需。当前元素
   * currentIndex可选。当前元素的索引
   * arr可选。当前元素所属的数组对象。
   */
  return actions.reduce(
    (promise: Promise<any>, action, currentIndex) => promise.then((result: ActionQueueProps) => {
      if (result && result.callback) {
        endActions.push(result.callback);
      }
      if (result && result.clearAction) {
        clearActions.push(result.clearAction);
      }
      const lastAction = () => {
        if (endActions.length > 0) {
          endActions[endActions.length - 1](); // 由于队尾动作都是刷新，只执行一个最后一个回调
        }
        // 所有动作执行完毕后，执行一次外部回调
        typeof endCallBack === 'function' && endCallBack();
      };
      const endClear = () => {
        clearActions.forEach((clearAction: Function) => {
          try {
            if (clearAction && typeof clearAction === 'function') {
              clearAction();
            }
          } catch (e) {
            // eslint-disable-next-line no-console
            console && console.error(e);
          }
        });
      };

      // 消息提醒
      if (result && result?.status !== undefined) {
        const needHide = validateToHideMsg(buttonKey, actions, currentIndex, !!result?.failNum);
        if (!needHide) {
          MMessage.action(result, result?.extraEvent, lastAction, endClear);
        }
      }

      // 执行到队尾,最后执行
      if (action && typeof action === 'function') {
        if (isEmpty(result?.extraEvent) && currentIndex === len) {
          lastAction();
          endClear();
        }
        return;
      }

      if (result?.interrupt) {
        throw ActionException.Break;
      } // 执行中断

      // 动作失败后
      if (result && result.status === false) {
        return Promise.reject(result);
      }

      // 下一次动作
      const { actionType } = action;
      /**
         * 系统按钮没有事件名称，默认取传进来的当前事件
         * 自定义按钮取事件名称
         */
      const res = emitFns(actionType === ActionType.System ? buttonKey : actionType, routerProps)(
        action,
        buttonKey,
      );
      return Promise.resolve(res);
    }),
    Promise.resolve(),
  );
}

const useAction = { triggerActions };
export default useAction;
