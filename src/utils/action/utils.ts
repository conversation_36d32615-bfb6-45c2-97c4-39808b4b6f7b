import { confirmAction } from './confirm';
import { CardButtonType } from '../../constants';
import { ActionType } from '../../constants/common';
import { ActionRecord } from './types';
import JsCode from '../js-code';

/**
 * 此方法处理规则合并，相邻的规则统一合并在一起  &&&  esb场景
 * @param actions
 * @returns
 */
export function mergeAction(actions: ActionRecord[]): any[] {
  const mergedActions = [] as ActionRecord[];

  // 判断相邻action是不是都是动作流
  const mergeEsb = (action: ActionRecord, lastAction: ActionRecord) => {
    const isTrue = action.actionType === ActionType.Esb && lastAction;
    return isTrue && lastAction.actionType === ActionType.Esb;
  };

  actions.forEach((action: ActionRecord) => {
    if (action.actionType === ActionType.System) {
      mergedActions.push(action);
    } else if (action.enable === 1) {
      // 开启的动作
      const lastIndex = mergedActions.length - 1 || 0;
      const lastAction = mergedActions[lastIndex];

      if (mergeEsb(action, lastAction)) {
        // 合并esb按钮
        lastAction.recordIds = [...lastAction.recordIds, action.idStr];
        mergedActions.splice(lastIndex, 1, lastAction);
      } else if (action.actionType === ActionType.Esb) {
        mergedActions.push({
          ...action,
          recordIds: [action.idStr],
        });
      } else {
        mergedActions.push(action);
      }
    }
  });

  // 函数执行需要抛出异常信息result，reduce函数需要多执行一次
  return [...mergedActions, Promise.resolve];
}

/**
 * @function isSaveAction
 * @param actionKey [string] 按钮key
 * @desc 是否为保存动作，保存动作前，可执行校验规则
 * */
const isSaveAction = (actionKey: string) => actionKey === CardButtonType.Save
  || actionKey === CardButtonType.EditSave
  || actionKey === CardButtonType.SaveAndCreate
  || actionKey === CardButtonType.TempSave; // 暂存实际没有任何动作

/**
 * @function markSpecialAction
 * @param actionKey [string] 按钮key
 * @param actions [ActionRecord<any>[]] 按钮上的动作序列
 * @desc 特殊执行的动作:在保存动作之前执行的规则，需特殊标记下，走不同的接口
 * */
export const markSpecialAction = (buttonKey: string, actions: ActionRecord[]) => {
  let markActions: ActionRecord[] = [];
  if (isSaveAction(buttonKey)) {
    // 保存动作
    const systemActionIndex = actions.findIndex(
      (item: ActionRecord) => item.actionType === ActionType.System,
    ); // 以系统动作为分界线
    const prevActions = actions.slice(0, systemActionIndex); // 系统动作前面的动作
    const restActions = actions.slice(systemActionIndex);
    if (prevActions.length > 0) {
      markActions = prevActions.map((item: ActionRecord) => {
        if (item.actionType === ActionType.Esb) {
          // ESB动作前置执行需执行校验接口
          return {
            ...item,
            isRunBeforeSave: true,
          };
        }
        return item;
      });
    }
    return [...markActions, ...restActions];
  }
  return actions;
};

export const openConfirmBox = (config: any, isMobile: boolean, registerActionResolve: Function) => {
  const {
    confirmTitle: title,
    confirmContent: content,
    confirmTrue: okText,
    confirmFalse: cancelText,
  } = config;
  confirmAction(
    {
      title,
      content,
      okText,
      cancelText,
    },
    isMobile,
    registerActionResolve,
  );
};

export const runJsCode = (
  config: any,
  isMobile: boolean,
  routerProps: any,
  registerActionResolve: Function,
) => {
  const act = new JsCode();
  act.runJsCode(config, isMobile, routerProps, registerActionResolve);
};

const utils = {};
export default utils;
