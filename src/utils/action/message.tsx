import {
  Button, <PERSON>alog, Icon, MDialog,
} from '@weapp/ui';
import { classnames, getLabel, isEmpty } from '@weapp/utils';
import { action, observable } from 'mobx';
import React from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { closeWindow } from '../closeWindow';
import { ebdfClsPrefix } from '../../constants';
import { isMobile, isSpaCard } from '../index';
import { ExtraEvent, PageActionType, ReminderType } from './types';
import { ActionQueueProps } from './useAction';

/**
 * 设计器预览的时候，isMobile这个函数不准，优先使用data.mobile判断
 * */
const isM = (data: any) => (data.isMobile !== undefined ? data.isMobile : isMobile());

export class Message {
  @observable promptRef: any = null;

  @observable promptRef2: any = null;

  @action
  getInstance = (el: any) => {
    this.promptRef = el;
  };

  @action
  getInstance2 = (el: any) => {
    this.promptRef2 = el;
  };

  @action
  onPress = () => {
    this.promptRef && this.promptRef.onClose();
  };

  /** 规则 主提示信息 */
  getTip = (fNum: number, failMsg: string, sNum: number, detail?: () => React.ReactNode) => (
    <div className={`${ebdfClsPrefix}-action-message`}>
      <div className={classnames({ [`${ebdfClsPrefix}-content-center`]: !detail })}>
        {getLabel('235056', '您总共执行 {0} 条数据', [`${fNum + sNum}`])}
      </div>
      <When weId="rule_message_jseqft" condition={!!sNum}>
        <div className={`${ebdfClsPrefix}-action-message-body`}>
          <Icon
            weId="Icon-correct02_91vokq"
            name="Icon-complete"
            size="md"
            className={`${ebdfClsPrefix}-action-message-success`}
          />
          <span>{getLabel('235057', '{0} 条执行成功', [`${sNum}`])}</span>
        </div>
      </When>
      <When weId="rule_message_5fv8dw" condition={!!failMsg && fNum > 0}>
        <div className={`${ebdfClsPrefix}-action-message-body`}>
          <Icon
            weId="Icon-error02_g16inj"
            name="Icon-cancel"
            size="md"
            className={`${ebdfClsPrefix}-action-message-fail`}
          />
          <If weId="rule_message_46a05q" condition={fNum > 1}>
            <Then weId="rule_message_2mj8mx">
              <span>
                {getLabel('235058', '{0} 条执行失败, {1} 条终止执行', ['1', `${fNum - 1}`])}
              </span>
            </Then>
            <Else weId="rule_message_8wsvrh">
              <span>{getLabel('235059', '{0} 条执行失败', ['1'])}</span>
            </Else>
          </If>
          <When weId="_nqudaw" condition={!!detail}>
            {detail?.()}
          </When>
        </div>
      </When>
    </div>
  );

  @action
  show = (data: ActionQueueProps) => {
    if (data?.ignore) return; // 消息忽略提醒

    const { successNum = '0', failNum = '0', failMsg = '' } = data;

    const successMsg = data?.successMsg || getLabel('235060', '您已成功执行了 {0} 条数据', [`${successNum}`]);

    const sNum = typeof successNum === 'string' ? parseInt(successNum, 10) : successNum;
    const fNum = typeof failNum === 'string' ? parseInt(failNum, 10) : failNum;

    /** 错误详情 */
    const openErrorDetail = () => {
      const title = getLabel('235062', '详情信息');
      /** 错误信息 */
      const msg: React.ReactNode = (
        <div className={`${ebdfClsPrefix}-action-message-detail-info`}>
          {/* <p>{conditionFailMsg}</p> */}
          <p>{failMsg}</p>
          <When weId="rule_message_asvk0f" condition={fNum > 1}>
            <p>{getLabel('235063', '因上一条失败，后续 {0} 条终止执行', [`${fNum - 1}`])}</p>
          </When>
        </div>
      );
      if (isM(data)) {
        MDialog.prompt({
          title,
          mask: true,
          maskClosable: true,
          message: msg,
          onClose: () => {},
          prompt: false,
          getInstance: this.getInstance,
          footer: [
            {
              key: 'cancel',
              text: getLabel('235064', '关闭'),
              onPress: this.onPress,
            },
          ],
        });
      } else {
        Dialog.confirm({
          width: 600,
          className: `${ebdfClsPrefix}-action-message-confirm`,
          title,
          content: msg,
        });
      }
    };

    /** 执行 */
    if (isM(data)) {
      // 移动端或模拟移动端
      if (!fNum) {
        if (successNum) {
          // 只有成功，无失败
          MDialog.toast({
            type: 'success',
            content: successMsg,
          });
        } else {
          // 无成功，无失败，异常
          MDialog.toast({
            type: 'fail',
            content: getLabel('235065', '执行异常'),
          });
        }
      } else if (fNum === 1 && sNum === 0) {
        // 执行失败
        MDialog.toast({ type: 'fail', content: failMsg });
      } else {
        MDialog.prompt({
          // 执行失败
          title: getLabel('235066', '消息提醒'),
          mask: true,
          maskClosable: true,
          message: (
            <div className={`${ebdfClsPrefix}-action-msg-mobile`}>
              {this.getTip(fNum, failMsg, sNum)}
            </div>
          ),
          onClose: () => {},
          onOk: openErrorDetail,
          prompt: false,
          okText: getLabel('235067', '查看详情'),
        });
      }
    } else if (!fNum) {
      // PC端
      if (successNum) {
        // 只有成功，无失败
        Dialog.message({
          type: 'success',
          content: successMsg,
        });
      } else {
        // 无成功，无失败，异常
        Dialog.message({
          type: 'error',
          content: getLabel('235065', '执行异常'),
        });
      }
    } else if (fNum === 1 && sNum === 0) {
      // 执行失败
      Dialog.message({ type: 'error', content: failMsg });
    } else {
      Dialog.confirm({
        // 执行失败
        title: getLabel('235066', '消息提醒'),
        content: this.getTip(fNum, failMsg, sNum, () => (
          <Button
            weId="fail_d4g58r"
            type="link"
            className={`${ebdfClsPrefix}-action-message-detail`}
            onClick={openErrorDetail}
          >
            {getLabel('235067', '查看详情')}
          </Button>
        )),
      });
    }
  };

  closeDialogOrWindow = () => {
    try {
      if (isSpaCard()) {
        // 卡片单页打开
        closeWindow();
      } else if (
        window.location.href.includes('/card/')
        || window.location.href.includes('dlg/')
        || window.location.href.includes('EbCardViewDialog/')
      ) {
        window.history.go(-1);
      } else {
        closeWindow();
      }
    } catch {
      return false;
    }
  };

  @action
  action = (
    data: ActionQueueProps,
    extraEvent?: any,
    lastAction?: Function,
    endClear?: Function,
  ) => {
    if (extraEvent && !isEmpty(extraEvent)) {
      const evt = (extraEvent || {}) as ExtraEvent;
      const { status, successMsg, failMsg } = data;
      const handlePageAction = () => {
        this.promptRef2 && this.promptRef2.onClose();
        if (evt.pageAction === PageActionType.JumpPage) {
          if (evt?.jumpUrl) {
            window.location.href = evt.jumpUrl;
          }
        } else if (evt.pageAction === PageActionType.ClosePage) {
          if (isM(data)) {
            window.history.go(-1);
          } else {
            this.closeDialogOrWindow();
          }
        } else if (evt.pageAction === PageActionType.Refresh) {
          lastAction?.();
          endClear?.();
        }
        endClear?.();
      };
      if (
        evt.reminder === ReminderType.Remind
        || evt.reminder === ReminderType.Warn
        || evt.reminder === ReminderType.Error
      ) {
        const msg = status ? successMsg : failMsg;
        let mType: 'info' | 'fail' | 'success' | 'loading' | 'warn';
        let type: 'info' | 'error' | 'success';
        switch (evt.reminder) {
          case ReminderType.Warn:
            mType = 'info';
            type = 'info';
            break;
          case ReminderType.Error:
            mType = 'fail';
            type = 'error';
            break;
          case ReminderType.Remind:
          default:
            mType = 'success';
            type = 'success';
            break;
        }
        if (isM(data)) {
          MDialog.toast({ type: status ? mType : 'fail', content: msg });
        } else {
          Dialog.message({ type: status ? type : 'info', content: msg });
        }
        handlePageAction();
      } else if (evt.reminder === ReminderType.Confirm) {
        const msg = status ? successMsg : failMsg;
        if (isM(data)) {
          MDialog.prompt({
            title: getLabel('105946', '信息确认'),
            mask: true,
            maskClosable: true,
            getInstance: this.getInstance2,
            message: <div className={`${ebdfClsPrefix}-action-msg-mobile`}>{msg}</div>,
            onClose: () => {},
            footer: [
              {
                key: 'ok',
                text: getLabel('221901', '确定'),
                onPress: handlePageAction,
              },
            ],
            prompt: false,
          });
        } else {
          Dialog.confirm({
            // 执行失败
            title: getLabel('105946', '信息确认'),
            content: msg,
            onOk: handlePageAction,
          });
        }
      } else {
        handlePageAction();
      }
    } else {
      this.show(data);
    }
  };
}

export default new Message();
