import { ActionType } from '../../constants/common';

export enum ActionException {
  Break = 'EB_Action_Break', // 中断动作队列
}

export type PageNode = {
  id: string;
  type: string;
  name: string;
  appid: string;
  pName?: string;
  refId?: string;
  objId?: string;
};

export type ActionHandler<RecordData> = (record: RecordData) => void;

export type ActionRecord = {
  id: string;
  idStr: string;
  actionType: ActionType;
  name: string;
  // esb场景
  scene: string; // "[{\"name\":\"ljc\",\"id\":\"676330699361173504\"}]"
  sceneIds?: string;

  buttonId: string;
  enable: number; // 启用
  // openType: "0"
  // showOrder: 1
  // url: ""
  // windowSize: 60
  record: Array<any>; // 记录规则的数据
  recordIds: Array<string>; // 记录规则动作的id
};

/**
 * 页面动作
 * */
export enum PageActionType {
  None = '0', // 无操作
  Refresh = '1', // 刷新页面
  ClosePage = '2', // 关闭页面
  JumpPage = '3' // 跳转页面
}

/**
 * 提醒方式
 * */
export enum ReminderType {
  None = '0', // 无操作
  Remind = '1', // 提醒框
  Confirm = '2', // 确认框
  Warn = '3', // 警告
  Error = '4', // 错误
}

export interface ExtraEvent {
  callbackFunc: any;
  jumpUrl: string;
  pageAction: PageActionType;
  reminder: ReminderType;
}
