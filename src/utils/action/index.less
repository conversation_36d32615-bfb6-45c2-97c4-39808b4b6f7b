@import (reference) '../../style/index.less';

//common
.@{prefix}-action-message {
  width: 100%;
  padding: 0 var(--h-spacing-lg);
  font-size: var(--font-size-12);
  & > div {
    margin-bottom: var(--v-spacing-md);
    display: flex;
    align-items: center;
  }
  &-success {
    color: var(--success);
    display: inline-block;
    margin-right: var(--h-spacing-md);
  }
  &-fail {
    color: var(--danger);
    display: inline-block;
    margin-right: var(--h-spacing-md);
  }
  &-detail {
    display: inline-block;
    margin-left: var(--h-spacing-md);
    &-info {
      font-size: var(--font-size-12);
      p {
        margin: 0;
        line-height: 1.4;
      }
    }
  }
  .@{prefix}-content-center {
    width: 100%;
    text-align: center;
    display: block;
    font-size: var(--font-size-14);
  }
  &-confirm {
    .@{uiPcClsPrefix}-dialog-content {
      max-height: 600px;
    }
  }
}

//H5
.@{prefix}-action-msg-mobile {

  .@{prefix}-action-message-body {
    margin-left: -16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
