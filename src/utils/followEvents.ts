import { CardButtonType, EbuilderModule, ListButtonType } from '../constants';
import { ajax } from './ajax';

/**
 * 对接系统关注事项接口调用
 * @param entityId  事项id 1 || [1,2]
 * @param type 按钮类型
 * @param isApp  是否h5
 * @returns
 */
export const followEvents = (
  entityId: Array<string>,
  type: ListButtonType | CardButtonType,
  isApp: boolean,
) => {
  let url: string = '';
  let params = {};
  switch (type) {
    case ListButtonType.Follow:
      url = `/api${isApp ? '/app' : ''}/my/watch/add`;
      params = { entityId: `${entityId}` };
      break;
    case ListButtonType.Unfollow:
      url = `/api${isApp ? '/app' : ''}/my/watch/cancel`;
      params = { entityId: `${entityId}` };
      break;
    case ListButtonType.BatchFollow:
      url = `/api${isApp ? '/app' : ''}/my/watch/batchAdd`;
      params = { entityIds: entityId };
      break;
    case ListButtonType.BatchUnfollow:
      url = `/api${isApp ? '/app' : ''}/my/watch/batchCancel`;
      params = { entityIds: entityId };
      break;

    default:
      break;
  }

  return ajax({
    url,
    method: 'POST',
    data: {
      module: EbuilderModule,
      ...params,
    },
    ebBusinessId: '10000000000000000',
  });
};

const exportObj = { followEvents };
export default exportObj;
