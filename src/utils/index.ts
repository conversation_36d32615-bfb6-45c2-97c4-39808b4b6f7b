import { SyntheticEvent } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { utils, SearchDatas, AnyObj } from '@weapp/ui';
import { corsImport, isEmpty, isArray, isEqual, has, omit, isBoolean, cloneDeep, isString } from '@weapp/utils';
import { LocaleExValueDataType } from '@weapp/ebdcoms/lib/common/locale/types';
import { qs, setTitle } from '@weapp/utils';
import { isObservable, toJS } from 'mobx';
import { EtComponentKey } from '../constants/EtComponent';
import { SysFieldType, PageType, ListButtonType, GroupType } from '../constants/common';
import { ajax } from './ajax';
import ebdcoms from './ebdcoms';
import { DataSetItem } from '../components/common/DataSet/types';
import { GroupDataProps } from '../components/baseBoard/engine/func-groups/types';

/*
 * 单级路由组件，可以动态挂在任何一级路由下
 * 需要获取父级路由
 */
export function formatParentPath(props: RouteComponentProps) {
  return props.match.url.replace(/\/$/, () => '');
}

export function needLayout() {
  return window.location.pathname.indexOf('/splayout') === 0;
}

export function isMobile() {
  return window.location.pathname.indexOf('/mobile') === 0;
}

/**
 * 阻止冒泡统一方法
 * @param e
 * @returns
 */
export const stopPropagation = (e: SyntheticEvent) => e.stopPropagation();

// *过滤掉url上的指定字段
export const filterUrlParams = (uParams: any) => {
  let newParams = {} as any;
  for (let i in uParams) {
    if (!(i.includes('field_') || i.includes('wea_link_show_console'))) {
      newParams[i] = uParams[i];
    }
  }
  return newParams;
};
/**
 * 获取url的参数
 */
export const getUrlParams = () => {
  const location = window.__ebdflocation__ || window.__location__ || window.location;
  const lastIndex = location.href.lastIndexOf('?');
  const search = lastIndex === -1 ? '' : location.href.slice(lastIndex).replace(/^\?/, '');
  // 重置掉__location__ ,放置影响关联页面参数取值
  // ios下页面url参数取参存在问题
  window.__location__ = null;
  window.__ebdflocation__ = null;
  const params = {
    ...qs.parse(search),
  };
  return filterUrlParams(params);
};
export const isValidGroupInfo = (group: GroupDataProps) => {
  const { options = [], rootField, active } = group || {};
  const dateRangeGroup = options.find((i: any) => i.groupType === GroupType.dateRange) || {};
  const fieldGroup = options.find((i: any) => i.groupType === GroupType.field) || {};
  // 当前分组是日期分组且日期分组下没默认的日期字段不请求 因为会报错
  if (`${active}` === GroupType.dateRange && !dateRangeGroup.fieldId) {
    return false;
  }
  // 当前分组是字段分组且字段分组下没默认的字段不请求 因为会报错
  if (`${active}` === GroupType.field && !fieldGroup.fieldId) {
    return false;
  }
  // 当前分组是差异化分组但是没有rootField 不请求
  if (`${active}` === GroupType.diff && !rootField) {
    return false;
  }
  return true;
};
/**
 *
 * @function isNeedRefreshByConfig
 * @desc 根据用户配置判断是否需要更新
 * @param p:prevConfig
 * @param n: nextConfig
 * @return refresh [boolean]
 * */
export const isNeedRefreshByConfig = (p: any, n: any) => {
  const { dataset: nextDataset } = n;
  if (!nextDataset) {
    return false;
  } else {
    // 判断下面配置是否配置了显示字段 没配置不更新
    if (isEmpty(n.card)) {
      return false;
    }
    if (!isValidGroupInfo(n.group)) {
      return false;
    }
    // 不切换数据源 以下配置改变了也会更新
    const updateKey = ['dataset', 'card', 'markSetting', 'group', 'orders', 'condition', 'buttonSet', 'pageSize', 'laneConfig'];
    for (let i = 0; i < updateKey.length; i++) {
      const key = updateKey[i];
      if (!isEqual(p[key], n[key])) {
        return true;
      }
    }
  }
  return false;
};
// 提供随机颜色
export const getRandomColor = () =>
  `#${Math.floor(Math.random() * 0xffffff)
    .toString(16)
    .padEnd(6, '0')}`;

/**
 * 设置页面标题
 * /**
 * 统一处理设置浏览器标题，判断主框架下不设置
 * 方案：主框架添加判断标识，window.inWeappLayout,判断是否在主框架模式中。在主框架存在的情况下业务模块入口不执行复写title的逻辑，由主框架统一规则处理title显示
 * @param title
 * @returns
 */
export const setDocumentTitle = (title: string = 'e-Builder') => {
  const { pathname } = window.location;
  const isPcLayoutAppView =
    pathname.indexOf('/ebdapp/view') >= 0 && // 是应用预览
    pathname.indexOf('/sp/ebdapp/view') < 0 && // 不是单页应用预览
    pathname.indexOf('/mobile/ebdapp/view') < 0; // 是移动端的应用预览
  if (isPcLayoutAppView) {
    // pc单页模式下的应用预览，在主框架下并且不是h5页面不需要title
    return;
  }
  if (!window.inWeappLayout) {
    setTitle({ title });
  }
};

// 多条件，目前只需要对为空不为空处理
export const conditionNullArr = [
  'isNull', // 为空
  'isNotNull', // 不为空
];
// 多条件后缀
export const conditionSuffix = 'condition_type';
/**
 * 搜索条件过滤掉空值,eg:
 * 1 {"field_1":[{"field_1_condition_type": "like","field_1": "11"}]}满足
 *   {"field_1":[{"field_1_condition_type": "like"}]} 只有条件，不满足
 * 2 日期：{value:[],selectKeys:""} 不满足
 * 3 金额：{max:"",min:''} 不满足
 * 4 但是xxx_condition_type = conditionNullArr为空不为空选项的 : 都满足
 */
export const getConditionFormDatas = (formDatas: any) => {
  const newFormDatas: any = {};
  Object.keys(formDatas).forEach(key => {
    const item = formDatas[key].filter((valueObj: any) => {
      const fieldValue = valueObj[key];
      let hasValue = !!fieldValue;
      if (Object.prototype.toString.call(fieldValue) === '[object Object]') {
        if ('selectedKey' in fieldValue) {
          // 是日期组件
          hasValue = fieldValue?.value?.length > 0;
        } else if ('min' in fieldValue || 'max' in fieldValue) {
          // 是金额组件
          const { min = '', max = '', id } = fieldValue || {};
          hasValue = min !== '' || max !== '' || !!id;
        }
      }
      return hasValue || conditionNullArr.includes(valueObj[`${key}_${conditionSuffix}`]);
    });
    if (item.length > 0) {
      newFormDatas[key] = item;
    }
  });
  return newFormDatas;
};

export const specialFieldsFilter = (field: any) => {
  /**
   * 筛选中不允许设置的类型
   * */
  if (
    field?.fieldId === '8' ||
    field?.id === '8' ||
    field?.fieldId === '9' ||
    field.id === '9' ||
    field?.fieldId === '10' ||
    field.id === '10' ||
    field?.fieldId === '11' ||
    field.id === '11' ||
    field?.fieldId === '12' ||
    field.id === '12'
  ) {
    // 数据状态或流程状态或当前阶段
    return false;
  }
  const isSpecialType = [
    'MatrixComponent',
    EtComponentKey.TextArea,
    EtComponentKey.FileComponent,
    EtComponentKey.ImageComponent,
    EtComponentKey.EinvoiceComponent,
    EtComponentKey.ImageRadioBox,
    EtComponentKey.ComboSelect,
    EtComponentKey.TreeSelect,
    'DividingLine',
    EtComponentKey.ImageCheckBox,
    EtComponentKey.ImageRadioBox,
  ].includes(field?.compType || field?.componentKey);
  return !isSpecialType;
};

/**
 * 解析系统字段的字段类型
 * @param fieldNameOrId 系统常量
 */
export function getSystemFieldType(fieldNameOrId: string, type: EtComponentKey) {
  switch (type) {
    case EtComponentKey.Date: // 创建时间fieldId: "3"
      return EtComponentKey.DateComponent;
    default:
      break;
  }

  switch (fieldNameOrId) {
    case 'creator': // 创建者fieldId: "2"
    case '2': // 创建者fieldId: "2"
    case '6': // 最后更新人fieldId: "6"
    case '102':
      return EtComponentKey.Employee;
    case 'create_time': // 创建时间fieldId: "3"
    case '3': // 创建时间fieldId: "3"
    case '103':
      return EtComponentKey.DateComponent;
    case 'id': // 数据ID fieldId: "5"
    case '5': // 数据ID fieldId: "5"
    case '105':
    case '16': // 审批编号
      return EtComponentKey.Text;
    case 'data_status': // 数据状态 fieldId: "8"
    case '8': // 数据状态 fieldId: "8"
    case '108':
      return EtComponentKey.Select;
    case 'name': // 标题 fieldId: "1"
    case '1': // 标题 fieldId: "1"
    case '101':
      return EtComponentKey.Text;
    case 'update_time': // 标题 fieldId: "4"
    case '4': // 最后更新时间
    case '104':
      return EtComponentKey.DateComponent;
    case 'flow_status': // 流程状态 fieldId: "9"
    case '9': // 流程状态 fieldId: "9"
    case '109':
      return EtComponentKey.Select;
    case 'current_step': // 当前阶段 fieldId: "10"
    case '10': // 当前阶段 fieldId: "10"
    case '1010':
      return EtComponentKey.Text;
    case '15':
      return SysFieldType.Classification;
    default:
      return type;
  }
}

// 判断是否是详情页面
export const isDetailPage = (page?: any) => {
  if (!page) return false;

  const { SystemPageType } = ebdcoms.get();

  const { pageType, pageId } = page;
  const detailPages = [
    SystemPageType.CustomerDetail,
    SystemPageType.ContactDetail,
    SystemPageType.ContractDetail,
    SystemPageType.OrderDetail,
    SystemPageType.PriceDetail,
    SystemPageType.OpportunityDetail,
    SystemPageType.ClueDetail,
    SystemPageType.ProductDetail,
    SystemPageType.MarketActivityDetail,
    SystemPageType.CompetitorDetail,
    SystemPageType.DocumentDetail,
    SystemPageType.ApprovalDetail,
    SystemPageType.SubmitApplication,
    SystemPageType.DataDetail,
    SystemPageType.ProjectDetail,
    SystemPageType.TaskDetail,
  ] as string[];

  return pageType === PageType.SystemPage && detailPages.includes(pageId);
};

/**
 * 获取关联eb浏览按钮的target表单id
 * @param field
 * @returns
 */
export const getEbBrowserTargetFormId = (field: any) => {
  if (!field?.config) {
    return field?.formId || '10000000000000000';
  }
  const fieldConfig = typeof field?.config === 'string' ? JSON.parse(field?.config) : field?.config;
  return fieldConfig?.sformId;
};

// 判断是否是关联浏览-eb自定义浏览
export const isEbRelationBrowser = (field: any) => {
  const { componentKey, otherProperties, config, compType } = field;
  let params = otherProperties || config || '{}';
  params = isString(params) ? JSON.parse(params) : params;
  // return (componentKey === EtComponentKey.RelateBrowser || compType === EtComponentKey.RelateBrowser) && !!params?.ebuilderRelateBrowser;
  return componentKey === EtComponentKey.RelateBrowser || compType === EtComponentKey.RelateBrowser;
};
// 判断是否是Ebuilder字段
export const isEbuilderField = (field: any = {}) => {
  const { componentKey, compType } = field;
  return componentKey === EtComponentKey.Ebuilder || compType === EtComponentKey.Ebuilder;
};
/**
 * 判断筛选值是否为空
 * */
export const isValueEmpty = (val: any): boolean => {
  if (!val) return true;
  let emptyFlag: boolean = false;
  if (Array.isArray(val)) {
    // 数组值
    emptyFlag = !val.length || (val as any[]).every((v: any) => utils.isEmpty(v));
  } else if (typeof val === 'object') {
    if ('min' in val || 'max' in val) {
      // 数值区间
      if (val?.min === '' && val?.max === '') {
        emptyFlag = true;
      }
    } else if (utils.isEmpty(val)) {
      // 对象值
      emptyFlag = true;
    }
  } else if (typeof val === 'string' && val === '') {
    // 字符串
    emptyFlag = true;
  }
  return emptyFlag;
};
/**
 * 默认常用筛选值格式化特殊字段：日期
 */
export const parseDateValue = (datas: AnyObj, items: any) => {
  const newDatas = { ...datas };
  Object.keys(newDatas).forEach((key: string) => {
    const _value = newDatas[key];
    /** 日期特殊格式处理 */
    if (items[key]?.itemType === 'DATEPICKER' && Object.prototype.toString.call(_value) === '[object Object]') {
      newDatas[key] = _value?.value || [];
    }
  });
  return newDatas;
};

export const parseAdData = (searchDatas: SearchDatas) => {
  const resData: any = { ...searchDatas };
  Object.keys(searchDatas).forEach((key: string) => {
    if (key === 'formDatas') {
      let formDatas: any = searchDatas[key];
      Object.keys(formDatas).forEach((formdataKey: any) => {
        const valItem = formDatas[formdataKey];
        if (Array.isArray(toJS(valItem))) {
          valItem.forEach((valObj: any) => {
            Object.keys(valObj).forEach((valKey: string) => {
              const val = valObj[valKey];
              if (val?.isComboSelect) {
                const children: any[] = val?.childValue;
                const conditionType = `${formdataKey}_${conditionSuffix}`;
                const conditions: any = {};
                children.forEach((obj: any) => {
                  if (conditionType in valObj) {
                    const conditionValue = valObj[conditionType];
                    const conditionName = `${obj.fieldName}_${conditionSuffix}`;
                    conditions[conditionName] = conditionValue;
                  }
                  formDatas = {
                    ...formDatas,
                    [obj.fieldName]: [
                      {
                        ...obj.fieldValue,
                        ...conditions,
                      },
                    ],
                  };
                });
                delete formDatas[formdataKey];
                if (conditionType in valObj) {
                  delete formDatas[conditionType];
                }
              }
            });
          });
        }
      });
      resData[key] = formDatas as any;
    }
  });
  return { ...resData };
};
/**
 * 自定义按钮或者关联显示数量
 * 传入需要查询的自定义按钮，所有的按钮集合，接口所需参数，callback回调更新所有按钮中有数据的
 * 根据自定义按钮或关联的类型分别掉不同的接口 获取一个 更新一个
 *
 * @returns
 */

export function getCustomButtonDatas(data: AnyObj, rightMenus: any[], baseParams: any, callBack: (data: any[]) => void) {
  let resInterface: any; // 根据不同类型掉接口
  data.forEach(async (f: any) => {
    if (f?.customTips?.tipsType === 'SQL') {
      resInterface = await ajax({
        url: '/api/ebuilder/form/sql/doCustomSqlTips',
        method: 'post',
        data: {
          tipsId: f.customTips.id,
          dataId: baseParams?.dataid || '',
          objId: baseParams?.objid || '',
          formDataId: baseParams?.formdataid || '',
          formFieldData: {},
          parm: baseParams?.urlParams,
        },
        ebBusinessId: baseParams?.appId,
      });
    }
    if (f?.customTips?.tipsType === 'ACTION') {
      resInterface = await ajax({
        url: '/api/ebuilder/form/action/doCustomActionTips',
        method: 'post',
        data: {
          tipsId: f.customTips.id,
          dataId: baseParams?.dataid || '',
          objId: baseParams?.objid || '',
          formDataId: baseParams?.formdataid || '',
          formFieldData: {},
          parm: baseParams?.urlParams,
        },
        ebBusinessId: baseParams?.appId,
      });
    }
    if (resInterface) {
      const updateButtonArray = rightMenus;
      for (let i = 0; i < updateButtonArray.length; i++) {
        if (updateButtonArray[i].id === f.id) {
          updateButtonArray[i] = { ...f, number: resInterface.tipsCount };
          break;
        }
      }
      callBack(updateButtonArray);
    }
  });
}
// 高级搜索中其他搜索条件 字段排序处理
export function searchAdFiltersSort(arr: any) {
  let tempFieldArr: any[] = arr;

  tempFieldArr = tempFieldArr.filter((f, i) => {
    if (f.fieldOrder === undefined) {
      if (i === 0) {
        f.fieldOrder = -1;
      } else {
        f.fieldOrder = tempFieldArr[i - 1].fieldOrder;
      }
    }
    return f;
  });
  return tempFieldArr.sort((a: any, b: any) => a.fieldOrder - b.fieldOrder);
}
/**
 * 快速排序字段
 * @param data
 * @param callBack
 */
export function getQuickSortBtn(data: AnyObj, _isMobile?: boolean) {
  const newData = data.map((f: any) => {
    if (f.buttonKey === ListButtonType.QuickOrder) {
      return {
        ...f,
        disabled: true,
        children: f.viewOrderList.map((ch: any) => ({
          ...ch,
          buttonKey: ListButtonType.QuickOrder,
          content: ch.fieldName,
          actions: [],
          isMobile: _isMobile,
          // dataCustomRenderItem: renderQuickSortItem,
        })),
      };
    }
    return f;
  });
  return newData;
}
/** =================== 移动端隐藏具有回复评论动作的按钮  start======= */
export const hideReplyButtons = (data: any) => {
  const newArr: any = [];
  // 自定义按钮的actions里面有reply动作时，去掉该动作
  (data || []).forEach((value: any, index: any) => {
    newArr[index] = value;
    newArr[index].actions = (value?.actions || []).filter((item: any) => item.actionType !== 'reply');
  });
  return newArr;
};

/**
 * 是否app客户端
 * @returns
 */
export const judgeIsPC = (): boolean => {
  const { ua } = window.weappUtils;
  return ua?.device === 'PC';
};

export const UUID = (len = 32) => ebdcoms.excu('UUID', len);

/** 执行事件动作前，对点击的DOM层做额外的逻辑处理 */
export const executeActions = (...args: any) => ebdcoms.excu('executeActions', ...args);

/**
 * 转换成数据源那边的params
 * @param arr
 * @param fn (item) => [name,value]
 * @returns Array<{name,value}>
 */
// eslint-disable-next-line max-len
export const toPageParams = (arr: any, fn: (item: any) => any) =>
  arr.reduce((pre: Array<{ name: string; value: string | any }>, cur: string) => {
    if (cur) {
      const [name, value] = fn(cur);
      pre.push({ name, value: typeof value === 'string' ? value : value?.id });
    }
    return pre;
  }, []);

/**
 * dataSource的params固定值解析成key：value
 * params : Array<{name: string, type: string, value: string}>
 * @returns {key:value}
 */
export const getFixedObj = (
  params: Array<{
    name: string;
    type: string;
    value: string | any;
    nameType?: string;
  }> = []
) =>
  params.reduce(
    (pre, cur) => ({
      ...pre,
      [cur.name]: typeof cur.value === 'string' ? cur.value : cur.value?.id,
    }),
    {}
  );

// 是否是单页显示的卡片
export function isSpaCard() {
  return window.location.pathname.indexOf('/ebdfpage/card') >= 0;
}
export const getLocaleValue = (value?: LocaleExValueDataType, nameAliasKey?: string) => ebdcoms.excu('getLocaleValue', value, nameAliasKey);

export const invoke = (plugin: any, actionName: string, params?: AnyObj) => {
  if (isEmpty(plugin)) return;
  if (!hasRegisterFuncInPlugin(plugin, actionName)) {
    return params?.hook && params?.hook(...params.args);
  }
  return plugin?.invoke(actionName, params);
};
// 是否在插件包里注册过事件
export const hasRegisterFuncInPlugin = (plugin: any, actionName: string) => {
  if (isEmpty(plugin) || !actionName) return false;
  let hasRegister = false;
  for (let i = 0; i < (plugin.pluginNames || []).length; i++) {
    const pluginName = plugin.pluginNames[i];
    if (plugin?.enabledPluginRecord[pluginName][actionName]) {
      hasRegister = true;
      break;
    }
  }
  return hasRegister;
};
// 去除数组相同项，只保留最新的一条
export const removeDuplicateItems = (array: any, key: string) => {
  const resultMap = new Map();
  for (let i = array.length - 1; i >= 0; i--) {
    const item = array[i];
    resultMap.set(item[key], item);
  }
  return Array.from(resultMap.values())[0];
};

/**
 * 获取关联类型配置
 * @param compType
 * @returns
 */
export const getBrowserTypeModule = (compType: EtComponentKey) => {
  switch (compType) {
    case EtComponentKey.Department:
      return { module: 'hrm', type: 'department' };
    case EtComponentKey.Employee:
      return { module: 'hrm', type: 'resource' };
    case EtComponentKey.Mainline:
      return { module: 'project', type: 'mainlineBrowser' };
    case EtComponentKey.Task:
      return { module: 'task', type: 'taskBrowser' };
    case EtComponentKey.Document:
      return { module: 'doc', type: 'document' };
    case EtComponentKey.Workflow:
      return { module: 'workflow/core', type: 'wfcRequest' };
    case EtComponentKey.AgendaComponent:
      return { module: 'calendar', type: 'calendarBrowser' };
    case EtComponentKey.FormComponent:
      return { module: 'formdatareport', type: 'frpt_component' };
    case EtComponentKey.CustomerComponent:
      return { module: 'crm', type: 'customer' };
    case EtComponentKey.ClueComponent:
      return { module: 'crm', type: 'clue' };
    case EtComponentKey.OrderComponent:
      return { module: 'crm', type: 'orderform' };
    case EtComponentKey.ContactComponent:
      return { module: 'crm', type: 'contact' };
    case EtComponentKey.ChanceComponent:
      return { module: 'crm', type: 'saleChance' };
    case EtComponentKey.ProductionComponent:
      return { module: 'crm', type: 'production' };
    case EtComponentKey.ContractComponent:
      return { module: 'crm', type: 'contract' };
    case EtComponentKey.ActivityComponent:
      return { module: 'crm', type: 'marketactivity' };
    case EtComponentKey.WorkreportComponent:
      return { module: 'plan', type: 'planBrowser' };
    case EtComponentKey.CompetitorComponent:
      return { module: 'crm', type: 'competitor' };
    case EtComponentKey.KpiFlowComponent:
      return { module: 'performance', type: 'performanceBrowser' };
    case EtComponentKey.QuoteComponent:
      return { module: 'crm', type: 'quote' };
    case EtComponentKey.AttendComponent:
      return { module: 'attend/web', type: 'attendPermissionEmpsBrowser' };
    case EtComponentKey.DataSource:
      return { module: 'attend/web', type: 'vacationTypeBrowser' };
    case EtComponentKey.Ebuilder:
      return { module: 'ebuilder/form', type: 'ebuilder' };
    case EtComponentKey.Subcompany:
      return { module: 'hrm/common', type: 'subcompany' };
    case EtComponentKey.CashBook:
      return { module: 'inc/book', type: 'cashbook' };
    case EtComponentKey.ODocReceiveComponent:
      return { module: 'odoc', type: 'odocReceiveUnit' };
    case EtComponentKey.RelateODoc:
      return { module: 'odoc', type: 'odocRequest' };
    case EtComponentKey.EinvoiceComponent:
      return { module: 'inc/biz', type: 'invoice' };
    default:
      return { module: '', type: '' };
  }
};

/**
 * 格式化filter参数，暂不支持明细字段
 * @param parentFilter 父级filter
 * @param filter 子级filter
 * @param fieldData 字段集合
 */
const formatData = (parentFilter: any, filter: any[], fieldData: any[]) => {
  if (isArray(filter)) {
    filter.forEach((c: { [x: string]: any }) => {
      if (has(c, 'type') && has(c, 'filter')) {
        const _filter: any = {
          key: Math.round(Math.random() * 10000000),
          relationship: c.type === 'and' ? '1' : '0',
          isParent: true,
          isNewFilter: true,
          parentNode: parentFilter.key,
          isEdit: '1',
          datas: [],
        };
        parentFilter.datas.push(_filter);
        formatData(_filter, c.filter, fieldData);
      } else {
        let fieldFilter: any = {
          key: Math.round(Math.random() * 10000000),
          isParent: false,
          parentNode: parentFilter.key,
        };
        fieldData.forEach(el => {
          el.fields?.forEach((f: any) => {
            if (f.name === c.fieldName) {
              if (has(f.showName, 'nameAlias')) {
                f.text = f.showName.nameAlias;
              }
              if ((!f.usage || f.usage === 'query') && !f.isDetailtable) {
                f = ebdcoms.get().transformFields(f);
              }
              fieldFilter = omit(
                {
                  ...fieldFilter,
                  ...f,
                  fieldName: f.name,
                  fieldType: f.type,
                  type: 'condition',
                  conditions: {
                    func: c.relation,
                    value: c.value,
                    type: 'custom',
                    isEsb: false,
                  },
                },
                ['conditionType', 'conditionTypeValue', 'usage', 'showName']
              );
            }
          });
        });
        parentFilter.datas.push(fieldFilter);
      }
    });
  }
};
/**
 * 获取数据源字段信息，前端memo处理 减少请求
 */
export const getFields = (dataset: any, effectType?: string, needPic?: boolean, needDetail?: boolean) =>
  corsImport('@weapp/components').then(
    ({ dsUtils }) =>
      new Promise((resolver, reject) => {
        dsUtils
          .getFields(dataset, effectType, needPic, needDetail)
          .then((datas: any) => {
            resolver(datas);
          })
          .catch(() => reject());
      }) as Promise<any>
  );
/**
 * 业务抽离 过滤多选
 * @param dataset 数据源
 * @param cb 回调
 * @param filterField 过滤字段
 * @param mulit 过滤多选
 */
export const getRsFields = async ({ dataset, cb, filterField = [], mulit }: { dataset: DataSetItem; cb?: Function; filterField?: string[]; mulit?: boolean }) => {
  const fieldData = (await getFields(dataset, '', false, false)) || [{ fields: [] }];
  let FieldOpts = fieldData[0].fields.filter((data: any) => (mulit ? true : `${data?.multiSelect}` === 'false')).map((data: any) => ({ ...data, id: data.id, content: data.text }));
  if (filterField.length) {
    FieldOpts = FieldOpts.filter((data: any) => filterField.includes(data.compType));
  }
  cb && cb(FieldOpts);
};
/** 处理JS API(SDK) 传递过来的筛选条件 */
export const formatSdkRefreshData = async (condition: any, dataset: any) => {
  if (!condition || (!has(condition, 'type') && !has(condition, 'filter'))) return null;

  const fieldData = await getFields(dataset);
  const datas: any = {
    key: Math.round(Math.random() * 10000000),
    relationship: condition.type === 'and' ? '1' : '0',
    isParent: true,
    isNewFilter: true,
    parentNode: '',
    isEdit: '1',
    datas: [],
  };
  const filterIsNull = isBoolean(condition.filterIsNull) ? condition.filterIsNull : true;

  formatData(datas, condition.filter, cloneDeep(fieldData) || []);

  const filters: any[] = [
    {
      actionId: UUID(),
      conditionSet: datas,
      filterIsNull,
      comId: condition.comId,
      isOverrideFilter: !!condition.isOverrideFilter,
    },
  ];

  return filters;
};

export const _setTimeout = (callback: Function, time = 30) => {
  let timer: any = setTimeout(() => {
    callback();

    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  }, time);
  return timer;
};

export const getOtherData = (config: AnyObj, key: string) => {
  const otherData = config?.group?.otherData || {};
  if (isEmpty(otherData)) return null;
  return otherData[key];
};

// 定义选项接口
interface DeepToJSOptions {
  handleCircular?: boolean;
  excludeKeys?: string[];
}

// 定义一个通用类型,可以是任何对象类型
type DeepToJSResult<T> = T extends Array<infer U> ? Array<DeepToJSResult<U>> : T extends Date ? Date : T extends object ? { [K in keyof T]: DeepToJSResult<T[K]> } : T;

/**
 * 递归处理 mobx 对象,将所有 observable 转换为普通对象
 * @param obj - 要处理的对象
 * @param options - 配置选项
 * @returns 处理后的普通对象
 */
export function deepToJS<T>(obj: T, options: DeepToJSOptions = {}): DeepToJSResult<T> {
  // WeakSet 用于检测循环引用
  const seen = new WeakSet();

  function process<U>(val: U): DeepToJSResult<U> {
    // 处理基础类型和 null
    if (val === null || typeof val !== 'object') {
      return val as DeepToJSResult<U>;
    }

    // 检测循环引用
    if (seen.has(val as object)) {
      return (options.handleCircular ? {} : val) as DeepToJSResult<U>;
    }
    seen.add(val as object);

    // 处理 Date 对象
    if (val instanceof Date) {
      return new Date(val) as DeepToJSResult<U>;
    }

    // 处理 observable 对象
    const baseObj = isObservable(val) ? toJS(val) : val;

    // 处理数组
    if (Array.isArray(baseObj)) {
      return baseObj.map(item => process(item)) as DeepToJSResult<U>;
    }

    // 处理普通对象
    const result = {} as { [key: string]: any };
    for (const key in baseObj) {
      if (Object.prototype.hasOwnProperty.call(baseObj, key)) {
        // 排除特定字段
        if (options.excludeKeys?.includes(key)) {
          continue;
        }
        result[key] = process(baseObj[key]);
      }
    }

    return result as DeepToJSResult<U>;
  }

  return process(obj);
}

export const safeSetInterval = (callback: Function, interval: number, maxAttempts: number, successCb: Function, failCb?: Function) => {
  let attempts: number = 0;
  const intervalId: NodeJS.Timeout = setInterval(() => {
    attempts++;
    const conditionMet: boolean = callback();

    if (conditionMet || attempts >= maxAttempts) {
      clearInterval(intervalId);
      if (conditionMet) {
        successCb();
      } else {
        // 超过最大轮询次数，条件未生效
        failCb && failCb();
      }
    }
  }, interval);
};

/**
 * 解析系统字段的字段类型
 * @param fieldNameOrId 系统常量
 */
export function parseSystemFieldType(data: any[]) {
  return data.map((item: any) => {
    for (const key in item) {
      switch (key) {
        case 'creator': // 创建者fieldId: "2"
          item['2'] = item[key];
          break;
        case 'create_time': // 创建时间fieldId: "3"
          item['3'] = item[key];
          break;
        case 'id': // 数据ID fieldId: "5"
          item['5'] = item[key];
          break;
        case 'data_status': // 数据状态 fieldId: "8"
          item['8'] = item[key];
          break;
        case 'name': // 标题 fieldId: "1"
          item['1'] = item[key];
          break;
        case 'update_time': // 标题 fieldId: "4"
          item['4'] = item[key];
          break;
        case 'flow_status': // 流程状态 fieldId: "9"
          item['9'] = item[key];
          break;
        case 'current_step': // 当前阶段 fieldId: "10"
          item['10'] = item[key];
          break;
        default:
          break;
      }
    }
    return item;
  });
}
