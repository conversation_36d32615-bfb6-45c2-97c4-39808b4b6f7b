import { ua, weappSDK } from '@weapp/utils';

// 单页关闭浏览器页面
export const closeWindow = () => {
  const isWeapp = ua?.browser === 'WeappPC' || ua?.browser === 'wxwork';
  const closeFn = () => {
    try {
      window.opener && window.close();
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('closeFn Error', e);
    }
  };
  if (isWeapp) {
    weappSDK.checkApi('closeWindow').then(() => {
      weappSDK.exec('closeWindow', {
        success: () => {},
        fail: closeFn,
      });
    });
  } else {
    closeFn();
  }
};

export default closeWindow;
