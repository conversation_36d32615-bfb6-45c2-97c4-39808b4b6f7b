import { DatePicker, Encry, MEncry } from '@weapp/ui';
import React, {ReactNode} from 'react';
import { EtComponentKey } from '../constants/EtComponent';
import { stopPropagation } from '../utils';

const { desensitization, encryption } = Encry;
/**
 * 列自定义
 * @param data
 * @param dom
 * @returns
 */
export const columnCustomRender = (data: any, dom: string | ReactNode) => ({
  props: {
    rowSpan: data.rowSpan,
  },
  children:
    typeof dom === 'string' ? (
      <span dangerouslySetInnerHTML={{ __html: dom }} onClick={stopPropagation} />
    ) : (
      dom
    ),
});
/**
 * 加密字段
 */
export const EntryKsys = [
  EtComponentKey.Text,
  EtComponentKey.TextArea,
  EtComponentKey.NumberComponent,
  EtComponentKey.Money,
  EtComponentKey.Email,
  EtComponentKey.Phone,
  EtComponentKey.Mobile,
  EtComponentKey.IDCard,
];

/**
 * 拦截加密值判断是否加密
 * @param value
 * @returns
 */

// eslint-disable-next-line max-len
export const isEntryEnable = (value: string): React.ReactNode | string => !!(desensitization(value) || encryption(value));

/**
 * 拦截加密值
 * @param value
 * @returns
 */
export const wrapEntry = (value: string, isMobile?: boolean): React.ReactNode | string => {
  if (isMobile) {
    return <MEncry weId="_sa6ubo" value={value} />;
  }
  return <Encry weId="_l6pila" value={value} />;
};

// 非加密非转换
export const noWrapEntryDom = (dom: string, parent: any) => {
  if (Object.prototype.toString.call(dom) === '[object Object]') {
    return React.cloneElement(
      parent,
      {
        ...parent.props,
      },
      dom,
    );
  }
  return React.cloneElement(parent, {
    ...parent.props,
    dangerouslySetInnerHTML: { __html: DatePicker.getLocaleDateString(dom) },
  });
};

/**
 * 拦截加密table的renderBody方法(table专用)
 * @param tempFn
 * @param componentKey
 * @returns {props,children}
 */
export const wrapEntryRenderFn = (col: any, isMobile?: boolean) => {
  const { bodyRender, componentKey, detail } = col;
  if (EntryKsys.includes(componentKey)) {
    return (data: any, pos: any, name: string) => (isEntryEnable(data[name])
      ? columnCustomRender(
        { ...data, rowSpan: detail ? 1 : data.rowSpan },
        wrapEntry(data[name], isMobile),
      )
      : bodyRender(data, pos, name));
  }
  return bodyRender;
};

/**
 * 加密正则
 */
// eslint-disable-next-line max-len
export const EncryReg = /desensitization_{2}[\dA-Fa-f]{8}(?:\b-[\dA-Fa-f]{4}){3}\b-[\dA-Fa-f]{12}/g;
