export default abstract class BaseEvent {
  /** 执行事件 */
  abstract executeEvent: (
    config: any,
    isMobile: boolean,
    routerProps: any,
    registerActionResolve: Function
  ) => void;

  /** 触发事件 */
  runJsCode = (
    config: any,
    isMobile: boolean,
    routerProps: any,
    registerActionResolve: Function,
  ) => {
    this.executeEvent(config, isMobile, routerProps, registerActionResolve);
  };
}
