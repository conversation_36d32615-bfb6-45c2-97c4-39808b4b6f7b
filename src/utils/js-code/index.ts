import { Dialog, MDialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import BaseEvent from './Base';
import { confirmAction } from '../action/confirm';

export default class JsCode extends BaseEvent {
  executeEvent = (
    config: any,
    isMobile: boolean,
    routerProps: any,
    registerActionResolve: Function,
  ) => {
    try {
      let needInterrupt = false; // 存在需要执行中断的操作
      if (config?.ecode) {
        const getConfirmMessage = (content: string) => {
          needInterrupt = true; // 执行中断，后续行为由确认框接管
          confirmAction({ content }, isMobile, registerActionResolve);
        };
        const data = routerProps?.data ? JSON.stringify(routerProps.data) : '{}';
        window._eventActionDom = {
          ...window._eventActionDom,
          dom: routerProps?.dom || null,
        };
        const jsCode = `this.data=${data};this.target=window._eventActionDom.${'dom'};${config?.ecode}`;
        // eslint-disable-next-line no-eval
        eval(`
          var getConfirmMessage = ${getConfirmMessage};
          var ebdFormSdk = window?.ebdFormSdk || {};
          ${jsCode}
        `);
        const rowFunction = window?.ebdfListSdk?.events?.rowFunction;
        if (rowFunction) {
          rowFunction(routerProps.data?.ebrecord_rowkey, routerProps.data);
        }
      }
      if (!needInterrupt) {
        // 如果无中断
        registerActionResolve(); // 顺序执行
      }
    } catch (e: any) {
      registerActionResolve({ interrupt: true });
      if (isMobile) {
        MDialog.toast({ type: 'info', content: getLabel('162878', '二次确认提示的js函数有错') });
      } else {
        Dialog.message({ type: 'info', content: getLabel('162878', '二次确认提示的js函数有错') });
      }
      // eslint-disable-next-line no-console
      console && console.error(e);
    }
  }
}
