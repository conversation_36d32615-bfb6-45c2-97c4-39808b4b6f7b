import ebdcoms from '../utils/ebdcoms';
export enum ColorStyleType {
  HasStyle = 'hasStyle',
  NoStyle = 'noStyle',
}
export enum ShowPosition {
  DropDown = '1', // 下拉
  Tile = '0', // 平铺
  // 分类搜索
  Top = '0', // 顶部
  Left = '1' // 左侧
}
// 看板分组方式
export enum GroupType {
  custom = '0', // 按固定名称
  field = '1', // 按字段
  dateRange = '2', // 按日期
  filter = '3', // 按条件
  dataset = '4', // 按数据源
  diff = '5', // 差异化分组
  lane = '6', // 泳道分组
}
// 看板泳道分组方式
export enum LaneGroupType {
  field = '1', // 按字段
  dateRange = '2', // 按日期
  filter = '3', // 按条件
}
export enum SysFieldType {
  Classification = 'classification' // 密级
}
/**
 * 和ebdcoms里面的同步，异步引入有问题，自己同步定义一份
 */
export enum PageType {
  Workflow = 'WORKFLOW',
  Search = 'SEARCH',
  Card = 'CARD',
  ViewPort = 'VIEWPORT', // 下面的未来删掉   日历 看板 甘特图
  Calendar = 'CALENDAR', // 日历
  Board = 'BOARD', // 看板
  Gantt = 'GANTT', // 甘特图
  Layout = 'LAYOUT',
  Page = 'PAGE',
  Link = 'LINK',
  NULL = '',
  Report = 'REPORT',
  SystemPage = 'SYSTEMPAGE',
  MobilePortal = 'MOBILEPORTAL',
  Scene = 'SCENE',
}

//  视图类型
export enum ViewType {
  List = 'list', // 主表
  DetailList = 'detailList', // 主子表
  GridList = 'gridList', // 网格视图
  ExcelList = 'excelList', // 可编辑Excel列表
}
export enum ActionType {
  System = 'system',
  Custom = 'custom',
  Interface = 'customInterface',
  OpenPage = 'openpage',
  Esb = 'esb',
  Reply = 'reply',
  ConfirmBox = 'confirmBox', // 确认框
  JsCode = 'jsCode', // 执行JavaScript
  SmallCard = 'smallCard', // 执行JavaScript
}
export enum RightMenuPosition {
  RightTop = 'rightTop',
  RightTopDropDown = 'rightTopDropDown',
  RightTopBatch = 'rightTopBatch',
}
/**
 * 列表按钮方法常量
 */
export enum ListButtonType {
  TableAdd = 'tableAdd',
  TableDelete = 'tableDelete', // 批量删除
  doImp = 'doImp',
  TableExp = 'tableExp', // 导出
  TableImp = 'tableImp', // 导入
  BatchEditValue = 'batchEditValue', // 批量编辑字段值
  BatchPrint = 'batchPrint', // 批量打印
  BatchAggregationPrint = 'batchAggregationPrint', // 批量打印
  BatchAdd = 'batchAdd', // 批量新增
  BatchEdit = 'batchEdit', // 批量编辑
  BatchFollow = 'batchFollow', // 批量关注
  BatchUnfollow = 'batchUnfollow', // 批量取消关注
  BatchMarkReadState = 'batchMarkReadState', // 批量已读
  MarkReadState = 'markReadState', // 标记为已读
  Follow = 'follow',
  Unfollow = 'unfollow',
  LikeBtn = 'likeBtn', // 赞
  DislikeBtn = 'disLikeBtn', // 踩
  CommentBtn = 'commentBtn', // 评论
  MyFollows = 'myFollows',
  BatchPubTags = 'batchPubTags', // 批量设置公共标签
  BatchMyTags = 'batchMyTags', // 批量设置我的标签
  SetTop = 'setTop', // 置顶
  CancelSetTop = 'cancelSetTop', // 取消置顶
  CustomBatch = 'customBatch', // 自定义批量操作
  BatchShare = 'batchShare', // 批量共享
  ShareToMe = 'shareToMe', // 共享给我的数据
  Personalization = 'personalization', // 个性化
  InitializeColumnWidth = 'initializeColumnWidth', // 初始化列宽
  MessageReceiveSet = 'messageReceiveSet', // 消息免打扰

  QuickOrder = 'quickOrder', // 快捷排序

  /**
   * 卡片按钮显示在行末
   * */
  DataEdit = 'edit', // 编辑
  DataCopy = 'copy', // 复制
  DataDelete = 'delete', // 删除
  DataPrint = 'print', // 打印
  DataShare = 'share', // 共享
  ExportPDF = 'exportPDF',
}
export enum ImpMenuKey {
  Add = 'add',
  Update = 'update'
}
export enum OrderType {
  DESC = 'desc', // 降序
  ASC = 'asc' // 升序
}
/** *
 * 卡片url参数
 */

export enum LayoutType {
  Show = '0', // 显示布局
  Add = '1', // 新建布局
  Edit = '2', // 编辑布局
}
export enum OpenMode {
  Default = "4",
  New = "0",
  SlideLeftFromRight = "1",
  SlideRightFromLeft = "2",
  SlideUpFromBottom = "3",
  Modal = "5",
  /** 当前布局 */
  Layout = "6",
  /** 右侧分栏 */
  RightLayout = "7",
  /** 重定向 */
  Replace = "8"
}
export enum SourceType {
  FORM = 'FORM', // 表单
  ETEAMS = 'ETEAMS', // 数据仓库
  BIZ = 'BIZ', // 标准业务数据
  CUSTOM = 'CUSTOM', // 自定义数据
  LOGIC = 'LOGIC', // 逻辑表
  MOCK = 'mock', // 模拟数据源
  EXTERNALDATASET = 'EXTERNALDATASET', // 数据集合
  EXTERNAL = 'EXTERNAL', // 外部数据源
}
/**
 * 列表按钮方法常量
 */
export enum ButtonType {
  TableAdd = 'tableAdd',
  TableDelete = 'tableDelete', // 批量删除
  doImp = 'doImp',
  TableExp = 'tableExp', // 导出
  TableImp = 'tableImp', // 导入
  BatchEditValue = 'batchEditValue', // 批量编辑字段值
  BatchPrint = 'batchPrint', // 批量打印
  BatchAdd = 'batchAdd', // 批量新增
  BatchEdit = 'batchEdit', // 批量编辑
  BatchFollow = 'batchFollow', // 批量关注
  BatchUnfollow = 'batchUnfollow', // 批量取消关注
  BatchMarkReadState = 'batchMarkReadState', // 批量已读
  MarkReadState = 'markReadState', // 标记为已读
  Follow = 'follow',
  Unfollow = 'unfollow',
  LikeBtn = 'likeBtn', // 赞
  DislikeBtn = 'disLikeBtn', // 踩
  MyFollows = 'myFollows',
  BatchPubTags = 'batchPubTags', // 批量设置公共标签
  BatchMyTags = 'batchMyTags', // 批量设置我的标签
  SetTop = 'setTop', // 置顶
  CancelSetTop = 'cancelSetTop', // 取消置顶
  CustomBatch = 'customBatch', // 自定义批量操作
  BatchShare = 'batchShare', // 批量共享
  ShareToMe = 'shareToMe', // 共享给我的数据
  Personalization = 'personalization', // 个性化
  InitializeColumnWidth = 'initializeColumnWidth', // 初始化列宽
  MessageReceiveSet = 'messageReceiveSet', // 消息免打扰

  /**
   * 卡片按钮显示在行末
   * */
  DataEdit = 'edit', // 编辑
  DataCopy = 'copy', // 复制
  DataDelete = 'delete', // 删除
  DataPrint = 'print', // 打印
  DataShare = 'share', // 共享
}

export enum ActionPositionType {
  LineEnd = 'lineEnd', // 行末
  LineEndDropDown = 'LineEndDropDown', // 行末下拉
  Link = 'link', // 理解为链接字段，行上
  ListLine = 'listline' // 理解为行动作
}
/**
 * 组件默认按钮
 */
export enum ComCustomConstBtn {
  ExportButton = 'ExportButton',
  ADDBUTTON = 'AddButton',
}
export const { EventsType } = ebdcoms.get();