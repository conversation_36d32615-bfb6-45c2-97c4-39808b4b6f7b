import { appInfo, getLabel } from '@weapp/utils';
import { INIT_PAGE_SIZE_CONFIG, INIT_PAGE_MODE, INIT_MARK_CONFIG, INIT_OTHERDARA_CONFIG, INIT_YD_OUTER_CONFIG } from '../components/baseBoard/config/cmps/constants';
/**
 * eb组件类名前缀
 */
export const appName = 'ebdboard';

export const ebdfClsPrefix = 'weapp-ebdf';
export const ebdBClsPrefix = 'weapp-ebdboard';
export const mBoardClsPrefix = 'weapp-ebdboard-m';

export const ebdBComsName = 'weappEbdboard';

export const boardUrlParamsCache = `${appName}UrlParamsCache`;

/**
 * 网关标识
 */
export const EbuilderFormModule = 'ebuilder/form';
export const EbdfDfValueParamsCacheKey = 'EbdfDfValueParamsCacheKey';
const { publicDomain, publicUrl } = appInfo('@weapp/ebdboard');
export const root = publicUrl || window.publicUrlstatic || window.publicUrl || '';
export const routeName = 'ebdboard';
export const FORM_URL = (url: string) => `/api/ebuilder/form/list/${url}`;
export const BATCH_URL = (url: string) => `/api/ebuilder/form/batch/${url}`;
export const spRouteName = `${root}/sp/${routeName}/`;
export const mobileRouteName = `${root}/mobile/${routeName}/`;

/**
 * 视图类型
 * */
export enum ModuleType {
  Form = 'formView', // 表单视图
  Table = 'tableView', // 表格
  Gantt = 'ganttView', // 甘特图
  Calendar = 'calendarView', // 日历
  Board = 'kanbanView', // 看板
  MindMap = 'mindMapView', // 思维导图
  Grid = 'gridView', // 网格视图
  Occupy = 'occupyView', // 网格视图
}
/** eb后台公用头部图标 */
export const dlgIconName = 'Icon-e-builder-o';
export const viewHeaderClsPrefix = `${ebdBClsPrefix}-view-header`;
/**
 * 向form表单中插入默认值时使用的key
 * */
export const FormDefaultValueSessionKey = 'ebdfpage_form_default_values';

/**
 * 应用名称，用于组件注入middleware传值
 */
export const ebdBoardAppName = appInfo('@weapp/ebdboard').libraryName;

/**
 * 高级搜索，快捷，筛选三者独立，本地缓存搜索状态
 */

export enum ListSearchCacheParams {
  CurrentSearchState = 'currentSearchState',
  MCurrentSearchState = 'mcurrentSearchState',
}
/**
 * 卡片按钮方法常量
 */
export enum CardButtonType {
  Comment = 'comment',
  View = 'view',
  OperationLog = 'operationLog', // 操作日志
  FormLog = 'formlog',
  Save = 'save',
  SaveAndCreate = 'saveAndCreate',
  SaveAndCopy = 'saveAndCopy',
  EditSave = 'editSave',
  TempSave = 'tempSave',
  Edit = 'edit',
  Copy = 'copy',
  Delete = 'delete',
  Print = 'print',
  Cancel = 'cancel',
  GoBack = 'goBack', // 返回
  Chart = 'chart', // 流程图
  Status = 'status', // 流程状态
  Follow = 'follow',
  Unfollow = 'unfollow',
  LikeBtn = 'likeBtn', // 赞
  DislikeBtn = 'disLikeBtn', // 踩
  CommentBtn = 'commentBtn', // 评论
  Remove = 'remove', // 彻底删除
  Restore = 'restore', // 还原
  Share = 'share', // 共享
  MattersForward = 'mattersForward', // 事项转发
  EncryptForward = 'encryptForward', // 加密转发
  MessageReceiveSet = 'messageReceiveSet', // 消息免打扰

  PrevData = 'prevData', // 上一条
  NextData = 'nextData', // 下一条
  ExportPDF = 'exportPDF', // 导出PDF
}

/**
 * 列表按钮方法常量
 */
export enum ListButtonType {
  TableAdd = 'tableAdd',
  TableDelete = 'tableDelete', // 批量删除
  doImp = 'doImp',
  TableExp = 'tableExp', // 导出
  TableImp = 'tableImp', // 导入
  BatchEditValue = 'batchEditValue', // 批量编辑字段值
  BatchPrint = 'batchPrint', // 批量打印
  BatchAggregationPrint = 'batchAggregationPrint', // 批量打印
  BatchAdd = 'batchAdd', // 批量新增
  BatchEdit = 'batchEdit', // 批量编辑
  BatchFollow = 'batchFollow', // 批量关注
  BatchUnfollow = 'batchUnfollow', // 批量取消关注
  BatchMarkReadState = 'batchMarkReadState', // 批量已读
  MarkReadState = 'markReadState', // 标记为已读
  Follow = 'follow',
  Unfollow = 'unfollow',
  LikeBtn = 'likeBtn', // 赞
  DislikeBtn = 'disLikeBtn', // 踩
  CommentBtn = 'commentBtn', // 评论
  MyFollows = 'myFollows',
  BatchPubTags = 'batchPubTags', // 批量设置公共标签
  BatchMyTags = 'batchMyTags', // 批量设置我的标签
  SetTop = 'setTop', // 置顶
  CancelSetTop = 'cancelSetTop', // 取消置顶
  CustomBatch = 'customBatch', // 自定义批量操作
  BatchShare = 'batchShare', // 批量共享
  ShareToMe = 'shareToMe', // 共享给我的数据
  Personalization = 'personalization', // 个性化
  InitializeColumnWidth = 'initializeColumnWidth', // 初始化列宽
  MessageReceiveSet = 'messageReceiveSet', // 消息免打扰

  QuickOrder = 'quickOrder', // 快捷排序

  /**
   * 卡片按钮显示在行末
   * */
  DataEdit = 'edit', // 编辑
  DataCopy = 'copy', // 复制
  DataDelete = 'delete', // 删除
  DataPrint = 'print', // 打印
  DataShare = 'share', // 共享
  ExportPDF = 'exportPDF',
}

export enum PageTypes {
  WORKFLOW = 'WORKFLOW',
  SEARCH = 'SEARCH',
  FORM = 'LAYOUT',
  PAGE = 'PAGE',
  REPORT = 'REPORT',
  SYSTEMPAGE = 'SYSTEMPAGE',
  LINK = 'LINK',
  VIEWPORT = 'VIEWPORT', // 日历等视图
  Scene = 'SCENE',
}
/**
 * 布局等右侧滑宽度缓存key
 */
export const EbFSlideRightWidthKey = 'EbFSlideRightWidthKey';
/**
 * 1、按钮参数缓存key，方便按钮动作参数解析url等参数
 * 2、获取默认值后缓存，方便布局参数获取
 */
export const EbdfUrlParamsCacheKey = 'EbdfUrlParamsCacheKey';

/** *
 * 卡片url参数
 */

export enum LayoutType {
  Show = '0', // 显示布局
  Add = '1', // 新建布局
  Edit = '2', // 编辑布局
}
/**
 * OpenMode 页面打开方式，
 * @description 来自 @weapp/ebdcoms src/constants/index.ts
 * */
export enum OpenMode {
  Default = '4',
  New = '0',
  SlideLeftFromRight = '1',
  SlideRightFromLeft = '2',
  SlideUpFromBottom = '3',
  Modal = '5',
  /** 当前布局 */
  Layout = '6',
  /** 右侧分栏 */
  RightLayout = '7',
  /** 重定向 */
  Replace = '8',
}
/**
 * 对外的模块标识 表单对接
 */
export const EbuilderModule = 'ebuilderform';

export enum CommonSearchType {
  Advanced = 0,
  TopSearch = 1,
}

// 多条件，目前只需要对为空不为空处理
export const conditionNullArr = [
  'isNull', // 为空
  'isNotNull', // 不为空
];
// 多条件后缀
export const conditionSuffix = 'condition_type';
/**
 * 单多选选中色值
 */
export const CheckedColor: string = '#5D9CEC';

//显示字段、条件设计器、动作、屏蔽标签字段
export const NotUseFields = [
  '11', //"公共标签"
  '12', //  "我的标签",
];

/**
 * 默认的分页大小
 */
export const KanBanDefaultPageSize: string = '20';
export const KanBanDefaultPluginName: string = 'kan_ban_plugin';
export const KanBanInitConfig = (initTitle = true) => {
  let config: any = {
    dataset: {
      id: '',
      type: 'FORM',
      groupId: '',
      text: '',
    },
    group: {
      active: '0',
      options: [],
      otherData: INIT_OTHERDARA_CONFIG(),
    }, // 数据配置
    card: {},
    orders: [],
    condition: {},
    comButton: [],
    markSetting: INIT_MARK_CONFIG(),
    // ...INIT_BOTTOM_BTN_CONFIG(),
    filter: { commonFilters: [], quickFilters: [] },
    statistics: {
      openValue: false,
      statisticsItem: [],
    },
    pageSize: INIT_PAGE_SIZE_CONFIG(),
    pageMode: INIT_PAGE_MODE,
    laneConfig: INIT_YD_OUTER_CONFIG(),
  };
  if (initTitle) {
    config = Object.assign(
      {
        title: getLabel('189032', '看板标题'),
        titleEnabled: false,
        footerEnabled: false,
      },
      config
    );
  }
  return config;
};
// export const KanBanDefaultBgColor = '#f0f1f4' // var(--bg-base)
export const KanBanDefaultBgColor = '#ffffff'; // var(--bg-base)

export const isCn = window.TEAMS?.locale?.lang === 'zh_CN';
export const DEFAULT_LANE_ID = '-1';
export const DEFAULT_LANE_ITEM = {
  id: DEFAULT_LANE_ID,
  name: '',
  groups: [],
};

export const INIT_LANE_GROUP = () => [DEFAULT_LANE_ITEM];
