import { PickerType } from '@weapp/ui/lib/components/date-picker/types';

// et的日期时间格式
export enum DateFormat {
  DayTimeSecond = 'yyyy-MM-dd HH:mm:ss',
  DayTime = 'yyyy-MM-dd HH:mm',
  Day = 'yyyy-MM-dd',
  Month = 'yyyy-MM',
  Year = 'yyyy'
}

// 映射date组件的type
export const DateFormatType:{[_:string]:PickerType} = {
  [DateFormat.Day]: 'day',
  [DateFormat.Month]: 'month',
  [DateFormat.Year]: 'year',
};

// 组件的日期格式
export enum TimeFormat {
  TimeSeconds = 'HH:mm:ss',
  Time = 'HH:mm'
}

export const DateQuickFilterFormats: {
  [key:string]:string
} = {
  [DateFormat.DayTimeSecond]: 'YYYY-MM-DD HH:mm:ss',
  [DateFormat.DayTime]: 'YYYY-MM-DD HH:mm',
  [DateFormat.Day]: 'YYYY-MM-DD',
  [DateFormat.Month]: 'YYYY-MM',
  [DateFormat.Year]: 'YYYY',
};
