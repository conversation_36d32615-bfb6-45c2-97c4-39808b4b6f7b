import { getLabel } from '@weapp/utils';
import { DEFAULT_LANE_ID } from '../constants';

export const KanbanMockData = () => [
  {
    id: DEFAULT_LANE_ID,
    name: '',
    groups: [
      {
        id: '920806289435164673',
        name: get<PERSON><PERSON><PERSON>('244914', '分组一'),
        type: 1,
        cards: [
          {
            extra: {
              data_status: '1',
            },
            '920805322950082561': `${getLabel('54220', '分组')}1-1`,
            id: '920805456237813765',
          },
          {
            extra: {
              data_status: '1',
            },
            '920805322950082561': `${getLabel('54220', '分组')}1-2`,
            id: '920805473416544257',
          },
        ],
      },
      {
        id: '920806289435164674',
        name: get<PERSON><PERSON><PERSON>('105298', '未分组'),
        type: 0,
        cards: [
          {
            extra: {
              data_status: '1',
            },
            '920805322950082561': `${getLabel('105298', '未分组')}1-2`,
            id: '920805537690058752',
          },
          {
            extra: {
              data_status: '1',
            },
            '920805322950082561': `${getLabel('105298', '未分组')}1-1`,
            id: '920805511929782272',
          },
        ],
      },
    ],
  },
];
export const KanbanMockEmptyData = () => [
  {
    id: DEFAULT_LANE_ID,
    name: '',
    groups: [
      {
        id: '',
        laneId: DEFAULT_LANE_ID,
        name: getLabel('105298', '未分组'),
        type: 0,
        cards: [],
      },
    ],
  },
];
export const KanbanMockConfig = {
  id: 'fac9b5af24bf490b859443fef2236043',
  pid: '',
  refCompId: 'cb47cd55c21c4dc5a73eb579db269cd0',
  type: 'Kanban',
  category: '4',
  refModule: '',
  tenantKey: 'tp7ed833w9',
  styleCategoryCompKey: 'BasicStyleConfig',
  styleCategoryPackage: '@weapp/ebdcoms',
  applyStyle: '1',
  lock: false,
  config: {
    dataset: {
      id: '920805279983632386',
      text: '',
      groupId: '920144031225970690',
      type: 'FORM',
    },
    group: {
      dataGroup: 0,
      active: 0,
      permissions: [],
      options: [
        {
          groupType: 0,
          enableColor: false,
          values: [
            {
              id: '920806289435164673',
              type: 1,
              name: getLabel('244914', '分组一'),
              color: '#2484ad',
            },
            {
              id: '920806289435164674',
              type: 0,
              name: getLabel('105298', '未分组'),
            },
          ],
        },
      ],
      dataOptions: [],
    },
    orders: [],
    filter: {},
    condition: {},
    card: {
      field: [
        {
          type: 'String',
          lazyLoad: false,
          compTypec: '',
          browserType: '',
          compType: 'Text',
          disAllowedUsage: [],
          text: '',
          mainPrimaryKey: false,
          formInfo: {
            fieldId: '920805322950082561',
          },
          mainField: true,
          deleteType: false,
          parentId: '',
          browserParams: {},
          placeholderMark: false,
          objId: '920805279983632386',
          name: '920805322950082561',
          dbFieldType: '',
          richEditor: false,
          config: {
            formId: '920805279983632387',
            groupId: '-1',
            isUnique: false,
            title: '',
            content: '',
            dataCount: 0,
            dataKey: 'fzm',
            isDefault: false,
            unique: false,
            name: '',
            showOrder: 0,
            describe: '',
            placeholder: '',
            componentKey: 'Text',
            columnName: 'fzm',
            fieldId: '920805322950082561',
            maxLen: 100,
          },
          multiSelect: 'false',
          group: 'form',
          overFlow: {
            number: '',
            type: 'line',
          },
          autoFocus: false,
          id: '920805322950082561',
          uid: 'f0e56f5d2fc949658120950dafb97505',
        },
      ],
      cardLayout: {
        split: [
          {
            index: 0,
            setting: {
              width: {
                type: '',
                value: '',
              },
            },
            id: 'c770ee68c8b54b06a2c723f8754dfa49',
            type: '0',
          },
        ],
        grid: [
          [
            [
              {
                x: 0,
                y: 0,
                w: 1,
                h: 1,
                field: {
                  id: '920805322950082561',
                  uid: 'f0e56f5d2fc949658120950dafb97505',
                  style: {},
                  width: {
                    type: '1',
                    value: '',
                    unit: 'px',
                  },
                  minWidth: '80',
                  showTrans: [],
                  align: 'left',
                  wordWrap: false,
                  padding: [0, 12, 0, 0],
                  image: {
                    showType: 'grid',
                    previewable: true,
                    h: '100%',
                    w: '100%',
                  },
                  horAlign: '0',
                  isTransAvatar: false,
                  overFlow: {
                    number: '',
                    type: 'line',
                  },
                  type: 'String',
                },
                i: 'c4e6ca19a0bf42e289d91d45a187f019',
              },
            ],
          ],
        ],
        size: [[4, 3]],
        row: [
          [
            {
              index: 0,
              isHide: false,
              setting: {
                height: '',
                alignItems: 'center',
              },
            },
            {
              index: 1,
              isHide: false,
              setting: {
                height: '',
                alignItems: 'center',
              },
            },
            {
              index: 2,
              isHide: false,
              setting: {
                height: '',
                alignItems: 'center',
              },
            },
            {
              index: 3,
              isHide: false,
              setting: {
                height: '',
                alignItems: 'center',
              },
            },
          ],
        ],
        extendArea: {
          id: 'fe7f6b1fd1b44d6abcdd0f5983de3ad4',
          display: 'none',
          type: 'current',
          pageMode: 'part',
          pageSize: 10,
        },
        shrinkDisplayType: 'single',
        shrinkRegionIndex: -1,
      },
    },
    layout: {
      hidden: false,
      wrapped: false,
      selected: true,
    },
    package: '@weapp/ebdfpage',
    titleEnabled: false,
    styles: [
      {
        customStyle: {
          container: {
            padding: '0px 16px 0px 0px',
            margin: '0px 0px 0px 0px',
            backgroundColor: '#f6f6f6',
          },
        },
        useCustom: true,
        id: '7',
        category: '7',
      },
      {
        customStyle: {
          title: {
            background: {
              imgUrl: '',
              backgroundColor: 'transparent',
              positionType: 'grid',
              backgroundImage: '',
              backgroundSize: 'auto',
              backgroundPosition: 'center center',
              backgroundRepeat: 'no-repeat',
            },
          },
        },
        useCustom: true,
        id: '',
        category: '1',
      },
    ],
    showLunar: true,
    flow: {
      parent: 'ROOT',
      nodes: [],
      isCanvas: false,
      linkedNodes: {},
      nodeId: '9RYRgNhBhi',
    },
    footerEnabled: false,
    objId: '920805279983632386',
    fromEbuilder: true,
    type: 'Kanban',
    canDelete: false,
    name: '',
  },
  package: '@weapp/ebdboard',
  isLock: false,
};
