import {
    ConditionDataType,
    RefreshInfoType,
  } from '@weapp/ebdcoms/lib/common/event-action-temp/type';
  import { LinkInfo } from '@weapp/ebdcoms/lib/common/picture-select/types';
  import { Attributes } from 'react';
  import { DataSet } from '../../types/common';
  
  export enum EventsType {
    Click = 'CLICK' /** 单击事件 */,
    ClickData = 'CLICKDATA' /** 单击每一条数据事件 */,
    DidMount = 'DIDMOUNT' /** 载入 */,
    PressEnter = 'PRESSENTER' /** 回车事件 */,
  }
  
  export type Events = {
    type: EventsAction /** 事件类型 */;
    conditionData?: ConditionDataType /** 条件控制信息 */;
    events?: Events[] /** 条件控制下的动作 */;
    linkInfo?: LinkInfo /** 页面参数 */;
    jsCode?: string /** js代码 */;
    refreshInfo?: RefreshInfoType /** 刷新组件 */;
    id: string;
    /** 显示菜单 */
    // menuInfo?: MenuInfo;
  };
  
  export enum EventsAction {
    IF = 'IF' /** 条件 */,
    ELSEIF = 'ELSEIF' /** 条件 */,
    ELSE = 'ELSE' /** 条件 */,
    NEWPAGE = 'NewPage' /** 打开页面 */,
    JSCODE = 'JsCode' /** js */,
    CLOSEPAGE = 'ClosePage' /** 关闭页面 */,
    REFRESHCOMP = 'RefreshComp' /** 刷新组件 */,
    EXPORTPDF = 'ExportPdf' /** 导出PDF */,
    SHOWMENU = 'ShowMenu' /** 显示菜单 */,
    SHOWHIDECOMPONENT = 'ShowHideComponent' /** 显示/隐藏组件 */,
  
  }
  
  export type EvtMap = {
    type: EventsType /** 事件类型 */;
    id: string /** 事件id */;
    name: string /** 事件名 */;
    events: Events[] /** 动作集合 */;
    disableDelete?: boolean /** 禁止删除 */;
    actionDesc?: string /** 动作描述 */;
  };
  
  export type EventsActionData = {
    eventGroup: EvtMap[];
    dataset: DataSet;
  };
  
  export interface EventsActionProps extends Attributes {
    data: EvtMap[];
    dataset: any;
    visible: boolean;
    page: any;
    isDialog?: boolean;
    onCancel?: () => void;
    onSure: (data: EvtMap[]) => void;
  }
  