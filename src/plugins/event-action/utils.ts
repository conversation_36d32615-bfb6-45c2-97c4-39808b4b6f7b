/**
 * <AUTHOR>
 * @createTime 2023 -01 -18
 */
// import { SystemPageType } from '@weapp/ebdcoms';
// import { PageLinkMode } from '@weapp/ebdcoms/lib/common/page-link/types';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { PageTypes } from '../../constants';
import { UUID } from '../../utils';
import ebdcoms from '../../utils/ebdcoms';
import { Events, EventsAction, EventsType, EvtMap } from './type';

export type PageLinkMode = 'dataList' | 'custom';
enum UrlLinkType {
  ChooseAddress = '0',
  ChooseField = '1',
  ChooseMenu = '2',
}

export enum NavCompType {
  NavPanel = 'NavPanel', // EB 导航面板
  Navigation = 'Navigation', // 原ET移动端导航组件
  FloatButton = 'FloatButton', // 浮动按钮
  TabBar = 'TabBar', // 标签栏
  BuildIn = 'BuildIn', // 只读的
}

export enum PageViewType {
  Calendar = 'CALENDAR', // 日历
  Board = 'KANBAN', // 看板
  Gantt = 'GANTT', // 甘特图
  Occupy = 'OCCUPY', // 占用视图
}

export interface PageInfo {
  pageType: PageTypes;
  pageId: string;
  pageName: string;
  terminalScopes?: any;
  appid?: string;
  isDir?: boolean; // 门户菜单是否是目录
  pageViewType?: PageViewType; // 页面显示类型，针对pagetype做扩充处理
  openMode?: string;
  /** 是否为系统菜单内的自定义EB页面 */
  isEbPage?: boolean;
  /** 选择菜单 - 集成登录 - 跳转路径 */
  pageExtend?: string;
}

const getName = (data?: PageInfo, type?: PageLinkMode, otherParams?: any) => {
  if (!data) return '';

  // 如果类型为数据列表，则直接返回数据列表作为value值
  if (type && type === 'dataList') return getLabel('87643', '数据列表');

  const { pageName, pageType, pageId, isDir, pageViewType } = data;
  const _pageName = pageName.replace(/\[.*?]/g, '');

  const { urlLinkType, urlFieldId, urlFieldText = '' } = otherParams || {};
  if (urlLinkType === UrlLinkType.ChooseField) {
    return urlFieldId ? `${getLabel('85498', '自定义字段')}：${urlFieldText}` : '';
  }

  switch (pageType) {
    case PageTypes.PAGE:
      return `${getLabel('54024', '页面')}: ${_pageName}`;
    case PageTypes.SEARCH:
      return `${getLabel('55625', '表格')}: ${_pageName}`;
    case PageTypes.LINK:
      if (!pageId) return '';
      return `${getLabel('54002', '自定义')}: ${pageId}`;
    case PageTypes.WORKFLOW:
      return `${getLabel('54203', '流程')}: ${_pageName}`;
    case PageTypes.REPORT:
      return `${getLabel('54204', '报表')}: ${_pageName}`;
    case PageTypes.Scene:
      return `${getLabel('100786', '绘图')}: ${_pageName}`;
    case PageTypes.SYSTEMPAGE:
      return `${getLabel('55626', '系统')}: ${_pageName}`;
    case PageTypes.VIEWPORT: {
      switch (pageViewType) {
        case PageViewType.Calendar:
          return `${getLabel('55794', '日历')}: ${_pageName}`;
        case PageViewType.Occupy:
          return `${getLabel('94012', '占用视图')}: ${_pageName}`;
        case PageViewType.Board:
          return `${getLabel('56252', '看板')}: ${_pageName}`;
        default:
          return pageName;
      }
    }
    default:
      return pageName;
  }
};

export const actionsOptions = ()=> [
  {
    id: EventsAction.NEWPAGE,
    content: getLabel('54993', '打开页面'),
    valueKey: 'linkInfo' /** 数据存放的key，必须每个选项有，最后保存会简化属性 */,
  },
  {
    id: EventsAction.CLOSEPAGE,
    content: getLabel('107951', '关闭窗口'),
    valueKey: '',
  },
  {
    id: EventsAction.IF,
    content: getLabel('107952', '条件控制'),
    valueKey: 'conditionData',
  },
  {
    id: EventsAction.JSCODE,
    content: 'JavaScript',
    valueKey: 'jsCode',
  },
  {
    id: EventsAction.REFRESHCOMP,
    content: getLabel('91792', '刷新组件'),
    valueKey: 'refreshInfo',
  },
  {
    id: EventsAction.EXPORTPDF,
    content: getLabel('124166', '导出PDF'),
    valueKey: '',
  },
  {
    id: EventsAction.SHOWMENU,
    content: getLabel('160134', '显示菜单'),
    valueKey: 'menuInfo',
  },
  {
    id: EventsAction.SHOWHIDECOMPONENT,
    content: getLabel('163310', '显示/隐藏组件'),
    valueKey: 'displayComs',
  },
];

const getActionDesc = (action: Events) => {
  if (!action) return '';
  if (action.type === EventsAction.NEWPAGE) {
    return getName(action.linkInfo?.page as any, (action.linkInfo as any)?.type);
  }
  if (action.type === EventsAction.JSCODE) {
    return action.jsCode || 'javascript';
  }
  const currentAct = actionsOptions().find(el => el.id === action.type);
  return currentAct?.content || '';
};

export const transActionsToEventGroup = (actions: any[]) => {
  const eventGroup0: EvtMap[] = actions.map((act: any) => {
    if (act?.actionType === 'openpage') {
      if (act?.url && typeof act.url === 'string') {
        act.record = JSON.parse(act.url);
      }
    }
    if (act.events) return act;
    const refClickData: Events[] = [
      {
        id: UUID(),
        type: EventsAction.NEWPAGE,
        linkInfo: {
          ...toJS(act.record),
          page: act.record.page,
          name: act.name,
        },
      },
    ];
    return {
      type: EventsType.Click,
      name: getLabel('107774', '单击'),
      id: UUID(),
      disableDelete: true,
      events: refClickData,
      actionDesc: getActionDesc(refClickData[0]),
    };
  });
  return { eventGroup: eventGroup0.length > 0 ? [eventGroup0] : [] };
};

/**
 * 获取点击事件默认第一个打开页面事件
 * @param eventGroup 事件动作组
 * @param type 事件动作类型
 * @returns
 */
export const getDftClickPageData = (eventGroup: EvtMap[][] = [], type: EventsType = EventsType.Click) => {
  const eventData: EvtMap[] = eventGroup[0];
  const clickIndex = eventData?.findIndex(el => el.type === type);
  if (clickIndex > -1 && eventGroup[0][clickIndex].events[0]?.type === EventsAction.NEWPAGE) {
    return {
      linkInfo: eventGroup[0][clickIndex].events[0].linkInfo,
      clickIndex,
    };
  }
  return {
    clickIndex,
  };
};

export const getSystemIcon = (systemId: string) => {
  const { SystemPageType } = ebdcoms.get();
  switch (systemId) {
    case SystemPageType.WriteDaily:
      return '#icon-edit';
    case SystemPageType.NewSchedule:
      return '#icon-calendar';
    case SystemPageType.UploadFile:
      return '#icon-upload03';
    case SystemPageType.NewCustomer:
      return '#icon-customer';
    case SystemPageType.NewContact:
      return '#icon-contact';
    case SystemPageType.NewContactRecord:
      return '#icon-record';
    case SystemPageType.Back:
      return '#icon-fanhui';
    case SystemPageType.Message:
      return '#icon-xiaoxi';
    case SystemPageType.Application:
      return '#icon-yingyong';
    case SystemPageType.Contacts:
      return '#icon-lianxiren';
    case SystemPageType.Me:
      return '#icon-wo';
    default:
      return '';
  }
};
// 导航面板和导航栏 图标默认颜色不一样
export const getInitIconStyle = (compType: string) => {
  const styleObj = {
    color: '#666',
    backgroundColor: 'transparent',
  };

  if (compType === NavCompType.NavPanel) {
    styleObj.color = '#2dc3e8';
    styleObj.backgroundColor = 'transparent';
  }

  return styleObj;
};
export const FORMFIELDS = 'FORMFIELDS';

// id固定值
export const IDKEY = 'id';
export const DefIdentifyIdVal = {
  name: IDKEY,
  type: FORMFIELDS,
  value: { id: IDKEY, content: '', type: IDKEY },
};
export const getParamsOpts = (fieldDatas: any[], formFieldsTitle?: string) => {
  const data: any[] = [];
  const dataGroupTitle: any[] = [];
  // 主键字段
  let mainPrimaryKeyField: any;
  // name为id的添加索引
  let nameIdFieldIndex: number = -1;

  fieldDatas.forEach(fieldData => {
    const { id, name, fields = [] } = fieldData;

    dataGroupTitle.push({ id, content: name });

    fields.forEach((field: any) => {
      if (field) {
        if (field.isDetailtable) {
          field.fields?.forEach((f: any) => {
            data.push({
              ...f,
              id: f.id,
              content: f.text,
              type: id,
              fieldType: f?.type,
            });
          });
        } else if (field.mainPrimaryKey) {
          // 如果是主键字段，记录下
          mainPrimaryKeyField = field;
        } else {
          if (field.name === IDKEY) {
            // 如果符合id固定值的name记录下
            nameIdFieldIndex = data.length;
          }
          data.push({
            ...field,
            id: field.id,
            content: field.text,
            type: id,
            fieldType: field?.type,
          });
        }
      }
    });
  });

  let primaryKey = '';
  let primaryContent = '';

  if (mainPrimaryKeyField) {
    // 先判断是否包含主键字段然后添加
    dataGroupTitle.unshift({ id: IDKEY, content: getLabel('70531', '主键字段') });
    primaryKey = mainPrimaryKeyField.id;
    primaryContent = mainPrimaryKeyField.text || getLabel('55955', '数据ID');
    data.unshift({
      id: primaryKey,
      content: primaryContent,
      type: IDKEY,
      objId: mainPrimaryKeyField.objId,
    });
  } else if (nameIdFieldIndex > -1) {
    // 再判断是否包含name是id字段然后添加
    dataGroupTitle.unshift({ id: IDKEY, content: getLabel('70531', '主键字段') });
    const [nameIdField] = data.splice(nameIdFieldIndex, 1);
    primaryKey = IDKEY;
    primaryContent = nameIdField.content || getLabel('55955', '数据ID');
    data.unshift({ ...nameIdField, id: IDKEY, type: IDKEY });
  }

  const paramOpts = [
    {
      type: FORMFIELDS,
      text: formFieldsTitle || getLabel('186601', '当前节点字段'),
      ref: {
        type: 'select',
        item: '',
        data,
        dataGroupTitle,
      },
      noPrimaryKey: !primaryKey,
    },
  ];
  const defIdentifyIdVal = {
    name: IDKEY,
    type: FORMFIELDS,
    value: { id: primaryKey, content: primaryContent, type: IDKEY },
  };

  return { paramOpts, defIdentifyIdVal };
};

export default {};
