import React from 'react';
import { configure } from 'mobx';
import { Provider } from 'mobx-react';
import { LazyType } from './types/public';

import Route from './routes/pc/Main';

/* 导出库的样式文件，只在应用内使用不做共享的，请写在 style/index.less */
import './style/lib.less';

/* mobx 配置 */
configure({ enforceActions: 'always' });

/* 常量导出 */
export * as constants from './constants';

const stores = {};

// 导出主路由组件，包裹共享的全局 store
export function RouterMain() {
    return (
        <Provider weId={`_dppjux`} {...stores}>
            <Route weId={`_giucfr`} />
        </Provider>
    );
}
// 对外抛出表单高级视图顶部按钮组
export const BoardBuilderHeader = React.lazy(() => import('./components/baseBoard/engine/board-builder')) as LazyType;
export const AdvanceFormEngine = React.lazy(() => import('./pages/designer/EbFormEngine/formEngine')) as LazyType;
export * as ebcoms from './ebcoms';
export * as ebComStyleConfigs from './ebComStyleConfigs';

export * as doc from './doc'; //导出文档

/* libVersion导出 */
export * as libVersion from './libVersion';

export * as ebplugins from './components/plugin-packages/ebplugins';