import { corsImport } from '@weapp/utils';
import { useEffect, useState } from 'react';
// import { IConfigPluginClass } from '../base/config/plugins/types';
// import { IListDesignPluginClass, IListPlugin, IListPluginClass } from '../base/view/plugins/types';
// import CorePlugins from './common/plugins/core';
type PluginInfoDataType = {
  /** 插件id */
  id: string;
  /** 插件的中文名称 */
  pluginName: string;
  /** 插件所在的前端仓库，如@weapp/ebdlist */
  resourcePackage: string;
  /** 插件的唯一标识，如Data */
  resourceType: string;
  /** 插件执行顺序 */
  pluginOrder: number;
  /** 是否启用 */
  enabled: boolean;
  /** 插件属于前端/后端/前后端都有 */
  resourceScope: string;
  /** 是否删除 */
  deleteType?: number
};
export type PluginPackDataType = {
  /** 插件包id，唯一标志 */
  id: string;
  /** 插件包名称 */
  packName: string;
  /** 是否启用 */
  enabled: boolean;
  /** 插件包使用的组件类型,ex: NList */
  componentType: string;
  /** 所属团队 */
  tenantKey?: string;
  /** 是否删除 */
  deleteType?: number;
  /** 插件包中包含的插件 */
  plugins?: PluginInfoDataType[];
};
export interface IConfigPluginClass {
  new (opts?: any): any;
}
// type IPluginTypes = {
//   Config: IConfigPluginClass;
//   MConfig: IConfigPluginClass;
//   View: IListPluginClass;
//   MView: IListPluginClass;
//   Design: IListDesignPluginClass;
//   MDesign: IListDesignPluginClass;
// };
type PluginTypes = {
  Config: any;
  MConfig: any;
  View: any;
  MView: any;
  Design: any;
  MDesign: any;
};
interface PluginProps {
}

export function loadPluginClasses<T extends keyof PluginTypes>(
  pluginPack: PluginPackDataType = {
    id: '',
    packName: '',
    enabled: true,
    componentType: 'NList',
    plugins: [],
  },
  viewType: T,
): Promise<PluginTypes[T][]> {
  const { plugins = [] } = pluginPack;

  return Promise.all(
    // 先限制插件包中只能包含两个插件，后续再考虑放开
    plugins.slice(0, 2).map((plugin) => {
      const { resourceType, resourcePackage } = plugin;
      if (resourcePackage) {
        return corsImport(resourcePackage).then(
          (weappModule) => new Promise((reslove) => {
            const pluginsResource = weappModule.ebplugins?.[resourceType];
            if (pluginsResource) {
              if (typeof pluginsResource === 'function') {
                pluginsResource().then((res: any) => {
                  const mod = res.default;
                  reslove(mod[viewType]);
                });
              } else if (typeof pluginsResource[viewType] === 'function') {
                pluginsResource[viewType]().then((res: any) => {
                  const mod = res.default;
                  reslove(mod);
                });
              } else {
                reslove(pluginsResource[viewType]);
              }
            } else {
              reslove(null);
            }
          }),
        ) as any;
      }
      return null;
    }),
  ).then((datas) => datas.filter(Boolean).flat());
}

export default function usePluginClasses(
  type: keyof PluginTypes,
  pluginPack?: PluginPackDataType,
) {
  const [classes, setClasses] = useState<PluginProps[] | null>(() => {
    // 未设置插件包走不需要走load逻辑
    if (!pluginPack) return [];

    return null;
  });

  useEffect(() => {
    if (pluginPack) {
      loadPluginClasses(pluginPack, type).then((plugins) => {
        // 统一管理插件包核心插件
        // const corePlugins = (CorePlugins as any)[type];
        const corePlugins = {} as any;
        // setClasses([...corePlugins, ...plugins]);
        setClasses([...plugins]);
      });
    }
  }, []);

  return classes;
}

export function getPluginClasses(
  type: keyof PluginTypes,
  pluginPack?: PluginPackDataType,
  cb?: Function
) {
  if (!pluginPack) return [];
  loadPluginClasses(pluginPack, type).then((plugins) => {
    // 统一管理插件包核心插件
    // const corePlugins = (CorePlugins as any)[type];
    // const corePlugins = {} as any;
    // setClasses([...corePlugins, ...plugins]);
    cb && cb([...plugins])
  });
}
