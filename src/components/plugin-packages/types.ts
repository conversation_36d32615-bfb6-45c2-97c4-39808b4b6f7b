import { ReactElement } from 'react';
import { BaseConfigDataType } from '../baseBoard/types';
export interface TPluginCenter {

}
export interface IPluginClass {
  new (opts?: any): IPlugin;
}

export type TPluginCom = {
  forceUpdate: () => void;
  readonly props: any;
};

export type TPluginOpts<T = {}> = T & {
  com: TPluginCom;
  pluginCenter: TPluginCenter;
};

export type TPluginMode = 'View' | 'Design';

export interface IPlugin {
  /** ---------- 内置属性/方法 ---------- */

  /** 组件实例 */
  com?: TPluginCom;
  /** board组件实例 */
  board?: {
    forceUpdate: () => void;
    readonly props: any;
  };

  pluginCenter?: TPluginCenter;

  /** ---------- 插件支持的属性/方法 ----------  */

  /** 插件名称，唯一标志，如：AutoScroll */
  name: string;

  /** 插件的选项，支持外部进行二次扩展等操作, list和store为plugincenter层传入，便于插件在constructor中做初始化操作 */
  opts?: TPluginOpts<Record<string, any>>;

  /** 插件是否启用 */
  readonly isEnabled?: boolean;

  /** 插件使用环境，运行默认不判断 */
  readonly mode?: TPluginMode[];

  /** 插件销毁，会在List组件unmount或者插件禁用后调用 */
  destory?: () => void;

  /**
   * -------------------------------------------------------------------
   * 仅适用于List Design的定义
   * 针对列表Design开的口子，冗余在IListPlugin的类型声明中 不单独再定义类型
   * -------------------------------------------------------------------
   */

  // /** 在设计视图处理属性变更后的回调 */
  handlePropChange?(key: keyof BaseConfigDataType, value: any): void;

  // /** 在设计视图处理配置变更后的回调（在配置视图通过onConfigChange调用的情况） */
  handleConfigChange?(changes: Partial<BaseConfigDataType>): void;

  // /** --------------------------------------- List组件回调 --------------------------------------- */

  // /** 自定义ui列表props的方法 */
  // getListProps?(props: Partial<ListProps>): Partial<ListProps>[];

  // /** 自定义ui表格props的方法 */
  // getTableProps?(props: Partial<ITableProps>): Partial<ITableProps>[];

  // /** 运行动态更新table columns */
  // getTableViewColumns?(tableField: TableField[]): TableField[][];

  // /** 自定义元素特殊样式的方法 */
  // getListCellStyle?(cellStyle: IListCellStyle, opt?: IListCellStyleOpts): IListCellStyle[];

  // /** 自定义元素渲染的方法 */
  // getListCellContent?(
  //   cellValue: ReactElement,
  //   opt?: IListCellContentOpts
  // ): (ReactElement | IListCellContentOpts)[];

  // /** 表格式自定义列方法 */
  // getTableColumns?(columns: ITableColumn[]): ITableColumn[][];

  // /** List组件mount前的回调，此时store和插件已经初始化完成，可用于外部插件更新一些插件的opts */
  // onBeforeMount?(store: BaseListStore): void;

  // /** List组件didmount的回调 */
  // onDidMount?(listDom: HTMLElement, store: BaseListStore): void;

  // /** 列表内容自定义渲染，包含renderAddonBefore和renderAddonAfter */
  // renderContent?(hook: RenderHookType): RenderHookType;

  // /** 表格内容自定义渲染，不包含renderAddonBefore和renderAddonAfter */
  // renderTableContent?(hook: RenderHookType): RenderHookType;

  // /** 列表内容自定义渲染，不包含renderAddonBefore和renderAddonAfter */
  // renderListContent?(hook: RenderHookType): RenderHookType;

  // /** 在列表之前的自定义渲染 */
  // renderAddonBefore?(hook: RenderHookType): (datas: any[]) => ReactElement | undefined;

  // /** 在列表之后的自定义渲染 */
  // renderAddonAfter?(hook: RenderHookType): RenderHookType;

  // /** 在列表之上的自定义渲染 */
  // renderAddonHeader?(hook: RenderHookType): RenderHookType;

  // /** List组件渲染的回调，用于重写组件的render */
  // render?(hook: RenderHookType): RenderHookType;

  // /** List标题自定义渲染 */
  // renderTitle?(hook: RenderHookType): (title: any, config: any) => ReactElement | undefined;

  // /** List单元格自定义渲染 */
  // renderCell?(hook: RenderHookType): RenderHookType;

  // /** List单元格前的自定义渲染 */
  // renderCellAddonBefore?(hook: RenderHookType): (
  //   colIndex: number,
  //   options: {
  //     data: ListData;
  //     isMainLayout?: boolean;
  //     layoutIndex?: number;
  //     isExtendList?: boolean;
  //   }
  // ) => ReactElement | undefined;

  // /** 列表式-列表每行数据下方的自定义渲染 */
  // renderGridAddonAfter?(
  //   hook: RenderHookType
  // ): (index: number, data: ListData) => ReactElement | undefined;

  // /** 列表式-layout左侧自定义渲染 */
  // renderLayoutAddonBefore?(hook: RenderHookType):
  // (rowData: ListData) => ReactElement | undefined;

  // /** 列表式-layout右侧自定义渲染 */
  // renderLayoutAddonAfter?(hook: RenderHookType): (rowData: ListData) => ReactElement | undefined;

  // /** 列表式-listItem自定义渲染 */
  // renderListItem?(
  //   hook: RenderHookType
  // ): (rowData: ListData, index: number, opts?: any) => ReactElement | undefined;

  // /** --------------- List组件方法回调 ---------------- */

  // /** 单击每一条数据事件动作的方法 */
  // excuEventActions?(data: any, index: number, e?: React.MouseEvent): void;

  // /** View视图抛出上下文的方法 */
  // getViewMount?(context: Record<string, any>): Record<string, any>[];

  // /** --------------------------------------- 插件的方法回调 --------------------------------------- */
  // /** 内容顶部左侧的渲染 */
  // renderContentHeaderBefore?(hook: RenderHookType): RenderHookType;
  // /** 内容顶部右侧的渲染 */
  // renderContentHeaderAfter?(hook: RenderHookType): RenderHookType;
  // /** 当页码改变后的回调，参数分别为改变后的页码和每页条数 */
  // onPageNoChange?(value: number, pageSize: number): void;
  // /** 内容顶部的渲染条件 */
  // getEnableContentHeader?(enabled: boolean): boolean[];
  // /** 内容顶部区域的渲染条件 */
  // getEnableContentHeaderCell?(enabled: boolean): boolean[];
  // /** 内容顶部区域的渲染否决条件 */
  // getRejectRenderHeader?(rejected: boolean): boolean[];
  // /** 内容顶部按钮区域的渲染条件 */
  // getEnableHeaderButton?(enabled: boolean): boolean[];
  // /** 渲染在快捷按钮之后 */
  // renderQuickBtnsAfter?(hook: RenderHookType): RenderHookType;
  // /** 展开至顶部按钮内部组件的渲染 */
  // renderShrinkContent?(hook: RenderHookType): RenderHookType;
  // /** 单击每一条数据事件动作时的自定义链接参数 */
  // getCustomLinkOpts?(opts: Record<string, any>): Record<string, any>[];
  // /** 是否开启表格式表头排序功能 */
  // getSortIconRender?(enabled: boolean): boolean[];
  // /** 筛选或高级搜索是否直接显示在内容上方 */
  // getShowSearchContentTop?(enabled: boolean): boolean[];
  // /** 是否渲染到选项卡右上角 */
  // getRenderTabRight?(enabled: boolean): boolean[];
  // /** 是否允许禁止请求count接口 */
  // getForbidCount?(enabled: boolean): boolean[];
}

export type PluginCenterOptsType = {
  /**
   * 对启用状态的插件进行自定义过滤
   * 设计视图下因为配置会发生频繁改动，避免插件进行频繁的启用和销毁
   * 所以针对设计视图下的插件统一都进行启用，相关逻辑需要在逻辑内部进行控制
   */
  filterEnabledPlugin?: (isEnabled: boolean, plugin?: IPlugin) => Boolean;
};

export type TPluginHook = Exclude<
  keyof IPlugin,
  'pluginCenter' | 'com' | 'isEnabled' | 'name' | 'opts'
>;

export type TPluginRenderHook = keyof Pick<IPlugin, keyof IPlugin & `render${string}`>;

export type TPluginReduceHook = keyof Pick<IPlugin, keyof IPlugin & `get${string}`>;

export type TPluginRenderHookValue = (...args: any[]) => ReactElement | null;

export type TPluginDesignHook = 'handlePropChange' | 'handleConfigChange';

export type TPluginNormalHook = Exclude<
  TPluginHook,
  TPluginRenderHook | TPluginDesignHook | TPluginReduceHook
>;
