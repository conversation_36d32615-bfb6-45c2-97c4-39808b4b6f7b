import { useEffect, useState } from 'react';
import { isEmpty } from '@weapp/utils';
import GroupStatistics from '../../baseBoard/config/GroupStatistics';
import { DesignComProps } from '../../baseBoard/types';
import withPluginDataSet from './withDataSet';
import './index.less';

export interface PsCardOpenStaticProps extends DesignComProps {
  dataSetKey: string
  [x: string]: any;
}

const PluginOpenStaticSet = (props: PsCardOpenStaticProps) => {
  const { onChange, id, config, groupId, dataSetKey } = props;
  const [value, setValue] = useState([] as any);

  useEffect(() => {
    let defaultValue = config[id as keyof typeof config];
    // 兼容老数据
    if (isEmpty(defaultValue?.statisticsItem)) {
      defaultValue.statisticsItem = []
    }
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
  return (
    <GroupStatistics
      weId={`${props.weId || ''}_uujlgx`}
      {...props}
      config={{dataset: currentDataSet}}
      onChange={handleChange}
      value={value}
      statisticsData={value}
      hideWithUnOpen
    />
  );
};
export default withPluginDataSet(PluginOpenStaticSet);
