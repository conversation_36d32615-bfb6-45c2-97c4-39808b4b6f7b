import React, { Component, ComponentType } from 'react';
import { LaneKanbanAppName, PhaseKanbanAppName } from '../constants';

export interface WithPluginDataSetProps {
  config?: any;
  onChange?: (changes: any) => void;
  value?: any;
  props?: {
    [key: string]: any;
  };
}
function withPluginDataSet<P extends WithPluginDataSetProps>(WrappedComponent: ComponentType<P>): any {
  return class WithPluginDataSet extends Component<P> {
    static displayName = `WithPluginDataSetProps`;
    state = {
      dataSetKey: '',
      pluginCompName: '',
    };

    componentDidMount() {
      const type = this.props?.config?.type;
      let dataSetKey = '';
      switch (type) {
        case LaneKanbanAppName:
          dataSetKey = 'DataSet';
          break;
        case PhaseKanbanAppName:
          dataSetKey = '_dataset';
          break;
        default:
          dataSetKey = 'DataSet';
          break;
      }
      this.setState({ dataSetKey, pluginCompName: type });
    }
    render() {
      const { dataSetKey, pluginCompName } = this.state;
      if (!dataSetKey) return null;
      return <WrappedComponent weId={`8khzhe`} {...(this.props as P)} dataSetKey={dataSetKey} pluginCompName={pluginCompName} />;
    }
  };
}
export default withPluginDataSet;
