import { useState, useEffect } from 'react';
import { isEmpty } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../constants';
import { DesignComProps } from '../../baseBoard/types';
import Sort from '../../baseBoard/config/Sort';
import withPluginDataSet from './withDataSet';

export interface PsSortSetProps extends DesignComProps {
  dataSetKey: string
  [x: string]: any;
}

const PluginSortSet = (props: PsSortSetProps) => {
  const { onChange, id, config, groupId, dataSetKey } = props;
  const [value, setValue] = useState(undefined);

  useEffect(() => {
    const defaultValue = config?.[id as keyof typeof config] ?? {};
    setValue(isEmpty(defaultValue) ? undefined : defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };

  const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
  const hasValue = !isEmpty(value)
  return <Sort
    weId={`${props.weId || ''}_unlw4m`}
    {...props}
    config={{ dataset: currentDataSet }}
    onChange={handleChange}
    value={value}
    extCls={hasValue ? `${ebdBClsPrefix}-kanbanPlugin-configItem-hasValue` : ''}
  />;
};
export default withPluginDataSet(PluginSortSet);
