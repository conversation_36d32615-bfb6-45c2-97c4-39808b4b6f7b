import { useEffect, useState } from 'react';
import { AnyObj } from '@weapp/ui';
import { DesignComProps } from '../../baseBoard/types';
import FieldSelect from '../../common/field-select';
import { ebdBClsPrefix } from '../../../constants';
import { EtComponentKey } from '../../../constants/EtComponent';
import withPluginDataSet from './withDataSet';
import { LaneKanbanAppName, LanePluginWrapItemCls } from '../constants';

export interface PsFieldSelectProps extends DesignComProps {
  dataSetKey: string;
  pluginCompName?: string;
  [x: string]: any;
}
const cls = `${ebdBClsPrefix}-kanbanPlugin-fieldCheck`;
const PluginFieldSelectSet = (props: PsFieldSelectProps) => {
  const { onChange, id, config, groupId, dataSetKey, pluginCompName } = props;
  const [value, setValue] = useState('');

  useEffect(() => {
    let defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any) => {
    onChange(changes);
    setValue(changes);
  };
  const renderContent = () => {
    const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
    return (
      <FieldSelect
        weId={`${props.weId || ''}_ibp0a0`}
        dataset={currentDataSet}
        onChange={handleChange}
        value={value}
        filterField={[EtComponentKey.Ebuilder]}
        type="select"
      />
    );
  };
  return (
    <div className={`${cls} ${pluginCompName === LaneKanbanAppName ? LanePluginWrapItemCls : ''}`} style={{ width: '100%' }}>
      {renderContent()}
    </div>
  );
};
export default withPluginDataSet(PluginFieldSelectSet);
