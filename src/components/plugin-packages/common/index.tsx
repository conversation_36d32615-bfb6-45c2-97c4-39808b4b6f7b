import { ComponentType } from 'react';
import Loadable from '../../react-loadable';
import { PsConditionProps } from './conditionSet';
import { PsCardSetProps } from './card';
import { PsSortSetProps } from './sort';
import { PsFieldCheckProps } from './fieldCheck';
import { PsFieldSelectProps } from './fieldSelect';
import { PsCardOpenStaticProps } from './openStatic';
import { PsFilterSetProps } from './filter';
import { ProjectFilterProps } from './projectFilter';
import { PsBtnSetProps } from './buttonSet';
import { PluginPageSizeProps } from './PageSize';
import { PluginPageModeProps } from './PageMode';

const PluginFilter = Loadable({
  name: 'PluginFilter',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_projectFilter" */
      './projectFilter'
    ),
}) as ComponentType<ProjectFilterProps>;

const PluginCondition = Loadable({
  name: 'PluginCondition',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_Type" */
      './conditionSet'
    ),
}) as ComponentType<PsConditionProps>;

const PluginCardSet = Loadable({
  name: 'PluginCardSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_cardSet" */
      './card'
    ),
}) as ComponentType<PsCardSetProps>;

const PluginSortSet = Loadable({
  name: 'PluginSortSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_sortSet" */
      './sort'
    ),
}) as ComponentType<PsSortSetProps>;

const PluginFieldSelectSet = Loadable({
  name: 'PluginFieldSelectSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_fieldSelectSet" */
      './fieldSelect'
    ),
}) as ComponentType<PsFieldSelectProps>;

const PluginFieldCheckSet = Loadable({
  name: 'PluginFieldCheckSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_fieldCheckSet" */
      './fieldCheck'
    ),
}) as ComponentType<PsFieldCheckProps>;

const PluginOpenStaticSet = Loadable({
  name: 'PluginOpenStaticSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_openStatic" */
      './openStatic'
    ),
}) as ComponentType<PsCardOpenStaticProps>;

const PluginFilterSet = Loadable({
  name: 'PluginFilterSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_filter" */
      './filter'
    ),
}) as ComponentType<PsFilterSetProps>;

const PluginBtnSet = Loadable({
  name: 'PluginBtnSet',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_btn" */
      './buttonSet'
    ),
}) as ComponentType<PsBtnSetProps>;

const PluginPageSize = Loadable({
  name: 'PluginPageSize',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_PageSize" */
      './PageSize'
    ),
}) as ComponentType<PluginPageSizeProps>;

const PluginPageMode = Loadable({
  name: 'PluginPageMode',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_pluginComp_PageMode" */
      './PageMode'
    ),
}) as ComponentType<PluginPageModeProps>;

export {
  PluginFilter,
  PluginCondition,
  PluginCardSet,
  PluginSortSet,
  PluginFieldCheckSet,
  PluginFieldSelectSet,
  PluginOpenStaticSet,
  PluginFilterSet,
  PluginBtnSet,
  PluginPageSize,
  PluginPageMode,
};
