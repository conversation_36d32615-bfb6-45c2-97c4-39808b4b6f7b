// 插件包通用背景色配置

import { useEffect, useState } from 'react';
import { CorsComponent } from '@weapp/ui';
import { DesignComProps } from '../../baseBoard/types';
import withPluginDataSet from './withDataSet';
import ButtonSet from '../../baseBoard/config/cmps/btn-set';
import './index.less';

export interface PsBtnSetProps extends DesignComProps {
  [x: string]: any;
}

const PluginBtnSet = (props: PsBtnSetProps) => {
  const { onChange, id, config, groupId, dataSetKey } = props;
  const [value, setValue] = useState({} as any);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
  // return <CorsComponent
  //   app="@weapp/components"
  //   compName='ButtonConfig'
  //   dataset={currentDataSet}
  //   onSure={handleChange}
  //   onSure={handleChange}
  // />;
  return (
    <ButtonSet
      {...props}
      comId={props.comId!}
      pageId={props.pageId!}
      weId={`${props.weId || ''}_pg4l8w`}
      config={{ ...props.config, comButton: [], dataset: currentDataSet }}
      onChange={handleChange}
    />
  );
};
export default withPluginDataSet(PluginBtnSet);
