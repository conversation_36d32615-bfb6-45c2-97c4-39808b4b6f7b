@import (reference) '../../../style/prefix.less';

.@{ebdBClsPrefix}-kanbanPlugin-fieldCheck {
  width: 100%;
  & > .ui-btn.active{
    border-color: var(--btn-default-hover-color);
    color: var(--btn-default-hover-color);
  }
  .ui-icon-xs{
    color: var(--secondary-fc);
    transition: all .3s;
    cursor: pointer;
    &:hover{
      color: var(--primary);
    }
  }
}
.@{ebdBClsPrefix}-kanbanPlugin-configItem-hasValue {
  .ui-btn {
    border-color: var(--btn-default-hover-color);
    color: var(--btn-default-hover-color);
  }
}

.@{ebdBClsPrefix}-kanbanPlugin-filter{
  position: relative;
  &-mask{
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    cursor: not-allowed;
  }
  &.isMobile{
    width: 100%;
    max-width: 100%;
    padding: 0;
    display: flex;
    justify-content: flex-end;
    .ebcoms-mlist-search{
      width: 100%;
    }
    input{
      line-height: 14px;
    }
  }
}