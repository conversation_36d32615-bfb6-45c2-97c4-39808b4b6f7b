import { useEffect, useState } from 'react';
import ConditionSet from '../../baseBoard/config/ConditionSet';
import { DesignComProps } from '../../baseBoard/types';
import withPluginDataSet from './withDataSet';

export interface PsConditionProps extends DesignComProps {
  dataSetKey: string;
  [x: string]: any;
}

const PluginCondition = (props: PsConditionProps) => {
  const { onChange, id, config, groupId, dataSetKey } = props;
  const [value, setValue] = useState('');

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
  return (
    <ConditionSet
      weId={`${props.weId || ''}_v6y11z`}
      {...props}
      config={{ dataset: currentDataSet }}
      onChange={handleChange}
      value={value}
    />
  );
};
export default withPluginDataSet(PluginCondition);
