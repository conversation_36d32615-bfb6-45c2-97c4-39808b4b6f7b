import { CorsComponent, AnyObj } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

export interface ProjectFilterProps {
  onChange?: (type: string) => void;
  config?: any;
  weId?: string;
  page?: AnyObj;
  compId?: string;
  isDesign?: boolean;
  isMobile?: boolean;
  events?: AnyObj;
  comName?: string; // 筛选组件名
  ownDataSetKey: string; // 筛选数据源
  ownSearchConditionKey: string; // 筛选配置
}

const cls = `${ebdBClsPrefix}-kanbanPlugin-filter`;
const PluginFilter = (props: ProjectFilterProps) => {
  const { page, compId, config, isMobile, ownDataSetKey, ownSearchConditionKey, comName } = props;
  const searchCondition = config[ownSearchConditionKey] || {};
  const dataset = config?.[ownDataSetKey] || {};
  if (isEmpty(searchCondition) || isEmpty(dataset)) return null;
  const filterComName = isMobile ? 'MSearchAdvanced' : (comName ? comName : 'SearchCommon')
  return (
    <div className={`${cls} ${isMobile ? 'isMobile' : ''}`}>
      {props.isDesign && <div className={`${cls}-mask`} />}
      <CorsComponent
        weId={`${props.weId || ''}_x0a21w`}
        app="@weapp/ebdcontainercoms"
        compName="SearchConidtions"
        comName={filterComName}
        comProps={{
          ...props,
          config: {
            searchConditions: toJS(searchCondition),
            dataset,
          },
          id: compId,
          page,
          pageId: page?.id,
          events: props?.events,
        }}
        searchConfigKey="searchConditions"
        isPreView={!props.isDesign}
      />
    </div>
  );
};
export default PluginFilter;
