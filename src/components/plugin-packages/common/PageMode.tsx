import { isEmpty } from '@weapp/utils';
import React, { useEffect, useState } from 'react';
import PageMode, { PageModeProps } from '../../baseBoard/config/cmps/PageMode';
import './index.less';

export interface PluginPageModeProps extends PageModeProps {
  [x: string]: any;
}
const PluginPageMode = (props: PluginPageModeProps) => {
  const { onChange, id, config } = props;
  const [value, setValue] = useState(undefined);

  useEffect(() => {
    const defaultValue = config?.[id as keyof typeof config] ?? {};
    setValue(isEmpty(defaultValue) ? undefined : defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  // 进展看板的项目信息设置需要
  const needPageNumTypeIdList = ['projPageType']
  return <PageMode weId={`${props.weId || ''}_bf9gee`} {...props} onChange={handleChange} config={{ pageMode: value }} needPageNumType={needPageNumTypeIdList.includes(id)} />;
};

export default PluginPageMode;
