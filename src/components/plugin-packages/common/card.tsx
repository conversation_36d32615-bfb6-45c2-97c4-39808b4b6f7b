import { useEffect, useState } from 'react';
import { isEmpty } from '@weapp/utils';
import CardSet from '../../baseBoard/config/CardSet';
import { DesignComProps } from '../../baseBoard/types';
import { ebdBClsPrefix } from '../../../constants';
import withPluginDataSet from './withDataSet';
import { LaneKanbanAppName, LanePluginWrapItemCls } from '../constants';
import './index.less';

export interface PsCardSetProps extends DesignComProps {
  dataSetKey: string;
  pluginCompName?: string;
  [x: string]: any;
}

const PluginCardSet = (props: PsCardSetProps) => {
  const { onChange, id, config, groupId, dataSetKey, pluginCompName } = props;
  const [value, setValue] = useState({} as any);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
  const hasValue = !isEmpty(value) && !isEmpty(value?.cardLayout?.grid) && !isEmpty(value?.cardLayout?.grid[0]);
  return (
    <div className={pluginCompName === LaneKanbanAppName ? LanePluginWrapItemCls : ''} style={{ width: '100%' }}>
      <CardSet
        weId={`${props.weId || ''}_uujlgx`}
        {...props}
        config={{ dataset: currentDataSet }}
        onChange={handleChange}
        value={value}
        extCls={hasValue ? `${ebdBClsPrefix}-kanbanPlugin-configItem-hasValue` : ''}
        style={pluginCompName === LaneKanbanAppName ? { width: '100%' } : {}}
      />
    </div>
  );
};
export default withPluginDataSet(PluginCardSet);
