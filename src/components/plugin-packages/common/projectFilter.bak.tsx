// 后面筛选改造按这种来
import { CorsComponent, AnyObj, Title } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

export interface ProjectFilterProps {
  onChange?: (type: string) => void;
  config?: any;
  weId?: string;
  page?: AnyObj;
  compId?: string;
  isDesign?: boolean;
  isMobile?: boolean;
  events?: AnyObj;
  ownDataSetKey: string; // 筛选数据源
  ownSearchConditionKey: string; // 筛选配置
}

const cls = `${ebdBClsPrefix}-kanbanPlugin-filter`;
const PluginFilter = (props: ProjectFilterProps) => {
  const { page, compId, config, isMobile, ownDataSetKey, ownSearchConditionKey } = props;
  let searchCondition = config[ownSearchConditionKey] || {};
  const dataset = config?.[ownDataSetKey] || {};
  if (isEmpty(searchCondition) || isEmpty(dataset)) return null;
  const { commonSearchType, quickFilters } = searchCondition;
  let compName = 'SearchCommon';
  if (!isEmpty(quickFilters)) {
    compName = 'SearchQuick';
  }
  const getSearchPanel = () => {
    const { isDesign } = props;
    return (
      <CorsComponent
        weId={`${props.weId || ''}_n39dnm`}
        app="@weapp/ebdcontainercoms"
        compName="SearchConidtions"
        comName="SearchCommon"
        searchConfigKey="searchConditions"
        comProps={{
          ...props,
          id: compId,
          config: {
            searchConditions: toJS(searchCondition),
            dataset,
          },
        }}
        isPreView={!isDesign}
      />
    );
  };
  const renderGroup = () => {
    const { isDesign } = props;
    return (
      <CorsComponent
        weId={`${props.weId || ''}_sltk99`}
        app="@weapp/ebdcontainercoms"
        compName="SearchConidtions"
        comName="SearchGroup"
        comProps={{
          config: {
            searchConditions: toJS(searchCondition),
            dataset,
          },
          id: compId,
          page,
          pageId: page?.id,
          events: props?.events,
        }}
        type="0"
        searchConfigKey="searchConditions"
        isPreView={!isDesign}
      />
    );
  };
  const renderQuick = () => {
    const { isDesign } = props;

    if (
      searchCondition?.quickFilters?.length > 0 ||
      (searchCondition?.commonSearchType === '0' && searchCondition?.commonFilters?.length > 0)
    ) {
      return (
        <div className={`milstone-search-right ${isDesign ? 'isDesign' : ''}`}>
          <CorsComponent
            weId={`${props.weId || ''}_3dicvm`}
            app="@weapp/ebdcontainercoms"
            compName="SearchConidtions"
            comName="SearchQuick"
            comProps={{
              config: {
                searchConditions: toJS(searchCondition),
                dataset,
              },
              id: compId,
              page,
              pageId: page?.id,
              events: props?.events,
            }}
            searchConfigKey="searchConditions"
            isPreView={!isDesign}
          />
        </div>
      );
    }

    return <></>;
  };
  return (
    <div className={cls}>
      {props.isDesign && <div className={`${cls}-mask`} />}
      {renderGroup()}
      {renderQuick()}
      {getSearchPanel()}
      {/* <CorsComponent
        weId={`${props.weId || ''}_x0a21w`}
        app="@weapp/ebdcontainercoms"
        compName="SearchConidtions"
        comName={isMobile ? 'MSearchCondition' : compName}
        comProps={{
          config: {
            searchConditions: toJS(searchCondition),
            dataset,
          },
          id: compId,
          page,
          pageId: page?.id,
          events: props?.events,
        }}
        searchConfigKey="searchConditions"
        isPreView={!props.isDesign}
      />
      {commonSearchType === '0' && !isMobile ? (
        <CorsComponent
          weId={`${props.weId || ''}_x0a21w`}
          app="@weapp/ebdcontainercoms"
          compName="SearchConidtions"
          comName={'SearchCommon'}
          comProps={{
            ...props,
            config: {
              searchConditions: toJS(searchCondition),
              dataset,
            },
            id: compId,
            page,
            pageId: page?.id,
            events: props?.events,
          }}
          searchConfigKey="searchConditions"
          isPreView={!props.isDesign}
        />
      ) : null} */}
    </div>
  );
};
export default PluginFilter;
