import { useEffect, useState } from 'react';
import { CorsComponent } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { DesignComProps } from '../../baseBoard/types';
import withPluginDataSet from './withDataSet';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

export interface PsFilterSetProps extends DesignComProps {
  [x: string]: any;
}

const PluginFilterSet = (props: PsFilterSetProps) => {
  const { onChange, id, config, groupId, dataSetKey, showMenu = ['commonFilters', 'quickFilters'], hideCommonSearchType = [''] } = props;
  const [value, setValue] = useState({} as any);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
  const hasValue = !isEmpty(value);
  return (
    <div className={`${hasValue ? `${ebdBClsPrefix}-kanbanPlugin-configItem-hasValue` : ''}`}>
      <CorsComponent
        weId={`${props.weId || ''}_pg4l8w`}
        app="@weapp/ebdcontainercoms"
        compName="SearchConditionConfig"
        {...props}
        dataProps={{
          commonFilters: value?.commonFilters || [],
          quickFilters: value?.quickFilters || [],
          groupFilters: value?.groupFilters || [],
          showMenu, // 左侧展示的菜单类别，这里不需要分类搜索
          hideCommonSearchType, // 常用搜索隐藏的类型  ['0']  01
          commonSearchType: hideCommonSearchType ? '1' : '0', // 常用搜索的类型，这里默认选中筛选0 1
          filterHidePosition: false, // 常用搜索类型，筛选隐藏显示位置的配置，默认仅有下拉
          needId: true, //是否需要内部生成id
        }}
        value={value}
        config={{ ...props?.config, dataset: currentDataSet }}
        onChange={handleChange}
      />
    </div>
  );
};
export default withPluginDataSet(PluginFilterSet);
