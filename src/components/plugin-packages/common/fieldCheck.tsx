import { useEffect, useState, useMemo } from 'react';
import { Button, Icon } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { DesignComProps } from '../../baseBoard/types';
import FieldSelect from '../../common/field-select';
import { ebdBClsPrefix } from '../../../constants';
import withPluginDataSet from './withDataSet';
import { LaneKanbanAppName, LanePluginWrapItemCls } from '../constants';

export interface PsFieldCheckProps extends DesignComProps {
  dataSetKey: string;
  pluginCompName?: string;
  [x: string]: any;
}

const cls = `${ebdBClsPrefix}-kanbanPlugin-fieldCheck`;
const PluginFieldCheckSet = (props: PsFieldCheckProps) => {
  const { onChange, id, config, groupId, dataSetKey, pluginCompName } = props;

  const [value, setValue] = useState('');
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  const renderContent = () => {
    const currentDataSet = config[`${groupId}${dataSetKey}` as keyof typeof config];
    if (isEmpty(currentDataSet)) {
      return;
    }
    return (
      <div>
        <FieldSelect
          weId={`${props.weId || ''}_o3dxb5`}
          {...props}
          dataset={currentDataSet}
          onChange={handleChange}
          value={value}
          onClose={() => setVisible(false)}
          type="dialog"
        />
      </div>
    );
  };
  const btnComp = useMemo(() => {
    if (pluginCompName === LaneKanbanAppName)
      return (
        <Icon
          weId={`${props.weId || ''}_x4q563`}
          name={isEmpty(value) ? 'Icon-set-up-o' : 'Icon-set-up01'}
          onClick={() => setVisible(true)}
        />
      );
    return (
      <Button weId={`${props.weId || ''}_owbleg`} size="small" onClick={() => setVisible(true)} className={!isEmpty(value) ? 'active' : ''}>
        {getLabel('54205', '设置')}
      </Button>
    );
  }, [pluginCompName, props?.weId, value]);
  return (
    <div className={`${cls} ${pluginCompName === LaneKanbanAppName ? LanePluginWrapItemCls : ''}`} style={{ width: '100%' }}>
      {btnComp}
      {visible && renderContent()}
    </div>
  );
};
export default withPluginDataSet(PluginFieldCheckSet);
