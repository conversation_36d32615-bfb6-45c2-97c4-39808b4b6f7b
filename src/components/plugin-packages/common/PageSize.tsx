import { isEmpty } from '@weapp/utils';
import React, { useEffect, useState } from 'react';
import PageSize, { PageSizeProps } from '../../baseBoard/config/cmps/PageSize';
import './index.less';

export interface PluginPageSizeProps extends PageSizeProps {
  [x: string]: any;
}
const PluginPageSize = (props: PluginPageSizeProps) => {
  const { onChange, id, config } = props;
  const [value, setValue] = useState(undefined);
  const [isInit, setIsInit] = useState(false);

  useEffect(() => {
    setIsInit(false)
    const defaultValue = config?.[id as keyof typeof config] ?? {};
    setValue(isEmpty(defaultValue) ? undefined : defaultValue);
    setIsInit(true)
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    onChange(changes);
    setValue(changes);
  };
  if (!isInit) return null;
  return <PageSize weId={`${props.weId || ''}_bf9gee`} {...props} onChange={handleChange} config={{ pageSize: value }} />;
};

export default PluginPageSize;
