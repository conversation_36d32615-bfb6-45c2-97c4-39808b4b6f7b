import React from 'react';
import { toJS } from 'mobx';
import { getLabel, isEqual, throttle } from '@weapp/utils';
import { Icon } from '@weapp/ui';
import Store from '../common/store';
import { IPlugin } from '../../types';
import { ebdBClsPrefix } from '../../../../constants';
import BoardEmpty from '../../../common/empty';
import { GroupItem } from '../../../common/board/types';
import PhaseStep from '../../../common/high-light/phaseStep';
import { EbdBoardEventName } from '../../../../types/common';
import LaneFilter from '../common/comps/viewFilter';
import './index.less';

const cls = `${ebdBClsPrefix}-projectLane-view`;
export class ProjectLaneViewPlugin implements IPlugin {
  name = 'View-ProjectLane';

  pluginCenter!: any;
  _props!: any;
  isMobile: boolean;
  store: Store; // 插件的store
  isExpand = true; // 默认展开
  filters = []; // 筛选条件

  constructor(opts: any) {
    this.store = new Store({
      props: opts.com?.props as any,
      pluginCenter: opts.pluginCenter,
      isDesign: opts.com?.props?.isDesign,
    });
    this._props = opts.com?.props;
    this.isMobile = opts.com?.props?.page?.client === 'MOBILE';
    this.addExpandEvent();
    this.addScrollBottomEvent();
    this.handleExpand = this.handleExpand.bind(this); // 确保绑定正确的 this 上下文
  }
  // 指定为二维分组 - 调用底层二维能力
  getIsLaneBoard = () => [true];
  getPageSize = () => {
    const { progressPage = {} } = this.store?.config;
    if (progressPage.customPage) {
      return [progressPage.customPageNum];
    }
    return [progressPage.pageNum];
  };
  getPageMode = () => {
    const { progressPageType } = this.store?.config;
    return [progressPageType];
  };

  // 复写泳道标题底层卡片配置
  getLaneTitleConfig = () => {
    const { projDataSet, projShowField } = this.store.config;
    const cardConfig = {
      dataset: toJS(projDataSet),
      field: toJS(projShowField),
    };
    return [cardConfig];
  };
  getIsLaneLoading = () => {
    return [toJS(this.store.laneLoading)];
  };
  getCustomLaneTitle = () => {
    return [getLabel('54690', '项目名称'), getLabel('291673', '阶段信息')];
  };
  addExpandEvent = () => {
    this._props?.events?.on('onChangeLaneBoardCollapsed', this._props.id, this.changeExpand);
  };
  addScrollBottomEvent = () => {
    this._props?.events?.on('onLaneBoardPageChange', this._props.id, this.onChangeLanePage);
  };
  changeExpand = (value: boolean) => {
    this.store.setState({ isExpand: value });
  };
  handleExpand = () => {
    const { isExpand, setState, baseStore } = this.store;
    this._props?.events?.emit?.(EbdBoardEventName.onLaneBoardCollapsed, this._props.id, isExpand ? 'collapsed' : 'expand', baseStore?.laneGroups);
    setState({ isExpand: !isExpand });
  };
  // 触发更新时重新获取
  onCustomDidMount = () => {};
  // 自定义刷新
  onCustomReload = (params: any) => {};
  onDestroy = () => {
    this._props?.events?.off('onChangeLaneBoardCollapsed', this._props.id, this.changeExpand);
    this._props?.events?.off('onLaneBoardPageChange', this._props.id, this.onChangeLanePage);
  };
  // 获取单个看板数据
  getBoardData = (params: any) => {
    this.store.fetchData(params?.extraParams?.laneGroupId, params?.item?.id);
  };
  // 卡片不允许拖拽
  getCardDraggable = (config: any) => {
    return [false];
  };
  getGroupMenus = (groupId: string, loadMore: false) => {};
  // 自定义新建传参
  getBoardAddParams = (params: any, exParams: any) => {
    const { baseStore, config } = this.store;
    const { groupEditingData } = baseStore;
    let newExParams = [
      {
        name: `field_${config.progressStageField}`,
        type: 'fixed',
        value: groupEditingData.id,
      },
    ];
    if (config.progressProjField) {
      newExParams.push({
        name: `field_${config.progressProjField}`,
        type: 'fixed',
        value: groupEditingData.laneId,
      });
    }
    const newParams = {
      ...params,
      // 这里的objid和appid不能用数据源默认的 要用进展看板设置所用的
      appId: config.progressDataSet?.appId,
      objId: config.progressDataSet?.id,
    };
    return [newParams, newExParams];
  };

  onBeforeMount = (baseStore: any) => {
    if (this.isMobile) return;
    this.store.setBaseStore(baseStore);
  };
  onPageChange = (pageInfo: any) => {
    // this.store.syncPageInfo(pageInfo);
  };
  onChangeLanePage = (params: { type: string; pageNo: string }) => {
    this.store.syncLanePageInfo(params.pageNo, params.type);
  };
  onFilter = (props: any) => {
    const { events, compId } = props;
    const { setState, handleRefreshFilter, setPageFilter, isDesign } = this.store;
    if (!events || isDesign) return;
    setState({ events });
    const handleFilter = (filters: any) => {
      // 全部重请求
      if (isEqual(this.filters, filters)) return;
      this.filters = filters;
      setPageFilter(filters);
    };
    const throttleFilter = throttle(handleFilter, 3000);
    events.on('filter', compId, (filters: any, typeKey: string, needGetDate: boolean) => {
      // if (typeKey.indexOf('quick_EXCLUDECACHE') > -1) {
      //   // 快捷搜索的 不清除类型 只刷新数据
      //   return handleRefreshFilter(filters);
      // }
      throttleFilter(filters);
    });
  };
  renderContentTop = () => {
    const { config, baseStore, isDesign, events, isExpand, isEmptyProjConfig } = this.store;
    if (this.isMobile || isEmptyProjConfig) return null;
    const filterCmp = () => {
      if (!Object.keys(config.projSearchFilter || {}).length) return null;
      return (
        <>
          <LaneFilter
            {...this._props}
            weId={`cn15gd`}
            config={config}
            compId={baseStore?.compId}
            isDesign={isDesign}
            events={events}
            page={this._props?.page}
            isMobile={this.isMobile}
            ownDataSetKey="projDataSet"
            ownSearchConditionKey="projSearchFilter"
          />
        </>
      );
    };
    return (
      <div className={`${cls}-top`}>
        <div className={`${cls}-top-left`}>{filterCmp()}</div>
        <div className={`${cls}-top-right`} onClick={this.handleExpand}>
          {isExpand ? <Icon weId={`kf5wfd`} name="Icon-collapse-menu" /> : <Icon weId={`pgbsos`} name="Icon-One-button-Stow" />}
          {isExpand ? getLabel('291701', '一键收起') : getLabel('291702', '一键展开')}
        </div>
      </div>
    );
  };
  onCustomUpdate = (props: any) => {};
  onCustomGetExtraCardInfo = () => {};
  onLoadMore = async (laneId: string, groupId: string, func?: Function) => {
    await this.store.fetchData(laneId, groupId, true, () => {
      func && func();
    });
  };
  renderMain = (hook: any) => {
    const { isEmptyProjConfig } = this.store;
    return (_props: any) => {
      if (this.isMobile) {
        return <div className={`${cls}-m`}>{getLabel('291703', '进展看板仅支持PC端使用')}</div>;
      }
      return (
        <div style={{ width: '100%', overflowY: 'scroll', flex: '1' }} className={`${cls}`}>
          {isEmptyProjConfig ? <BoardEmpty weId={`${_props.weId || ''}_txw3zt`} iconSize={{ width: 130, height: 130 }} /> : hook(_props)}
        </div>
      );
    };
  };
  renderCardContent = (hook: any) => {
    const { progressDataSet, progressShowField } = this.store.config;
    return (...args: any) => {
      const newConfig = {
        card: toJS(progressShowField),
        dataset: toJS(progressDataSet),
      };
      return hook(newConfig, args[1], args[2]);
    };
  };
  renderLaneBoardTitle = (hook: any) => {
    return (groupItem: GroupItem, index: number, groups: GroupItem[]) => {
      return (
        <div className={`${cls}-phaseStep ${groupItem.color ? 'hasBg' : ''}`}>
          <PhaseStep weId={`fmflgm`} pluginCenter={this.pluginCenter} item={groupItem} hideFirstArrow hideLastArrow index={index} groups={groups} />
        </div>
      );
    };
  };
  getCardAddVisible = (item: any) => {
    const { isEmptyProgressConfig, config } = this.store;
    const { progressDataSet } = config;
    // 非表单数据源暂时不放开新建
    if (progressDataSet.type !== 'FORM') {
      return null;
    }
    return [!isEmptyProgressConfig];
  };
  renderCardAddBtn = (hook: any) => {
    const { config, isEmptyProgressConfig } = this.store;
    const { progressButtons } = config;
    // 目前仅有一个按钮配置 且只有新建 默认取第0个
    const validBtnConfig = progressButtons[0];
    return (item: any, addBtnName: string, clickFunc: any) => {
      if (!validBtnConfig || !validBtnConfig.enable || isEmptyProgressConfig) return null;
      const _clickFunc = () => {
        sessionStorage.setItem('isEbuilderGroupAdd', '1');
        clickFunc();
      };
      return hook(`+ ${validBtnConfig.name}`, _clickFunc);
    };
  };
}

const View = [ProjectLaneViewPlugin];

export default View;
