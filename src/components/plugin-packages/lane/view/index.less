@import (reference) '../../../../style/prefix.less';

.@{mBoardViewClsPrefix} {
  .@{ebdBClsPrefix}-projectLane-view {
    &-top{
      flex-wrap: wrap;
      flex-direction: column-reverse;
      .@{ebdBClsPrefix}-projectLane-projectFilter{
        width: 100%;
        padding: 0;
        max-width: initial;
        .ebcoms-nlist-search-quick{
          width: 100%;
        }
        .ebcoms-mlist-search{
          border-bottom: none;
          width: 100%;
          .ui-m-searchAdvanced{
            padding: 0;
          }
        }
      }
    }
  }
}
.@{ebdBClsPrefix}-projectLane-view {
  // 第一行第一个字段不换行 后续默认换行
  .@{ebdBClsPrefix}-list-body .ui-list-body{
    .ebcoms-list-flex.ebcoms-list-grid-row:first-child > .ebcoms-list-flex.ebcoms-list-grid-row-main {
      flex-wrap: initial;
      align-items: flex-start;
      & > .ebcoms-list-render-cell:first-child{
        position: relative;
        .show-data.edc-trans-data-autowhmin{
          padding: 0;
        }
      }
    }
    .ebcoms-list-flex.ebcoms-list-grid-row:not(:first-child) > .ebcoms-list-flex.ebcoms-list-grid-row-main {
      color: var(--secondary-fc);
    }
  }
  &-top{
    display: flex;
    align-items: center;
    min-height: 40px;
    padding: 5px 0 0;
    &-left{
      flex: 1;
      padding: 0 16px 0 0;
    }
    &-right{
      font-size: var(--font-size-12);
      .flexCt;
      cursor: pointer;
      transition: all .3s;
      justify-content: flex-start;
      padding: 5px 10px;
      .ui-icon-wrapper{
        margin-right: 5px;
        line-height: 1;
      }
      &:hover{
        color: var(--primary);
        background: var(--bubble-color);
      }
    }
    .@{ebdBClsPrefix}-projectLane-projectFilter{
      max-width: 180px;
      flex-shrink: 0;
      display: flex;
      padding: 10px 16px 0 0;
    }
  }
  &-phaseStep{
    width: 100%;
    .@{ebdBClsPrefix}-phaseStep-text{
      padding: 0 16px;
    }
    &.hasBg{
      .@{ebdBClsPrefix}-phaseStep-text{
        padding: 0 32px;
      }
    }
  }
  &-bottom{
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &-more{
    .flexCt;
    padding: 10px;
    color: var(--secondary-fc);
  }
  &-m{
    height: 100%;
    .flexCt;
    font-size: var(--font-size-12);
    color: var(--secondary-fc);
  }
}