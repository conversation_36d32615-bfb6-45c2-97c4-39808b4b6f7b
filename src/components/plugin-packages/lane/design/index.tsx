import { DesignOptions } from '@weapp/ebdcoms';
import { eventEmitter, request } from '@weapp/utils';
import { defaultConfig, defaultOpts } from './constants';
import {ProjectLaneViewPlugin} from '../view';

export class ProjectLaneDesignPlugin extends ProjectLaneViewPlugin {
  static defaultProps: any = {
    config: { ...defaultConfig() },
    isDesign: true,
  };

  static defaultOpts: Partial<DesignOptions> = {
    ...defaultOpts,
    afterAddNewCom: (newCom: any, comDesignStore: any) => {
      if (window?.TEAMS?.licenseModules?.includes('project-sjr')) {
        request({
          url: '/api/ebproject/project/config/projectLaneKanban',
          method: 'GET',
        }).then((res) => {
          const { layoutStore } = comDesignStore || {}
          if (res.code === 200) {
            let defaultConfig = (JSON.parse(res.data || '{}'))
            layoutStore.updateComConfig(defaultConfig)
          }
        })
      }
    }
  };
  compId!: string;
  name = `Design-ProjectLane`;

  constructor(opts: any) {
    super(opts);
    this.compId = opts.com.props.id;
    this.store.setState({isDesign: true});
    eventEmitter.on('@weapp/designer', `propchange.${this.compId}`, this.handlePropChange);
    eventEmitter.on('@weapp/designer', `configchange.${this.compId}`, this.handleConfigChange);
  }
  componentWillUnmount() {
    eventEmitter.off('@weapp/designer', `propchange.${this.compId}`, this.handlePropChange);
    eventEmitter.off('@weapp/designer', `configchange.${this.compId}`, this.handleConfigChange);
  }
  /**
   * 只有通过wrapItem包裹才可以使用的钩子
   * 通过onConfigChange更新的属性监听
   * @param changes
   */
  handleConfigChange = (changes: any) => {
    this.store.changeConfig(changes);
  };
  handlePropChange = (key: string, val: any) => {
    this.store.changeSingleConfig({[key]: val});
  };
}
const Design = [ProjectLaneDesignPlugin];

export default Design;
