// 阶段看板插件包默认配置
import { getLabel } from '@weapp/utils';
import { LaneKanbanAppName } from '../../constants';

export const MOCK_DATA_SET = () => ({
  id: '',
  groupId: '',
  text: '',
  type: 'FORM',
});
export const INITIAL_CONFIG = (prefix: string) => ({
  [`${prefix}Filter`]: {},
  [`${prefix}Orders`]: [],
  [`${prefix}DataSet`]: MOCK_DATA_SET(),
});
export const INITIAL_PAGE_CONFIG = (prefix: string) => ({
  [`${prefix}PageType`]: '',
  [`${prefix}Page`]: {
    customPage: false,
    customPageNum: '',
    pageNum: '20',
  },
});
export const INITIAL_PROJ_CONFIG = () => ({
  ...INITIAL_CONFIG('proj'),
  ...INITIAL_PAGE_CONFIG('proj'),
  projShowField: {},
  projSortField: {},
  projSearchFilter: {},
});
export const INITIAL_STAGE_CONFIG = () => ({
  ...INITIAL_CONFIG('stage'),
  stageProjField: '',
  stageShowField: '',
  stageBackGround: [],
});
export const INITIAL_PROGRESS_CONFIG = () => ({
  ...INITIAL_CONFIG('progress'),
  ...INITIAL_PAGE_CONFIG('progress'),
  progressProjField: '',
  progressStageField: '',
  progressShowField: {},
  progressButtons: [
    {
      id: 'add',
      name: getLabel('54007', '新建'),
      enable: true,
    }
  ],
  progressStats: {
    openValue: false,
    statisticsItem: []
  },
});
export const defaultConfig: any = () => ({
  title: getLabel('290536', '项目进展看板'),
  type: LaneKanbanAppName,
  titleEnabled: true,
  footerEnabled: false,
  ...INITIAL_PROJ_CONFIG(),
  ...INITIAL_STAGE_CONFIG(),
  ...INITIAL_PROGRESS_CONFIG(),
});

export const defaultOpts = {
  mask: false,
};
