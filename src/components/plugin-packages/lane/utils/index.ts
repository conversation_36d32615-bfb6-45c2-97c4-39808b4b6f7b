/** 根据给定的排序，重排对象内属性 */
export const resortPropByArray = (object: any, ...sortArrays: string[][]) => {
    const resortObject: any = {};
    const _sortArrays = sortArrays.map((sortArray) => sortArray.slice(0));
    const waiteItems: any = {};
    const keys = Object.keys(object);
    const getSortArray = (key: string) => _sortArrays.find((sortArray) => sortArray.includes(key));
    const addFirstKeyOfArray = (key: string, _sortArray: string[], isLoop?: boolean) => {
      const sortIndex = isLoop ? 0 : _sortArray?.indexOf(key);
      const loopAdd = () => {
        _sortArray.shift();
        if (_sortArray.length) {
          addFirstKeyOfArray(_sortArray[0], _sortArray, true);
        }
      };
      if (sortIndex === 0 && !isLoop) {
        resortObject[key] = object[key];
        loopAdd();
      } else if (isLoop) {
        if (waiteItems[key]) {
          resortObject[key] = waiteItems[key];
          delete waiteItems[key];
          loopAdd();
        }
      } else {
        waiteItems[key] = object[key];
      }
    };
    keys.forEach((key) => {
      const _sortArray = getSortArray(key);
      if (_sortArray) {
        addFirstKeyOfArray(key, _sortArray);
      } else {
        resortObject[key] = object[key];
      }
    });
    return resortObject;
  };