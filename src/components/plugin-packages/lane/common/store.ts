import { isEmpty, forIn, isEqual } from '@weapp/utils';
import { AnyObj } from '@weapp/ui';
import axios from 'axios';
import { toJS, observable, computed, runInAction, action } from 'mobx';
import { ajax, format2FormData } from '../../../../utils/ajax';
import { defaultConfig } from '../design/constants';
import { BoardViewStore } from '../../../common/board/store';
import { EbdBoardEventName } from '../../../../types/common';
import { PageModeType, PageSize } from '../../../baseBoard/config/cmps/types';
import { LaneStatType } from '../../../baseBoard/config/cmps/yd/types';
import { INIT_PAGE_SIZE_CONFIG, INIT_LANE_PAGE } from '../../../baseBoard/config/cmps/constants';

type StoreOptsType = {
  props: any;
  pluginCenter: any;
  isDesign?: boolean;
};
const baseUrl = '/api/ebuilder/coms/kanban/progress';
// 进展看板store
class ProjectLaneStore {
  pluginCenter: any; // 插件中心
  isDesign = false; // 是否为设计器视图
  events = {} as any; // 事件集合

  @observable baseStore = {} as BoardViewStore;
  @observable config = defaultConfig();
  // 展开状态
  @observable isExpand = true;
  // 泳道加载状态
  @observable laneLoading = false;
  // 阶段状态值
  @observable boardLoading = false;
  // 筛选值
  @observable filterValues: any = {};
  // 组件刷新参数
  @observable pageFilter: any = [];
  // 看板数据-分页信息
  @observable pageInfo: any = {};
  @observable cancelToken: any = {};

  @computed get baseParams() {
    const params = {
      compId: this.baseStore.compId,
      pageId: this.baseStore.pageId,
      ...this.baseStore.searchParams,
    };
    if (this.isDesign) {
      return {
        ...params,
        config: this.config,
      };
    }
    return params;
  }
  @computed
  get isEmptyProjConfig() {
    return !this.config.projDataSet?.id || isEmpty(this.config.projShowField);
  }
  @computed
  get isEmptyStageConfig() {
    return (
      this.isEmptyProjConfig || !this.config.stageDataSet?.id || isEmpty(this.config.stageShowField) || isEmpty(this.config.stageProjField)
    );
  }
  @computed
  get isEmptyProgressConfig() {
    return (
      this.isEmptyStageConfig ||
      !this.config.progressDataSet?.id ||
      isEmpty(this.config.progressStageField) ||
      isEmpty(this.config.progressShowField)
    );
  }
  constructor(opts: StoreOptsType) {
    this.pluginCenter = opts.pluginCenter;
    this.isDesign = !!opts.isDesign;
    this.events = opts.props.events;
  }
  getExtPrams = (dataset: any, extParams: any = {}) => {
    if (dataset?.ebuilderDataConfig?.dataSetup?.dataPermission === 'ALL') {
      return { ...extParams, isNoPermissionList: '1' };
    }
    return extParams;
  };
  onReload = () => {
    this.baseStore.resetViewportLoading();
    this.events.emit(EbdBoardEventName.onReload, this.baseParams.compId);
  };
  onRightReload = (key?: string, loadData?: boolean) => {
    this.events.emit(EbdBoardEventName.onLaneRightBoardReload, this.baseParams.compId, key);
    if (loadData) {
      this.baseStore.resetViewportLoading();
    }
  };
  getDefaultPageNum = (pageSize?: PageSize) => {
    if (!pageSize) return 0;
    if (pageSize.customPage) {
      return pageSize.customPageNum;
    }
    return pageSize.pageNum;
  };
  @action setBaseStore = (store: any) => {
    runInAction(() => {
      this.baseStore = store;
      for (let i in this.config) {
        if (store.config.hasOwnProperty(i)) {
          this.config[i] = store.config[i];
        }
      }
      // 同步一次分页默认
      const lanePage = { pageType: this.config.projPageType, pageSize: this.config.projPage || INIT_PAGE_SIZE_CONFIG() };
      this.baseStore.setLanePageState({ ...lanePage, pageNum: String(this.getDefaultPageNum(lanePage.pageSize)) });
      this.fetchProj();
    });
  };
  @action setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };
  // 获取泳道信息
  fetchProj = async (type?: string) => {
    if (this.isEmptyProjConfig || this.laneLoading) return;
    try {
      if (this.cancelToken?.compId === this.baseStore.compId) {
        this.cancelToken?.func?.cancel('Request canceled');
      }
      this.cancelToken = {
        func: axios.CancelToken.source(),
        compId: this.baseStore.compId,
      };
      if (type !== PageModeType.Scroll && type !== PageModeType.More) {
        this.laneLoading = true;
      }
      const suffix = this.isDesign ? 'previewLaneData' : 'getLaneData';
      const response = await ajax({
        url: `${baseUrl}/${suffix}`,
        method: 'POST',
        data: format2FormData({
          ...this.baseParams,
          extParam: {
            filter: this.pageFilter,
            searchConditions_new: this.filterValues,
            pageNo: this.baseStore.lanePage.pageNo,
            pageNum: this.baseStore.lanePage.pageNum,
          },
        }),
        cancelToken: this.cancelToken?.func?.token,
        error: (info: any) => { },
      });
      runInAction(() => {
        const newLaneGroups = response.list.map((item: any) => {
          return {
            ...item,
            groups: [],
          };
        });
        let handleLaneGroups = toJS(this.baseStore.laneGroups);
        if (this.baseStore.lanePage.moreLoading) {
          if (newLaneGroups.length) {
            handleLaneGroups = handleLaneGroups.concat(newLaneGroups);
          }
        } else {
          handleLaneGroups = newLaneGroups;
          this.onReload();
        }
        this.baseStore.setState({ laneGroups: handleLaneGroups });
        // 无分组 清空阶段和所有看板数据
        // 获取每个泳道下的分组
        if (!this.isEmptyStageConfig) {
          this.fetchStage(true, true);
        } else {
          this.baseStore.setState({ laneGroupsLoading: false });
        }
        // 分页
        this.baseStore.setLanePageState({
          total: response?.count,
          moreLoading: false,
          hasMore: response?.count > (+this.baseStore.lanePage.pageNum)! && response.list.length,
        });
      });
    } catch (err) {
    } finally {
      runInAction(() => {
        this.laneLoading = false;
      });
    }
  };
  // 获取统计
  controlFetchStat = async (laneGroups = this.baseStore.laneGroups) => {
    let statPromise: Promise<any>[] = []; // 统计相关
    laneGroups.forEach(async (i: any, index: number) => {
      // 目前所有的分组都一样 未来可以扩展传入泳道id
      const stageIds = i.groups.map((i: any) => i.id).join(',');
      if (this.config.progressStats?.openValue) {
        // 判断有统计配置
        if (stageIds && !isEmpty(this.config.progressStats?.statisticsItem)) {
          statPromise.push(this.fetchStat(i.id, stageIds));
        }
      } else {
        this.baseStore.setState({ laneStats: [] });
      }
    });
    // 获取统计数据
    Promise.allSettled(statPromise);
  }
  // 获取分组
  fetchStage = async (needData?: boolean, needStat?: boolean, cb?: Function) => {
    // 联动条件满足 === 所属项目有值
    if (this.isEmptyStageConfig || isEmpty(this.baseStore.laneGroups)) {
      if (needData) {
        this.onRightReload();
      }
      return;
    }
    this.baseStore.setState({ laneGroupLoading: true });
    try {
      this.baseStore.setState({ laneBoardPageInfo: {} });
      const suffix = this.isDesign ? 'previewStages' : 'getStages';
      const response = await ajax({
        url: `${baseUrl}/${suffix}`,
        method: 'POST',
        data: format2FormData({
          ...this.baseParams,
          extParam: {
            projIds: this.baseStore.laneGroups.map((i: any) => i.id).join(','),
          },
        }),
      });
      runInAction(() => {
        let laneGroups = toJS(this.baseStore.laneGroups);
        if (isEmpty(response.data)) {
          return;
        }
        laneGroups.forEach(async (i: any, index: number) => {
          // 目前所有的分组都一样 未来可以扩展传入泳道id
          const singleLaneGroup = response.data[i.id];
          if (!singleLaneGroup) return;
          const idx = laneGroups.findIndex((item: any) => item.id === i.id);
          laneGroups[idx].groups = singleLaneGroup;
        });
        this.baseStore.setState({ laneGroups });
        // 获取统计数据
        needStat && this.controlFetchStat(laneGroups)
      });
    } catch (err) {
    } finally {
      runInAction(() => {
        this.onRightReload('boardHeader');
        this.baseStore.setState({ laneGroupsLoading: false });
        if (needData) {
          this.onRightReload('', true);
        }
        cb && cb();
      });
    }
  };
  // 获取数据
  fetchData = async (projId: string, stageId: string, loadMore?: boolean, cb?: Function) => {
    if (this.isEmptyProgressConfig || !projId || !stageId) {
      return;
    }
    const laneGroups = toJS(this.baseStore.laneGroups);
    const laneIdx = laneGroups.findIndex(i => i.id === projId);
    if (laneIdx < 0 || isEmpty(laneGroups[laneIdx].groups)) {
      return;
    }
    // 所属项目或所属阶段
    const progressProjField = this.config.progressProjField;
    const progressStageField = this.config.progressStageField;
    if (!(progressProjField || progressStageField)) return;
    const { current = 1, hasMore, isLoading } = this.baseStore.laneBoardPageInfo[`${projId}_${stageId}`] || {};
    if ((loadMore && !hasMore) || isLoading) return;
    const fetcher = () => {
      const suffix = this.isDesign ? 'previewData' : 'getData';
      return ajax({
        url: `${baseUrl}/${suffix}`,
        method: 'POST',
        data: format2FormData({
          extParam: {
            projId,
            stageId,
            pageNo: loadMore ? current + 1 : 1,
          },
          ...this.baseParams,
        }),
      });
    };
    try {
      this.baseStore.dealWithFetchData(
        stageId,
        loadMore,
        fetcher,
        projId,
        (response: any) => {
          cb && cb(response);
        },
      );
    } catch (error) {}
  };
  // 获取统计配置
  fetchStat = async (projId: string, stageId: string) => {
    try {
      const suffix = this.isDesign ? 'previewLaneStat' : 'getLaneStat';
      const response = await ajax({
        url: `${baseUrl}/${suffix}`,
        method: 'POST',
        data: format2FormData({
          ...this.baseParams,
          extParam: {
            projId,
            stageId,
          },
        }),
      });
      runInAction(() => {
        const laneStats = toJS(this.baseStore.laneStats);
        const stat: LaneStatType = {
          laneId: projId,
          stageId,
          stats: response,
        };
        const idx = laneStats.findIndex(i => i.laneId === projId);
        if (idx > -1) {
          laneStats[idx] = stat;
        } else {
          laneStats.push(stat);
        }
        if (!isEqual(laneStats, toJS(this.baseStore.laneStats))) {
          this.baseStore.setState({ laneStats });
          this.onRightReload('left');
        }
      });
    } catch (err) {
    } finally {
    }
  };
  // 项目刷新
  reloadFetchProj = (type?: string) => {
    // 向下滚动不清空数据
    if (type !== PageModeType.Scroll && type !== PageModeType.More) {
      this.baseStore.setState({ laneGroups: [], laneStats: [], laneLoading: true });
      this.onReload();
    }
    this.fetchProj(type);
  };
  /**
   * 阶段刷新
   * @param needData 需要请求数据
   * @param needStat 需求请求阶段
   */
  reloadFetchStage = (needData?: boolean, needStat?: boolean) => {
    const laneGroups = toJS(this.baseStore.laneGroups);
    if (isEmpty(laneGroups)) return;
    const newLaneGroups = laneGroups.map(i => {
      return { ...i, groups: [] };
    });
    this.baseStore.setState({ laneGroups: newLaneGroups });
    this.fetchStage(needData, needStat, () => {
      if (needData) {
        this.baseStore.resetViewportLoading();
      }
    });
  };
  // 阶段下看板数据刷新
  // 指定laneId则刷新指定泳道，否则刷新全部
  reloadFetchData = (laneId?: string) => {
    const laneGroups = toJS(this.baseStore.laneGroups);
    if (isEmpty(laneGroups)) return;
    if (laneId) {
      // todo 刷新指定泳道
    } else {
      const newLaneGroups = laneGroups.map(i => {
        const newGroups = i.groups?.map(k => {
          return { ...k, cards: [] };
        });
        return { ...i, groups: newGroups };
      });
      this.baseStore.setState({ laneGroups: newLaneGroups, laneStats: [] });
      if (!this.isEmptyProgressConfig) {
        // 需要重新获取数据
        this.onRightReload('', true);
        // 再更新一下统计
        this.controlFetchStat()
      }
    }
  };
  @action resetLane = () => {
    this.resetStages()
    this.config = defaultConfig();
    this.baseStore = {} as BoardViewStore;
  }
  @action resetStages = () => {
    this.baseStore.resetLane();
  };
  @action resetFilter = () => {
    this.pageFilter = [];
    this.filterValues = {};
  };
  @action('所有配置改变-数据源改变') changeConfig = (changes: any) => {
    this.config = { ...this.config, ...changes };
    this.baseStore.config = { ...this.baseStore.config, ...this.config };
    const baseDId = changes.progressDataSet?.id;
    const phaseDId = changes.stageDataSet?.id;
    const projDId = changes.projDataSet?.id;
    // 满足这些条件说明是切换了项目数据源 需要清空
    if (!phaseDId && !baseDId && projDId) {
      this.reloadFetchProj();
    }
    // 切换了阶段数据源
    if (phaseDId && !baseDId) {
      this.reloadFetchStage(true, true);
    }
    // 切换了基础表数据源
    if (baseDId) {
      this.reloadFetchData();
    }
  };
  @action('改变单个配置') changeSingleConfig = (changes: any) => {
    runInAction(() => {
      this.config = { ...this.config, ...changes };
      this.baseStore.config = { ...this.baseStore.config, ...this.config };
      const changeKeys = Object.keys(changes);
      // 项目表-直接重新请求
      if (changeKeys.some(key => key.indexOf('proj') > -1)) {
        if (changeKeys.includes('projPageType')) {
          let pageJson: any = {
            ...INIT_LANE_PAGE(),
            pageType: changes['projPageType'],
            pageSize: this.config?.projPage || INIT_PAGE_SIZE_CONFIG(),
          };
          pageJson.pageNum = this.getDefaultPageNum(pageJson.pageSize);
          this.baseStore.setLanePageState({ ...pageJson });
        }
        if (changeKeys.includes('projPage')) {
          let pageJson: any = {
            ...INIT_LANE_PAGE(),
            pageSize: changes['projPage']
          };
          pageJson.pageNum = this.getDefaultPageNum(pageJson.pageSize);
          this.baseStore.setLanePageState({ ...pageJson });
        }
        this.reloadFetchProj();
      }
      // 阶段表
      if (changeKeys.some(key => key.indexOf('stage') > -1)) {
        const needDataKeys = ['stageFilter', 'stageOrders', 'stageProjField', 'stageBackGround'];
        const needData = changeKeys.some(key => needDataKeys.includes(key));
        this.reloadFetchStage(needData, needData);
      }
      // 基础表
      if (changeKeys.some(key => key.indexOf('progressStats') > -1)) {
        // 改变统计配置
        const hasValidData = (changes?.progressStats?.statisticsItem || []).every(
          (i: any) => i.inputValue?.valueLocale && !isEmpty(i.eBFilterData)
        );
        if (hasValidData) {
          this.controlFetchStat();
        }
      } else {
        if (
          changeKeys.some(key => key.indexOf('progress') > -1) ||
          changeKeys.some(key => key.indexOf('linkProjFieldItems') > -1) ||
          changeKeys.some(key => key.indexOf('linkPhaseItems') > -1)
        ) {
          this.reloadFetchData();
        }
      }
    });
  };
  @action handleRefreshFilter = (filters: any) => {
    this.filterValues = filters;
    // 没有分组 重新获取
    if (this.baseStore.laneGroups.every(i => (i.groups || []).length === 0)) {
      // this.fetchPhase();
      return;
    }
    // this.reloadBaseBoard('handleRefreshFilter');
  };
  @action setPageFilter = (filters: any) => {
    this.pageFilter = filters;
    this.laneLoading = false
    this.reloadFetchProj();
  };
  @action('同步看板分页信息') syncPageInfo = (pageInfo: any) => {
    this.pageInfo = pageInfo;
  };
  @action('同步泳道分页信息') syncLanePageInfo = (pageInfo: any, type: string) => {
    if (!this.baseStore.lanePage.hasMore) return;
    this.baseStore.setLanePageState({ pageNo: pageInfo, moreLoading: type === PageModeType.Scroll || type === PageModeType.More });
    this.reloadFetchProj(type);
  };
}

export default ProjectLaneStore;
