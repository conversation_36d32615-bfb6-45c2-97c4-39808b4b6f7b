import React from 'react';
import { CorsComponent, AnyObj } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { ebdBClsPrefix } from '../../../../../constants';
// import './index.less';

export interface LaneFilterProps {
  onChange?: (type: string) => void;
  config?: any;
  weId?: string;
  page?: AnyObj;
  compId?: string;
  isDesign?: boolean;
  isMobile?: boolean;
  events?: AnyObj;
  ownDataSetKey: string; // 筛选数据源
  ownSearchConditionKey: string // 筛选配置
}

const cls = `${ebdBClsPrefix}-kanbanPlugin-filter`;
const LaneFilter = React.memo((props: LaneFilterProps) => {
  const { page, compId, config, ownDataSetKey, ownSearchConditionKey } = props;
  const searchCondition = config[ownSearchConditionKey] || {};
  const dataset = config?.[ownDataSetKey] || {};
  if (isEmpty(searchCondition) || isEmpty(dataset)) return null;
  return (
    <div className={cls}>
      {props.isDesign && <div className={`${cls}-mask`} />}
      <CorsComponent
        weId={`${props.weId || ''}_x0a21w`}
        app="@weapp/ebdcontainercoms"
        compName="SearchConidtions"
        comName={'SearchCommon'}
        comProps={{
          ...props,
          config: {
            ...props.config,
            searchConditions: toJS(searchCondition),
            dataset,
          },
          id: compId,
          page,
          pageId: page?.id,
          events: props?.events,
        }}
        searchConfigKey="searchConditions"
        isPreView={!props.isDesign}
      />
    </div>
  );
})
export default LaneFilter;
