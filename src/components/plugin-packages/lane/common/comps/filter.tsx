import { DesignComProps } from '../.././../../baseBoard/types';
import { PluginFilterSet } from '../../../common';
import '../../../common/index.less';

export interface PsFilterSetProps extends DesignComProps {
  [x: string]: any;
}

const LaneFilterSet = (props: PsFilterSetProps) => {
  const handleChange = (changes: any) => {
    props.onChange?.(changes);
    // * 同步searchConditions到config上 搜索组件才会读取
    props.onConfigChange?.({searchConditions: changes});
  };
  return (
    <PluginFilterSet weId={`${props.weId || ''}_rz3wbz`} {...props} onChange={handleChange} showMenu={['commonFilters']} hideCommonSearchType={['0']} />
  );
};
export default LaneFilterSet
