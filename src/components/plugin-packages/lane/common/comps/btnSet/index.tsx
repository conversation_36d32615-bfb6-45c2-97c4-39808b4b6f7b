import { useEffect, useState } from 'react';
import { Dialog, Button, Table, Switch, AnyObj, TableTextAlign } from '@weapp/ui';
import { getLabel, cloneDeep, isEmpty } from '@weapp/utils';
import { ebdBClsPrefix, dlgIconName } from '../../../../../../constants';
import { DesignComProps } from '../../../../../baseBoard/types';
import LocaleInput from '../../../../../common/locale/LocaleInput';
import { LocaleExValueDataType } from '../../../../../common/locale/types';
import './index.less';


const {message} = Dialog
export type progressButtonsType = {
  name: string | AnyObj;
  enable: boolean;
};
export interface LaneBtnSetProps extends DesignComProps {
  [x: string]: any;
  progressButtons: progressButtonsType[];
}
const cls = `${ebdBClsPrefix}-LaneBtnSet`;
const diffKey = 'LaneBtnSet';
const LaneBtnSet = (props: LaneBtnSetProps) => {
  const { id, config, onChange } = props;
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState([] as any);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setData(cloneDeep(defaultValue));
  }, [config, id, visible]);

  const onSave = () => {
    // 默认取第0个
    const name = data[0]?.name?.nameAlias || data[0]?.name
    if (name.length > 10) {
      message({type: 'error', content: getLabel('291974', '按钮名称不能超过10位')})
      return;
    }
    onChange(data);
    onCancel();
  };
  const onCancel = () => {
    setVisible(false);
  };
  const handleChange = (key: string, value: any, pos: number) => {
    let newData = [...data]
    newData[pos][key] = value
    setData(newData)
  };
  const buttons = [
    <Button weId={`${props.weId || ''}_z8b8pk@${diffKey}`} key="onCancel" onClick={onCancel}>
      {getLabel('53937', '取消')}
    </Button>,
    <Button weId={`${props.weId || ''}_6lzjm9@${diffKey}`} key="save" type="primary" onClick={onSave}>
      {getLabel('40496', '保存')}
    </Button>,
  ];
  const columns = [
    {
      dataIndex: 'name',
      title: getLabel('56676', '按钮名称'),
      bodyRender: (data: progressButtonsType, pos: AnyObj) => {
        return (
          <LocaleInput
            weId={`${props.weId || ''}_vus9zi@${diffKey}`}
            ebBusinessId={id!}
            placeholder={getLabel('56234', '请输入')}
            value={data?.name}
            onChange={(v: LocaleExValueDataType) => handleChange('name', v, pos.rowComIndex)}
            required
            inputProps={{
              maxLength: 10
            }}
          />
        );
      },
    },
    {
      dataIndex: 'enable',
      title: getLabel('54086', '启用'),
      showCheckAll: true,
      isCustomTitle: true,
      align: 'center' as TableTextAlign,
      textAlign: 'center' as TableTextAlign,
      bodyRender: (data: progressButtonsType, pos: AnyObj) => {
        return (
          <Switch
            weId={`${props.weId || ''}_rub4jq@${diffKey}`}
            value={data.enable}
            onChange={(checked: boolean) => handleChange('enable', checked, pos.rowComIndex)}
          />
        );
      },
    },
  ];
  const hasValue = isEmpty(data) ? false : data[0]?.enable
  return (
    <div className={cls}>
      <Button weId={`${props.weId || ''}_vfowoi`} type="default" className={`${cls}-btn ${hasValue ? 'active' : ''}`} onClick={() => setVisible(true)}>
        <span>{getLabel('235053', '按钮配置')}</span>
      </Button>
      <Dialog
        weId={`${props.weId || ''}_jsn1yf`}
        footer={buttons}
        width={460}
        destroyOnClose
        visible={visible}
        title={getLabel('235053', '按钮配置')}
        mask
        closable
        onClose={onCancel}
        icon={dlgIconName}
        wrapClassName={`${cls}-dialog`}
      >
        <Table weId={`${props.weId || ''}_36wwbb`} rowKey="key" className={`${cls}-table`} columns={columns} data={data} />
      </Dialog>
    </div>
  );
};
export default LaneBtnSet;
