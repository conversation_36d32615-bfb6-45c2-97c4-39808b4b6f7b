import { useEffect, useState } from 'react';
import { LanePluginWrapItemCls } from '../../../constants';
import DataSet from '../../../../common/DataSet';
import { DesignComProps } from '../../../../baseBoard/types';
import { defaultConfig, INITIAL_STAGE_CONFIG, INITIAL_PROGRESS_CONFIG } from '../../design/constants';

export interface PsDataSetProps extends DesignComProps {
  [x: string]: any;
}
const LaneDataSet = (props: any) => {
  const { id, config, onConfigChange, onChange } = props;
  const [value, setValue] = useState({} as any);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    setValue(changes);
    let configVal = {} as any;
    // 改变权限不需要重置
    const isChangePer =
      value?.id === changes?.id &&
      value?.ebuilderDataConfig?.dataSetup?.dataPermission !== changes?.ebuilderDataConfig?.dataSetup?.dataPermission;
    if (isChangePer) {
      onChange(changes);
      return;
    }
    switch (id) {
      case 'projDataSet':
        configVal = {
          ...defaultConfig(),
          [id]: changes,
          // 必须更新数据源 筛选有用
          dataset: changes,
        };
        break;
      case 'stageDataSet':
        configVal = {
          ...INITIAL_STAGE_CONFIG(),
          ...INITIAL_PROGRESS_CONFIG(),
          [id]: changes,
        };
        break;
      case 'progressDataSet':
        configVal = {
          ...INITIAL_PROGRESS_CONFIG(),
          [id]: changes,
        };
        break;
      default:
        break;
    }
    onConfigChange && onConfigChange(configVal);
  };
  return (
    <div className={LanePluginWrapItemCls}>
      {/* <DataSet weId={`${props.weId || ''}_azo8l0`} {...props} onChange={handleChange} value={value} showEbFormData={false} /> */}
      <DataSet weId={`${props.weId || ''}_azo8l0`} {...props} onChange={handleChange} value={value} />
    </div>
  );
};
export default LaneDataSet;
