// 阶段背景色配置
import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Dialog, Table, CorsComponent, AnyObj } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { ebdBClsPrefix, dlgIconName } from '../../../../../../constants';
import HighlightSetting, { presetStyles } from '../../../../../common/high-light/HighlightSetting';
import PhaseStep from '../../../../../common/high-light/phaseStep';
import './index.less';

export interface PhaseBgProps {
  isMobile?: boolean;
  onChange: (data: AnyObj) => void;
  weId?: string;
  [x: string]: any;
}
export type PhaseBgItemType = {
  id: number;
  color: string;
  filter: AnyObj | string | number[];
};

const cls = `${ebdBClsPrefix}-projectLane-phaseBg`;
const diffKey = 'phaseBg';
const INITIAL_DATA = (id: number) => ({
  id,
  color: presetStyles[Math.floor(Math.random() * presetStyles.length)].background,
  filter: {},
});
const PhaseBg = (props: PhaseBgProps) => {
  const [visible, setVisible] = useState(false);
  const [filterInfo, setFilterInfo] = useState({} as PhaseBgItemType);
  const [phaseData, setPhaseData] = useState([] as PhaseBgItemType[]);

  const { onChange, config, id } = props;

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setPhaseData(defaultValue);
  }, [config, id, visible]);

  const addItem = () => {
    const idx = phaseData.length + 1;
    setPhaseData([...phaseData, INITIAL_DATA(idx)]);
  };
  const deleteItem = (item: PhaseBgItemType, pos: number) => {
    const newPhaseData = [...phaseData];
    newPhaseData.splice(pos, 1);
    setPhaseData(newPhaseData);
  };

  // 标识过滤条件设置弹窗(角标+竖线)
  const renderFilterDialog = () => {
    if (isEmpty(filterInfo)) return null;
    const { stageDataSet } = config;
    const onCancel = () => {
      setFilterInfo({} as PhaseBgItemType);
    };
    const onSave = (val: any) => {
      const pos = phaseData.findIndex(i => i.id === filterInfo.id)
      onSettingChange(val, filterInfo, 'filter', pos);
      onCancel();
    };

    return (
      <CorsComponent
        weId={`${props.weId || ''}_rxin8l`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        value={filterInfo.filter}
        onOk={onSave}
        onCancel={onCancel}
        onClear={() => onSave({})}
        visible
        // title={getLabel('-1', '占用标识条件')}
        dataSet={stageDataSet}
        showFilterType // 是否展示sql和自定义接口筛选类型
      />
    );
  };
  const onSettingChange = (v: any, data: PhaseBgItemType, type: 'color' | 'filter', pos: number) => {
    const newPhaseData = [...phaseData];
    newPhaseData[pos][type] = v;
    setPhaseData(newPhaseData);
  };
  const phaseDialog = () => {
    if (!visible) return null;
    const onCancel = () => {
      setVisible(false);
    };
    const onSave = () => {
      onChange(phaseData)
      onCancel()
    }
    const columns = [
      {
        dataIndex: 'style',
        title: getLabel('93226', '显示样式'),
        bodyRender: (data: PhaseBgItemType, pos: AnyObj) => {
          return (
            <HighlightSetting
              weId={`${props.weId || ''}_orlft8@${pos}`}
              // content={phaseItem(data.bgColor)}
              customRenderContent={<PhaseStep weId={`${props.weId || ''}_u4omih@${diffKey}`} item={data} index={pos.rowComIndex} />}
              bgColor={data.color}
              onConfirm={value => onSettingChange(value, data, 'color', pos.rowComIndex)}
            />
          );
        },
      },
      {
        dataIndex: 'filter',
        title: getLabel('109838', '条件设置'),
        bodyRender: (data: PhaseBgItemType) => {
          return (
            <div className={`${cls}-table-block filter`}>
              <Button weId={`${props.weId || ''}_965qlo@${diffKey}`} type="link" onClick={() => setFilterInfo(data)}>
                {isEmpty(data.filter) ? getLabel('56776', '添加条件') : getLabel('56775', '编辑条件')}
              </Button>
            </div>
          );
        },
      },
      {
        dataIndex: 'menus',
        title: getLabel('54342', '操作'),
        bodyRender: (data: PhaseBgItemType, pos: AnyObj) => {
          return (
            <div className={`${cls}-table-block`}>
              <Button weId={`${props.weId || ''}_7d29ow@${diffKey}`} type="link" onClick={() => deleteItem(data, pos.rowComIndex)}>
                {getLabel('53951', '删除')}
              </Button>
            </div>
          );
        },
      },
    ];
    const footerButtons = [
      <Button weId={`${props.weId || ''}_ziwe33@${diffKey}`} key="onCancel" onClick={onCancel}>
        {getLabel('53937', '取消')}
      </Button>,
      <Button weId={`${props.weId || ''}_tzq4px@${diffKey}`} key="save" type="primary" onClick={onSave}>
        {getLabel('40496', '保存')}
      </Button>,
    ];
    return (
      <Dialog
        weId={`${props.weId || ''}_p118uo`}
        width={700}
        destroyOnClose
        visible
        title={getLabel('291696', '背景颜色设置')}
        mask
        closable
        onClose={onCancel}
        icon={dlgIconName}
        buttons={[
          <Button weId={`${props.weId || ''}_rj17ga`} onClick={addItem}>
            {getLabel('54007', '新建')}
          </Button>,
        ]}
        footer={footerButtons}
      >
        <Table
          weId={`${props.weId || ''}_seyz20`}
          columns={columns}
          data={phaseData}
          isShowIndex={true}
          isShowIndexInCheck
          sortable
          sortableType='icon'
          className={`${cls}-table`}
        />
      </Dialog>
    );
  };
  return (
    <div className={`${cls}`}>
      <Button weId={`${props.weId || ''}_owbleg`} size="small" onClick={() => setVisible(true)} className={!isEmpty(phaseData) ? 'active' : ''}>
        {getLabel('291696', '背景颜色设置')}
      </Button>
      {phaseDialog()}
      {renderFilterDialog()}
    </div>
  );
};
export default PhaseBg;
