import { getDefaultOperationBtns } from '../utils/getDefaultOperationBtns';
import getProjectConfig from './Config';
import { IPlugin } from '../../types';
import './index.less'


export default class ProjectConfigPlugin implements IPlugin {
  name = 'Config-ProjectLane';

  formatConfig = (
    initialDatas: any,
    datas: any,
    store: any,
  ) => {
    const projectConfig = getProjectConfig(initialDatas);
    const { wrapItem } = store;

    let result = {
      ...projectConfig,
      getDefaultOperationBtns,
    }
    const { items } = result as any;
    const restItems: any = {};
    Object.keys(items).forEach((itemKey) => {
      restItems[itemKey] = items[itemKey]
      if (restItems[itemKey].customRender) {
        restItems[itemKey].customRender = wrapItem(restItems[itemKey].customRender, itemKey);
      }
    });
    return {
      ...result,
      items: restItems
    }
  }
}
