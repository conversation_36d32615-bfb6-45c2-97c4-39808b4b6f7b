import { FormInitAllDatas, FormLayoutProps } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import LaneDataSet from '../common/comps/dataSet';
import PhaseBg from '../common/comps/phaseBg';
import LaneBtnSet from '../common/comps/btnSet';
import LaneFilterSet from '../common/comps/filter';
import {
  PluginCondition,
  PluginCardSet,
  PluginSortSet,
  PluginFieldCheckSet,
  PluginFieldSelectSet,
  PluginOpenStaticSet,
  PluginPageSize,
  PluginPageMode,
} from '../../common';
import './index.less';

const LabelSpan = 8;
const groups = [
  {
    id: 'proj',
    title: getLabel('291697', '项目信息设置'),
    visible: true,
  },
  {
    id: 'stage',
    title: getLabel('282007', '项目阶段设置'),
    visible: true,
  },
  {
    id: 'progress',
    title: getLabel('291698', '进展看板设置'),
    visible: true,
  },
];
const getCommonItems = (type: string) => {
  let linkProjFieldItems = {};
  let linkPhaseItems = {};
  if (type === 'stage') {
    // 设置阶段和项目信息表关联关系，仅支持选择关联e-bulider字段
    linkProjFieldItems = {
      [`${type}ProjField`]: {
        label: getLabel('56268', '所属项目'),
        customRender: PluginFieldSelectSet,
        hide: false,
        labelSpan: LabelSpan,
        required: true,
      },
    };
  }
  if (type === 'progress') {
    // 设置阶段和项目信息表关联关系，仅支持选择关联e-bulider字段
    linkProjFieldItems = {
      [`${type}ProjField`]: {
        label: getLabel('56268', '所属项目'),
        customRender: PluginFieldSelectSet,
        hide: false,
        labelSpan: LabelSpan,
      },
    };
    linkPhaseItems = {
      [`${type}StageField`]: {
        label: getLabel('291699', '所属阶段'),
        customRender: PluginFieldSelectSet,
        hide: false,
        labelSpan: LabelSpan,
        required: true,
      },
    };
  }
  const items: any = {
    [`${type}DataSet`]: {
      label: getLabel('55058', '数据源'),
      customRender: LaneDataSet,
      required: true,
    },
    ...linkProjFieldItems,
    ...linkPhaseItems,
    [`${type}ShowField`]: {
      label: type === 'stage' ? getLabel('180183', '显示标题') : getLabel('53855', '显示字段'),
      customRender: type === 'stage' ? PluginFieldCheckSet : PluginCardSet,
      hide: false,
      required: true,
    },
    [`${type}Filter`]: {
      label: getLabel('53857', '数据过滤'),
      customRender: PluginCondition,
      hide: false,
    },
    [`${type}Orders`]: {
      label: getLabel('96961', '排序设置'),
      customRender: PluginSortSet,
      hide: false,
    },
  };
  for (let i in items) {
    items[i] = {
      groupId: type,
      labelSpan: LabelSpan,
      itemType: 'CUSTOM',
      ...items[i],
    };
  }
  return items;
};
// 分页设置
const pageItems = (prefix: string) => {
  return {
    [`${prefix}PageType`]: {
      label: getLabel('98869', '分页模式'),
      itemType: 'CUSTOM',
      labelSpan: LabelSpan,
      groupId: prefix,
      hide: false,
      customRender: PluginPageMode,
    },
    [`${prefix}Page`]: {
      label: getLabel('59334', '每页行数'),
      itemType: 'CUSTOM',
      labelSpan: LabelSpan,
      groupId: prefix,
      hide: false,
      customRender: PluginPageSize,
    },
  };
};
export default function getProjectStateConfig(initialConfig: any) {
  // 项目信息设置
  const projItems = {
    ...getCommonItems('proj'),
    projSearchFilter: {
      label: getLabel('55818', '搜索条件'),
      itemType: 'CUSTOM',
      labelSpan: LabelSpan,
      groupId: 'proj',
      hide: false,
      customRender: LaneFilterSet,
    },
    ...pageItems('proj'),
  };
  // 项目阶段设置
  const stageItems = {
    ...getCommonItems('stage'),
    stageBackGround: {
      label: getLabel('291700', '阶段背景颜色'),
      itemType: 'CUSTOM',
      labelSpan: 24,
      groupId: 'stage',
      hide: false,
      customRender: PhaseBg,
    },
  };
  // 项目进展设置
  const progressItems = {
    ...getCommonItems('progress'),
    progressButtons: {
      label: getLabel('235054', '按钮设置'),
      labelSpan: LabelSpan,
      groupId: 'progress',
      hide: false,
      itemType: 'CUSTOM',
      customRender: LaneBtnSet,
    },
    ...pageItems('progress'),
    progressStats: {
      label: getLabel('245989', '开启统计'),
      labelSpan: 0,
      groupId: 'progress',
      hide: false,
      itemType: 'CUSTOM',
      customRender: PluginOpenStaticSet,
    },
  };
  const getReferences = (items: any) => {
    let references = {} as any;
    const keys = Object.keys(items);
    for (let i in items) {
      if (i.includes('Dataset')) {
        if (i === 'projDataSet') {
          references[i] = [...Object.keys(projItems), ...Object.keys(stageItems), ...Object.keys(progressItems)];
        } else if (i === 'stageDataSet') {
          references[i] = [...Object.keys(stageItems), ...Object.keys(progressItems)];
        } else {
          references[i] = keys;
        }
      } else {
        references[i] = keys;
      }
    }
    return references;
  };
  const references = {
    ...getReferences(projItems),
    ...getReferences(stageItems),
    ...getReferences(progressItems),
  };
  const config = {
    ...initialConfig,
    items: {
      ...progressItems,
      ...projItems,
      ...stageItems,
    },
    groups,
    customHide: function customHide(col: FormLayoutProps) {
      return { ...col, hide: false };
    },
    references,
  };
  console.log('**111**config****', config);

  return config as unknown as FormInitAllDatas;
}
