const Config = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectLane_config" */
  './config/index'
);
const MConfig = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectLane_config_m" */
  './config/index'
);
const Design = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectLane_design" */
  './design'
);

const MDesign = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectLane_design_m" */
  './design-m/MDesign'
);

const View = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectLane_view" */
  './view'
);

const MView = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectLane_view_m" */
  './view-m/MView'
);

export default {
  Config,
  MConfig,
  Design,
  MDesign,
  View,
  MView,
};
