@import (reference) '../../../../style/prefix.less';

// @phaseSpace: 16px; // 阶段分组之间的间距 
@phaseSpace:6px; // 阶段分组之间的间距 

.@{mBoardViewClsPrefix} {
  .@{ebdBClsPrefix}-projectStage-view {
    &-top {
      flex-wrap: wrap;
      flex-direction: column-reverse;

      .@{ebdBClsPrefix}-projectStage-projectFilter {
        width: 100%;
        padding: 0;
        max-width: initial;

        .ebcoms-nlist-search-quick {
          width: 100%;
        }

        .ebcoms-mlist-search {
          border-bottom: none;
          width: 100%;

          .ui-m-searchAdvanced {
            padding: 0;
          }
        }
      }
    }
  }
}

.@{ebdBClsPrefix}-projectStage-view {

  // 第一行第一个字段不换行 后续默认换行
  .@{ebdBClsPrefix}-list-body .ui-list-body {
    .ebcoms-list-flex.ebcoms-list-grid-row:first-child>.ebcoms-list-flex.ebcoms-list-grid-row-main {
      flex-wrap: initial;
      &>.ebcoms-list-render-cell:first-child {
        position: relative;

        .show-data.edc-trans-data-autowhmin {
          padding: 0;
        }
      }
    }

    .ebcoms-list-flex.ebcoms-list-grid-row:not(:first-child)>.ebcoms-list-flex.ebcoms-list-grid-row-main {
      color: var(--secondary-fc);
    }
  }

  .ui-board-drag-handle:first-child {
    .weapp-ebdboard-list>.@{ebdBClsPrefix}-view-groupTitle::before {
      display: none;
      width: 0;
    }
  }

  // 高度调整
  .@{ebdBClsPrefix}-list-body {
    height: calc(100% - 60px);
    max-height: calc(100% - 60px) !important;
    // 内容区横向滚动适配
    .ui-scroller__wrap{
      margin-right: -7px !important;
    }
  }

  &-top {
    display: flex;

    .@{ebdBClsPrefix}-projectStage-projectType {
      flex: 1;
    }

    .@{ebdBClsPrefix}-kanbanPlugin-filter {
      flex-shrink: 0;
      display: flex;
      padding: 0px 16px 0 0;
      align-items: center;
      &:not(.isMobile) {
        max-width: 180px;
      }
    }
  }

  // 标题调整
  &-groupTitle {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
    // 复写标题箭头
    .@{ebdBClsPrefix}-phaseStep{
      height: calc(var(--hd)* 28);
      line-height: calc(var(--hd)* 28);
      width: 100%;
      border-radius: 1px 2px 2px 1px;//过度不圆滑
      // height: calc(var(--hd) * 36);
      // line-height: calc(var(--hd) * 36);
      // width: calc(100% - 10px);
      &.hasBgColor::after{
        border-width: calc(28 * var(--hd) / 2) 0 calc(28 * var(--hd) / 2) calc(28 * var(--hd) / 2);
        right: calc(-1 * calc(28 * var(--hd) / 2));
        // border-width: calc(36 * var(--hd) / 2) 0 calc(36 * var(--hd) / 2) calc(36 * var(--hd) / 2);
        // right: calc(-1 * calc(36 * var(--hd) / 2));
      }
      &.hasBgColor::before{
        border-width: calc(28 * var(--hd) / 2) 0 calc(28 * var(--hd) / 2) calc(28 * var(--hd) / 2);
        // border-width: calc(36 * var(--hd) / 2) 0 calc(36 * var(--hd) / 2) calc(36 * var(--hd) / 2);
      }
      &.hideFirstArrow{
        border-radius: 4px 2px 2px 4px;//放大缩小有间隙
      }
      &.hideLastArrow{
        border-radius: 0 4px 4px 0;
        width: calc(100%);
      }
      &.hideFirstArrow.hideLastArrow{
        border-radius: 4px;
      }
      &-text{
        // text-align: center;
        text-align: left;
        padding: 0 32px;
      }
    }
  }

  // 为空占位调整
  .@{ebdBClsPrefix}-board-emptyText {
    width: 100%;
    height: 66px;
    top: 0;
    .flexCt;
  }

  // 阶段分组之间间隔调整
  .@{ebdBClsPrefix}-content .ui-list-body > .ui-board-list {
    margin: 0 @phaseSpace 0 0;
    &:last-child {
      margin-right: 0;
    }
  }
}