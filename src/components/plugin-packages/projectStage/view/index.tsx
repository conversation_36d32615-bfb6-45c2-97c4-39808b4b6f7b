import ProjectStageStore from '../common/store';
import { IPlugin } from '../../types';
import { PluginFilter } from '../../common';
import ProjectType from '../common/comps/projectType';
import { ebdBClsPrefix, boardUrlParamsCache } from '../../../../constants';
import BoardEmpty from '../../../common/empty';
import { getUPRefreshInPhaseBoard } from '../../../common/board/utils';
import { GroupItem } from '../../../common/board/types';
import PhaseStep from '../../../common/high-light/phaseStep';
import './index.less';

const cls = `${ebdBClsPrefix}-projectStage-view`;
export class ProjectStateViewPlugin implements IPlugin {
  name = 'View-ProjectStage';

  pluginCenter!: any;
  _props!: any;
  isMobile: boolean;
  projectStageStore: ProjectStageStore; // 插件的store

  constructor(opts: any) {
    this.projectStageStore = new ProjectStageStore({
      props: opts.com?.props as any,
      pluginCenter: opts.pluginCenter,
      isDesign: opts.com?.props?.isDesign,
    });
    this._props = opts.com?.props;
    this.isMobile = opts.com?.props?.page?.client === 'MOBILE';
    this.addProjectTreeEvent();
  }
  addProjectTreeEvent = () => {
    if (!this.projectStageStore.isDesign) {
      const { coms = [] } = this._props;
      const targetComs = coms?.filter((com: any) => com?.type === 'ProjectTree' || com?.type === 'taskTree');
      const bindEvtComIds = targetComs
        ?.filter((com: any) => {
          const eventGroup = com?.config?.eventGroup;
          if (eventGroup && eventGroup?.length > 0) {
            return eventGroup?.some((groups: any[]) =>
              groups?.some(group =>
                group?.events?.some(
                  (e: any) => e?.type === 'RefreshComp' && e?.refreshInfo?.selectedCom?.some((i: string) => i === this._props?.id)
                )
              )
            );
          }
          return false;
        })
        ?.map((com: any) => com?.id);
      bindEvtComIds.forEach((id: string) => {
        this._props?.events?.on('onProjectChangeView_' + id, this.handleProjectTaskTreeViewSwitch);
      });
    }
  };
  handleProjectTaskTreeViewSwitch = (args: any) => {
    this.projectStageStore.resetFilter();
    this.projectStageStore.resetGroup();
    sessionStorage.removeItem(boardUrlParamsCache);
    const { originEventActionFn } = args || {};
    typeof originEventActionFn === 'function' && originEventActionFn();
  };
  // 触发更新时重新获取
  onCustomDidMount = () => {
    this.projectStageStore.judgeFetch('type');
  };
  // 获取单个看板数据
  getBoardData = (params: any) => {
    this.projectStageStore.judgeFetch('base', params?.item?.id);
  };
  // 卡片不允许拖拽
  getCardDraggable = (config: any) => {
    return [false];
  };
  getGroupMenus = (groupId: string, loadMore: false) => {};

  onBeforeMount = (baseStore: any) => {
    this.projectStageStore.setBaseStore(baseStore);
  };
  onPageChange = (pageInfo: any) => {
    this.projectStageStore.syncPageInfo(pageInfo);
  };
  onFilter = (props: any) => {
    const { events } = props;
    const { baseStore, setState, handleRefreshFilter, setPageFilter, isDesign } = this.projectStageStore;
    if (!events || isDesign) return;
    setState({ events });
    events.on('filter', baseStore.compId, (filters: any, typeKey: string, needGetDate: boolean) => {
      if (typeKey.indexOf('quick_EXCLUDECACHE') > -1) {
        // 快捷搜索的 不清除类型 只刷新数据
        return handleRefreshFilter(filters);
      }
      // 全部重请求
      const newParams = getUPRefreshInPhaseBoard(filters, this._props?.coms, this._props?.pluginCenter);
      setPageFilter(newParams);
    });
  };
  renderCardAddBtn = () => {
    return null;
  };
  renderContentTop = () => {
    const { types, typeLoading, selectedType, changeType, config, baseStore, isDesign, events } = this.projectStageStore;
    if (!(types.length || Object.keys(config.searchConditions || {}).length)) return null;
    return (
      <div className={`${cls}-top`}>
        <ProjectType
          weId={`s0p20h`}
          types={types}
          loading={typeLoading}
          selectedType={selectedType}
          onChange={changeType}
          isMobile={this.isMobile}
        />
        <PluginFilter
          weId={`cn15gd`}
          config={config}
          compId={baseStore?.compId}
          isDesign={isDesign}
          events={events}
          page={this._props?.page}
          isMobile={this.isMobile}
          ownDataSetKey="baseStageConfig_dataset"
          ownSearchConditionKey="searchConditions"
          comName="SearchQuick"
        />
      </div>
    );
  };
  onCustomUpdate = (props: any) => {};
  onCustomGetExtraCardInfo = () => {};
  onLoadMore = async (laneId: string, groupId: string, func?: Function) => {
    await this.projectStageStore.fetchData(groupId, true);
    func && func();
  };
  renderMain = (hook: any) => {
    const { isEmptyConfig } = this.projectStageStore;
    return (_props: any) => {
      return (
        <div style={{ width: '100%', overflowY: 'scroll', flex: '1' }} className={`${cls} ${this.isMobile ? 'm' : ''}`}>
          {isEmptyConfig ? <BoardEmpty weId={`${_props.weId || ''}_txw3zt`} iconSize={{ width: 130, height: 130 }} /> : hook(_props)}
        </div>
      );
    };
  };
  renderCardContent = (hook: any) => {
    return (...args: any) => {
      const currentConfig = args[0];
      const newConfig = {
        card: currentConfig.baseStageConfig_card,
        dataset: currentConfig.baseStageConfig_dataset,
      };
      return hook(newConfig, args[1], args[2]);
    };
  };
  // renderGroupTitle = (hook: any) => {
  //   return (...args: any) => {
  //     const {item, type, backgroundColor, fontColor} = args[0] || {}
  //     return <div className={`${cls}-groupTitle`}>
  //       {hook({item, type, backgroundColor: '#5B7ED9', fontColor: '#fff'})}
  //     </div>
  //   };
  // }
  renderGroupTitle = (hook: any) => {
    return (...args: any) => {
      const { item } = args[0] || {};
      const groups = args[1] || [];
      const index = groups.findIndex((i: GroupItem) => i.id === item.id)
      if (index < 0) return null;
      return (
        <div className={`${cls}-groupTitle`}>
          <PhaseStep
            weId={`yl5s26`}
            // item={{...item, color: '#2E66F6'}}
            item={{...item, color: '#5D84F3'}}
            hideFirstArrow
            hideLastArrow
            index={index}
            groups={groups}
          />
        </div>
      );
    };
  };
}

const View = [ProjectStateViewPlugin];

export default View;
