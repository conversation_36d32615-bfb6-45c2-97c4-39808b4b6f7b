import { FormInitAllDatas, FormLayoutProps } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import ProjectStageDataSet from '../common/comps/dataSet';
import StageFilterSet from '../common/comps/filter';
import {
  PluginCondition,
  PluginCardSet,
  PluginSortSet,
  PluginFieldCheckSet,
  PluginFieldSelectSet,
  PluginOpenStaticSet,
} from '../../common';
import { PageMode, PageSize } from '../../../baseBoard/config/cmps';
import './index.less';

const groups = [
  {
    id: 'typeStageConfig',
    title: getLabel('282006', '项目类型设置'),
    visible: true,
  },
  {
    id: 'phaseStageConfig',
    title: getLabel('282007', '项目阶段设置'),
    visible: true,
  },
  {
    id: 'baseStageConfig',
    title: getLabel('282005', '项目数据设置'),
    visible: true,
  },
  {
    id: 'other',
    title: getLabel('55756', '其他'),
    visible: true,
  },
];
const getCommonItems = (type: string) => {
  const datasetTitleMap = {
    type: getLabel('54682', '项目类型'),
    phase: getLabel('56267', '项目阶段'),
    base: getLabel('239473', '项目数据'),
  };
  let linkFieldItems = {};
  if (type === 'phase' || type === 'base') {
    linkFieldItems = {
      [`${type}StageConfig_${type === 'phase' ? 'type' : 'phase'}MappingField`]: {
        label: type === 'phase' ? getLabel('284767', '关联类型字段') : getLabel('284768', '关联阶段字段'),
        customRender: PluginFieldSelectSet,
        hide: false,
        labelSpan: 12,
        required: true,
      },
    };
  }
  const items: any = {
    [`${type}StageConfig_dataset`]: {
      label: datasetTitleMap[type as keyof typeof datasetTitleMap],
      customRender: ProjectStageDataSet,
      required: true,
      labelSpan: 10,
    },
    [type === 'base' ? `${type}StageConfig_card` : `${type}StageConfig_field`]: {
      label: type === 'base' ? getLabel('94492', '卡片设置') : getLabel('282008', '名称显示'),
      labelSpan: 12,
      customRender: type === 'base' ? PluginCardSet : PluginFieldCheckSet,
      hide: false,
      align: 'right',
      required: true,
    },
    ...linkFieldItems,
    [`${type}StageConfig_dataFilter`]: {
      label: getLabel('53857', '数据过滤'),
      customRender: PluginCondition,
      hide: false,
    },
    [`${type}StageConfig_sortField`]: {
      label: getLabel('54298', '排序'),
      customRender: PluginSortSet,
      hide: false,
    },
  };
  for (let i in items) {
    items[i] = {
      groupId: `${type}StageConfig`,
      labelSpan: 24,
      itemType: 'CUSTOM',
      ...items[i],
    };
  }
  return items;
};
export default function getProjectStateConfig(initialConfig: any) {
  // 项目类型设置
  const typeItems = getCommonItems('type');
  // 项目阶段设置
  const phaseItems = {
    ...getCommonItems('phase'),
  };
  // 项目主表设置
  const baseItems = {
    ...getCommonItems('base'),
    searchConditions: {
      label: getLabel('40540', '搜索'),
      labelSpan: 24,
      groupId: 'baseStageConfig',
      hide: false,
      itemType: 'CUSTOM',
      customRender: StageFilterSet,
    },
    statistics: {
      label: getLabel('245989', '开启统计'),
      labelSpan: 0,
      groupId: 'baseStageConfig',
      hide: false,
      itemType: 'CUSTOM',
      customRender: PluginOpenStaticSet,
    },
  };
  // 项目其他设置
  const otherItems = {
    pageSize: {
      label: getLabel('59334', '每页行数'),
      itemType: 'CUSTOM',
      labelSpan: 7,
      groupId: 'other',
      hide: false,
      customRender: PageSize,
    },
    pageMode: {
      label: getLabel('98869', '分页模式'),
      itemType: 'CUSTOM',
      labelSpan: 7,
      groupId: 'other',
      hide: false,
      customRender: PageMode,
    },
  };
  const getReferences = (items: any) => {
    let references = {} as any;
    const keys = Object.keys(items);
    for (let i in items) {
      if (i.includes('_dataset')) {
        if (i === 'typeStageConfig_dataset') {
          references[i] = [...Object.keys(typeItems), ...Object.keys(phaseItems), ...Object.keys(baseItems)];
        } else if (i === 'phaseStageConfig_dataset') {
          references[i] = [...Object.keys(phaseItems), ...Object.keys(baseItems)];
        } else {
          references[i] = keys;
        }
      } else {
        references[i] = keys;
      }
    }
    return references;
  };
  const references = {
    ...getReferences(typeItems),
    ...getReferences(phaseItems),
    ...getReferences(baseItems),
    ...getReferences(otherItems),
  };
  const config = {
    ...initialConfig,
    items: {
      ...baseItems,
      ...typeItems,
      ...phaseItems,
      ...otherItems,
    },
    groups,
    customHide: function customHide(col: FormLayoutProps) {
      return { ...col, hide: false };
    },
    references: references,
  };

  return config as unknown as FormInitAllDatas;
}
