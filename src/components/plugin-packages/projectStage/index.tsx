const Config = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectStage_config" */
  './config/index'
);
const MConfig = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectStage_config_m" */
  './config/index'
);
const Design = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectStage_design" */
  './design'
);

const MDesign = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectStage_design_m" */
  './design-m/MDesign'
);

const View = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectStage_view" */
  './view'
);

const MView = () => import(
  /* webpackChunkName: "ebdboard_ebplugin_projectStage_view_m" */
  './view-m/MView'
);

export default {
  Config,
  MConfig,
  Design,
  MDesign,
  View,
  MView,
};
