import { DesignOptions } from '@weapp/ebdcoms';
import { eventEmitter, request } from '@weapp/utils';
import { defaultStateConfig, defaultOpts } from './constants';
import { ProjectStateViewPlugin } from '../view';
import { isSjrEnv } from '../../../../utils';

export class ProjectStageDesignPlugin extends ProjectStateViewPlugin {
  static defaultProps: any = {
    config: { ...defaultStateConfig() },
    isDesign: true,
  };

  static defaultOpts: Partial<DesignOptions> = {
    ...defaultOpts,
    afterAddNewCom: (newCom: any, comDesignStore: any) => {
      if (isSjrEnv()) {
        request({
          url: '/api/ebproject/project/config/projectPhase',
          method: 'GET',
        }).then(res => {
          const { layoutStore } = comDesignStore || {};
          if (res.code === 200) {
            let defaultConfig = JSON.parse(res.data || '{}');
            layoutStore.updateComConfig(defaultConfig);
          }
        });
      }
    },
  };
  compId!: string;
  name = `Design-ProjectStage`;

  constructor(opts: any) {
    super(opts);
    this.compId = opts.com.props.id;
    this.projectStageStore.setState({ isDesign: true });
    eventEmitter.on('@weapp/designer', `propchange.${this.compId}`, this.handlePropChange);
    eventEmitter.on('@weapp/designer', `configchange.${this.compId}`, this.handleConfigChange);
  }
  componentWillUnmount() {
    eventEmitter.off('@weapp/designer', `propchange.${this.compId}`, this.handlePropChange);
    eventEmitter.off('@weapp/designer', `configchange.${this.compId}`, this.handleConfigChange);
  }
  /**
   * 只有通过wrapItem包裹才可以使用的钩子
   * 通过onConfigChange更新的属性监听
   * @param changes
   */
  handleConfigChange = (changes: any) => {
    this.projectStageStore.changeConfig(changes);
  };
  handlePropChange = (key: string, val: any) => {
    this.projectStageStore.changeSingleConfig({ [key]: val });
  };
}
const Design = [ProjectStageDesignPlugin];

export default Design;
