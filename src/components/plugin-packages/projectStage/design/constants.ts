// 阶段看板插件包默认配置
import { getLabel } from '@weapp/utils';
import { PhaseKanbanAppName } from '../../constants';

export const MOCK_DATA_SET = () => ({
  id: '',
  groupId: '',
  text: '',
  type: 'FORM',
});
export const INITIAL_CONFIG = (prefix: string) => ({
  [`${prefix}_dataFilter`]: {},
  [`${prefix}_sortField`]: [],
  [`${prefix}_dataset`]: MOCK_DATA_SET(),
});
export const INITIAL_TYPE_CONFIG = () => ({
  ...INITIAL_CONFIG('typeStageConfig'),
  typeStageConfig_field: '',
});
export const INITIAL_PHASE_CONFIG = () => ({
  ...INITIAL_CONFIG('phaseStageConfig'),
  phaseStageConfig_field: '',
  phaseStageConfig_typeMappingField: '',
  phaseStageConfig_openStatic: false,
});
export const INITIAL_BASE_CONFIG = () => ({
  ...INITIAL_CONFIG('baseStageConfig'),
  baseStageConfig_card: {},
  baseStageConfig_phaseMappingField: '',
  searchConditions: {},
  statistics: {},
});
export const defaultStateConfig: any = () => ({
  title: getLabel('271601', '项目阶段看板'),
  type: PhaseKanbanAppName,
  titleEnabled: true,
  footerEnabled: false,
  ...INITIAL_TYPE_CONFIG(),
  ...INITIAL_PHASE_CONFIG(),
  ...INITIAL_BASE_CONFIG(),
});

export const defaultOpts = {
  mask: false,
};
