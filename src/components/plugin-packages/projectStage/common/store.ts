import { isEmpty, forIn } from '@weapp/utils';
import { toJS, observable, computed, runInAction, action } from 'mobx';
import { ajax, format2FormData } from '../../../../utils/ajax';
import { defaultStateConfig } from '../design/constants';
import { ProjectTypeItemProps } from '../common/comps/projectType';
import { BoardViewStore } from '../../../common/board/store';
import { DEFAULT_LANE_ID } from '../../../../constants';

type StoreOptsType = {
  props: any;
  pluginCenter: any;
  isDesign?: boolean;
};
// 阶段看板store
class ProjectStageStore {
  pluginCenter: any; // 插件中心
  isDesign = false; // 是否为设计器视图
  events = {} as any; // 事件集合

  @observable baseStore = {} as BoardViewStore;
  @observable config = defaultStateConfig();
  // 类型列表相关状态值
  @observable types: ProjectTypeItemProps[] = [];
  @observable selectedType = ''; // 选中类型值
  @observable typeLoading = false;
  // 阶段状态值
  @observable boardLoading = false;
  // 筛选值
  @observable filterValues: any = {};
  // 组件刷新参数
  @observable pageFilter: any = [];
  // 分页信息
  @observable pageInfo: any = {};
  // 是否初始化完成(分类和阶段fetch完成)
  @observable isInitial = false;

  @computed get baseParams() {
    const params = {
      compId: this.baseStore.compId,
      pageId: this.baseStore.pageId,
      ...this.baseStore.searchParams,
    };
    if (this.isDesign) {
      return {
        ...params,
        config: this.config,
      };
    }
    return params;
  }
  @computed
  get isEmptyConfig() {
    return !this.config.typeStageConfig_dataset?.id || !this.types.length;
  }
  constructor(opts: StoreOptsType) {
    this.pluginCenter = opts.pluginCenter;
    this.isDesign = !!opts.isDesign;
    this.events = opts.props.events;
  }
  getExtPrams = (dataset: any, extParams: any = {}) => {
    if (dataset?.ebuilderDataConfig?.dataSetup?.dataPermission === 'ALL') {
      return { ...extParams, isNoPermissionList: '1' };
    }
    return extParams;
  };
  @action setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };
  @action reloadBaseBoard = (changeFunc?: string) => {
    this.baseStore.resetViewportLoading(`projectStageStore-${changeFunc}`);
  };
  @action resetStages = () => {
    this.baseStore.resetLane();
    this.fetchPhase();
  };
  @action resetTypes = () => {
    this.types = [];
    this.isInitial = false;
    this.selectedType = '';
    this.resetStages();
  };
  @action resetFilter = () => {
    this.pageFilter = []
    this.filterValues = {}
  }
  @action reloadAllGroup = () => {
    this.baseStore.resetGroupData();
  }
  @action resetGroup = (groupId?: string) => {
    if (groupId) {
      // 重置特定分组的数据
      this.baseStore.resetGroupData(groupId);
      // 重置当前列表分页
      this.baseStore.resetSpecificBoards([`${DEFAULT_LANE_ID}_${groupId}`]);
    } else {
      // 重置所有分组的数据
      this.reloadAllGroup();
      this.reloadBaseBoard();
    }
  };
  @action('所有配置改变-数据源改变') changeConfig = (changes: any) => {
    this.config = { ...this.config, ...changes };
    this.baseStore.config = { ...this.baseStore.config, ...this.config };
    const baseDId = changes.baseStageConfig_dataset?.id;
    const phaseDId = changes.phaseStageConfig_dataset?.id;
    const typeDId = changes.typeStageConfig_dataset?.id;
    // 满足这些条件说明是切换了类型数据源 需要清空
    if (!phaseDId && !baseDId && typeDId) {
      this.resetTypes();
    }
    // 切换了阶段数据源
    if (phaseDId && !baseDId) {
      this.fetchPhase();
    }
    // 切换了基础表数据源
    if (baseDId) {
      this.reloadAllGroup();
    }
  };
  @action('改变单个配置') changeSingleConfig = (changes: any) => {
    runInAction(() => {
      this.config = { ...this.config, ...changes };
      this.baseStore.config = { ...this.baseStore.config, ...this.config };
      const changeKeys = Object.keys(changes);
      if (changeKeys.some(key => key.indexOf('typeStageConfig') > -1)) {
        this.judgeFetch('type');
      }
      if (changeKeys.some(key => key.indexOf('phaseStageConfig') > -1)) {
        this.judgeFetch('phase');
      }
      if (changeKeys.some(key => key.indexOf('baseStageConfig') > -1)) {
        this.judgeFetch('base', '', changeKeys);
      }
      if (changeKeys.some(key => key.indexOf('pageSize') > -1) || changeKeys.some(key => key.indexOf('pageMode') > -1)) {
        // 改变配置自动发起 清除数据 触发一次看板数据请求
        this.resetGroup();
      }
      if (changeKeys.some(key => key.indexOf('statistics') > -1)) {
        // 改变统计配置
        const hasValidData = (changes?.statistics?.statisticsItem || []).every(
          (i: any) => i.inputValue?.valueLocale && !isEmpty(i.eBFilterData)
        );
        if (hasValidData) {
          this.reloadBaseBoard('changeKeys-statistics');
        }
      }
    });
  };
  @action handleRefreshFilter = (filters: any) => {
    this.filterValues = filters;
    // 没有分组 重新获取
    if (this.baseStore.laneGroups.every(i => (i.groups || []).length === 0)) {
      this.fetchPhase();
      return;
    }
    this.reloadBaseBoard('handleRefreshFilter');
  };
  @action setPageFilter = (filters: any) => {
    this.pageFilter = filters;
    this.resetTypes();
    this.fetchType();
  };
  // 判断是否要请求
  @action judgeFetch = (type: string, phaseId?: string, changeKey?: string[]) => {
    const config = this.config;
    const dataset = config[`${type}StageConfig_dataset`];
    if (!isEmpty(dataset)) {
      switch (type) {
        case 'type':
          this.fetchType();
          break;
        case 'phase':
          this.fetchPhase();
          break;
        case 'base':
          if (
            changeKey?.includes(`baseStageConfig_dataset`) ||
            changeKey?.includes(`baseStageConfig_card`) ||
            changeKey?.includes(`baseStageConfig_dataFilter`) ||
            changeKey?.includes(`baseStageConfig_sortField`) ||
            changeKey?.includes(`statistics`) ||
            changeKey?.includes(`baseStageConfig_phaseMappingField`)
          ) {
            // 改变配置自动发起 触发一次看板数据请求
            if (config.baseStageConfig_dataset?.id && config.baseStageConfig_card && config.baseStageConfig_phaseMappingField) {
              // 特殊处理卡片配置 因为存在缓存 需要重新渲染
              if (changeKey?.includes(`baseStageConfig_card`)) {
                this.reloadAllGroup();
              } else {
                this.reloadBaseBoard('judgeFetch-base');
              }
            } else {
              this.reloadAllGroup();
            }
            return;
          }
          // 未完成分类和阶段的初始化 不加载
          if (!this.isInitial) {
            return;
          }
          // 分组列表自动发起（懒加载）
          this.fetchData(phaseId!);
          break;
        default:
          break;
      }
    }
  };
  fetchType = async () => {
    if (isEmpty(this.baseStore)) return;
    if (!this.config.typeStageConfig_field) {
      this.resetTypes();
      return;
    }
    const dataset = this.config.typeStageConfig_dataset;
    try {
      this.typeLoading = true;
      const suffix = this.isDesign ? 'previewType' : 'getType';
      const response = await ajax({
        url: `/api/ebuilder/coms/phaseBoard/${suffix}`,
        method: 'POST',
        data: format2FormData({
          ...this.baseParams,
          extParam: this.getExtPrams(dataset),
        }),
      });
      runInAction(() => {
        this.types = response.list ?? [];
        // 无分组 清空阶段和所有看板数据
        if (isEmpty(this.types)) {
          this.resetTypes();
          return;
        }
        this.selectedType = this.types[0].id;
        this.judgeFetch('phase');
      });
    } catch (err) {
    } finally {
      runInAction(() => (this.typeLoading = false));
    }
  };
  fetchPhase = async () => {
    // 联动条件满足
    const typeMappingField = this.config.phaseStageConfig_typeMappingField;
    if (!typeMappingField) {
      this.baseStore.resetLane();
      this.reloadBaseBoard('fetchPhase');
      return;
    }
    if (!this.selectedType) return;
    const dataset = this.config.phaseStageConfig_dataset;
    try {
      this.baseStore.resetLane();
      this.baseStore.setState({ paginationData: {} });
      const suffix = this.isDesign ? 'previewPhaseByType' : 'getPhaseByType';
      const response = await ajax({
        url: `/api/ebuilder/coms/phaseBoard/${suffix}`,
        method: 'POST',
        data: format2FormData({
          ...this.baseParams,
          extParam: this.getExtPrams(dataset, {
            typeId: this.selectedType,
          }),
        }),
      });
      runInAction(() => {
        const groups = response.list.map((group: any) => {
          return {
            id: group.id,
            name: group.phase_board_title,
            type: 0,
            cards: [],
          };
        });
        this.baseStore.localUpdateGroups(groups);
        if (!isEmpty(groups)) {
          this.isInitial = true;
          this.reloadBaseBoard('fetchPhase');
        }
      });
    } catch (err) {
    } finally {
      runInAction(() => this.baseStore.setState({ loading: false }));
    }
  };
  fetchData = async (phaseId: string, loadMore?: boolean) => {
    const dataset = this.config.baseStageConfig_dataset;
    const cardConfig = this.config.baseStageConfig_card;
    const phaseMappingField = this.config.baseStageConfig_phaseMappingField;
    const typeField = this.config.typeStageConfig_field;
    const phaseField = this.config.phaseStageConfig_field;
    // 联动条件满足
    if (!phaseId || !typeField || !phaseField) return;
    // 自身条件是否满足
    if (!dataset || !cardConfig || !phaseMappingField) {
      // 重置当前列表
      this.resetGroup(phaseId);
      return;
    }
    const { current = 1, hasMore = true, isLoading } = this.pageInfo[phaseId] || {};
    if ((loadMore && !hasMore) || isLoading) return;
    const fetcher = () => {
      const suffix = this.isDesign ? 'previewData' : 'getData';
      return ajax({
        url: `/api/ebuilder/coms/phaseBoard/${suffix}`,
        method: 'POST',
        data: format2FormData({
          ...this.baseParams,
          extParam: this.getExtPrams(dataset, {
            typeId: this.selectedType,
            phaseId,
            searchConditions_new: this.filterValues,
            pageFilter: this.pageFilter,
          }),
          pageSize: this.baseStore.pageSize || 10,
          pageNo: loadMore ? current + 1 : 1,
        }),
      });
    };
    try {
      this.baseStore.dealWithFetchData(phaseId, loadMore, fetcher, DEFAULT_LANE_ID, () => {
        this.baseStore.setState({ loading: false });
      });
    } catch (error) {}
  };
  // 改类型
  @action changeType = (type: string) => {
    this.selectedType = type;
    this.fetchPhase();
  };
  @action('同步分页信息') syncPageInfo = (pageInfo: any) => {
    this.pageInfo = pageInfo;
  };

  @action setBaseStore = (store: any) => {
    runInAction(() => {
      this.baseStore = store;
      for (let i in this.config) {
        if (store.config.hasOwnProperty(i)) {
          this.config[i] = store.config[i];
        }
      }
      // 触发一次更新
      this.judgeFetch('type');
    });
  };
}

export default ProjectStageStore;
