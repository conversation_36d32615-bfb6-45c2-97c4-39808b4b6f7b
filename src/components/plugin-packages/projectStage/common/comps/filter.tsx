import { DesignComProps } from '../.././../../baseBoard/types';
import { PluginFilterSet } from '../../../common';
import '../../../common/index.less';

export interface PsFilterSetProps extends DesignComProps {
  [x: string]: any;
}

const StageFilterSet = (props: PsFilterSetProps) => {
  return (
    <PluginFilterSet weId={`${props.weId || ''}_rz3wbz`} {...props} showMenu={[, 'quickFilters']} hideCommonSearchType={['1']} />
  );
};
export default StageFilterSet
