import { useEffect, useState } from 'react';
import DataSet from '../../../../common/DataSet';
import { DesignComProps } from '../../../../baseBoard/types';
import { defaultStateConfig, INITIAL_PHASE_CONFIG, INITIAL_BASE_CONFIG } from '../../design/constants';

export interface PsDataSetProps extends DesignComProps {
  [x: string]: any;
}
const ProjectStageDataSet = (props: any) => {
  const { id, config, onConfigChange, onChange } = props;
  const [value, setValue] = useState({} as any);

  useEffect(() => {
    const defaultValue = config[id as keyof typeof config];
    setValue(defaultValue);
  }, [config, id]);

  const handleChange = (changes: any, ...args: any[]) => {
    setValue(changes);
    let configVal = {} as any;
    // 改变权限不需要重置
    const isChangePer =
      value?.id === changes?.id &&
      value?.ebuilderDataConfig?.dataSetup?.dataPermission !== changes?.ebuilderDataConfig?.dataSetup?.dataPermission;
    if (isChangePer) {
      onChange(changes);
      return;
    }
    switch (id) {
      case 'typeStageConfig_dataset':
        configVal = {
          ...defaultStateConfig(),
          [id]: changes,
        };
        break;
      case 'phaseStageConfig_dataset':
        configVal = {
          ...INITIAL_PHASE_CONFIG(),
          ...INITIAL_BASE_CONFIG(),
          [id]: changes,
        };
        break;
      case 'baseStageConfig_dataset':
        configVal = {
          ...INITIAL_BASE_CONFIG(),
          [id]: changes,
          // 必须更新数据源 筛选有用
          dataset: changes,
        };
        break;
      default:
        break;
    }
    onConfigChange && onConfigChange(configVal);
  };
  return <DataSet weId={`${props.weId || ''}_azo8l0`} {...props} onChange={handleChange} value={value} showLogicData={false} />;
};
export default ProjectStageDataSet;
