@import (reference) '../../../../../style/prefix.less';

.@{ebdBClsPrefix}-projectStage-projectType {
  width: 100%;
  max-height: 120px;
  overflow-y: scroll;
  &.m{
    & > div{
      padding: 4px 0;
    }
  }

  &>div {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 16px 0;
    gap: 10px;

    .ui-menu-list-item {
      max-width: 160px;
      cursor: pointer;
      transition: all .3s;
      color: #646A73;
      background-color: transparent;
      height: 28px;

      &:hover {
        color: var(--primary);
        border-color: transparent;
        background-color: var(--tag-primary-bg-color);
      }

      &.ui-menu-list-item-active {
        color: var(--primary);
        border-color: transparent;
        background-color: var(--tag-primary-bg-color);
        &:after{
          background-color: transparent;
        }
      }
    }

    .ui-menu-secondtab-new {
      border-bottom: none;
    }
  }

  &-item {
    max-width: 160px;
    font-size: var(--font-size-14);
    cursor: pointer;
    transition: all .3s;
    color: var(--main-fc);

    &:hover,
    &.primary {
      color: var(--primary);
    }
  }
}