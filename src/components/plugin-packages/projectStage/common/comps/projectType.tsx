import { useMemo, useCallback } from 'react';
import { Spin, Menu } from '@weapp/ui';
import { ebdBClsPrefix } from '../../../../../constants';
import './index.less';

export interface ProjectTypeProps {
  types?: ProjectTypeItemProps[];
  loading?: boolean;
  isMobile?: boolean;
  selectedType?: string;
  onChange?: (type: string) => void;
  weId?: string;
}
export interface ProjectTypeItemProps {
  id: string;
  name: string;
  [x: string]: any;
}

const cls = `${ebdBClsPrefix}-projectStage-projectType`;
const ProjectType = (props: ProjectTypeProps) => {
  const { loading, types = [], selectedType = '', onChange, isMobile } = props;

  const menuData = useMemo(() => {
    return types.map(i => ({ ...i, content: i.phase_board_title }));
  }, [types]);

  const renderMenus = useCallback(() => {
    return (
      <Menu
        weId={`${props.weId || ''}_1f6yrs`}
        value={selectedType}
        data={menuData}
        type="secondtab"
        overflowType="more"
        onChange={onChange}
        secondtabType='new'
      />
    );
  }, [menuData, selectedType, props.weId, onChange]);

  if (!types.length && !loading) return <div />;

  return (
    <div className={`${cls} ${isMobile ? 'm' : ''}`}>
      {loading && <Spin weId={`${props.weId || ''}_agvf32`} size="small" />}
      {!loading && types.length ? <div>{renderMenus()}</div> : null}
    </div>
  );
};
export default ProjectType;
