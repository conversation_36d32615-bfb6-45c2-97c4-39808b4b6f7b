import { DataSet } from '@weapp/ebdcoms';
import { isEmpty } from '@weapp/utils';
import { SourceType, GroupType } from '../../../constants/common';
// 业务模块-eb应用数据源
export const isEbData = (dataset?: DataSet) => dataset && dataset.type === SourceType.LOGIC && dataset.groupId === 'weaver-ebuilder-app-service';
// 业务模块数据源
export const isLogicData = (dataset?: DataSet) => dataset && dataset.type === SourceType.LOGIC;
// 数仓数据源
export const isEteamsData = (dataset?: DataSet) => dataset && dataset.type === SourceType.ETEAMS;

// 业务模块-eb表单业务数据源
export const isEbBusinessFormData = (dataset?: DataSet) => dataset
  // && dataset.type === SourceType.LOGIC
  && dataset.groupId === 'weaver-ebuilder-form-service';


// 是否为eb表单数据源 新版
export const isEbFormDataV2 = (config: any = {}) => {
  const { dataset = {} } = config
  if (isEmpty(config) || isEmpty(dataset)) {
    return true
  }
  // return isEbBusinessFormData(dataset) || dataset?.type === 'FORM' || config?.fromEbuilder
  return dataset?.type === 'FORM' || config?.fromEbuilder
}

// 判断是否为非EB表单数据源下的自定义分组
export const isUnEbDataSetCustomOptions = (dataset: any, groupType: string) => (isEteamsData(dataset) || isLogicData(dataset)) && `${groupType}` === GroupType.custom;