/**
 * <AUTHOR>
 * @createTime 2021-11-15
 */
import { CorsComponent, utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { DataSetItem, DataSetProps } from './types';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

const { isEqual } = utils;
export default class DataSet extends PureComponent<DataSetProps> {
  onChange = (val: DataSetItem) => {
    const { value, onChange } = this.props;
    if (!isEqual(val, value)) {
      onChange?.(val);
    }
  };

  render() {
    const { value, showLogicData = true } = this.props;
    const localEteamsDataTag = localStorage.getItem(`${ebdBClsPrefix}_EteamsData`)
    const localCustomDataTag = localStorage.getItem(`${ebdBClsPrefix}_CustomData`)
    const localBizDataTag = localStorage.getItem(`${ebdBClsPrefix}_BizData`)
    const localExternalDataSetTag = localStorage.getItem(`${ebdBClsPrefix}_ExternalDataSet`)
    const localExternalDataTag = localStorage.getItem(`${ebdBClsPrefix}_ExternalData`)
    const localdsDataTag = localStorage.getItem(`${ebdBClsPrefix}_LocaldsData`)
    let showEteamsData = localEteamsDataTag === '1'
    let showCustomData = localCustomDataTag === '1'
    let showBizData = localBizDataTag === '1'
    let showExternalDataSet = localExternalDataSetTag === '1'
    let showExternalData = localExternalDataTag === '1'
    let showLocalds = localdsDataTag === '1'
    return (
      <div className={`${ebdBClsPrefix}-dataset-config`}>
        <CorsComponent
          weId={`${this.props.weId || ''}_3766hu`}
          app="@weapp/ebdcoms"
          compName="DataSetView"
          showBizData={showBizData} // 显示标准业务数据
          showCustomData={showCustomData} // 显示自定义数据
          showEteamsData={true} // 显示数据仓库数据,默认显示
          showLogicData={showLogicData} // 业务模块
          showExternalDataSet={showExternalDataSet} // 数据集合
          showExternalData={showExternalData} // 外部数据源
          showLocalds={showLocalds} // 数据集
          placeholder={getLabel('57052', '选择数据源')}
          {...this.props}
          value={value?.id ? value : undefined}
          onChange={this.onChange}
        />
      </div>
    );
  }
}
