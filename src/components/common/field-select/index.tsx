// 通用的选择指定字段组件
// 支持弹窗和选择字段两种形式

import React from 'react';
import { If, Then, Else } from 'react-if';
import { Icon, Button, Dialog, Tag, Spin, Select, AnyObj } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { ebdBClsPrefix, dlgIconName } from '../../../constants';
import BoardEmpty from '../../common/empty';
import { getRsFields } from '../../../utils';
import './index.less';

const cls = `${ebdBClsPrefix}-field-select`;
export type FieldSelectType = 'select' | 'dialog';
export interface FieldSelectProps {
  type?: FieldSelectType;
  filterField?: string[]; // 过滤字段
  value?: any;
  dataset?: any;
  fieldsMap?: any[];
  onChange?: (fieldId: string, selectedField: AnyObj) => void;
  onClose?: () => void;
}
const diffKey = 'FieldSelect';
export default class FieldSelect extends React.PureComponent<FieldSelectProps> {
  state = {
    fieldsMap: [] as any[],
    value: '',
    selectedField: {} as AnyObj,
    loading: true,
  };
  componentDidMount() {
    if (!isEmpty(this.props.fieldsMap)) {
      this.setState({ fieldsMap: this.setFieldsMap(this.props.fieldsMap!), loading: false });
    } else {
      this.handleGetRsFields();
    }
    this.setState({ value: this.props.value });
  }
  componentWillReceiveProps(nextProps: Readonly<FieldSelectProps>) {
    if (nextProps.dataset !== this.props.dataset && !nextProps.fieldsMap) {
      this.setState({ fieldsMap: [] });
      this.handleGetRsFields(nextProps);
    }
    if (nextProps.value !== this.props.value) {
      this.setState({ value: nextProps.value });
    }
  }
  setFieldsMap = (fieldsMap: any[]) => {
    return [
      {
        id: '-1',
        content: getLabel('40502', '请选择')
      },
      ...fieldsMap
    ]
  }
  handleGetRsFields = async (props = this.props) => {
    const { dataset, filterField = [] } = props;
    if (!dataset || !dataset.id) {
      this.setState({ loading: false });
      return;
    }
    getRsFields({
      dataset,
      filterField,
      mulit: true,
      cb: (_fields: any[]) => {
        this.setState({ fieldsMap: this.setFieldsMap(_fields), loading: false });
      }
    });
  };
  setIcon = (hasValue: boolean, clickFunc?: () => void) => {
    return <Icon weId={`${this.props.weId || ''}_78j2nl`} name={`${hasValue ? 'Icon-set-up01' : 'Icon-set-up-o'}`} onClick={clickFunc} />;
  };
  onConfirm = () => {
    const { onChange } = this.props;
    let { selectedField, value } = this.state;
    this.close();
    onChange && onChange(value, selectedField);
  };
  close = () => {
    const { onClose } = this.props;
    onClose && onClose();
  };
  setShowField = (field: any) => {
    const fieldItem = field.id === '-1' ? null : this.state.fieldsMap.find(i => i.id === field.id)
    const value = field.id === '-1' ? '' : field.id
    this.setState({ value, selectedField: fieldItem }, () => {
      // 下拉切换后立即生效
      if (this.props.type === 'select') {
        this.onConfirm();
      }
    });
  };
  renderDialog = () => {
    const { value, loading } = this.state;
    const { fieldsMap } = this.state;
    // 与后端lzh沟通 不处理id和标题
    // const data = getTargetFieldsByType(relFields, EtComponentKey.Text).filter(i => `${i.id}` !== '5');
    const data = fieldsMap;
    let buttons = [
      data && data.length > 0 ? (
        <Button weId={`${this.props.weId || ''}_6dgyna@${diffKey}`} key="save" type="primary" onClick={this.onConfirm}>
          {getLabel('221901', '确定')}
        </Button>
      ) : (
        ''
      ),
      <Button weId={`${this.props.weId || ''}_xm2ol6@${diffKey}`} key="onCancel" onClick={this.close}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    return (
      <Dialog
        weId={`${this.props.weId || ''}_6io6lz`}
        title={getLabel('277036', '分组标题设置')}
        footer={buttons}
        width={500}
        destroyOnClose
        visible
        mask
        closable
        onClose={this.close}
        icon={dlgIconName}
      >
        <div className={`${cls}`}>
          <If weId={`${this.props.weId || ''}_27ye8f`} condition={loading}>
            <Then weId={`${this.props.weId || ''}_g0ajn9`}>
              <div className={`${cls}-spin`}>
                <Spin weId={`${this.props.weId || ''}_vmlog8`} />
              </div>
            </Then>
            <Else weId={`${this.props.weId || ''}_hfhleg`}>
              <If weId={`${this.props.weId || ''}_27ye8f`} condition={data && data.length > 0}>
                <Then weId={`${this.props.weId || ''}_pi3mio`}>
                  {data.map(item => {
                    return (
                      <div key={item.id} className={`${cls}-tag`} onClick={() => this.setShowField(item)}>
                        <Tag weId={`${this.props.weId || ''}_7r9eql@${diffKey}`} type={value === item.id ? 'primary' : 'default'}>
                          {item.content}
                        </Tag>
                      </div>
                    );
                  })}
                </Then>
                <Else weId={`${this.props.weId || ''}_hpwh0x`}>
                  <div className={`${cls}-empty`}>
                    <BoardEmpty weId={`${this.props.weId || ''}_4whwnu`} />
                  </div>
                </Else>
              </If>
            </Else>
          </If>
        </div>
      </Dialog>
    );
  };
  renderSelect = () => {
    const { value, loading } = this.state;
    const { fieldsMap } = this.state;
    if (loading) {
      return (
        <div className={`${cls}-inlineSpin`}>
          <Spin weId={`${this.props.weId || ''}_hd3zdv`} size="small" />
        </div>
      );
    }
    return (
      <Select
        weId={`${this.props.weId || ''}_2zhadk`}
        data={fieldsMap}
        value={value}
        onChange={value => this.setShowField({ id: value })}
        style={{width: '100%'}}
      />
    );
  };
  render() {
    const { type = 'select' } = this.props;
    if (type === 'dialog') {
      return this.renderDialog();
    } else {
      return this.renderSelect();
    }
  }
}
