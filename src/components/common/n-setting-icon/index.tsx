import React from 'react';
import { Icon, IconNames } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';
interface NSettingIconProps extends React.AriaAttributes {
  weId?: string;
  loading?: boolean
  hasValue?: boolean
  iconName?: IconNames
  onClick?: () => void
  clearable?: boolean
  clearFunc?: () => void
}
const NSettingIcon = (props: NSettingIconProps) => {
  const { loading, hasValue, onClick, iconName, clearable, clearFunc } = props;
  if (loading) {
    return <Icon weId={`${props.weId || ''}_hezrqh`} name="Icon-Loading" spin moduleColor='blue' />;
  }
  return <span className={`${ebdBClsPrefix}-n-setting-icon`}>
    <Icon weId={`${props.weId || ''}_tc7njv`} name={`${hasValue ? 'Icon-set-up01' : 'Icon-set-up-o'}` || iconName} onClick={onClick} />
    {clearable && hasValue ? <span className={`${ebdBClsPrefix}-n-setting-icon-clear`} onClick={() => clearFunc?.()}>{getLabel('-1', '清空')}</span> : null}
  </span>;
};

export default NSettingIcon;
