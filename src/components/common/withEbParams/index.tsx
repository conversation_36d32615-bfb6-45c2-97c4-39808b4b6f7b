import { utils } from '@weapp/ui';
import { IReactComponent } from '@weapp/utils/lib/tools/middleware';
import React, { Component } from 'react';
import ebdcoms from '../../../utils/ebdcoms'

const { isEmpty } = utils;
type IRc = IReactComponent<any>;
/**
 * @function withEbParams
 * <AUTHOR>
 * @desc 崔帅那边使用window存储条件参数，pageId作为key，因此相同pageId页面存在脏数据
 *       加一个withEbParams函数，获取ebParams保存到内部state,再执行清除clearEbData
 * @return HOC function
 * */
/**
 * dataSource的params固定值解析成key：value
 * params : Array<{name: string, type: string, value: string}>
 * @returns {key:value}
 */
export const getFixedObj = (params: Array<{
  name: string;
  type: string,
  value: string,
  nameType?:string
}> = []) => params.reduce((pre, cur) => ({ ...pre, [cur.name]: cur.value }), {});
// 按钮那边没返回type，因此先把所有都拼接上去
// .filter(({ type, nameType }) => type === 'fixed' || nameType === 'fixed')

/**
 * 删除缓存数据
 */
export function clearEbData(pageId: string) {
  const { clearEbParams } = ebdcoms.get();
  clearEbParams('pageParams', pageId);
}
/**
 * 获取高级试图条件参数params
 * @param pageId
 * @returns
 */
export function getParams(pageId: string) {
  const params = getEbData(pageId, 'params') || [];
  const otherParams = getEbData(pageId, 'otherParams') || {};
  const urlParams = getEbData(pageId, 'urlParams') || {};
  return { ...getFixedObj(params), ...otherParams,...urlParams };
}
/**
 * 获取从pageView传来的参数
 * @param key condition||params 获取的参数key
 * @returns
 */
export function getEbData(pageId: string, key: 'condition' | 'params' | 'otherParams' | 'urlParams') {
  try {
    const { getEbParams } = ebdcoms.get();
    const ebParams = getEbParams?.('pageParams', pageId) || {};
    return ebParams[key];
  } catch (e) {}
}

const withEbParams = (WrpComponent: IRc): any => class EbParamsWrap extends Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ebParams: null,
    };
  }

  setEbParams(ebParams: any) {
    this.setState({ ebParams });
  }

  componentDidMount() {
    const ebParams = getParams(this.props.pageId) || {};
    this.setEbParams(ebParams);
    // if (!isEmpty(ebParams)) {
    //   clearEbData(this.props.pageId);
    // }
  }

  render() {
    const { ebParams } = this.state;
    if (!ebParams) {
      return <></>;
    }
    return (
      <WrpComponent
        weId={`${this.props.weId || ''}_pmaflj`}
        {...this.props}
        ebParams={ebParams}
      />
    );
  }
};

export default withEbParams;
