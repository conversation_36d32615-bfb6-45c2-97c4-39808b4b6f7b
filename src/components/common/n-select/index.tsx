import { useMemo } from 'react';
import { Select, SelectProps, Icon } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';

interface NSelectProps extends SelectProps {
  weId?: string;
  targetValue?: any; // 依赖外部字段
  loading?: boolean;
}
const NSelect = (props: NSelectProps) => {
  const { data, targetValue, loading } = props;

  const inputIcon = useMemo(() => {
    if ('targetValue' in props ? !isEmpty(targetValue) && isEmpty(data) : 'loading' in props ? loading : isEmpty(data)) {
      return <Icon weId={`${props.weId || ''}_hezrqh`} name="Icon-Loading" spin />;
    }
    return <Icon weId={`${props.weId || ''}_pb0l1q`} name="Icon-Down-arrow01" />;
  }, [data, targetValue]);

  return <Select weId={`${props.weId || ''}_aq61dj`} {...props} inputIcon={inputIcon} />;
};

export default NSelect;
