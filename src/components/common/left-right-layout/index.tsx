import { AnyObj, Layout, CorsComponent } from '@weapp/ui';
import { middleware } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import { appName, ebdBClsPrefix } from '../../../constants';
import './index.less';

const layoutPrefix = `${ebdBClsPrefix}-left-right-layout`;

interface LeftRightLayoutProps {
  hasLeftGroup?: boolean;
  leftWidth?: number;
  leftMenuDatas?: any[];
  dfSelectKeys?: AnyObj;
  searchParams?: AnyObj;
  leftOnChange?: (data: any) => void;
}

@middleware(appName, 'LeftRightLayout')
@observer
export default class LeftRightLayout extends PureComponent<LeftRightLayoutProps & Attributes> {
  render(): React.ReactNode {
    const {
      hasLeftGroup = false,
      leftWidth = 200,
      leftMenuDatas = [],
      leftOnChange,
      dfSelectKeys = {},
      searchParams = {},
      children,
    } = this.props;
    return (
      <Layout weId={`${this.props.weId || ''}_z744jx`} className={`${layoutPrefix}`}>
        {hasLeftGroup && (
          // 分类搜索左侧
          <Layout.Box weId={`${this.props.weId || ''}_g193ku`} className={`${layoutPrefix}-left`} type="side" width={leftWidth}>
            <CorsComponent
              weId={`${this.props.weId || ''}_8ayp0f`}
              app="@weapp/ebdfpage"
              compName="pcComps.GroupSearchLeftCom"
              dfSelectKeys={toJS(dfSelectKeys)}
              data={toJS(leftMenuDatas)}
              searchParam={toJS(searchParams)}
              onChange={leftOnChange}
            />
          </Layout.Box>
        )}
        <Layout.Box weId={`${this.props.weId || ''}_zjic59`} className={`${layoutPrefix}-right`}>
          {children}
        </Layout.Box>
      </Layout>
    );
  }
}
