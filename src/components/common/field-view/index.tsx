import { DataSet } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { ReactNode } from 'react';

type configType = {
  dataset: DataSet
  mode?: string;
  field?:any[]
}

interface FieldViewProps{
  config: configType,
  value: any,
  onChange: (val: any) => void;
  externalElement?: (config: any, showModalFC: () => void) => ReactNode;
  otherParams?: any
}

export default class FieldView extends React.PureComponent<FieldViewProps> {
  render() {
    const {
      config, value, onChange, externalElement, otherParams = {},
    } = this.props;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_30wszt`}
        app="@weapp/ebdcoms"
        compName="FieldView"
        type="field"
        config={config}
        placeholder={getLabel('87540', '点击设置字段')}
        // value={toJS(config?.field || value)}
        value={[]}
        onChange={onChange}
        externalElement={externalElement}
        {...otherParams}
      />
    );
  }
}
