import React from 'react';
import FieldView from '.';

export default class ListShowFieldsCom extends React.PureComponent<any> {
  onChange = (val: any) => {
    const { onChange } = this.props;
    onChange(JSON.stringify(val));
  };

  render() {
    const { value, config } = this.props;
    const newVal = value ? JSON.parse(value) : [];
    return (
      <FieldView
        weId={`${this.props.weId || ''}_wgl49n`}
        config={{ dataset: config.dataset }}
        value={newVal}
        onChange={this.onChange}
      />
    );
  }
}
