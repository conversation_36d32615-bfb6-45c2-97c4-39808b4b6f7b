import { Empty, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { IReactComponent } from '@weapp/utils/lib/tools/middleware';
import { observer } from 'mobx-react';
import React from 'react';
import { ebdBClsPrefix } from '../../../constants';
import './style/index.less';

interface EmptyElemProps extends React.Attributes {
  title: string;
  description?: string;
}

const EmptyElem = (props: EmptyElemProps) => {
  const { title, description } = props;
  return (
    <div className={`${ebdBClsPrefix}-no-permission`}>
      <Empty
        weId={`${props.weId || ''}_iikm1f`}
        title={title || getLabel('77125', '请求异常，请联系管理员')}
        description={description}
        image={
          <Icon
            weId={`${props.weId || ''}_sq9eq6`}
            name={title ? 'Icon-empty-No-permission' : 'Icon-Loading-failed'}
            style={{ width: '200px', height: '200px' }}
          />
        }
      />
    </div>
  );
};
type IRc = IReactComponent<any>;
/**
 * @function withAuthorized
 * <AUTHOR>
 * @desc 装饰器 根据store中的responseError字段判断页面权限，是否授权进入
 * @param storeName [string] store的名称
 * @return HOC function
 * */

const withAuthorized = (storeName: string): any => (WrpComponent: IRc) => observer((props: any) => {
  try {
    const store = props[storeName];
    const { responseError, loading = false } = store!;
    if (responseError && !loading) {
      return (
        <EmptyElem
          weId={`${props.weId || ''}_xvsazb`}
          title={responseError?.message}
          description={responseError?.description}
        />
      );
    }
  } catch {
    return (
      <EmptyElem
        weId={`${props.weId || ''}_8hp9it`}
        title=""
      />
    );
  }
  return <WrpComponent weId={`${props.weId || ''}_4pc8tj`} {...props} />;
});

export default withAuthorized;
