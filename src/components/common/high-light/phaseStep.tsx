import { FC } from 'react';
import { getLabel } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../constants';
import { GroupItem } from '../board/types';
import { invoke } from '../../../utils';
import { EbdBPlKeys } from '../../../types/common';
import "./index.less";

interface IPhaseStepProps {
  weId: string;
  customtitle?: string | React.ReactNode;
  bgColor?: string;
  item?: any;
  onClick?: () => void;
  hideFirstArrow?: boolean; // 是否隐藏第一个元素左侧箭头
  hideLastArrow?: boolean; // 是否隐藏第最后个元素右侧箭头
  index: number;
  groups?: GroupItem[]
  pluginCenter?: any
}

const cls = `${ebdBClsPrefix}-phaseStep`;

const PhaseStep: FC<IPhaseStepProps> = props => {
  const { customtitle, onClick = () => { }, hideFirstArrow, index = 0, hideLastArrow, groups = [], pluginCenter, item } = props;
  const { color } = item;
  const groupsLen = groups.length
  const renderContent = (item: any, index: number, groups: GroupItem[]) => {
    return <div className={`${cls}-text`}>{customtitle || item.title || item.name}</div>
  }
  return (
    <div
      className={`${cls} ${color ? 'hasBgColor' : ''} ${hideFirstArrow && index === 0 ? `hideFirstArrow` : ''} ${hideLastArrow && index === groupsLen -1 ? `hideLastArrow` : ''}`}
      style={{
        backgroundColor: color,
        ['--arrow-color' as string]: color,
      }}
      onClick={onClick}
    >
      {pluginCenter ? invoke(pluginCenter, EbdBPlKeys.renderLaneBoardTitleContent, { hook: renderContent, args: [item, index, groups] }) : renderContent(item, index, groups)}
    </div>
  );
};

export default PhaseStep;
