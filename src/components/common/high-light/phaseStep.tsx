import { FC, useMemo } from 'react';
import { Popover } from '@weapp/ui';
import { ebdBClsPrefix } from '../../../constants';
import { GroupItem } from '../board/types';
import { invoke } from '../../../utils';
import { EbdBPlKeys } from '../../../types/common';
import { dealGroupCountData } from '../../baseBoard/comp/board-view/core/utils';
import './index.less';

interface IPhaseStepProps {
  weId: string;
  customtitle?: string | React.ReactNode;
  bgColor?: string;
  item?: any;
  onClick?: () => void;
  hideFirstArrow?: boolean; // 是否隐藏第一个元素左侧箭头
  hideLastArrow?: boolean; // 是否隐藏第最后个元素右侧箭头
  index: number;
  groups?: GroupItem[];
  pluginCenter?: any;
}

const cls = `${ebdBClsPrefix}-phaseStep`;

const PhaseStep: FC<IPhaseStepProps> = props => {
  const { customtitle, onClick = () => {}, hideFirstArrow, index = 0, hideLastArrow, groups = [], pluginCenter, item } = props;
  const { color } = item;
  const groupsLen = groups.length;

  const groupCountData = dealGroupCountData(item?.groupCount);

  const popup = useMemo(() => {
    return <div style={{ lineBreak: 'anywhere' }}>{groupCountData.name}</div>;
  }, [groupCountData]);

  const renderContent = (item: any, index: number, groups: GroupItem[]) => {
    let statItem = null;
    if (groupCountData?.showgroup) {
      statItem = (
        <Popover weId={`${props.weId || ''}_wxamud`} popup={popup} placement={'top'} popoverType="tooltip">
          <span>{`（${groupCountData?.count}）`}</span>
        </Popover>
      );
    }
    return (
      <div className={`${cls}-text`}>
        {customtitle || item.title || item.name}
        {statItem}
      </div>
    );
  };
  return (
    <div
      className={`${cls} ${color ? 'hasBgColor' : ''} ${hideFirstArrow && index === 0 ? `hideFirstArrow` : ''} ${hideLastArrow && index === groupsLen - 1 ? `hideLastArrow` : ''}`}
      style={{
        backgroundColor: color,
        ['--arrow-color' as string]: color,
      }}
      onClick={onClick}
    >
      {pluginCenter ? invoke(pluginCenter, EbdBPlKeys.renderLaneBoardTitleContent, { hook: renderContent, args: [item, index, groups] }) : renderContent(item, index, groups)}
    </div>
  );
};

export default PhaseStep;
