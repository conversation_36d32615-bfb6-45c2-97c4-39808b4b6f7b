import { FC, useState } from "react";
import { ebdBClsPrefix } from "../../../constants";
import { Button, ColorPicker, List, Popover } from "@weapp/ui";
import HighlightRenderer from "./HighlightRenderer";
import { getLabel } from "@weapp/utils";
import { useBoolean, useMemoizedFn } from "../../../utils/hookUtils";

interface IHighlightSettingProps {
    weId: string;
    bgColor: string;
    content?: string | React.ReactNode;
    customRenderContent?: string | React.ReactNode;
    onConfirm: (bgColor: string) => void;
};

const HighlightSettingClsPrefix = `${ebdBClsPrefix}-highlightSetting`;
/** 显示转换设置 */
const HighlightSetting: FC<IHighlightSettingProps> = (props) => {
    const { content, customRenderContent } = props;
    const [bgColor, setBgColor] = useState(props.bgColor);
    const [innerBgColor, setInnerBgColor] = useState(props.bgColor);
    const [popoverVisible, { toggle: onVisibleToggle }] = useBoolean(false);

    const onConfirm = () => {
        setBgColor(innerBgColor);
        props.onConfirm(innerBgColor);
        onVisibleToggle();
    };

    const onVisibleChange = () => {
        setBgColor(props.bgColor);
        setInnerBgColor(props.bgColor);
        onVisibleToggle();
    };

    const customRenderPreset = useMemoizedFn((item, index) => {
        const onPresetClick = () => {
            setInnerBgColor(item.background);
        };
        return <HighlightRenderer weId={`${props.weId || ''}_ssabqj`}
            key={item.content}
            content={item.content}
            bgColor={item.background}
            onClick={onPresetClick}
        />;
    });

    const popupRender = useMemoizedFn(() => <div className={`${HighlightSettingClsPrefix}-popup-content`}>
        <div className={`${HighlightSettingClsPrefix}-popup-top`}>
            <span>{getLabel('56341', '样式设置')}</span>
        </div>
        <div className={`${HighlightSettingClsPrefix}-popup-preset`}>
            <List weId={`${props.weId || ''}_5vi0yd`}
                data={presetStyles}
                customRenderItem={customRenderPreset}
            />
        </div>
        <div className={`${HighlightSettingClsPrefix}-popup-bgColor`}>
            <span className={`${HighlightSettingClsPrefix}-popup-line-title`}>
                {getLabel('55014', '背景颜色') + ':'}
            </span>
            <ColorPicker weId={`${props.weId || ''}_qylsjb`}
                value={innerBgColor}
                onChange={setInnerBgColor}
                triggerProps={{
                    mask: false
                }}
            />
        </div>
        <div className={`${HighlightSettingClsPrefix}-popup-preview`}>
            <span className={`${HighlightSettingClsPrefix}-popup-line-title`}>
                {getLabel('147338', '效果预览') + ':'}
            </span>
            <HighlightRenderer weId={`${props.weId || ''}_a59h95`}
                content={content || getLabel('54596', '文字')}
                bgColor={innerBgColor}
            />
        </div>
        <div className={`${HighlightSettingClsPrefix}-popup-footer`}>
            <Button weId={`${props.weId || ''}_bzymj3`}
                onClick={onConfirm}
                type='primary'
            >
                {getLabel('221901', '确定')}
            </Button>
            <Button weId={`${props.weId || ''}_38wsfd`}
                onClick={onVisibleChange}
            >
                {getLabel('53937', '取消')}
            </Button>
        </div>
    </div>);

    return <div className={`${HighlightSettingClsPrefix}`}>
        <Popover weId={`${props.weId || ''}_4lc7vx`}
            popup={popupRender}
            visible={popoverVisible}
            onVisibleChange={onVisibleChange}
            action='click'
            placement='top'
            mask
            maskClosable
            className={`${HighlightSettingClsPrefix}-popup`}
        >
            {customRenderContent ? customRenderContent : <HighlightRenderer weId={`${props.weId || ''}_m7xx6s`}
                content={content || getLabel('54596', '文字')}
                bgColor={bgColor}
            /> }
        </Popover>
    </div>;
};

export default HighlightSetting;


export const presetStyles = [
    { fontColor: "#FFFFFFFF", background: "#0099FFFF", borderColor: "", content: "#01" },
    { fontColor: "#FFFFFFFF", background: "#FF5500FF", borderColor: "", content: "#02" },
    { fontColor: "#FFFFFFFF", background: "#FF8C00FF", borderColor: "", content: "#03" },
    { fontColor: "#FFFFFFFF", background: "#0AD37FFF", borderColor: "", content: "#04" },
    { fontColor: "#FFFFFFFF", background: "#EA4335FF", borderColor: "", content: "#05" },
    { fontColor: "#FFFFFFFF", background: "#9933CCFF", borderColor: "", content: "#06" },
    { fontColor: "#FFFFFFFF", background: "#FF6666FF", borderColor: "", content: "#07" },
    { fontColor: "#FFFFFFFF", background: "#0000CCFF", borderColor: "", content: "#08" },
    { fontColor: "#FFFFFFFF", background: "#002966FF", borderColor: "", content: "#09" },
    { fontColor: "#FFFFFFFF", background: "#2DB7F5FF", borderColor: "", content: "#10" },
    { fontColor: "#FFFFFFFF", background: "#666699FF", borderColor: "", content: "#11" },
    { fontColor: "#FFFFFFFF", background: "#FF6600FF", borderColor: "", content: "#12" },
    { fontColor: "#FFFFFFFF", background: "#993399FF", borderColor: "", content: "#13" },
    { fontColor: "#FFFFFFFF", background: "#66CC66FF", borderColor: "", content: "#14" },
    { fontColor: "#FFFFFFFF", background: "#2DB7F5FF", borderColor: "", content: "#15" },
    { fontColor: "#FFFFFFFF", background: "#FF33CCFF", borderColor: "", content: "#16" },
    { fontColor: "#FFFFFFFF", background: "#009966FF", borderColor: "", content: "#17" },
    { fontColor: "#FFFFFFFF", background: "#FF9900FF", borderColor: "", content: "#18" },
    { fontColor: "#FFFFFFFF", background: "#0099CCFF", borderColor: "", content: "#19" },
    { fontColor: "#FFFFFFFF", background: "#FF99CCFF", borderColor: "", content: "#20" },
    { fontColor: "#FFFFFFFF", background: "#FF9966FF", borderColor: "", content: "#21" },
    { fontColor: "#FFFFFFFF", background: "#009999FF", borderColor: "", content: "#22" },
    { fontColor: "#FFFFFFFF", background: "#33CC99FF", borderColor: "", content: "#23" },
    { fontColor: "#FFFFFFFF", background: "#108EE9FF", borderColor: "", content: "#24" },
    { fontColor: "#FFFFFFFF", background: "#CC3366FF", borderColor: "", content: "#25" },
    { fontColor: "#FFFFFFFF", background: "#DF89DFFF", borderColor: "", content: "#26" },
    { fontColor: "#FFFFFFFF", background: "#22A846FF", borderColor: "", content: "#27" },
    { fontColor: "#FFFFFFFF", background: "#CC6600FF", borderColor: "", content: "#28" },
    { fontColor: "#FFFFFFFF", background: "#0099FFFF", borderColor: "", content: "#29" },
    { fontColor: "#FFFFFFFF", background: "#FF6666FF", borderColor: "", content: "#30" },
    { fontColor: "#FFFFFFFF", background: "#FB7835FF", borderColor: "", content: "#31" },
    { fontColor: "#FFFFFFFF", background: "#FF0033FF", borderColor: "", content: "#32" }
];