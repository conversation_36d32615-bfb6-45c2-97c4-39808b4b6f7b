import { FC } from "react";
import { ebdBClsPrefix } from "../../../constants";

interface IHighlightRendererProps {
    weId: string;
    content: string | React.ReactNode;
    bgColor?: string;
    onClick?: () => void;
};

const HighlightRendererClsPrefix = `${ebdBClsPrefix}-highlightRenderer`;

/** 显示转换渲染 */
const HighlightRenderer: FC<IHighlightRendererProps> = (props) => {
    const { bgColor, content, onClick } = props;
    return <div className={`${HighlightRendererClsPrefix}`}
        style={{ backgroundColor: bgColor || 'transparent' }}
        onClick={onClick}
    >
        <span
            className={`${HighlightRendererClsPrefix}-content`}
            style={{ color: bgColor ? '#FFFFFF' : '#000000' }}
        >
            {content}
        </span>
    </div>;
};

export default HighlightRenderer;