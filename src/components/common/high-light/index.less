@import (reference) '../../../style/prefix.less';

.@{ebdBClsPrefix}-phaseStep {
  @stepHeight: calc(var(--hd) * 28);
  @stepArrorwHeight: calc(@stepHeight / 2);
  position: relative;
  color: var(--main-fc);
  font-size: var(--font-size-12);
  height: @stepHeight;
  line-height: @stepHeight;
  cursor: pointer;
  width: 100%;
  border-radius: 1px 2px 2px 1px;//过度不圆滑
  &-text{
    .ellipsis;
  }

  &.hideFirstArrow.hasBgColor::before{
    content: none;
  }
  &.hideLastArrow.hasBgColor::after{
    content: none;
  }

  &.hasBgColor {
    color: white;
    &::before {
      content: ' ';
      display: block;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: @stepArrorwHeight 0 @stepArrorwHeight @stepArrorwHeight;
      // 左侧箭头保持白色
      border-color: transparent transparent transparent #fff;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      margin-left: -1px;
    }
    &::after {
      content: ' ';
      display: block;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: @stepArrorwHeight 0 @stepArrorwHeight @stepArrorwHeight;
      // 使用CSS变量来设置箭头颜色
      border-color: transparent transparent transparent var(--arrow-color);
      position: absolute;
      top: 0;
      right: calc(-1 * @stepArrorwHeight);
      z-index: 2;
      margin-right: 1px;
    }
  }
}