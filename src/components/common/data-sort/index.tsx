import { CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { DataSetItem } from '../DataSet/types';

export interface DataSortProps {
  onChange: (val: any) => void;
  value?: any;
  dataset: DataSetItem;
  btnElmType?: string;
  buttonType?: 'primary' | 'default' | 'success' | 'warning' | 'danger' | 'link';
  checked?: boolean;
  btnElm?: React.ReactNode
  customBtnRender?: (showSortSet: () => void) => React.ReactNode
}

@observer
export default class DataSort extends PureComponent<DataSortProps & React.Attributes> {
  render() {
    const {
      dataset, value, onChange, btnElmType, checked, btnElm, buttonType, customBtnRender
    } = this.props;
    const _value = typeof value === 'string' ? JSON.parse(value) : value;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_8ayp0f`}
        app="@weapp/ebdform"
        compName="FieldSort"
        dataset={dataset}
        data={_value}
        onSure={onChange}
        showType="1" // dialog模式
        btnElmType={btnElmType}
        buttonType={buttonType}
        configType="ebDesigner"
        checked={checked}
        btnElm={btnElm}
        customBtnRender={customBtnRender}
      />
    );
  }
}
