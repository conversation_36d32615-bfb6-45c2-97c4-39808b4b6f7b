import { getLabel } from '@weapp/utils';

/** 多条件后缀 */
export const conditionSuffix = 'condition_type';

/**
 * 网关标识，搜索
 */
export const EbuilderComsModule = 'ebuilder/coms';

export const BIMODULE = 'BI';

/** 禁止filter缓存标识 */
export const FilterNoCacheKey = '_EXCLUDECACHE';
export const INITIAL_PAGE_INFO = (pageSize: number = 1) => ({
  current: 1,
  pageSize,
  total: 0,
  hasMore: true,
  isLoading: true,
  isMoreLoading: false,
});

export const INITIAL_GROUP_INFO = (name: string = getLabel('105298', '未分组')) => ({
  id: '',
  name,
  type: 0,
  cards: [],
});

export const mBoardInfo = 'ebdboard_Info';
export const FAKE_ADD = {
  id: 'fake-add',
  name: getLabel('279287', '新增分组'),
  type: '0',
  cards: [],
};
