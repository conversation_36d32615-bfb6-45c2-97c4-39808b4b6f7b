import { AnyObj, SearchAdvancedStore, SearchDatas, DatePicker, Dialog, MDialog } from '@weapp/ui';
import { dayjs, getLabel, isArray, isObject, forEach, isNumber, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { MultiBtnDataType } from '@weapp/ebdcoms/lib/components/filter-condition/types';
import { DataSet, EbdBPlKeys } from '../../../types/common';
import { DescItemProps } from '../../baseBoard/engine/func-groups/types';
import { RelationGroupsType } from '../../baseBoard/engine/func-groups/diff/constants';
import { BoardConfigData } from '../../baseBoard/types';
import { LaneConfigProps, LaneItemData } from '../../baseBoard/config/cmps/yd/types';
import { FilterNoCacheKey, mBoardInfo } from './constants';
import { GroupType } from '../../../constants/common';
import { UUID, hasRegisterFuncInPlugin, invoke } from '../../../utils';
import { DateOpteLevel, SearchConditions, SearchType, DataSourceValueType, GroupItem } from './types';
import { conditionNullArr, KanBanDefaultBgColor, boardUrlParamsCache, DEFAULT_LANE_ID } from '../../../constants';
import { EtComponentKey, DateDftNumberType, BrowserType, NumberType, SelectType } from '../../../constants/EtComponent';
import ebdcoms from '../../../utils/ebdcoms';
import { BoardPluginCenter } from '../../common/board/types';

const { getServerDateTime } = DatePicker;
export const getIsWhiteColors = (colorVal: any = '#ffffff') => {
  const _colorVal = `${colorVal}`.toLowerCase();
  if (_colorVal.length === 4) {
    return _colorVal === '#fff';
  }
  if (_colorVal.length === 7) {
    return _colorVal === '#ffffff';
  }
  if (_colorVal.length === 9) {
    return _colorVal.substring(0, 7) === '#ffffff' || _colorVal.substring(7, 9) === '00';
  }
  return false;
};

export const getRgbBorderColor = (hex: any = '#ffffff') => {
  if (getIsWhiteColors(hex) || !hex) return '';
  const r = parseInt(`0x${hex.slice(1, 3)}`, 16);
  const g = parseInt(`0x${hex.slice(3, 5)}`, 16);
  const b = parseInt(`0x${hex.slice(5, 7)}`, 16);
  const rgb = `rgb(${r},${g},${b})`;
  return getIsLightColor(r, g, b) ? `rgb(${r - 30},${g - 30},${b - 30})` : rgb;
};

export const getBoardTitleFontColor = (hex: any = '#ffffff') => {
  if (getIsWhiteColors(hex) || !hex) return '#1F2329';
  const r = parseInt(`0x${hex.slice(1, 3)}`, 16);
  const g = parseInt(`0x${hex.slice(3, 5)}`, 16);
  const b = parseInt(`0x${hex.slice(5, 7)}`, 16);
  return getIsLightColor(r, g, b) ? '#1F2329' : 'var(--base-white)';
};

export const getIsLightColor = (r: any, g: any, b: any) => r * 0.299 + g * 0.578 + b * 0.114 >= 192;

// 提供随机颜色
export const getRandomColor = () =>
  `#${Math.floor(Math.random() * 0xffffff)
    .toString(16)
    .padEnd(6, '0')}`;
export const formatMenuConfig = (menu: any) => {
  const deal = (i: any) => {
    return {
      ...i,
      id: i.key,
      content: i.text,
    };
  };
  return menu.map((i: any) => {
    return {
      ...i,
      ...deal(i),
      children: !isValueEmpty(i.children) ? i.children.map((k: any) => deal(k)) : [],
    };
  });
};

/**
 * 处理参数有isPhysical的话加一下
 * @param params
 * @returns
 */
export const addPhysicalParams = (params: any = {}, _dataset?: DataSet) => {
  console.log('_dataset', _dataset);
  if (_dataset && Object.prototype.hasOwnProperty.call(_dataset, 'isPhysical')) {
    params.isPhysical = false;
    // params.isPhysical = _dataset.isPhysical;
  }
  return params;
};
export const getDateAry = (weekStart: number = 0): MultiBtnDataType[] => {
  const today = dayjs().day();
  const diff = weekStart <= today ? weekStart - today : weekStart - today - 7; // 每周开始日调整
  const season = dayjs().quarter(); // 本季度开始日
  return [
    {
      type: 'today',
      title: getLabel('55076', '今天'),
      startDate: dayjs(),
      endDate: dayjs(),
      level: DateOpteLevel.DayLevel,
    },
    {
      type: 'yesterday',
      title: getLabel('54358', '昨天'),
      startDate: dayjs().subtract(1, 'day'),
      endDate: dayjs().subtract(1, 'day'),
      level: DateOpteLevel.DayLevel,
    },
    {
      type: 'tomorrow',
      title: getLabel('55184', '明天'),
      startDate: dayjs().add(1, 'day'),
      endDate: dayjs().add(1, 'day'),
      level: DateOpteLevel.DayLevel,
    },
    {
      type: 'thisWeek',
      title: getLabel('55077', '本周'),
      startDate: dayjs().add(diff, 'day'),
      endDate: dayjs().add(diff, 'day').add(6, 'day'),
      level: DateOpteLevel.DayLevel,
    },
    {
      type: 'lastWeek',
      title: getLabel('55078', '上一周'),
      startDate: dayjs().add(diff, 'day').subtract(7, 'day'),
      endDate: dayjs().add(diff, 'day').add(6, 'day').subtract(7, 'day'),
      level: DateOpteLevel.DayLevel,
    },
    {
      type: 'nextWeek',
      title: getLabel('222261', '下一周'),
      startDate: dayjs().add(diff, 'day').add(7, 'day'),
      endDate: dayjs().add(diff, 'day').add(6, 'day').add(7, 'day'),
      level: DateOpteLevel.DayLevel,
    },
    {
      type: 'thisMonth',
      title: getLabel('222262', '本月'),
      startDate: dayjs().set('date', 1),
      endDate: dayjs()
        .set('month', dayjs().month() + 1)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'lastMonth',
      title: getLabel('206825', '上一月'),
      startDate: dayjs()
        .set('month', dayjs().month() - 1)
        .set('date', 1),
      endDate: dayjs()
        .set('month', dayjs().month() - 1 + 1)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'nextMonth',
      title: getLabel('222263', '下一月'),
      startDate: dayjs()
        .set('month', dayjs().month() + 1)
        .set('date', 1),
      endDate: dayjs()
        .set('month', dayjs().month() + 1 + 1)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'thisSeason',
      title: getLabel('55182', '本季'),
      startDate: dayjs()
        .set('month', (season - 1) * 3)
        .set('date', 1),
      endDate: dayjs()
        .set('month', season * 3)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'lastSeason',
      title: getLabel('222264', '上一季'),
      startDate: dayjs()
        .set('month', (season - 1 - 1) * 3)
        .set('date', 1),
      endDate: dayjs()
        .set('month', (season - 1) * 3)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'nextSeason',
      title: getLabel('206828', '下一季'),
      startDate: dayjs()
        .set('month', (season - 1 + 1) * 3)
        .set('date', 1),
      endDate: dayjs()
        .set('month', (season + 1) * 3)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'thisYear',
      title: getLabel('222265', '今年'),
      startDate: dayjs().set('month', 0).set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() + 1)
        .set('month', 0)
        .set('date', 0),
      level: DateOpteLevel.YearLevel,
    },
    {
      type: 'firstHalfYear',
      title: getLabel('189886', '上半年'),
      startDate: dayjs().set('year', dayjs().year()).set('month', 0).set('date', 1),
      endDate: dayjs().set('year', dayjs().year()).set('month', 6).set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'nextHalfYear',
      title: getLabel('189887', '下半年'),
      startDate: dayjs().set('year', dayjs().year()).set('month', 6).set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() + 1)
        .set('month', 0)
        .set('date', 0),
      level: DateOpteLevel.MonthLevel,
    },
    {
      type: 'lastYear',
      title: getLabel('189888', '去年'),
      startDate: dayjs()
        .set('year', dayjs().year() - 1)
        .set('month', 0)
        .set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() - 1 + 1)
        .set('month', 0)
        .set('date', 0),
      level: DateOpteLevel.YearLevel,
    },
    {
      type: 'nextYear',
      title: getLabel('189889', '明年'),
      startDate: dayjs()
        .set('year', dayjs().year() + 1)
        .set('month', 0)
        .set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() + 1 + 1)
        .set('month', 0)
        .set('date', 0),
      level: DateOpteLevel.YearLevel,
    },
  ];
};
/**
 * 转化pc、mobile运行过滤数据
 * @param value 过滤数据
 * @param isEteams 是否是数仓
 * @returns
 */
export const transformValue = (value: any, isEteams?: boolean) => {
  const changes: any = {};
  Object.keys(value).forEach(k => {
    if (Array.isArray(value[k])) {
      let isBrowser = false;
      value[k].forEach((item: any) => {
        if (isObject(item) && 'id' in item) {
          isBrowser = true;
        }
      });
      if (isBrowser) {
        if (isEteams) {
          changes[k] = value[k].map((item: any) => (item.reportId ? item.reportId : item.id)).join(',');
        } else {
          changes[k] = value[k].map((item: any) => item.id).join(',');
        }
      } else {
        changes[k] = value[k].join(',');
      }
    } else if (isObject(value[k]) && ('min' in value[k] || 'max' in value[k]) && !('id' in value[k])) {
      // @ts-ignore
      changes[k] = [value[k].min, value[k].max].join(',');
    } else {
      changes[k] = value[k];
    }
    changes[k] = changes[k] === ',' ? '' : changes[k];
  });
  return changes;
};
export const getActualDate = (date: string[]) => {
  let value: any[] = [];
  if (isArray(date) && date.length === 3) {
    const primaryAry = getDateAry(1);
    const curDate = primaryAry.find(el => el.type === date[2]);
    if (curDate) {
      value = [
        dayjs(curDate.startDate as any)
          .hours(0)
          .minutes(0)
          .seconds(0)
          .format('YYYY-MM-DD HH:mm:ss'),
        dayjs(curDate.endDate as any)
          .hours(23)
          .minutes(59)
          .seconds(59)
          .format('YYYY-MM-DD HH:mm:ss'),
      ];
    } else {
      value = [date[0], date[1]];
    }
  } else {
    value = date;
  }
  return value;
};

/**
 * 处理字段结构eg：浏览框切换成逗号分隔
 * 1 日期字段处理
 * 2 复用佳敏的transformValue方法处理数组等
 * 3 数值开启选项，取选项的scope
 */
export const formatSearchValue = (fieldId: string, value: any, comConfigParams: { compId: string; config: any; commonFilters: any[] }) => {
  const isEteams: boolean = comConfigParams.config?.dataset?.type === 'ETEAMS';
  // eslint-disable-next-line max-len
  const fieldConfig = comConfigParams.commonFilters.find((f: any) => f.fieldId === fieldId) || {};
  // 高级搜索 人员选择
  if (value[0] === 'chooseEmp' && value.length > 1) {
    let targetList: any = [];
    forEach(value, (item: any) => {
      if (Array.isArray(item)) {
        targetList = item;
      }
    });
    value = targetList;
  }
  if (fieldConfig?.componentKey === EtComponentKey.Employee && value?.length > 1) {
    if (value[1] === '') {
      value = value.splice(0, 1);
    }
  }
  const changes: any = { [fieldId]: value };
  // 日期字段处理
  if (DateDftNumberType.includes(fieldConfig?.componentKey as EtComponentKey)) {
    changes[fieldId] = getActualDate(changes[fieldId]);
  }
  // 转换浏览框等字段
  let newValue = '';
  if (isEteams && BrowserType.includes(fieldConfig?.componentKey as EtComponentKey)) {
    newValue = transformValue(changes, isEteams)?.[fieldId];
  } else {
    newValue = transformValue(changes)?.[fieldId];
  }
  // 数值类型 开启选项
  if (NumberType.includes(fieldConfig.componentKey)) {
    const { listFilterSets = [], optionEnable } = fieldConfig.listFilterDefaultSets;
    if (optionEnable === '1' && newValue) {
      /**
       * 高级搜索返回的数组
       * 顶部筛选返的对象
       * 都兼容一下
       */
      let numOptId = value;
      if (isArray(numOptId)) {
        numOptId = value?.[0];
      } else if (isObject(value)) {
        numOptId = (value as any)?.id;
      }

      const { scope, dateScope, id } = listFilterSets.find((p: any) => p.id === numOptId) || {};
      let _scope = scope;
      if ((id === '999999999999999' || dateScope === 'customNumber') && isObject(value)) {
        _scope = value as any;
      }
      const { min = '', max = '' } = _scope || {};

      newValue = min === '' && max === '' ? '' : `${min},${max}`;
    }
  }
  if ([EtComponentKey.Country, EtComponentKey.Province, EtComponentKey.City, EtComponentKey.District, EtComponentKey.Cascader].includes(fieldConfig.componentKey)) {
    const _value = fieldConfig?.listFilterDefaultSets?.startValue || value;
    newValue = typeof _value === 'string' && _value.startsWith('[') ? JSON.parse(_value) : _value;
  }
  return newValue;
};

export const getCurrentDescByGroupType = (descInfo: any = {}, laneGroupId: string, groupId?: string): DescItemProps | undefined => {
  const _descInfoByLaneGroupId = descInfo;
  if (isEmpty(_descInfoByLaneGroupId) || isEmpty(_descInfoByLaneGroupId.info)) {
    return undefined;
  }
  switch (_descInfoByLaneGroupId?.groupType) {
    case GroupType.custom:
      return (_descInfoByLaneGroupId.info as DescItemProps[]).find(i => `${i.id}` === `${groupId}`);
    case GroupType.field:
    case GroupType.dateRange:
      return _descInfoByLaneGroupId.info as DescItemProps;
    default:
      return undefined;
  }
};
// 去掉“{}”
const removeBraces = (str: string) => {
  if (typeof str === 'string') {
    return str.replace(/[{}]/g, '');
  }
  return str;
};
const handleProgress = (value: number) => {
  let result = '';
  // 是否为0.xxx这种类型的 就是没*100的进度
  function isZeroPointX(num: number) {
    return typeof num === 'number' && num > 0 && num < 1;
  }
  if (value > 100) {
    value = 100;
  }
  if (isZeroPointX(value)) {
    value = value * 100;
  }
  result = value + '%';
  return result;
};
export const getDescText = async (ganttModule: any, descInfo: any, groupId: string, config: any, cards: any[], statFields?: any[]) => {
  if (!descInfo) return '';
  const { descField = {} } = descInfo;
  let text = descInfo?.descText || '';
  // 选中的数据id
  if (descField?.id === '5') {
    return groupId;
  }
  // 处理自定义文本
  const params = {
    isDesign: false,
    config,
    type: 'text',
    content: descField.content,
    field: descField,
    value: {},
  };
  text = await ganttModule.transformDynamicData(params);
  // 处理数字统计
  if (descField && !isEmpty(descField.fieledListMap)) {
    // 有选数据源字段
    descField.fieledListMap.forEach(async (i: any) => {
      // 单独处理数字类型 为分组下所有卡片该数字字段之和
      if (i.type === 'Number') {
        // 是否为按字段分组
        const isFieldMode = `${config.group?.active}` === GroupType.field;
        const currentStatField = statFields?.find((f: any) => f.field === i.id);
        // 算总额 - 如果是按字段分组 且开启了统计按钮 走后端获取，如果是自定义分组，还是前端的逻辑
        let totalValue: any =
          isFieldMode && descField.useStats && currentStatField
            ? +currentStatField?.sum || 0
            : cards.reduce((sum: number, cur: any) => {
                return sum + (Number(cur[i.id]) || 0);
              }, 0);
        if (!isNumber(totalValue)) totalValue = 0;
        switch (i.compType) {
          case 'Money':
            totalValue = totalValue.toFixed(2);
            break;
          case 'ProgressBar':
            totalValue = handleProgress(totalValue);
            break;
          default:
            if (i.id === 'progress') {
              totalValue = handleProgress(totalValue);
            }
            break;
        }
        // 自定义文本需要处理替换
        if (descField?.id === '-1') {
          const fieldKey = `${i.objId}.${i.id}`;
          let regex = new RegExp(fieldKey);
          text = text.replace(regex, totalValue);
        } else {
          text = totalValue;
        }
      }
    });
  } else {
    // 去掉有问题的小数位
    text = (text + '').replace(/\.\d+/, '');
  }
  if (params.type === 'text') {
    text = removeBraces(text);
  }
  return text;
};
/** 是否存在搜索条件 */
export const isHasSearch = (searchConfig: SearchConditions | null) => {
  if (!searchConfig || isEmpty(searchConfig)) {
    return false;
  }

  const { commonFilters = [], groupFilters = [], quickFilters = [] } = searchConfig || {};
  const searchType: AnyObj = { commonFilters, groupFilters, quickFilters };
  const hasSearch = searchType && Object.keys(searchType).some(key => searchType[key].length > 0);

  return hasSearch;
};
export const getSearchSetting = (searchConfig: SearchConditions | null, client: string = 'PC') => {
  if (!searchConfig || isEmpty(searchConfig)) {
    return {
      hasGroup: false,
      hasTopGroup: false,
      hasLeftGroup: false,
      hasQuick: false,
      hasAd: false,
      hasFilter: false,
      hasSearch: false,
    };
  }

  const {
    commonFilters = [],
    groupFilters = [],
    quickFilters = [],
    /** 显示方式 */
    commonSearchType = '0',
  } = searchConfig || {};

  const hasSearch = isHasSearch(searchConfig);

  if (client === 'PC') {
    /**
     * 由于独立展示需求
     * 1 高级搜索时，将独立展示的字段转化成顶部搜索字段
     * 2 顶部搜索时，逻辑不变
     */
    const topFilter = commonFilters.filter((f: any) => `${f.listFilterDefaultSets?.isDateMenu}` === '1');
    /** 顶部分类搜索配置 */
    const topGroupFilter = groupFilters.filter((g: any) => g.showPosition === '0');
    /** 左侧分类搜索配置 */
    const leftGroupFilter = groupFilters.filter((g: any) => g.showPosition === '1');

    return {
      hasGroup: groupFilters.length > 0,
      hasTopGroup: topGroupFilter.length > 0,
      hasLeftGroup: leftGroupFilter.length > 0,
      hasQuick: quickFilters.length > 0,
      hasAd: commonFilters.length > 0 && commonFilters.length > topFilter.length && commonSearchType === '0',
      hasFilter: (commonFilters.length > 0 && commonSearchType === '1') || topFilter.length > 0,
      hasSearch,
    };
  }

  /**
   * 移动端：
   * 1 高级、顶部、分类中的浏览框字段都转换为高级
   * 2 快捷照旧
   * 3 分类的纯选择框显示在分类顶部
   */
  const group = groupFilters.filter((g: any) => SelectType.includes(g.type || g.componentKey || g.compType) || g.componentKey === 'GroupCustom');

  return {
    hasGroup: group.length > 0,
    hasQuick: quickFilters.length > 0,
    hasAd: commonFilters.length > 0 || group.length !== groupFilters.length,
    hasFilter: false,
    hasSearch,
  };
};
export const appendParams = (formParams: any, key: string, val: any) => {
  if (formParams instanceof FormData) {
    const values = isObject(val) ? JSON.stringify(val) : val;
    formParams.append(key, values);
  } else {
    formParams[key] = val;
  }
};
export const getUrlParams = (pageId?: string) => ebdcoms.excu('getUrlParams', pageId) as any;

export const getFilterFields = (
  key: string,
  fieldsParams: {
    fields: any[];
    compId: string;
    dataset: DataSourceValueType;
  }
) => {
  const field = (fieldsParams.fields || []).find(f => f.id === key);

  const filterFields: any = {
    compId: fieldsParams.compId,
    compType: field?.compType,
    dataset: fieldsParams.dataset,
    fieldName: field?.name,
    formatType: field?.formatType,
    id: field?.id,
    objId: field?.objId,
    type: field?.type,
  };

  // 数据源分组字段，shortKey是唯一标识
  if (field?.shortKey) {
    filterFields.shortKey = field.shortKey;
  }

  return filterFields;
};
/**
 * 处理高级搜索相关的参数
 * @param comConfigParams
 * @param searchAdvancedStore
 * @param param2
 * @param dataSource
 * @param otherParams
 * @returns
 */
export const getSearchTypeAndfilters = (
  comConfigParams: {
    compId: string;
    config: any;
    commonFilters: any[];
    fields: any[];
  },
  searchAdvancedStore: SearchAdvancedStore,
  { formDatas = {}, quickSearchDatas = {} }: SearchDatas,
  dataSource: string,
  otherParams?: any
) => {
  const { type } = otherParams || {};
  const { conditionValue } = searchAdvancedStore;
  let filters: any[] = [];
  let searchType: SearchType;
  const dfFilterInfo = {
    conditionType: conditionValue,
    dataType: 'field',
    id: UUID(),
  };

  if (type === 'snap' || dataSource === 'fromSearchPanel') {
    searchType = 'advance';

    Object.keys(formDatas).forEach(key => {
      (formDatas[key] as any).forEach((item: any) => {
        // 注：之前是id包含GroupCustom_，
        // 武宇敬和超哥的常用条件调整了id，暂时通过commonFilters得componentKey判断, 有问题再想其他方案
        // const [, customedId] = key.split('GroupCustom_');
        const configItem = comConfigParams.commonFilters.find(f => f.id === key);
        const funcValue = item[`${key}_condition_type`];

        if (item[key] || conditionNullArr.includes(funcValue)) {
          if (configItem?.componentKey === 'GroupCustom') {
            filters.push({
              ...dfFilterInfo,
              conditionComId: `${comConfigParams.compId}_advance${FilterNoCacheKey}`,
              dataType: 'classification',
              filterFields: [],
              funcType: funcValue,
              value: conditionNullArr.includes(funcValue) ? funcValue : formatSearchValue(key, item[key], comConfigParams),
            });
          } else {
            filters.push({
              ...dfFilterInfo,
              conditionComId: `${comConfigParams.compId}_advance${FilterNoCacheKey}`,
              dataType: 'field',
              filterFields: [
                getFilterFields(key, {
                  compId: comConfigParams.compId,
                  dataset: comConfigParams.config?.dataset,
                  fields: comConfigParams.fields,
                }),
              ],
              funcType: funcValue,
              value: conditionNullArr.includes(funcValue) ? funcValue : formatSearchValue(key, item[key], comConfigParams),
            });
          }
        }
      });
    });
  } else {
    // 快捷搜索
    searchType = 'quick';
    filters = Object.keys(quickSearchDatas).map(key => ({
      ...dfFilterInfo,
      conditionType: 'or',
      conditionComId: `${comConfigParams.compId}_quick${FilterNoCacheKey}`,
      filterFields: [
        getFilterFields(key, {
          compId: comConfigParams.compId,
          dataset: comConfigParams.config?.dataset,
          fields: comConfigParams.fields,
        }),
      ],
      value: quickSearchDatas[key] || '',
    }));
  }

  return { filters, searchType };
};

export const ebdComsAjax = (params: any) => ebdcoms.excu('ajax', params);

/**
 * 判断筛选值是否为空
 * */
export const isValueEmpty = (val: any): boolean => {
  if (!val) return true;
  let emptyFlag: boolean = false;
  if (Array.isArray(val)) {
    // 数组值
    emptyFlag = !val.length || (val as any[]).every((v: any) => isEmpty(v));
  } else if (typeof val === 'object') {
    if ('min' in val || 'max' in val) {
      // 数值区间
      if (val?.min === '' && val?.max === '') {
        emptyFlag = true;
      }
    } else if (isEmpty(val)) {
      // 对象值
      emptyFlag = true;
    }
  } else if (typeof val === 'string' && val === '') {
    // 字符串
    emptyFlag = true;
  }
  return emptyFlag;
};

// 获取分组请求时 组装extParams
export const getOptionsExtParams = (config: any = {}, diffRootId?: string) => {
  const { group = {}, dataset } = config!;
  const { active = GroupType.custom, options = [] } = group!;
  const currentGroup = options.find((i: any) => +i.groupType === +active) ?? {};
  let params: any = {
    groupType: active,
    // @ts-ignore
    fieldId: `${active}` === GroupType.field ? currentGroup?.fieldId : undefined,
  };
  // * 字段分组下-关联ebbuiler字段单独处理（事井然）
  let ebuilderFieldParams = {};
  // @ts-ignore
  if (`${active}` === GroupType.field && currentGroup.fieldType === EtComponentKey.Ebuilder) {
    ebuilderFieldParams = {
      showField: currentGroup.showField,
      orderField: currentGroup.orderField,
      filter: currentGroup.filter,
      defaultGroup: currentGroup.defaultGroup,
    };
  }
  // 全部权限数据源
  if (dataset?.ebuilderDataConfig?.dataSetup?.dataPermission === 'ALL') {
    params.isNoPermissionList = '1';
  }
  // 差异化分组选中项id
  if (`${group.active}` === `${RelationGroupsType.Custom}` && diffRootId) {
    params.rootid = diffRootId;
  }
  return { ...params, ...ebuilderFieldParams };
};

// 获取看板背景色配置
// 自定义分组背景色>其他配置背景色>默认白色底色
export const getBoardBg = (enableColor: boolean, color?: string) => {
  const backgroundColor = enableColor && color ? color : KanBanDefaultBgColor;

  return {
    backgroundColor: getIsWhiteColors(backgroundColor) ? '' : backgroundColor,
    borderColor: getRgbBorderColor(backgroundColor),
    fontColor: getBoardTitleFontColor(backgroundColor),
  };
};

// 根据当前日期分组id重写带时区的时间戳id
// 自定义分组背景色>其他配置背景色>默认白色底色
export const rewriteBoardDateGroup = (groups: any[]) => {
  const groupsMap = groups.map((group: any) => {
    const groupId = `${group.id}`;
    switch (groupId) {
      // 未分组/今天
      case '0':
      case '-1':
      case '3':
        group.baseTime = getServerDateTime(dayjs().startOf('day'), 'timestamp');
        break;
      // 前天
      case '1':
        group.baseTime = getServerDateTime(dayjs().subtract(2, 'day').startOf('day'), 'timestamp');
        break;
      // 昨天
      case '2':
        group.baseTime = getServerDateTime(dayjs().subtract(1, 'day').startOf('day'), 'timestamp');
        break;
      // 明天
      case '4':
        group.baseTime = getServerDateTime(dayjs().add(1, 'day').startOf('day'), 'timestamp');
        break;
      // 后天
      case '5':
        group.baseTime = getServerDateTime(dayjs().add(2, 'day').startOf('day'), 'timestamp');
        break;
      default:
        break;
    }
    return group;
  });
  return groupsMap;
};

export const onMessage = (isMobile: boolean, type: 'info' | 'success', content: any) => {
  if (isMobile) {
    MDialog.toast({
      type,
      content,
    });
  } else {
    Dialog.message({ type, content });
  }
};

export const getExParamsByEbParams = (searchParams: any, pageId: string) => {
  const { getEbParams } = ebdcoms.get();
  const { exParams = [] } = Object.keys(searchParams || {}).length ? searchParams : {};
  const pageParams = getEbParams?.('pageParams', pageId);
  return pageParams?.urlParams?.exParams || toJS(exParams) || [];
};

export const isIE = () => {
  let ua = window.navigator.userAgent;
  let isIE = ua.indexOf('MSIE ') > -1 || ua.indexOf('Trident/') > -1;
  return isIE;
};

export const isCardOpened = () => window.location.href.indexOf('/card/') > -1 || window.location.href.indexOf('/flowpage/') > -1;

export const getCacheData = () => {
  const info = window.sessionStorage.getItem(mBoardInfo);
  if (info) {
    return JSON.parse(info);
  }
  return null;
};
export const setCacheData = (data: any | null) => {
  if (data === null) {
    window.sessionStorage.removeItem(mBoardInfo);
    return;
  }
  const prevData = getCacheData();
  if (prevData) {
    window.sessionStorage.setItem(
      mBoardInfo,
      JSON.stringify({
        ...prevData,
        ...data,
      })
    );
  } else {
    window.sessionStorage.setItem(mBoardInfo, JSON.stringify(data));
  }
};

export const getIsMulti = (coms: any, compId: string, pluginCenter?: BoardPluginCenter) => {
  // 二开口子支持定制是否多选
  if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.getRefreshCompIsMulti)) {
    const [isMulti] = invoke(pluginCenter, EbdBPlKeys.getRefreshCompIsMulti, {
      args: [coms, compId],
    });
    return isMulti;
  }
  // 目前仅项目树任务树支持开启多选配置（rowMultiple）
  const multiCompsType = ['ProjectTree', 'TaskTree'];
  const currentComp = coms.find((i: any) => i.id === compId && multiCompsType.includes(i.type));
  if (!currentComp) return false;
  return currentComp?.config?.rowMultiple;
};
/**
 * 业务组件阶段看板使用：主要针对项目树任务树组件
 * 对接刷新组件，支持单选及多选
 * @param val 选中值
 * @param coms 组件列表
 * @returns params
 */
export const getUPRefreshInPhaseBoard = (val: any[], coms: any = [], pluginCenter?: BoardPluginCenter) => {
  let urlParams: any = [];
  const getUrlParamsWidthRefresh = (val: any) => {
    const { filterIsNull, isOverrideFilter } = val;
    const urlParams = {
      esbFilter: [],
      filter: val?.conditionSet,
      filterIsNull,
      isOverrideFilter,
    } as any;
    return urlParams;
  };
  val.forEach((i: any, index: number) => {
    const isMulti = getIsMulti(coms, i.comId, pluginCenter);
    const urlParamsCache = JSON.parse(sessionStorage.getItem(boardUrlParamsCache) || '{}');
    if (isMulti) {
      const currentId = (i.conditionSet?.datas || [])[0]?.conditions?.value;
      const _urlParamsCache = urlParamsCache[i.comId] || [];
      const hasFilterIndex = currentId ? _urlParamsCache.findIndex((k: any) => (k.conditionSet?.datas || [])[0]?.conditions?.value === currentId) : -1;
      if (hasFilterIndex > -1) {
        // 删除
        _urlParamsCache.splice(hasFilterIndex, 1);
        urlParams = _urlParamsCache;
      } else {
        // 添加
        urlParams = [..._urlParamsCache, i];
      }
    } else {
      urlParams = [i];
    }
    if (isMulti) {
      // 二开口子支持定制刷新参数
      const [newParams] = invoke(pluginCenter, EbdBPlKeys.getRefreshCompParams, {
        args: [urlParams],
      }) || [urlParams];
      const newCache = { ...urlParamsCache, [i.comId]: newParams };
      if (isMulti) sessionStorage.setItem(boardUrlParamsCache, JSON.stringify(newCache));
    }
  });
  return urlParams.map((i: any) => getUrlParamsWidthRefresh(i));
};

/**
 * 后续标准看板看看能不能走着 暂无实际使用地方
 * 对接刷新组件，支持单选及多选
 * @param val 选中值
 * @param coms 组件列表
 * @returns params
 */
export const getUrlParamsWidthRefresh = (val: any[], coms: any = [], pluginCenter?: BoardPluginCenter) => {
  let urlParams: any = {};
  val.forEach((i: any, index: number) => {
    const { dynamicFieldsValue = [] } = i || {};
    const isMulti = getIsMulti(coms, i.comId, pluginCenter);
    const urlParamsCache = JSON.parse(sessionStorage.getItem(boardUrlParamsCache) || '{}');
    if (isMulti) {
      urlParams = urlParamsCache[i.comId] || {};
    }
    (i?.paramsSet || []).forEach((element: any) => {
      const { value = {} } = element || {};
      const item = dynamicFieldsValue.find((k: any) => k.id === value.id);
      if (item) {
        const itemValue = Array.isArray(item.value) ? item.value.map((i: any) => i.id).join(',') : item.value;
        // 开启了多选 就push
        if (isMulti) {
          if (isEmpty(urlParams[value.name])) urlParams[value.name] = [];
          if (urlParams[value.name].includes(itemValue)) {
            // 删除
            urlParams[value.name].splice(urlParams[value.name].indexOf(itemValue), 1);
          } else {
            // 添加
            urlParams[value.name].push(itemValue);
          }
        } else {
          // 单选
          urlParams[element.name || value.name] = itemValue;
        }
      }
    });
    if (index === val.length - 1 && isMulti) {
      // 二开口子支持定制刷新参数
      const [_urlParams] = invoke(pluginCenter, EbdBPlKeys.getRefreshCompParams, {
        args: [urlParams],
      });
      const newCache = { ...urlParamsCache, [i.comId]: _urlParams };
      if (isMulti) sessionStorage.setItem(boardUrlParamsCache, JSON.stringify(newCache));
    }
  });
  // 字符串化
  const result: any = {};
  for (const key in urlParams) {
    if (isEmpty(urlParams[key])) continue;
    result[key] = Array.isArray(urlParams[key]) ? urlParams[key].join(',') : urlParams[key];
  }
  return result;
};

// 按配置处理分组数据
export const handleGroupsWithCf = (groups: GroupItem[], config: BoardConfigData, groupType: GroupType | RelationGroupsType, laneId: string) => {
  const otrGroupInfo = config.group?.otherData || {};
  // 开启了隐藏未分组
  if (otrGroupInfo.hideWfz) {
    groups = groups.filter((i: any) => `${i.type}` !== '0');
  }
  // 日期分组需要根据当前时区来重写分组id
  if (`${groupType}` === GroupType.dateRange) {
    groups = rewriteBoardDateGroup(groups);
  }
  const newGroups = groups.map((i: any) => {
    return {
      ...i,
      desc: '',
      // 填充全局laneId
      laneId,
    };
  });
  return newGroups;
};

// 按配置处理泳道分组数据
export const handleLaneGroupsWithCf = (laneGroups: LaneItemData[], config?: BoardConfigData) => {
  const laneConfig = config?.laneConfig;
  const laneGroup = laneConfig?.laneGroup;
  // 开启了隐藏未分组
  if (laneGroup?.displayNoGroup) {
    laneGroups = laneGroups.filter((i: any) => `${i.type}` !== '0');
  }
  // 日期分组需要根据当前时区来重写分组id
  if (`${laneGroup?.active}` === GroupType.dateRange) {
    laneGroups = rewriteBoardDateGroup(laneGroups);
  }
  if (!config?.group?.displaySealing) {
    laneGroups = laneGroups.filter((i: any) => i?.archive !== '1');
  }
  return laneGroups;
};

/**
 * 兼容泳道处理开启无数据分组
 * 因为不存在多泳道不同分组 分组是固定的 需要整体来看 只要该泳道分组下有cards数据 就保留
 * @param laneGroups LaneItemData[]
 * @returns
 */
export const handleGroupsNoData = (laneGroups: LaneItemData[] = []): LaneItemData[] => {
  return laneGroups.map(i => {
    i.groups = i.groups?.filter(k => {
      const currentGroupInAllLane = laneGroups.map(f => f.groups).map((d: any) => d.find((g: any) => g.id === k.id));
      const cards = toJS(currentGroupInAllLane.map((j: any) => j?.cards ?? []));
      return cards.reduce((pre: any, cur: any) => pre + cur.length, 0);
    });
    return i
  })
};

export const handleLaneNoData = (laneGroups: LaneItemData[]): LaneItemData[] => {
  let showLanes = [] as LaneItemData[];
  laneGroups.forEach(i => {
    i?.groups?.forEach(ele => {
      if (!isEmpty(ele.cards) && !showLanes.find(k => i.id === k.id)) {
        showLanes.push(i);
      }
    });
  });
  return showLanes;
};

// 处理隐藏无数据分组
export const handleGroupsMenus = (groups: GroupItem[], menus: any[], otrGroupInfo?: any, wfzId?: string) => {
  const { hideWfz, customWfz, hideNoDataFz } = otrGroupInfo;
  return menus
    .map((i: any) => {
      // 移动到其他分组需要校验
      if (i.key === 'moveData') {
        // 是否开启了隐藏无数据分组
        if (hideNoDataFz || customWfz) {
          i.children = i.children
            .map((c: any) => {
              const groupId = c.key.split('moveData_')[1];
              const groupItem = groups.find(i => i.id === groupId);
              let newJson = { ...c };
              // 开启了隐藏无数据 那可移动的分组内是无数据的 或者是没有对应的分组（被过滤掉的） 需要去掉
              if ((hideNoDataFz && isEmpty(groupItem?.cards)) || !groupItem) {
                newJson.filtered = true;
              }
              if (customWfz && wfzId && `${c.key}` === `moveData_${wfzId}`) {
                newJson.text = customWfz?.nameAlias ? customWfz?.nameAlias : customWfz;
              }
              return newJson;
            })
            .filter((i: any) => !i.filtered);
        }
        // 是否开启了隐藏未分组
        if (hideWfz && wfzId) {
          i.children = i.children.filter((i: any) => i.key !== `moveData_${wfzId}`);
        }
        if (isEmpty(i.children)) {
          i.filtered = true;
        }
      }
      return i;
    })
    .filter((i: any) => !i.filtered);
};

export const getLaneExtParams = (laneConfig: LaneConfigProps, item?: LaneItemData, isMobile?: boolean) => {
  if (!laneConfig || !laneConfig.status || !item || isMobile) return {};
  return { laneGroup: { id: item.id, baseTime: item.baseTime, laneFilter: item.filters || {} } };
};

export const getLaneById = (laneGroups: LaneItemData[], laneId?: string) => {
  return laneId ? laneGroups.find(i => i.id === laneId) : undefined;
};

export const setLaneSingleGroup = (laneGroups: LaneItemData[], laneId: string, groups: GroupItem[]) => {
  const idx = laneGroups.findIndex(i => i.id === laneId);
  if (idx > -1) {
    laneGroups[idx].groups = groups;
  }
  return laneGroups;
};
