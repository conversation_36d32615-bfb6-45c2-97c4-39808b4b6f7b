import { Dialog, <PERSON>ialog, SearchDatas, utils as UiUtils } from '@weapp/ui';
import { getLabel, corsImport, dayjs, forEach, forIn, isEmpty, cloneDeep } from '@weapp/utils';
import axios from 'axios';
import { action, computed, observable, runInAction, toJS } from 'mobx';
import { followApi } from '../../../api/common';
import { CardButtonType, FormDefaultValueSessionKey, ListSearchCacheParams, KanBanDefaultPageSize, mBoardClsPrefix, ebdBClsPrefix, KanBanDefaultBgColor, DEFAULT_LANE_ID } from '../../../constants';
import { EtComponentKey, H5GroupShieldFieldTypes } from '../../../constants/EtComponent';
import { KanbanMockEmptyData } from '../../../constants/mock';
import { NewViewBaseStore } from '../../../store/NewViewBaseStore';
import { CommonSearchType, SearchType, AnyObj, DataSet } from '../../../types/common';
import { executeActions } from '../../../utils';
import { ajax, format2FormData } from '../../../utils/ajax';
import { followEvents } from '../../../utils/followEvents';
import ls from '../../../utils/ls';
import {
  setDocumentTitle,
  getConditionFormDatas,
  getLocaleValue,
  invoke,
  hasRegisterFuncInPlugin,
  formatParentPath,
  filterUrlParams,
  UUID,
  deepToJS,
  parseSystemFieldType,
  getFields,
} from '../../../utils';
import { jumpToCard } from '../../../utils/jumpLink';
import { ShowPosition, ColorStyleType, GroupType, LayoutType, OpenMode } from '../../../constants/common';
import { KanbanMockData } from '../../../constants/mock';
import { CustomMenuAction } from './types';
import { onAfterAdInit } from '../../../utils/advanceFn';
import { parseAdData } from '../../../utils/searchFn';
import { BaseSearchStore } from './BaseSearchStore';
import { DesignerStore } from './DesignerStore';
import { SearchStore } from './searchStore';
import BtnActionCommonStore from '../../../store/btnActionCommonStore';
import { AllGroupData, BaseParamsProps, CardMenuActionType, CardSpecialField, GroupItem, GroupPermission, ListMenuActionType, OrderType, BoardPluginCenter } from './types';
import {
  formatMenuConfig,
  addPhysicalParams,
  getDescText,
  getCurrentDescByGroupType,
  getOptionsExtParams,
  onMessage,
  getExParamsByEbParams,
  isIE,
  isCardOpened,
  getCacheData,
  setCacheData,
  handleGroupsWithCf,
  handleGroupsMenus,
  getLaneExtParams,
  handleLaneGroupsWithCf,
  getLaneById,
} from './utils';
import RefreshComFunc from '../../../utils/refreshComClass';
import { EbdBoardEventName, EbdBPlKeys } from '../../../types/common';
import { isEbFormDataV2, isEbBusinessFormData, isEteamsData, isUnEbDataSetCustomOptions } from '../DataSet/utils';
import { INIT_PAGE_SIZE_CONFIG, INIT_PAGE_MODE, INIT_LANE_PAGE } from '../../baseBoard/config/cmps/constants';
import { PageModeType } from '../../baseBoard/config/cmps/types';
import { BoardConfigData } from '../../baseBoard/types';
import { LanePageProps, LaneItemData, LaneStatType } from '../../baseBoard/config/cmps/yd/types';
import { getRelationBrowserFields } from '../../baseBoard/engine/func-groups/utils';
import ebdcoms from '../../../utils/ebdcoms';
import { INITIAL_PAGE_INFO, INITIAL_GROUP_INFO, FAKE_ADD } from './constants';
import { RelationGroupsType } from '../../baseBoard/engine/func-groups/diff/constants';

const { isValueEmpty } = UiUtils;

export class BoardViewStore extends NewViewBaseStore {
  initGroup = [INITIAL_GROUP_INFO()];
  @observable designerStore: DesignerStore | null = null;
  @observable btnActionCommonStore = new BtnActionCommonStore();

  @observable compId: string = ''; // compId

  @observable pageId: string = '';

  @observable isMobile: boolean = false;

  @observable isDesign: boolean = false;
  @observable pluginCenter: BoardPluginCenter = {}; // 引入插件包机制
  @observable events: any = {};

  @observable config: BoardConfigData = {
    dataset: {
      id: '',
      type: 'FORM',
      text: '',
      groupId: '',
    },
    condition: {},
    filter: {},
    layout: {},
    group: {
      active: GroupType.custom,
      options: [],
    },
    card: {},
    orders: [],
    pageSize: INIT_PAGE_SIZE_CONFIG(),
    pageMode: INIT_PAGE_MODE,
  };

  @observable addData: boolean = false;

  @observable addGroup: boolean = false;

  @observable title: string = '';

  /** **************************************** */

  @observable groupEditingData: any = {
    // 新建、编辑分组
    id: null,
    name: '',
    type: 0,
    color: KanBanDefaultBgColor,
  };

  @observable cardEditingData: any = {}; // 编辑卡片

  @observable selectedCardDatas: any[] = [];

  @observable groupEditVisible: boolean = false;

  /** ***************datas****************** */
  @observable groups: GroupItem[] = []; // 前端存放所有看板分组的数据
  @observable rawGroups: GroupItem[] = [];

  @observable groupMenuData: AllGroupData = {}; // 分组顶部的操作按钮们

  @observable paginationData: AllGroupData = {}; // 所有分组的分页数据
  @observable cardSpecialField: CardSpecialField = {
    // 卡片特殊字段
    Date: [],
    Employee: [],
  };

  @observable customMenuActions: CustomMenuAction[] = []; // 自定义按钮事件集合

  /** ******************orders******************* */
  @observable orders: any[] = [];

  /** *****************search********************* */
  @observable commonFilters: any = [];

  @observable quickFilters: any = [];

  @observable baseSearch = new BaseSearchStore(this);
  @observable newSearchStore: any;

  // 分类搜索
  @observable groupFilters: any = [];

  @observable groupSearchParams: any = {};

  @observable leftMenuTreeDatas: any[] = [];

  @observable groupTopSearchDatas: any = [];

  @observable cacheSearchParams: any = {};
  @observable ganttModule: any;
  // 分组描述信息
  // @observable descInfo: DescInfoProps = {
  //   groupType: GroupType.custom, // 默认自定义分组
  //   info: [],
  // };
  @observable descInfo = {} as any;
  @observable groupRefreshKey = new Date().getTime(); // 刷新key
  /** *********************** MOBILE ******************************* */

  timer: any = null;

  // 分类搜索
  @observable groupSearchDatasH5: any[] = [];

  @observable boardColorStyle: any = 'noStyle'; // 判断看板背景色类型
  @observable isEbFormDataSet = true; // 是否为表单数据源
  @observable loading: boolean = true;
  @observable inVideLoaded = false;
  @observable pendingFunc: any[] = [];
  // 已加载看板
  @observable loadedBoardIds = new Set<string>();
  @observable loadingTimestamp: number = -1;
  @observable showLoadingStatus: boolean = false; // 是否显示loading状态
  @observable optionsCancelToken = {} as any;
  @observable ordersCancelToken = {} as any;
  @observable menusCancelToken = {} as any;
  @observable boardRightCancelToken = {} as any;
  @observable isHideNoDataGroupLoading = true;
  // 泳道数据
  @observable laneGroups: LaneItemData[] = [];
  @observable laneStats: LaneStatType[] = [];
  @observable laneLoading = true;
  @observable laneGroupsLoading = false;
  // 泳道分页相关
  @observable laneBoardPageInfo: AllGroupData = {}; // 泳道-分组-看板内数据的分页数据
  @observable lanePage = INIT_LANE_PAGE(); // 泳道-总数
  isHideNoDataGroup = false; // 是否隐藏无数据分组
  routerProps: any = {};

  RefreshComFunc: RefreshComFunc = new RefreshComFunc();
  // todo 新版本筛选数据存放
  filterValues: any;

  /** **************************** 基础接口 ***************************** */
  // 自定义分组
  @computed
  get isCustomMode() {
    return `${this.config.group?.active}` === GroupType.custom || `${this.config.group?.active}` === RelationGroupsType.Normal;
  }
  // 条件分组
  @computed
  get isFilterMode() {
    return `${this.config.group?.active}` === GroupType.filter;
  }
  // *是否显示添加数据按钮
  @computed
  get hasAddCardRight() {
    if (this.isEbFormDataSet) {
      return this.addData;
    }
    return false;
  }
  // *是否显示添加分组按钮
  @computed
  get hasAddGroupRight() {
    if (this.isEbFormDataSet) {
      return this.addGroup;
    }
    return false;
  }

  @computed
  get activeGroup() {
    const { active = 0, options = [] } = this.config.group || {};
    return options.find((i: any) => `${i.groupType}` === `${active}`) || {};
  }

  @computed // 判断是否有左侧分栏，占位
  get hasGroupleft() {
    const { filter } = this.config;
    const leftFilters = filter?.groupFilters?.filter((g: any) => g.showPosition === '1') || [];
    return leftFilters.length > 0;
  }
  @computed
  get pageSize(): number {
    return +(this.config?.pageSize?.pageNum || KanBanDefaultPageSize);
  }
  @computed
  get pageMode() {
    // 默认为下拉更多
    return this.config?.pageMode || PageModeType.Scroll;
  }
  // 差异化分组选中id
  @computed
  get diffRootId() {
    // @ts-ignore
    const { diffGroupCustomId } = this.config;
    // 差异化分组-自定义分组才有值
    return (this.searchParams || {}).rootid || diffGroupCustomId || '';
  }

  @computed
  get extParams() {
    const { active = '' } = this.config.group || {};
    let newExtParams = getOptionsExtParams(this.config, this.diffRootId);
    const unEbDataSetCustomOptions = isUnEbDataSetCustomOptions(this.config?.dataset, active);
    if (unEbDataSetCustomOptions) {
      newExtParams.customGroup = '1';
    }
    return newExtParams;
  }
  // @computed
  // get bothLaneExtParams() {
  //   if (!this.isLaneConfig) return {};
  // }
  @computed
  get isEbuilderField() {
    const groupType = `${this.activeGroup.groupType}`;
    const isCustomDiff = groupType === RelationGroupsType.Custom;
    return groupType === GroupType.field && `${this.activeGroup.fieldType}` === EtComponentKey.Ebuilder && !isCustomDiff;
  }
  @computed
  get dealSearchParams() {
    return filterUrlParams(this.searchParams);
  }
  @computed
  get otrGroupInfo() {
    const { otherData = {} } = this.config.group || {};
    return otherData;
  }
  // 是否开启了隐藏未分组
  @computed
  get isHideWfz() {
    return this.config?.group?.otherData?.hideWfz;
  }
  // 是否开启了泳道分组
  @computed
  get isLaneConfig() {
    const isLaneConfig = this.config?.laneConfig?.status;
    const [hasLaneBoard] = invoke(this.pluginCenter, EbdBPlKeys.getIsLaneBoard) || [isLaneConfig];
    return hasLaneBoard;
  }
  @computed
  get hasDragGroupRight() {
    return this.isCustomMode && isEbFormDataV2(this.config);
  }
  // 拖拽到其他分组
  @computed
  get hasDragCardToOtherRight() {
    return this.isCustomMode && (isEbBusinessFormData(this.config?.dataset) || isEteamsData(this.config?.dataset) || isEbFormDataV2(this.config));
  }
  // 当前分组组内排序
  @computed
  get hasDragCardInGroupRight() {
    // 目前所放开的数据源都支持组内排序
    // 排除条件分组 不支持拖拽
    if (this.isFilterMode) return false;
    return isEbBusinessFormData(this.config?.dataset) || isEteamsData(this.config?.dataset) || isEbFormDataV2(this.config);
  }
  @action setSearchParams = (uParams: any = {}) => {
    this.setState({ searchParams: uParams });
  };
  @action('补充extParams公共参数')
  extParamsExtra(groupId?: string) {
    let _extParams = this.extParams;
    if (`${this.activeGroup.groupType}` === GroupType.dateRange) {
      const currentGroup = this.groups.find((g: any) => `${g.id}` === groupId);
      _extParams.baseTime = currentGroup?.baseTime;
    }
    return _extParams;
  }
  @action
  setState = (params: { [key: string]: any }, cb?: () => void) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
    cb && setTimeout(cb, 300);
  };
  @action('设置泳道分页相关') setLanePageState = (params: Partial<LanePageProps>) => {
    Object.entries(params).forEach(([key, val]) => {
      if (key in this.lanePage) {
        (this.lanePage[key as keyof LanePageProps] as any) = val;
      }
    });
  };
  @action
  updateConfig = (key: keyof BoardConfigData, value: any) => {
    this.config[key] = value;
  };

  @action
  baseInit = (params: BaseParamsProps) => {
    this.viewComInfo = params; // compId，pageId，config,isMobile,isDesign
    this.compId = params.compId;
    this.pageId = params.pageId;
    this.isMobile = params.isMobile;
    this.isDesign = params.isDesign;
    this.config = params.config;
    this.events = params.events;
    this.pluginCenter = params.pluginCenter!;
    this.groups = [];
    // 新增处理隐藏无数据分组
    this.isHideNoDataGroup = !!params.config?.group?.otherData?.hideNoDataFz;
    if (params.config?.filter) {
      const { commonSearchType } = params.config?.filter!;
      this.commonSearch = +commonSearchType;
    }
    if (params?.comServicePath) {
      this.setComServicePath(params.comServicePath);
    }
    this.isEbFormDataSet = !!isEbFormDataV2(params.config);
  };

  @action
  initConfig = async (params: BaseParamsProps, uParams: any = {}) => {
    this.setSearchParams(uParams);
    if (params.isDoc) {
      this.groups = KanbanMockData() as any;
      this.config = params.config;
    } else {
      this.newSearchStore = new SearchStore(params);
      this.baseInit(params); // 获取基础配置
    }
  };

  @action
  initData = () => {
    this.getGroupFilters();
    const initFunc = async () => {
      // 泳道分组获取分组数据
      if (this.isLaneConfig) {
        await this.getLineGroupData();
      }
      if (this.isDesign) {
        runInAction(() => {
          this.designerStore = new DesignerStore(this);
          this.designerStore.init(); // params, uParams
        });
      } else {
        if (!this.config?.dataset?.id) {
          // 给mock数据
          this.groups = KanbanMockEmptyData();
          this.loading = false;
          return;
        }
        try {
          await this.getDefaultFilter(); // 初始化，需要先请求默认值
          // 非eb表单数据源也需要
          this.getMenuActions();
          if (this.isEbFormDataSet) {
            this.getOrders();
            await this.getGroupsControl();
            this.getBoardRight();
          } else {
            this.resetOrders();
            this.resetMenuActions();
            this.resetBoardRight();
            await this.getGroupsControl();
          }
        } catch (error) {}
      }
    };
    if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBPlKeys.initBoardStore)) {
      invoke(this.pluginCenter, EbdBPlKeys.initBoardStore, { args: [this] });
      return;
    }
    initFunc();
  };
  @action
  reset = () => {
    this.setState({ groups: this.initGroup, orders: [], addData: false, addGroup: false, laneStats: [] });
  };
  @action
  resetGroup = () => {
    this.setState({ groups: [], orders: [], addData: false, addGroup: false, paginationData: {} });
  };
  @action
  clearState = () => {
    this.setOrders();
  };

  @action setFilter = (val: any[], filterId?: string) => {
    this.RefreshComFunc.setFilter(val, filterId);
  };
  // 当前可视视图下所有看板加载完
  // 执行存储的滞后ajax(重新加载等)
  @action onViewportLoaded = () => {
    // 执行滞后的操作
    this.inVideLoaded = true;
    this.pendingFunc.forEach((item: any, index: number) => {
      item && item();
      this.getGroupsControl();
      this.pendingFunc.splice(index, 1);
    });
  };
  // 检查看板是否已加载
  @action isBoardLoaded = (boardId: string) => {
    return this.loadedBoardIds.has(boardId);
  };
  // 设置单个看板已加载
  @action setBoardLoaded = (boardId: string) => {
    this.loadedBoardIds.add(boardId);
  };
  // 重置可视区域加载状态
  @action resetViewportLoading = (sourceFunc?: string) => {
    // 先清理存储id再改变时间去触发事件
    this.loadedBoardIds.clear();
    this.loadingTimestamp = UUID();
    const pageKey = this.isLaneConfig ? 'laneBoardPageInfo' : 'paginationData';
    // 重置分页信息
    const pageInfo = toJS(this[pageKey]);
    for (let i in pageInfo) {
      // 清理滞后加载
      if (this[pageKey][i]?.cancelToken?.comId === this.compId) {
        this[pageKey][i]?.cancelToken?.func?.cancel();
      }
      pageInfo[i] = { ...pageInfo[i], isLoading: false };
    }
    // 同步给插件包
    invoke(this.pluginCenter, EbdBPlKeys.onPageChange, { args: [pageInfo] });
    this.setState({ [pageKey]: pageInfo });
  };
  // 重置特定看板的加载状态
  @action resetSpecificBoards = (boardIds: string[]) => {
    boardIds.forEach(id => this.loadedBoardIds.delete(id));
    this.loadingTimestamp = UUID();
  };

  @action('二开口子：外部更新数据')
  handleExternalUpdateData = () => {
    const setGroupData = (data: { groups: GroupItem[]; listData: any }) => {
      const { groups = [], listData = {} } = data || {};
      runInAction(() => {
        this.groups = groups.map((group: any) => {
          const cards = listData[group.id] || [];
          return { ...group, cards };
        });
      });
    };
    return (
      <>
        {invoke(this.pluginCenter, EbdBoardEventName.onSetData, {
          args: [setGroupData],
        })}
      </>
    );
  };
  // !按返回数据处理描述
  @action getDescByData = async (laneGroupId: string, groupId: string, statFields: any[], cards: any[]) => {
    let descInfo: any = toJS(this.descInfo);
    // 有配置才加载
    if (isValueEmpty(descInfo[laneGroupId]) || isValueEmpty(descInfo[laneGroupId].info)) {
      return;
    }
    try {
      let ganttModule = this.ganttModule;
      if (!ganttModule) {
        // 加载甘特图组件：描述配置信息
        ganttModule = await corsImport('@weapp/ebdgantt');
        runInAction(() => (this.ganttModule = ganttModule));
      }
      const currentDescInfo = getCurrentDescByGroupType(this.descInfo, laneGroupId, groupId);
      const desc = await getDescText(ganttModule, currentDescInfo, groupId, this.config, cards, statFields);
      return desc;
    } catch (error) {}
  };
  @action('获取泳道分组')
  getLineGroupData = async () => {
    if (!this.compId || !this.pageId) return [];
    let res = await ajax({
      url: `/api/${this.comServicePath}/kanban/${this.isDesign ? 'previewLaneGroups' : 'getLaneGroups'}`,
      method: 'post',
      data: format2FormData({
        compId: this.compId,
        pageId: this.pageId,
        extParam: JSON.stringify(this.extParams),
        config: JSON.stringify(this.config),
        ...this.dealSearchParams,
        ...this.getNowPageParams(), //setEbParams传递的参数实时取
      }),
      ebBusinessId: this.pageId,
      error: (info: any) => {},
    });
    if (isEmpty(res) || !Array.isArray(res)) return [];
    const laneGroups = res.map((item: any) => {
      return {
        ...item,
        groups: [],
      };
    });
    runInAction(() => {
      this.laneGroups = handleLaneGroupsWithCf(laneGroups, this.config?.laneConfig);
    });
  };
  @action('分组控制中心')
  getGroupsControl = async (cb?: Function, fetcher?: Function) => {
    this.getGroupsbefore();
    // 泳道存在多分组概念 单独走
    if (this.isLaneConfig) {
      const laneGroups = toJS(this.laneGroups);
      // 开启泳道loading
      this.setState({ laneLoading: true });
      // 设置泳道数据
      const setLane = (idx: number, _laneGroups: LaneItemData[]) => {
        this.setState({ laneGroups: _laneGroups });
        if (idx === laneGroups.length - 1) {
          this.setState({ laneLoading: false });
        }
      };
      if (isEmpty(laneGroups)) {
        this.setState({ laneLoading: false });
      } else {
        // 目前是分组数据跟着看板数据关联  无多分组 只要请求一次
        const singleLaneGroup = fetcher ? await fetcher(DEFAULT_LANE_ID, cb) : await this.getGroups({ laneGroupId: DEFAULT_LANE_ID });
        laneGroups.forEach(async (i: any, index: number) => {
          const idx = laneGroups.findIndex((item: any) => item.id === i.id);
          const formatGroups = singleLaneGroup.map((k: any) => {
            return { ...k, laneId: i.id };
          });
          laneGroups[idx].groups = formatGroups;
          setLane(index, laneGroups);
          if (this.config?.laneConfig?.laneStat?.openValue) {
            this.getLaneStat(i);
          }
        });
      }
    } else {
      // 有注册插件下复写方法 走复写 不直接获取数据
      if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBoardEventName.onSetData)) {
        this.handleExternalUpdateData();
        return;
      }
      fetcher ? await fetcher({ laneGroupId: DEFAULT_LANE_ID, cb }) : await this.getGroups({ laneGroupId: DEFAULT_LANE_ID });
    }
    this.getGroupsAfter(cb);
  };
  @action('获取分组前置操作')
  getGroupsbefore = () => {
    // * 1201 隐藏无数据分组 隐藏内容区
    if (this.isHideNoDataGroup) {
      this.isHideNoDataGroupLoading = true;
    }
    // 重置懒加载
    // this.resetViewportLoading();
  };
  @action('获取分组后置操作')
  getGroupsAfter = async (cb?: Function) => {
    // 为空的情况可能为二开去掉了未分组 默认设置可视区域已加载完
    const groups = toJS(this.groups);
    if (isEmpty(groups)) {
      runInAction(() => (this.inVideLoaded = true));
    }
    // * 1201 隐藏无数据分组 需要先请求完所有数据
    if (this.isHideNoDataGroup && !this.isDesign) {
      // 取消懒加载
      runInAction(() => {
        groups.map(i => this.setBoardLoaded(i.id));
      });
      if (this.isLaneConfig) {
        const laneGroups = toJS(this.laneGroups);
        laneGroups.forEach(async (i: any, index: number) => {
          let getDataPromise: Promise<any>[] = [];
          i.groups.forEach((d: GroupItem) => {
            getDataPromise.push(this.getData({ groupId: groups[i].id, laneId: i.id }));
          });
          // 等所有异步都执行完
          await Promise.allSettled(getDataPromise);
        });
      } else {
        try {
          let getDataPromise = [];
          for (let i in groups) {
            getDataPromise.push(this.getData({ groupId: groups[i].id }));
          }
          // 等所有异步都执行完
          await Promise.allSettled(getDataPromise);
          runInAction(() => {
            // 执行获取分组菜单 -- 需要等待异步执行完
            this.groups.forEach(i => {
              const groupId = i.id;
              invoke(this.pluginCenter, EbdBPlKeys.getGroupMenus, {
                hook: () => this.getGroupMenus(groupId),
                args: [groupId],
              });
            });
          });
        } catch (error) {}
      }
    } else {
      this.resetViewportLoading('loadAllGroupInfo');
    }
    // * 触发分组加载完成事件
    this.emitEvent(EbdBoardEventName.onGroupDataLoaded, groups);
    cb && cb();
  };
  @action emitEvent = async (name: string, payload: any, unEmit?: string) => {
    if (this.events && this.events.emit && unEmit !== 'emit') {
      this.events.emit(name, this.compId, payload);
    }
    if (unEmit !== 'pluginCenter') {
      invoke(this.pluginCenter, name, { args: payload });
    }
  };

  @action
  reloadSingleGroup = async () => {
    await this.getData({ groupId: this.getGroupEditingData('id') });
  };

  @action resetOrders = () => {
    this.orders = [];
  };
  @action('获取排序信息')
  getOrders = async () => {
    if (this.ordersCancelToken?.compId === this.compId) {
      this.ordersCancelToken?.func?.cancel('Request canceled');
    }
    this.ordersCancelToken = {
      func: axios.CancelToken.source(),
      compId: this.compId,
    };

    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/getOrders`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify({}),
      }),
      ebBusinessId: this.pageId,
      cancelToken: this.ordersCancelToken?.func?.token,
      error: (info: any) => {},
    });
    const { orders = [], status } = res;
    if (status) {
      runInAction(() => {
        if (orders.length > 0) {
          this.orders = [
            {
              id: '',
              fieldName: getLabel('105948', '拖动排序'),
              selected: true,
            },
          ].concat(orders.map((o: any) => ({ ...o, id: o.fieldId })));
        } else {
          this.orders = [];
        }
      });
    } else {
      onMessage(this.isMobile, 'info', res?.msg || getLabel('77125', '请求异常，请联系管理员'));
    }
  };

  /**
   * @desc 设置排序方式
   * */
  @action
  setOrders = (id: string = '') => {
    this.orders = this.orders.map((o: any) => {
      let { orderType } = o;
      if (o.id === id && o?.selected && o.id) {
        orderType = orderType === OrderType.DESC ? OrderType.ASC : OrderType.DESC;
      }
      return {
        ...o,
        orderType,
        selected: o.id === id,
      };
    });
  };

  @action
  setFormField(option: any) {
    if (!this.isCustomMode) {
      const formDefaultValues: any[] = [];
      // 单独处理日期分组
      if (`${this.activeGroup.groupType}` === GroupType.dateRange) {
        const defaultValue = {
          fieldId: this.activeGroup.fieldId,
          ...(option.id !== '-1' && {
            data: { content: dayjs(option.baseTime).format('YYYY-MM-DD') },
          }),
        };
        formDefaultValues.push(defaultValue);
      }
      const formDefaultDatas = JSON.stringify(formDefaultValues);
      window.sessionStorage.removeItem(FormDefaultValueSessionKey);
      window.sessionStorage.setItem(FormDefaultValueSessionKey, formDefaultDatas);
    }
  }

  @action('获取自定义分组信息')
  getGroups = async ({ laneGroupId = DEFAULT_LANE_ID }: { laneGroupId?: string }): Promise<GroupItem[]> => {
    if (!this.compId || !this.pageId) return [];
    if (this.optionsCancelToken?.compId === this.compId && this.optionsCancelToken?.laneGroupId && this.optionsCancelToken?.laneGroupId === laneGroupId) {
      this.optionsCancelToken?.func?.cancel('Request canceled');
    }
    this.optionsCancelToken = {
      func: axios.CancelToken.source(),
      compId: this.compId,
      laneGroupId,
    };

    let res = await ajax({
      url: `/api/${this.comServicePath}/kanban/getOptions`,
      method: 'post',
      data: format2FormData({
        compId: this.compId,
        pageId: this.pageId,
        extParam: JSON.stringify(this.extParams),
        ...this.dealSearchParams,
        ...this.getNowPageParams(), //setEbParams传递的参数实时取
      }),
      ebBusinessId: this.pageId,
      cancelToken: this.optionsCancelToken?.func?.token,
      error: (info: any) => {},
    });
    // 插件包：拦截分组数据并返回新处理的数据
    const [newRes] = invoke(this.pluginCenter, EbdBPlKeys.getGroupData, {
      args: [res],
    }) || [res];
    const groups = handleGroupsWithCf(newRes?.options || [], this.config, this.activeGroup.groupType!);
    runInAction(() => {
      // 存一份原始数据
      this.rawGroups = newRes?.options;
      // *目前分组描述用的都是同一个分组的 不需要根据泳道区分
      this.descInfo[laneGroupId] = newRes?.descInfo;
      if (groups) {
        if (laneGroupId === DEFAULT_LANE_ID) {
          this.groups = groups.map((i: any) => {
            return {
              ...i,
              desc: '',
            };
          });
        }
        forEach(res?.options, (item: any) => {
          if (item.color) {
            this.boardColorStyle = ColorStyleType.HasStyle;
          }
        });
      } else {
        this.groups = [];
      }
    });
    return groups;
  };

  @action('获取泳道分组统计')
  getLaneStat = async (laneItem: LaneItemData) => {
    const payload = {
      compId: this.compId,
      pageId: this.pageId,
      extParam: JSON.stringify({ ...this.extParams, ...getLaneExtParams(this.config?.laneConfig!, laneItem) }),
      ...this.dealSearchParams,
      ...this.getNowPageParams(), //setEbParams传递的参数实时取
    };
    if (this.isDesign) payload.config = JSON.stringify({ ...this.config, timeStamp: new Date().getTime() });
    let res = await ajax({
      url: `/api/${this.comServicePath}/kanban/${this.isDesign ? 'previewLaneStat' : 'getLaneStat'}`,
      method: 'post',
      data: format2FormData(payload),
      ebBusinessId: this.pageId,
      cancelToken: this.optionsCancelToken?.func?.token,
      error: (info: any) => {},
    });
    runInAction(() => {
      const stats = Object.entries(res).map(([key, value]) => ({[key]: value})) as LaneStatType[];
      this.laneStats = stats.map(i => ({...i, laneId: laneItem.id}));
    });
  };

  @action
  resetBoardRight = () => {
    this.addGroup = false;
    this.addData = false;
    this.title = '';
  };
  @action
  getBoardRight = async () => {
    try {
      if (this.boardRightCancelToken?.compId === this.compId) {
        this.boardRightCancelToken?.func?.cancel('Request canceled');
      }
      this.boardRightCancelToken = {
        func: axios.CancelToken.source(),
        compId: this.compId,
      };
      const res = await ajax({
        url: `/api/${this.comServicePath}/kanban/${this.isDesign ? 'previewKanbanMenu' : 'getKanbanMenu'}`,
        method: 'POST',
        data: format2FormData({
          pageId: this.pageId,
          compId: this.compId,
          extParam: JSON.stringify({}),
        }),
        ebBusinessId: this.pageId,
        error: (info: any) => {},
        cancelToken: this.boardRightCancelToken?.func?.token,
      });
      const { addData, addGroup, name = 'e-builder' } = res;
      runInAction(() => {
        this.addGroup = addGroup;
        this.addData = addData;
        this.title = name;
        setDocumentTitle(name);
        // 如果能新建分组 且为pc端 需要手动push 一个fake分组进去
        if (this.hasAddGroupRight && !this.isMobile) {
          runInAction(() => {
            this.groups.push(FAKE_ADD);
          });
        }
      });
    } catch (error) {}
  };

  @action
  getGroupMenus = async (groupId: string, laneGroupId: string = DEFAULT_LANE_ID) => {
    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/getOptionMenu`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify({
          currentOptionId: groupId ? `${groupId}` : '',
          ...this.extParamsExtra(groupId),
        }),
      }),
      ebBusinessId: this.pageId,
      error: (info: any) => {},
    });
    const menus = (res.menus || []).map((b: any) => ({
      ...b,
      // 处理转化小地球多语言
      name: getLocaleValue(b.text),
    }));
    const [newMenus] = invoke(this.pluginCenter, EbdBPlKeys.getGroupMenu, {
      args: [menus],
    }) || [menus];
    runInAction(() => {
      // 适配泳道分组
      const group = this.groups.find((g: any) => g.id === groupId);
      const wfzItem = this.rawGroups.find(i => `${i.type}` === '0');
      let _groupMenuData = toJS(this.groupMenuData);
      _groupMenuData = isEmpty(_groupMenuData) ? { [laneGroupId]: {} } : _groupMenuData;
      (_groupMenuData[laneGroupId] || {})[groupId] = handleGroupsMenus(this.groups, cloneDeep(newMenus), this.otrGroupInfo, wfzItem?.id).filter((m: any) => {
        if (`${group?.type}` === '0') {
          return m.key !== ListMenuActionType.updateOption && m.key !== ListMenuActionType.deleteOption;
        }
        return true;
      });
      this.groupMenuData = { ..._groupMenuData };
    });
  };

  /** ****************************** view ********************************* */
  @action
  toLoadMore = async (groupId: string, laneId: string, oldScrollHeight = 0) => {
    const fetcher = async () => {
      if (this.isDesign) {
        await this.designerStore?.previewData(groupId, laneId, true);
      } else {
        await this.getData({ groupId, loadMore: true });
      }
    };
    const handleFunc = () => {
      if (!this.isDesign) {
        const scrollBox: any = document.querySelector(`.${ebdBClsPrefix}-list-body#list_${groupId} .ui-scroller__wrap`);
        if (scrollBox && oldScrollHeight !== scrollBox.scrollHeight) {
          scrollBox.scrollTop = oldScrollHeight - 80;
        }
      }
    };
    // 插件包：拦截分组加载更多事件
    if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBPlKeys.onLoadMore)) {
      invoke(this.pluginCenter, EbdBPlKeys.onLoadMore, {
        args: [laneId, groupId, handleFunc],
      });
      return;
    }
    await fetcher();
    handleFunc();
  };

  @action
  getExtraCardInfo = async (ids: string, groupId: string) => {
    const _data: any = {
      pageId: this.pageId,
      compId: this.compId,
      extParam: JSON.stringify({
        ...this.extParamsExtra(groupId),
        ids,
      }),
    };
    if (this.isDesign) {
      _data.config = JSON.stringify({ ...this.config });
    }
    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/${this.isDesign ? 'previewExtraCardInfo' : 'getExtraCardInfo'}`,
      method: 'POST',
      data: format2FormData(_data),
      ebBusinessId: this.pageId,
    });
    const listData = res.list || [];
    const list = listData.map((i: any) => {
      return { ...i, cardMenus: formatMenuConfig(i.cardMenus) };
    });
    // 支持二开
    const [newList] = invoke(this.pluginCenter, EbdBPlKeys.onCustomSetExtraCardInfo, {
      args: [list],
    }) || [list];
    runInAction(() => {
      this.groups = this.groups.map((group: GroupItem) => {
        if (group.id === groupId) {
          const cards = (group.cards || []).map((card: any) => {
            const item = newList.find((l: any) => l.id === card.id);
            return {
              ...card,
              ...item,
            };
          });
          return {
            ...group,
            cards,
          };
        }
        return group;
      });
      newList.forEach((item: GroupPermission) => {
        if (!item.cardDraggable) {
          const dom: any = document.getElementById(item.id);
          if (dom) {
            const className = dom.getAttribute('class');
            dom.setAttribute('class', `${className} ignore-elements`);
          }
        }
      });
    });
  };

  @action('设置分组数据') setGroupDataById = async (groupId: string, response: any, loadMore: Boolean, cb?: Function) => {
    const { list = [], groupCount, statFields = [] } = response;
    const idx = this.groups.findIndex((g: any) => g.id === groupId);
    if (idx === -1) {
      return;
    }
    const { cards = [] } = this.groups[idx];
    const newList = loadMore ? cards.concat([...list]) : list;
    // 拿描述信息
    const laneGroupId = DEFAULT_LANE_ID;
    const desc = await this.getDescByData(laneGroupId, groupId, statFields, newList);
    runInAction(() => {
      this.groups[idx] = { ...this.groups[idx], cards: newList, groupCount, desc, laneId: DEFAULT_LANE_ID };
      if (this.isHideNoDataGroup && !this.isDesign) {
        // * 1201最后一条数据更新完毕 过滤掉无数据分组
        setTimeout(() => {
          const isLoaded = !this.groups.filter((i: any) => this.paginationData[i.id]?.isLoading).length;
          if (isLoaded) {
            const validGroups = this.groups.filter((i: any) => (i.cards || []).length > 0 || i.id === 'fake-add');
            runInAction(() => {
              this.groups = validGroups;
              this.isHideNoDataGroupLoading = false;
              cb && cb(validGroups);
            });
          }
        }, 300);
      } else {
        cb && cb(this.groups);
      }
      // 获取卡片上的按钮信息
      const listIds = list.map((item: any) => item.id).join(',');
      if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBPlKeys.onCustomGetExtraCardInfo)) {
        return invoke(this.pluginCenter, EbdBPlKeys.onCustomGetExtraCardInfo, { args: [groupId, listIds, this.getExtraCardInfo] });
      }
      this.getExtraCardInfo(listIds, groupId);
    });
  };
  @action('设置泳道分组数据') setLaneGroupDataById = async (laneGroupIdx: number, groupId: string, response: any, loadMore: Boolean, cb?: Function) => {
    const { list = [], groupCount, statFields = [] } = response;
    if (laneGroupIdx === -1) {
      return;
    }
    const groupIdx = this.laneGroups[laneGroupIdx].groups?.findIndex((i: any) => i.id === groupId)!;
    if (groupIdx && groupIdx <= -1) {
      return;
    }
    const { cards = [] } = this.laneGroups[laneGroupIdx].groups?.[groupIdx] || {};
    const newList = loadMore ? cards.concat([...list]) : list;
    // 拿描述信息
    const desc = await this.getDescByData(this.laneGroups[laneGroupIdx].id, groupId, statFields, newList);
    runInAction(() => {
      this.laneGroups[laneGroupIdx].groups![groupIdx] = {
        ...this.laneGroups[laneGroupIdx].groups![groupIdx],
        cards: newList,
        groupCount,
        desc,
        laneId: this.laneGroups[laneGroupIdx].id,
      };
      if (this.isHideNoDataGroup && !this.isDesign) {
        setTimeout(() => {
          const isLoaded = !this.laneGroups[laneGroupIdx].groups?.filter((i: any) => this.laneBoardPageInfo[`${this.laneGroups[laneGroupIdx].id}_${groupId}`].isLoading).length;
          if (isLoaded) {
            const validGroups = this.laneGroups[laneGroupIdx].groups?.filter((i: any) => (i.cards || []).length > 0);
            runInAction(() => {
              this.laneGroups[laneGroupIdx].groups = validGroups;
              this.isHideNoDataGroupLoading = false;
              cb && cb(validGroups);
            });
          }
        }, 300);
      } else {
        cb && cb(this.laneGroups);
      }
    });
  };
  getNowPageParams = () => {
    const { getEbParams, getUrlParams } = ebdcoms.get();
    const _params = getUrlParams(this.pageId) || {};
    return filterUrlParams(_params);
  };
  @action('处理通用看板分页数据')
  dealWithPage = async (groupId: string, loadMore: boolean = false, laneId: string) => {
    return new Promise((r, j) => {
      let pageInfo = this.isLaneConfig ? toJS(this.laneBoardPageInfo) : toJS(this.paginationData);
      let pageKey = groupId;
      if (this.isLaneConfig) {
        const laneGroupIdx = this.laneGroups.findIndex((i: any) => i.id === laneId);
        // 不合法 不处理
        if (laneGroupIdx <= -1) return;
        pageKey = `${this.laneGroups[laneGroupIdx].id}_${groupId}`;
        pageInfo = toJS(this.laneBoardPageInfo);
      }
      if (!groupId || pageInfo[pageKey]?.isLoading || pageInfo[pageKey]?.isMoreLoading) {
        j('cancel');
        return;
      }
      if (!loadMore || !pageInfo[pageKey]) {
        // 初始化
        pageInfo[pageKey] = {
          ...INITIAL_PAGE_INFO(this.pageSize),
          cancelToken: {
            func: axios.CancelToken.source(),
            compId: this.compId,
          },
        };
      } else {
        // 加载更多
        const { current, total, hasMore } = pageInfo[pageKey];
        if (!hasMore) {
          j('cancel');
          return;
        }
        pageInfo[pageKey] = {
          ...pageInfo[pageKey],
          isMoreLoading: loadMore,
          total,
          current: current + 1,
        };
      }
      if (this.isLaneConfig) {
        this.laneBoardPageInfo = pageInfo;
      } else {
        this.paginationData = pageInfo;
      }
      invoke(this.pluginCenter, EbdBPlKeys.onPageChange, { args: [pageInfo] });
      r(pageInfo);
    });
  };
  @action('处理通用看板响应数据')
  dealWithResponseData = async (groupId: string, response: any, loadMore: boolean, laneId: string, cb?: Function) => {
    // !一定要拿最新的分页信息
    let pageInfo = this.isLaneConfig ? toJS(this.laneBoardPageInfo) : toJS(this.paginationData);
    // 存在反复切换数据导致页面丢失问题 需要重新生成一次默认
    if (isEmpty(pageInfo)) {
      pageInfo = (await this.dealWithPage(groupId, loadMore, laneId)) as any;
    }
    runInAction(() => {
      const pageKey = this.isLaneConfig ? `${laneId}_${groupId}` : groupId;
      const { fieldInfo = {}, count } = response;
      const { current } = pageInfo[pageKey] || 1;
      // 设置当前分组数据
      if (this.isLaneConfig) {
        const laneGroupIdx = this.laneGroups.findIndex((i: any) => i.id === laneId);
        this.setLaneGroupDataById(laneGroupIdx, groupId, response, loadMore, cb);
      } else {
        this.setGroupDataById(groupId, response, loadMore, cb);
      }
      if (response?.list && response?.list?.length) {
        this.cardSpecialField = fieldInfo;
        if (pageInfo[pageKey]) {
          pageInfo[pageKey].total = +count;
        }
      }
      const [pageSize] = invoke(this.pluginCenter, EbdBPlKeys.getPageSize) || [this.pageSize];
      pageInfo[pageKey] = {
        ...pageInfo[pageKey],
        cancelToken: null,
        pageSize,
        hasMore: current * pageSize < +count,
        isLoading: false,
        isMoreLoading: false,
      };
      if (this.isLaneConfig) {
        this.laneBoardPageInfo = pageInfo;
      } else {
        this.paginationData = pageInfo;
      }
      invoke(this.pluginCenter, EbdBPlKeys.onPageChange, { args: [pageInfo] });
      cb && cb(response);
    });
  };
  @action('处理看板请求：兼容插件包')
  dealWithFetchData = async (groupId: string, loadMore: boolean = false, fetcher: Function, laneId: string = DEFAULT_LANE_ID, cb?: Function) => {
    try {
      this.inVideLoaded = false;
      await this.dealWithPage(groupId, loadMore, laneId);
      // 获取单个数据
      const response = await fetcher();
      if (!this.isDesign && !this.isHideNoDataGroup) {
        invoke(this.pluginCenter, EbdBPlKeys.getGroupMenus, {
          hook: () => this.getGroupMenus(groupId),
          args: [groupId],
        });
      }
      // 插件包：拦截卡片列表数据并返回新处理的数据
      if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBoardEventName.onSetData)) {
        this.handleExternalUpdateData();
        return;
      }
      if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBPlKeys.getData)) {
        const [_res] = invoke(this.pluginCenter, EbdBPlKeys.getData, {
          args: [{ ...response, groupId }],
        }) || [response];
        return this.dealWithResponseData(groupId, response, loadMore, laneId, cb);
      }
      this.dealWithResponseData(groupId, response, loadMore, laneId, cb);
    } catch (error) {}
  };
  @action('获取标准看板数据') getDataFetcher = async (groupId: string, current: string = '1', pageSize: string = KanBanDefaultPageSize, laneGroupId?: string) => {
    const params = {
      optionId: groupId || '',
      current,
      pageSize,
      ...this.dfSearchValues,
      searchParamData: {
        ...this.baseSearch.lastSearchParams,
        groupFields: !isEmpty(toJS(this.groupSearchParams)) ? toJS(this.groupSearchParams) : {},
      },
    };
    const sortParams = this.orders.filter((o: any) => o.id && o.selected).map((o: any) => ({ orderKey: o.fieldKey, orderType: o.orderType }));
    const { pageFilter, filter } = this.RefreshComFunc.getFilter({
      config: this.config,
      pageId: this.pageId,
      comId: this.compId,
    });
    const laneItem = getLaneById(this.laneGroups, laneGroupId);
    let _extParams = {
      ...this.extParamsExtra(groupId),
      pageFilter,
      sortParams,
      ...params,
      ...getLaneExtParams(this.config?.laneConfig!, laneItem),
    };
    return ajax({
      url: `/api/${this.comServicePath}/kanban/getData`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify(_extParams),
        pageSize,
        pageNo: current,
        filter: JSON.stringify(filter),
        // todo 后续对接筛选组件走这
        // filter: this.filterValues,
        // ...this.dealSearchParams,
        // ...this.getNowPageParams(), //setEbParams传递的参数实时取
      }),
      ebBusinessId: this.pageId,
      // todo
      cancelToken: this.paginationData[groupId]?.cancelToken?.func?.token,
      error: (info: any) => {},
    });
  };
  @action('获取标准看板数据') getData = async ({ groupId, loadMore, cb, laneId = DEFAULT_LANE_ID }: { groupId: string; loadMore?: boolean; cb?: Function; laneId?: string }) => {
    if (!this.config.dataset?.id || !groupId) return;
    const lanePageKey = this.isLaneConfig ? `${laneId}_${groupId}` : '';
    if (this.isLaneConfig) {
      if (this.laneBoardPageInfo[lanePageKey]?.cancelToken?.func && this.laneBoardPageInfo[lanePageKey]?.cancelToken?.compId === this.compId) {
        this.laneBoardPageInfo[lanePageKey]?.cancelToken?.func?.cancel('Request canceled');
      }
    } else {
      if (this.paginationData[groupId]?.cancelToken?.func && this.paginationData[groupId]?.cancelToken?.compId === this.compId) {
        this.paginationData[groupId]?.cancelToken?.func?.cancel('Request canceled');
      }
    }
    return new Promise(async (r, j) => {
      // 分页信息
      const current = (this.isLaneConfig ? this.laneBoardPageInfo[lanePageKey]?.current : this.paginationData[groupId]?.current) || 1;
      const pageSize = this.isLaneConfig ? this.laneBoardPageInfo[lanePageKey]?.pageSize : this.paginationData[groupId]?.pageSize;
      const fetcher = () => this.getDataFetcher(groupId, loadMore ? current + 1 : current, pageSize, laneId);
      this.dealWithFetchData(groupId, loadMore, fetcher, laneId, (newGroups: GroupItem[]) => {
        cb && cb(newGroups);
        r(newGroups);
      });
    });
  };
  // 静默更新分组才cards数据
  @action updateGroupData = (groupId: string, data: any[]) => {
    if (!groupId || !data?.length) return;
    const targetGroup = this.groups.find(group => group.id === groupId);
    if (targetGroup) {
      targetGroup.cards = [...data, ...targetGroup.cards];
    }
    this.groups = [...this.groups];
    this.getExtraCardInfo(data[0].id, groupId);
  };
  /** ************************** card **************************** */
  @action('新增看板数据')
  addCard = async (dataId: string, isFlow?: boolean, cb?: Function) => {
    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/addCard`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify({
          optionId: this.getGroupEditingData('id'),
          dataId: isFlow ? '' : dataId,
          requestId: isFlow ? dataId : '',
          ...this.dfSearchValues,
        }),
      }),
      ebBusinessId: this.pageId,
    });
    const { status, data } = res;
    if (status) {
      // * 0318 新增静默更新当前分组数据 下面的逻辑暂时废弃
      this.updateGroupData(this.getGroupEditingData('id'), parseSystemFieldType([data]));
      onMessage(this.isMobile, 'success', getLabel('158312', '新建成功'));
      setCacheData(null);
      cb && cb();
      // 自定义分组刷新当前分组即可
      // if (`${this.activeGroup.groupType}` === GroupType.custom) {
      //   this.resetSpecificBoards([this.getGroupEditingData('id')]);
      // } else {
      //   // 存在添加的时候，修改字段选项分组的问题，需要全数据刷新
      //   this.resetViewportLoading('addCard');
      // }
    }
  };

  @action('批量删除看板数据')
  deleteCards = async (optionId: string, dataIds: string[]) => {
    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/deleteCards`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify({
          optionId,
          dataIds,
          ...this.dfSearchValues,
        }),
      }),
      ebBusinessId: this.pageId,
    });
    const { status, msg = getLabel('56709', '删除失败') } = res;
    if (status) {
      this.getData({ groupId: optionId });
    } else {
      onMessage(this.isMobile, 'info', msg);
    }
  };

  @action('批量拖拽卡片')
  transferGroupCard = async (srcOptionId: string, destOptionId: string, dataIds?: string[]) => {
    const targetGroup = this.groups.find((g: any) => `${g.id}` === destOptionId);
    const fromGroup = this.groups.find((g: any) => `${g.id}` === srcOptionId);
    if (fromGroup?.archive === '1') {
      onMessage(this.isMobile, 'info', `${getLabel('288158', '当前分组无法批量拖拽到其他分组，已封存')}：${fromGroup?.name}`);
      return;
    }
    if (targetGroup?.archive === '1') {
      onMessage(this.isMobile, 'info', `${getLabel('288159', '无法批量拖拽到该分组，已封存')}：${targetGroup?.name}`);
      return;
    }
    if (!dataIds) {
      const cards = this.groups.find((g: any) => g.id === srcOptionId)?.cards || [];
      dataIds = cards.map((c: any) => c.id);
    }
    let _extParams: any = {
      srcOptionId,
      destOptionId,
      dataIds,
      ...this.dfSearchValues,
    };
    if (`${this.activeGroup.groupType}` === GroupType.dateRange) {
      _extParams.baseTime = targetGroup?.baseTime;
    }
    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/transferGroupCard`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify(_extParams),
      }),
      ebBusinessId: this.pageId,
    });
    if (!res.status) {
      onMessage(this.isMobile, 'info', res?.msg || getLabel('77125', '请求异常，请联系管理员'));
    } else {
      if (res?.msg) {
        onMessage(this.isMobile, 'success', res?.msg);
      }
      this.getData({ groupId: srcOptionId });
      this.getData({ groupId: destOptionId });
    }
  };

  @action
  dragCard = async (startGroupId: string, endGroupId: string, dataId: string, prevId?: string) => {
    const currentGroup = this.groups.find((g: any) => `${g.id}` === endGroupId)!;
    let _extParams: any = {
      originOptionId: startGroupId,
      currentOptionId: endGroupId,
      prevId,
      dataId,
      // 当前分组拖到为分组
      currentNoGroup: `${currentGroup.type}` === '0' ? '1' : '0',
      ...this.dfSearchValues,
    };
    if (`${this.activeGroup.groupType}` === GroupType.dateRange) {
      _extParams.baseTime = currentGroup?.baseTime;
    }
    if (!this.isEbFormDataSet) {
      _extParams.customGroup = '1';
    }
    const res = await ajax({
      url: `/api/${this.comServicePath}/kanban/dragCard`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify(_extParams),
      }),
      ebBusinessId: this.pageId,
    });
    if (!res.status) {
      onMessage(this.isMobile, 'info', res?.msg || getLabel('77125', '请求异常，请联系管理员'));
    } else {
      if (res?.msg) {
        onMessage(this.isMobile, 'info', res?.msg);
      } else {
        onMessage(this.isMobile, 'success', getLabel('54026', '操作成功'));
      }
      this.setOrders();
      // *如果是按人员分组 且当前分组已经没数据了 重新获取分组数据 去掉当前人员分组
      if (`${this.activeGroup.groupType}` === GroupType.field && this.activeGroup.fieldType === EtComponentKey.Employee) {
        const startGroups = this.groups.filter((g: any) => `${g.id}` === startGroupId);
        if (!(startGroups.length - 1)) {
          this.getGroupsControl();
          return;
        }
      }
      this.getData({ groupId: startGroupId });
      this.getData({ groupId: endGroupId });
    }
  };

  @action
  onCardSortEnd = async (currentGroups: GroupItem[], dataId: string, startObj: any, endObj: any) => {
    const { groupId: startGroupId, startIndex } = startObj;
    const { groupId: endGroupId, endIndex } = endObj;
    const currentGroup = currentGroups.find((g: any) => g.id === endGroupId);
    if (startGroupId === endGroupId) {
      // 组内移动
      if (startIndex === endIndex || currentGroup!.cards.length <= 1) {
        // 数量小于1(0=>1)或位置没变
        return; // 移动了个寂寞
      }
    }
    this.groups = currentGroups;
    let prevId: string | undefined;
    if (currentGroup && endIndex !== 0) {
      prevId = currentGroup.cards[endIndex - 1].id;
    }
    await this.dragCard(startGroupId, endGroupId, dataId, prevId);
    const cutPayload = { startGroupId, endGroupId, dataId, prevId };
    this.emitEvent(EbdBoardEventName.onCardDragEvt, cutPayload);
  };

  /**
   * @function getGroupByCardId
   * @param dataId [string] 数据id
   * @desc 根据卡片id找到其所在分组的id
   * @return groupId [GroupItem | undefined]
   * */
  getGroupByCardId = (dataId: string): GroupItem => {
    const currentGroup = this.groups.find((group: any) => !!group.cards.find((c: any) => c.id === dataId));
    return currentGroup!;
  };

  @action
  exchangeCardFollowBtn = (dataId: string, becomeFollow: boolean) => {
    const nowgroup: any = this.getGroupByCardId(dataId);
    let nowcards = nowgroup.cards;
    nowcards = nowcards.map((card: any) => {
      if (card.id === dataId) {
        const cardMenus = card.cardMenus.map((menu: any) => {
          if (menu.id === 'follow') {
            menu.visible = becomeFollow;
          } else if (menu.id === 'unfollow') {
            menu.visible = !becomeFollow;
          }
          return menu;
        });
        return {
          ...card,
          cardMenus,
        };
      }
      return card;
    });
    runInAction(() => {
      nowgroup.cards = nowcards;
    });
  };

  @action
  getGroupEditingData = (prop?: string) => {
    if (!this.groupEditingData.id) {
      const cache = getCacheData();
      const data = cache?.groupEditingData || this.groupEditingData;
      return prop ? data[prop] : data;
    }
    return prop ? this.groupEditingData[prop] : this.groupEditingData;
  };
  @action resetMenuActions = async () => {
    this.customMenuActions = [];
  };
  @action('获取看板自定义按钮点击事件集合') getMenuActions = async () => {
    try {
      if (this.menusCancelToken?.compId === this.compId) {
        this.menusCancelToken?.func?.cancel('Request canceled');
      }
      this.menusCancelToken = {
        func: axios.CancelToken.source(),
        compId: this.compId,
      };
      const res = await ajax({
        url: `/api/${this.comServicePath}/button/openButtonList`,
        method: 'POST',
        ebBusinessId: this.pageId,
        data: format2FormData({
          pageId: this.pageId,
          compId: this.compId,
        }),
        cancelToken: this.menusCancelToken?.func?.token,
        error: (info: any) => {},
      });
      const _res = res.map((i: any) => {
        return {
          ...i,
          buttonType: 'CUSTOM',
          name: i.name.nameAlias ? i.name.nameAlias : i.name,
        };
      });
      runInAction(() => (this.customMenuActions = isValueEmpty(_res) ? [] : _res));
    } catch (error) {}
  };
  @action
  onAddNewData = (data: any) => {
    if (data.archive === '1') {
      onMessage(this.isMobile, 'info', `${getLabel('288165', '当前分组无法新建数据，已封存')}：${data?.name}`);
      return;
    }
    this.groupEditingData = data;
    setCacheData({ groupEditingData: data }); // 缓存正在操作的分组信息，H5 iframe下组件卸载重载后参数丢失的问题
    this.setFormField(data);
    this.createCardData();
  };

  @action
  onClearNewData = () => {
    this.groupEditingData = {};
    setCacheData({ groupEditingData: {} });
    window.sessionStorage.removeItem(FormDefaultValueSessionKey);
  };

  @action
  onEditCardData = (item: any) => {
    this.cardEditingData = item;
    this.showCardData(item.id, true);
  };

  onShowCardData = (item: any) => {
    // 移动端兼容处理（路由层级问题）
    if (this.isMobile) {
      this.showCardData(item.id);
      return;
    }
    this.showCardData(item.id);
  };

  @action
  onCardClick = (data: any) => {
    if (!data.cardDraggable && isCardOpened()) {
      window.history.go(-1);
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.onShowCardData(data);
      }, 0);
    } else {
      this.onShowCardData(data);
    }
  };

  @action
  handleFollow = async (dataids: string[], follow: boolean) => {
    try {
      const res = await followApi({
        objId: this.config.dataset?.id,
        follow,
        sourceIds: dataids,
      });
      if (res) {
        this.exchangeCardFollowBtn(dataids[0], !follow);
        followEvents(dataids, follow ? CardButtonType.Follow : CardButtonType.Unfollow, false);
        onMessage(this.isMobile, 'success', follow ? getLabel('101370', '关注成功!') : getLabel('101371', '取消关注成功!'));
      }
    } catch {
      return false;
    }
  };
  @action('行末下拉按钮点击')
  onCardMenuClick = async (key: any, data: any, props: any, currentDom: any) => {
    switch (key) {
      case CardMenuActionType.edit:
        this.onEditCardData(data);
        break;
      case CardMenuActionType.delete: {
        Dialog.confirm({
          title: getLabel('55437', '确认'),
          content: getLabel('54077', '确定要删除吗？'),
          mask: true,
          onOk: async () => {
            const currentGroup = this.getGroupByCardId(data.id);
            const delFunc = (groupId: string, dataId: string) => this.deleteCards(groupId, [dataId]);
            if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBPlKeys.onCardDel)) {
              invoke(this.pluginCenter, EbdBPlKeys.onCardDel, {
                args: [currentGroup, data, delFunc],
              });
              return;
            }
            await delFunc(currentGroup?.id!, data.id);
          },
        });
        break;
      }
      case CardMenuActionType.follow:
      case CardMenuActionType.unfollow:
        const isFollow = key === CardMenuActionType.follow;
        const followFunc = (dataId: string, isFollow: boolean) => this.handleFollow([dataId], isFollow);
        if (hasRegisterFuncInPlugin(this.pluginCenter, EbdBPlKeys.onCardFollow)) {
          invoke(this.pluginCenter, EbdBPlKeys.onCardFollow, {
            args: [data, isFollow, followFunc],
          });
          return;
        }
        await followFunc(data.id, isFollow);
        break;
      default:
        this.handleBoardCustomAction(key, { props, itemData: data }, currentDom);
        break;
    }
    // 处理sdk事件
    const actionKeys = [CardMenuActionType.edit, CardMenuActionType.follow, CardMenuActionType.unfollow, CardMenuActionType.delete];
    if (actionKeys.includes(key)) {
      const cutPayload = { type: key, data };
      this.emitEvent(EbdBoardEventName.onCardDataUpdate, cutPayload);
    }
  };

  @action('eb高级视图后台自定义按钮事件') handleCardCustomAction = (key: string, props?: any) => {
    // 匹配自定义按钮点击事件
    const btnConfig: any = this.customMenuActions.find(i => i.id === key);
    // todo set dataSet
    const dataset: any = {};
    const dataParams = {
      compId: this.compId,
      pageId: this.pageId,
      ids: [],
    } as any;
    const routeProps = {
      history: {
        location: window.location,
      },
      ...props,
      data: {},
      notNeedUpadte: true,
    };
    this.btnActionCommonStore.triggerButtonClick(
      {
        btnConfig,
        actions: btnConfig.actions,
        btnType: btnConfig.buttonKey,
        client: 'PC',
      },
      {
        ids: dataParams.ids,
        objId: dataset.id,
        compId: dataParams.compId,
        pageId: dataParams.pageId,
        formDataIds: dataParams.formDataIds,
        dataset,
      },
      routeProps
    );
  };
  @action('看板自定义按钮事件') handleBoardCustomAction = (key: string, otherParams: any, currentDom?: any) => {
    const { itemData: data, props } = otherParams;
    // 匹配自定义按钮点击事件
    const btnData: any = deepToJS(this.customMenuActions.find(i => i.id === key) || {});
    let {
      config: { dataset },
      history,
      match,
      page,
      compId,
    } = props;
    // 补一个name（pdf导出时没有文件名）
    if (!page.name) {
      page = { ...page, name: document.title };
    }
    const datasetMap: DataSet[] = [dataset as DataSet];
    const linkOpts = {
      history,
      match,
      clientType: props.isMobile ? 'MOBILE' : 'PC',
      fields: data,
    };
    if (!btnData?.actions[0]?.[0]?.events) {
      // 兼容eb高级视图配置的按钮 第一次同步过来 没有actions[0][0].events
      if (btnData.actions[0]?.actionType) {
        return this.handleCardCustomAction(key, otherParams.props);
      }
      return Dialog.message({ type: 'info', content: getLabel('61986', '该按钮未启用任何动作！') });
    }
    if (btnData?.actions[0]?.[0]?.events?.[0]?.linkInfo?.openMode === '0') {
      window._Opener_ebPageReloadList = (time: number = 0) =>
        setTimeout(() => {
          props.events?.emit('refresh.list', compId);
        }, time);
    }
    const params = {
      isChild: false,
      data,
      page,
      comId: compId,
      datasetMap,
      dom: currentDom as any,
      events: (props as any).events,
      renderLayout: props.renderLayout,
    };
    executeActions(currentDom as any, btnData.actions[0][0].events, linkOpts, params);
  };

  @action('批量拖拽卡片')
  transferGroupCardBefore = (sourceGroup: any, groupId: any) => {
    const group = this.groups.find((g: any) => g.id === groupId);
    const { cards = [] } = sourceGroup;
    let breakAll: any = 'break' + '-all';
    if (!this.isMobile) {
      if (!cards.length) {
        Dialog.message({ type: 'info', content: getLabel('116142', '当前分组中无数据！') });
        return;
      }
      Dialog.confirm({
        title: getLabel('105946', '信息确认'),
        content: getLabel('222260', '确定要将【{0}】中的数据转移至【{1}】吗？', [sourceGroup.name, group?.name]),
        mask: true,
        wrapStyle: {
          wordBreak: isIE() ? breakAll : 'break-word',
        },
        onOk: () => {
          this.transferGroupCard(sourceGroup.id, groupId);
        },
      });
    } else {
      if (!cards.length) {
        MDialog.toast({ type: 'info', content: getLabel('116142', '当前分组中无数据！') });
        return;
      }
      MDialog.prompt({
        title: getLabel('222260', '确定要将【{0}】中的数据转移至【{1}】吗？', [sourceGroup.name, group?.name]),
        mask: true,
        maskClosable: true,
        prompt: false,
        onClose: () => {},
        onOk: () => {
          this.transferGroupCard(sourceGroup.id, groupId);
        },
        wrapClassName: `${mBoardClsPrefix}-mialog-prompt`,
      });
    }
  };
  @action('右上角更多按钮菜单点击')
  onListMenuChange = async (key: string, data: any, otherParams: any) => {
    if (key === ListMenuActionType.moveData) {
      return MDialog.toast({ type: 'info', content: getLabel('223521', '无可移动分组') });
    }
    if (key.startsWith(`${ListMenuActionType.moveData}_`)) {
      const groupId = key.split('_')[1];
      this.transferGroupCardBefore(data, groupId);
      return;
    }
    switch (key) {
      case ListMenuActionType.addData:
        this.onAddNewData(data);
        break;
      case ListMenuActionType.deleteOption:
        if (this.isMobile) {
          MDialog.prompt({
            title: getLabel('54077', '确定要删除吗？'),
            mask: true,
            maskClosable: true,
            prompt: false,
            onClose: () => {},
            onOk: () => {
              this.deleteGroup(data);
            },
          });
          return;
        }
        Dialog.confirm({
          title: getLabel('55437', '确认'),
          content: getLabel('54077', '确定要删除吗？'),
          mask: true,
          onOk: () => {
            this.deleteGroup(data);
          },
        });
        break;
      case ListMenuActionType.updateOption:
        this.onGroupEditVisibleChange(data);
        break;
      default:
        this.handleBoardCustomAction(key, otherParams, otherParams?.e?.target);
        break;
    }
  };

  @action('兼容自定义编辑和关联ebuilder编辑')
  onGroupEditVisibleChange = (item?: any) => {
    if (this.isEbuilderField) {
      this.createGroupDataByEbuilder('edit', item.id);
      return;
    }
    this.groupEditingData = item;
    setCacheData({ groupEditingData: item });
    this.groupEditVisible = true;
  };

  @action
  closeGroupEdit = () => {
    this.groupEditVisible = false;
  };

  @action('触发分组相关二开口子事件')
  emitGroupEvt = (type: string, extParam: any) => {
    const payload = {
      type,
      extParam,
    };
    this.emitEvent(EbdBoardEventName.onGroupDataUpdate, payload);
  };
  @action('静默添加分组')
  addGroupStatic = async (item: any, laneGroupId: string = DEFAULT_LANE_ID) => {
    if (!item.id) {
      return;
    }
    // 前端生成mock分组
    const newGroup: GroupItem = {
      id: item.id,
      name: item.name,
      type: item.type || '1',
      cards: [],
    };
    let newGroups = toJS(this.groups);
    newGroups.unshift(newGroup);
    // 页面定位到当前分组
    this.events.emit(EbdBoardEventName.scrollToLeft);
    const promises: Promise<any>[] = [];
    newGroups = newGroups.map((group: any) => {
      // 重新同步下分组菜单 不然’移动列表数据到‘功能失效
      if (this.isBoardLoaded(group.id) && group.id !== newGroup.id) {
        promises.push(this.getGroupMenus(group.id, laneGroupId));
      }
      return group; // 其他分组
    });
    await Promise.all(promises);
    runInAction(() => {
      this.groups = newGroups;
      this.groupRefreshKey = new Date().getTime();
    });
  };
  @action
  addOrEditGroup = async (item: any, laneGroupId: string = DEFAULT_LANE_ID) => {
    // 如果开启了关联字段差异分组，这个字段要加上
    if (this.diffRootId) {
      item.rootid = this.diffRootId;
    }
    const extParam = { ...this.getGroupEditingData(), ...item };
    const success = () => {
      runInAction(() => {
        this.groupEditVisible = false;
        this.groupEditingData = {};
        setCacheData(null);
      });
    };
    if (!this.groupEditingData.id) {
      // 判断当前是否有重名看板，有的话不允许添加重名
      if (this.groups.some((g: any) => g.name === item.name)) {
        onMessage(this.isMobile, 'info', `${getLabel('280280', '分组名称重复，请重新输入')}！`);
        return;
      }
      // * 非表单数据源新建分组走二开口子自己新建 （业务方自己处理分组创建逻辑并刷新当前分组）
      if (!this.isEbFormDataSet) {
        this.emitGroupEvt('add', { addItem: extParam, groups: toJS(this.groups), cb: success });
        return;
      }
      // 正常老逻辑
      const response = await ajax({
        url: `/api/${this.comServicePath}/kanban/addGroup`,
        method: 'POST',
        data: format2FormData({
          pageId: this.pageId,
          compId: this.compId,
          extParam: JSON.stringify(extParam),
        }),
        ebBusinessId: this.pageId,
      });
      if (response.id) {
        // 静默添加
        this.addGroupStatic({ ...extParam, id: response.id }, laneGroupId);
      } else {
        await this.getGroupsControl();
      }
      this.emitGroupEvt('add', { addItem: extParam, groups: toJS(this.groups) });
    } else {
      await this.updateGroup(extParam);
    }
    success();
  };

  @action
  updateGroup = async (item?: any) => {
    const success = () => {
      runInAction(() => {
        this.groupEditingData = {};
        setCacheData(null);
        this.groupEditVisible = false;
      });
    };
    // * 非表单数据源更新分组走二开口子自己更新 （业务方自己处理分组更新逻辑并刷新当前分组）
    if (!this.isEbFormDataSet) {
      this.emitGroupEvt('update', { groups: toJS(this.groups), updateItem: item, cb: success });
      return;
    }
    await ajax({
      url: `/api/${this.comServicePath}/kanban/updateGroup`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify({ ...item, ...this.extParamsExtra(item?.id) }),
      }),
      ebBusinessId: this.pageId,
    });
    runInAction(() => {
      this.groups = this.groups.map((group: any) => {
        // 重新同步下分组菜单 不然’移动列表数据到‘功能失效
        if (this.isBoardLoaded(group.id)) {
          this.getGroupMenus(group.id);
        }
        if (item.id === group.id) {
          // 当前分组
          return { ...group, name: item.name, color: item.color };
        }
        return group; // 其他分组
      });
      this.emitGroupEvt('update', { groups: toJS(this.groups) });
      success();
    });
  };

  @action
  deleteGroup = async (item?: any) => {
    // * 非表单数据源删除分组走二开口子自己处理拖拽逻辑 （业务方自己处理分组并刷新当前分组）
    if (this.isEbFormDataSet) {
      const { name = '', ..._extParams } = { ...item, ...this.extParamsExtra(item?.id) };
      const res = await ajax({
        url: `/api/${this.comServicePath}/kanban/deleteGroup`,
        method: 'POST',
        data: format2FormData({
          pageId: this.pageId,
          compId: this.compId,
          extParam: JSON.stringify(_extParams),
        }),
        ebBusinessId: this.pageId,
      });
      if (!res.status) {
        onMessage(this.isMobile, 'info', res?.msg || getLabel('77125', '请求异常，请联系管理员'));
        return;
      }
      if (res?.msg) {
        onMessage(this.isMobile, 'info', res?.msg);
      }
      // 自定义分组支持静默删除 其他分组有不确定性问题 先不支持 后续验证
      const index = this.groups.findIndex((g: any) => g.id === item.id);
      if (index !== -1 && `${this.activeGroup.groupType}` === GroupType.custom) {
        runInAction(() => {
          this.groups.splice(index, 1);
          delete this.paginationData[item.id];
          delete this.groupMenuData[item.id];
          this.groups = this.groups.map((group: any) => {
            // 重新同步下分组菜单 不然’移动列表数据到‘功能失效
            if (this.isBoardLoaded(group.id)) {
              this.getGroupMenus(group.id);
              // * 如果未分组在可视范围内 且当前删除分组下cards有数据  需要重新刷新下未分组 因为数据在未分组内
              if (group.name === getLabel('105298', '未分组') && !isEmpty(item.cards)) {
                this.getData({ groupId: group.id });
              }
            }
            return group; // 其他分组
          });
        });
      } else {
        const setFakeGroup: Function = () => {
          // 如果能新建分组 且为pc端 需要手动push 一个fake分组进去
          const exists = this.groups.some(el => el.id === 'fake-add');
          if (!exists && this.hasAddGroupRight && !this.isMobile) {
            runInAction(() => {
              this.groups.push(FAKE_ADD);
            });
          }
        };
        await this.getGroupsControl(setFakeGroup);
      }
    }
    this.emitGroupEvt('delete', { deleteItem: item, groups: toJS(this.groups) });
  };

  @action
  dragGroup = async (nowGroups: any[]) => {
    // * 非表单数据源新建分组走二开口子自己处理拖拽逻辑 （业务方自己处理分组并刷新当前分组）
    // * 20250317 放开数仓+业务数据源自定义分组下可以支持分组拖拽排序
    if (this.hasDragGroupRight) {
      const res = await ajax({
        url: `/api/${this.comServicePath}/kanban/dragOption`,
        method: 'POST',
        data: format2FormData({
          pageId: this.pageId,
          compId: this.compId,
          extParam: JSON.stringify({
            options: nowGroups,
            // mark
            ...this.extParams,
          }),
        }),
        ebBusinessId: this.pageId,
      });
      if (!res.status) {
        onMessage(this.isMobile, 'info', res?.msg || getLabel('77125', '请求异常，请联系管理员'));
        return;
      }
      if (res?.msg) {
        onMessage(this.isMobile, 'info', res?.msg);
      } else {
        onMessage(this.isMobile, 'success', getLabel('54026', '操作成功'));
      }
    }
    this.emitGroupEvt('drag', { groups: nowGroups });
  };

  @action
  onListSortEnd = (item: any) => {
    this.groups = item;
    const nowGroups = item.map((x: any) => ({
      id: x.id,
      name: x.name,
      type: x.type,
    }));
    this.dragGroup(nowGroups);
  };

  /** **************************** search ******************************* */
  @action('获取默认值')
  getDefaultFilter = async (): Promise<any> => {
    if (!this.config.dataset?.id) return;
    // todo 非eb数据源的常用筛选 走filter数据里前端构建
    // todo 后续新的需求不走这里 走newSearchStore逻辑处理
    if (!this.isEbFormDataSet) {
      await this.baseSearch.getCommonFiltersWithoutEb();
      return;
    }
    // 这下面只在eb表单数据源下生效
    const { compId, pageId } = this.viewComInfo || {};
    const mobilePath = this.isMobile ? '/app' : '';
    const res = await ajax({
      url: `/api${mobilePath}/ebuilder/form/viewFilter/getFilterDefault/${pageId}/${compId}`,
      method: 'post',
      data: {
        ...this.dealSearchParams,
        ...this.getNowPageParams(), //setEbParams传递的参数实时取
      },
      ebBusinessId: pageId || '',
      error: () => {},
    });
    runInAction(async () => {
      if (!res) {
        return;
      }
      const { groupFields, ...params } = res?.searchParamData || {};
      const searchData = this.formatDfData(res);
      this.setFilterDefault(res, searchData);
      this.baseSearch.lastSearchParams = searchData;
      // todo 临时屏蔽 走前端config初始化 getGroupFilters
      // this.groupSearchParams = groupFields;
      // 获取分类搜索配置
      // todo 临时屏蔽 走前端config初始化 getGroupFilters
      // await this.getGroupFilters({ ...res, searchParamData: { ...searchData, groupFields } });
      // 加载高级搜索,并给高级搜索设置默认值
      const needAd = this.commonSearch === CommonSearchType.Advanced;
      const needTop = !needAd && !this.isMobile;
      if (needTop) {
        this.baseSearch.getCommonFilters();
      }
      this.baseSearch.initAdvance(needAd || false, () => {
        if (!needTop) {
          onAfterAdInit(this.baseSearch.searchAdvancedStore.formStore, params);
        }
      });
    });
  };
  @action('新版本：获取分类搜索配置')
  getGroupFilters = async (isInit?: boolean, params?: any) => {
    const { leftMenuDatas, groupTopDatas, groupSearchDefaultParams } = (await this.baseSearch.getGroupSearchFilter(isInit)) || {};
    this.setState({
      leftMenuTreeDatas: leftMenuDatas,
      groupTopSearchDatas: groupTopDatas,
      groupSearchParams: groupSearchDefaultParams,
    });
  };
  @action('获取分类搜索配置：表单高级视图下(待废弃)')
  getGroupFiltersV2 = (params?: any) => {
    const { compId, pageId } = this.viewComInfo || {};
    ajax({
      url: `/api${this.isMobile ? '/app' : ''}/ebuilder/form/viewFilter/getGroupSearch/${pageId}/${compId}`,
      method: 'post',
      data: addPhysicalParams(params, this.config?.dataset),
      ebBusinessId: pageId || '',
    }).then(res => {
      runInAction(() => {
        if (this.isMobile) {
          const _data = res.filter((dt: any) => H5GroupShieldFieldTypes.indexOf(dt.componentKey) < 0).map((d: any) => ({ ...d, content: d.showName, id: d.fieldName }));
          this.groupSearchDatasH5 = _data;
        } else {
          const _data = res.map((d: any) => ({ ...d, content: d.showName, id: d.fieldName }));
          this.groupTopSearchDatas = _data.filter((g: any) => g.showPosition === ShowPosition.Top);
          this.leftMenuTreeDatas = _data.filter((g: any) => g.showPosition === ShowPosition.Left);
        }
      });
    });
  };

  @action('快捷/高级搜索')
  onSearch = (datas: SearchDatas, dataSource: string, otherParams?: any) => {
    const { type } = otherParams || {};
    const { quickSearchDatas } = datas;
    // 快捷常用筛选\高级面板 ---> 走高级逻辑
    if (type === 'snap' || dataSource === 'fromSearchPanel') {
      const adData: SearchDatas = parseAdData(datas);
      const newFormDatas = getConditionFormDatas(adData.formDatas || {});
      ls.setItem(ListSearchCacheParams.CurrentSearchState, SearchType.AdvanceSearch);
      this.advancedParams = {
        ...this.advancedParams,
        formDatas: newFormDatas,
        type: adData.type,
      };
      this.baseSearch.lastSearchParams = {
        searchType: SearchType.AdvanceSearch,
        ...newFormDatas,
        type: adData.type,
      };
      this.baseSearch.onVisibleChange(false);
      this.baseSearch.searchAdvancedStore.setQuikSearchVisible(false);
    } else {
      // 快捷搜索 ---> 走快捷
      ls.setItem(ListSearchCacheParams.CurrentSearchState, SearchType.QuickSearch);
      this.advancedParams = { ...this.advancedParams, quickSearchDatas };
      this.baseSearch.lastSearchParams = {
        searchType: SearchType.QuickSearch,
        ...quickSearchDatas,
      };
    }
    this.getGroupsControl(() => {
      if (this.hasAddGroupRight && !this.groups.some(i => i.id === 'fale-add') && !this.isMobile) {
        runInAction(() => {
          this.groups.push(FAKE_ADD);
        });
      }
    });
  };

  @action('筛选搜索')
  filterSearch = (datas: any = {}, type: any) => {
    const getSearchData = () => {
      if (this.isHideNoDataGroup) {
        this.getGroupsControl();
      } else {
        this.resetViewportLoading('filterSearch');
      }
    };
    if (!this.isEbFormDataSet) {
      this.baseSearch.getSearchParamsWithoutEbV2(datas, type);
      getSearchData();
      return;
    }
    // 记录状态
    ls.setItem(ListSearchCacheParams.CurrentSearchState, SearchType.FilterSearch);
    const newFormDatas = getConditionFormDatas(datas || {});
    this.baseSearch.lastSearchParams = {
      searchType: SearchType.FilterSearch,
      ...newFormDatas,
      type,
    };
    getSearchData();
  };

  @action
  onOrderClick = async (id: string) => {
    this.setOrders(id);
    await this.getGroupsControl();
    this.resetViewportLoading('onOrderClick');
  };

  @action
  setRouteProps = (routerProps: AnyObj) => {
    this.routerProps = routerProps;
  };

  @action
  openCard = async ({
    type,
    objId,
    dataId,
    openMode = OpenMode.SlideLeftFromRight,
    otherLinkInfo,
    digType = 'card',
  }: {
    type: LayoutType;
    objId: string;
    dataId?: string;
    openMode?: OpenMode;
    otherLinkInfo?: any;
    digType: 'group' | 'card'; // 打开的卡片类型
  }) => {
    if (objId) {
      const { fieldId = '', values = [] } = this.activeGroup;
      const exEbParams: any = getExParamsByEbParams(toJS(this.dealSearchParams), this.pageId);
      let exParams: any = [];
      let newExParamsValue = this.groupEditingData?.valueKey || this.groupEditingData?.id;
      if (type === LayoutType.Add) {
        exParams = exParams.concat(exEbParams);
        if (`${this.activeGroup.groupType}` === GroupType.dateRange && this.groupEditingData.id !== '-1') {
          //日期分组格式化传值
          newExParamsValue = dayjs(this.groupEditingData.baseTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
        }
        if (this.groupEditingData.id !== '-1' && this.groupEditingData?.type !== 0) {
          exParams.push({
            //未分组的不进行默认值传递
            name: 'field_' + fieldId,
            value: newExParamsValue,
            type: 'fixed',
          });
        }
      }
      const params = {
        appId: this.config?.dataset?.groupId!,
        objId,
        type,
        dataId,
        // ["field_"+ fieldId]: toJS(values).map((el:any)=>el?.id).join(','),
        ['field_' + fieldId]: newExParamsValue,
      };
      // 支持二开
      const [newParams, newExParams] = invoke(this.pluginCenter, EbdBPlKeys.getBoardAddParams, {
        args: [params, exParams, digType],
      }) || [params, exParams];
      if (this.isMobile) {
        const historyPush = this.routerProps?.history?.push;
        historyPush({
          pathname: `${formatParentPath(this.routerProps)}/mCardDetailView`,
          state: newParams,
        });
        return;
      }
      jumpToCard(
        openMode,
        newParams,
        newExParams,
        { ...this.routerProps, compId: this.compId, clientType: this.isMobile ? 'MOBILE' : 'PC' },
        {
          modalWidth: otherLinkInfo?.modalWidth, // 弹出窗口时弹窗宽度的设置
          modalHeight: otherLinkInfo?.modalHeight, // 弹出窗口时弹窗高度的设置
          winSizeNew: otherLinkInfo?.winSizeNew || '50%', // 右侧滑出、右侧推出时窗口宽度的设置
        },
        digType
      );
    }
  };
  getDlgCusParams = (type: 'card' | 'group', params: any) => {
    const [newJson] = invoke(this.pluginCenter, type === 'group' ? EbdBPlKeys.getGroupEditDlgParams : EbdBPlKeys.getCardEditDlgParams, {
      args: [params],
    }) || [params];
    return newJson;
  };
  // 关联ebuilder字段新建分组 新建时 新建的是当前ebuilder字段的关联表单
  createGroupDataByEbuilder = async (type: 'add' | 'edit', dataId?: string) => {
    const handleOpenCard = (fieldId: string = '') => {
      const json = {
        type: type === 'add' ? LayoutType.Add : LayoutType.Edit,
        objId: fieldId,
        dataId,
      };
      this.setState({ groupEditingData: json });
      this.openCard({ ...this.getDlgCusParams('group', json), digType: 'group' });
    };
    let dataset: any = this.config.dataset;
    const data = await getRelationBrowserFields(dataset!);
    const sForm = data.find((k: any) => k.id === this.activeGroup.fieldId);
    if (!sForm?.config?.sformId) {
      onMessage(this.isMobile, 'info', getLabel('279135', '当前关联表单不存在'));
      return;
    }
    handleOpenCard(sForm?.config?.sformId);
  };

  /** *****************************************MOBILE*********************************** */
  createCardData = async () => {
    const json = {
      type: LayoutType.Add,
      objId: this.config?.dataset?.id as string,
    };
    this.openCard(this.getDlgCusParams('card', json));
  };
  showCardData = (dataId: string, edit?: boolean) => {
    const json = {
      type: edit ? LayoutType.Edit : LayoutType.Show,
      objId: this.config?.dataset?.id as string,
      dataId,
    };
    this.openCard(this.getDlgCusParams('card', json));
  };

  /** ********************↑↑↑↑↑↑↑↑↑↑↑↑↑↑ MOBILE FUNCTIONS↑↑↑↑↑↑↑↑↑↑↑********************** */
  /**
   * 获取实时的分组菜单的信息,前端更改分组名称时,不会重新请求
   * */
  getRealTimeGroupMenu = (laneId: string, nowGroupId: string) => {
    const { groupMenuData, groups } = this;
    const otherGroups = groups
      .filter((g: any) => {
        return g.id !== nowGroupId;
      })
      .map((g: any) => ({
        id: `moveData_${g.id}`,
        content: g.name,
      }));
    const menus = (groupMenuData[laneId]?.[nowGroupId] || []).map((mItem: any) => {
      if (mItem.id === 'moveData' && mItem?.children && mItem.children.length > 0) {
        return {
          ...mItem,
          children: otherGroups,
        };
      }
      return mItem;
    });
    return menus;
  };

  @action('分组操作后同步修改分组搜索对象')
  updateGroupSearchParams = (params: any = {}) => {
    this.groupSearchParams = {
      ...this.groupSearchParams,
      ...params,
    };
    this.resetViewportLoading('updateGroupSearchParams');
  };
}

export default new BoardViewStore();
