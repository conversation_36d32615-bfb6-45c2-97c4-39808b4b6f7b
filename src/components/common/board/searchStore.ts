import { SearchAdvancedStore, SearchDatas, Dialog } from '@weapp/ui';
import { getLabel, isEmpty, forIn } from '@weapp/utils';
import { action, observable, runInAction, toJS } from 'mobx';
import { conditionNullArr } from '../../../constants';
import { EtComponentKey } from '../../../constants/EtComponent';
import { AnyObj, FilterData } from '../../../types/common';
import { getSystemFieldType, _setTimeout } from '../../../utils';
import ebdcoms from '../../../utils/ebdcoms';
import { getSearchSetting, appendParams, getUrlParams, getSearchTypeAndfilters, ebdComsAjax, isValueEmpty } from './utils';
import { SearchType, BaseParamsProps } from './types';
import { conditionSuffix, EbuilderComsModule, BIMODULE, FilterNoCacheKey } from './constants';
import dialogObj from './dialogObj';

export enum OptionEnableType {
  Enable = '1',
  Disable = '0',
}

export class SearchStore {
  constructor(params: BaseParamsProps) {
    runInAction(() => {
      this.updateSearchSetting(params);
    });
  }

  @observable firstLoaded = false;
  @observable searchAdvancedStore: SearchAdvancedStore = new SearchAdvancedStore(); // 高级搜索
  @observable filterData: FilterData[] = []; // 常用筛选的字段数据
  @observable fields: any[] = [];
  @observable props: any;
  @observable searchConfig: any;
  @observable searchSetting: any;
  // 传入搜索配置名称，用来替换默认的名称searchConditions
  searchConfigKey = 'filter';
  /** 高级搜索接口自定义入参 */
  getTableConditionCustomParams?: Function;

  /** 是否为运行视图 */
  isPreView: boolean = true;
  // 网关标识
  @observable comServicePath: string = EbuilderComsModule;
  // 高级搜索唯一标识
  @observable conditionId: string = '';
  /** 记录设置了必填的项 */
  recordRequiredItems: any = {};

  getField = async () => {
    const { config } = this.props;
    const { dataset } = config;
    const { dsUtils } = await ebdcoms.get();
    await dsUtils.getFields(dataset).then((data: any) => {
      const fields: any[] = [];
      data.forEach((el: any) => {
        el.fields?.forEach((_field: any) => {
          const field = ebdcoms.get().transformFields(_field);
          // 系统字段类型转化表单类型
          field.compType = getSystemFieldType(field.id, field.compType as EtComponentKey);
          fields.push(field);
        });
      });
      runInAction(() => (this.fields = fields));
    });
  };

  @action
  setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };
  @action init = async () => {
    const { config, id: compId = '' } = this.props;
    const datasetId = config.dataset?.id;
    this.conditionId = `${compId}${datasetId}`.replace(/[A-Za-z]+/g, '');
    await this.getField();
    if (this.searchSetting.hasAd || this.searchSetting.hasQuick) {
      this.initAd();
    }
    // ** 分类搜索相关 先不做
    // if (this.searchSetting.hasGroup) {
    //   this.getGroup(true);
    // }
  };
  @action updateSearchSetting = (props: AnyObj) => {
    this.props = props;
    this.searchConfig = (props?.config as any)[this.searchConfigKey];
    this.searchSetting = getSearchSetting(this.searchConfig, props?.page?.client);
    const { config, id: compId = '' } = this.props;
    const datasetId = config.dataset?.id;
    this.conditionId = `${compId}${datasetId}`.replace(/[A-Za-z]+/g, '');
  };
  @action clear = () => {
    this.setState({
      conditionId: '',
      groupData: {},
      searchParams: {},
    });
    this.searchAdvancedStore.formStore.resetForm();
    this.onReset();
  };
  onReset = () => {
    const { commonFilterStore, formStore } = this.searchAdvancedStore;
    const { id, unCheckSnaps } = commonFilterStore;

    // 取消选中常用筛选项
    if (id) unCheckSnaps();

    const { items } = formStore;
    const initVal: any = {};

    Object.keys(items).forEach((key: any) => {
      if (items[key].itemType === 'SCOPE') {
        initVal[key] = { min: '', max: '' };
      } else if (items[key].itemType === 'TIMEPICKER') {
        initVal[key] = [];
      } else if (key.includes(conditionSuffix)) {
        initVal[key] = items[key].value;
      } else {
        initVal[key] = '';
      }
    });
    formStore.updateDatas(initVal);
  };
  @action
  initAd = async () => {
    let adParams: any = {
      data: {},
      groups: [
        {
          id: 'commonGroup',
          title: '',
          visible: true,
          custom: false,
        },
      ],
    };

    /** design前端处理数据；运行请求接口 */
    if (!this.isPreView) {
      const items: any = {};
      const layout: any = [];

      /** 快捷搜索配置 */
      const showFields = [...(this.searchConfig?.quickFilters || [])];

      if (this.searchSetting.hasAd) {
        // 高级搜索：蒙层不能点击，展示第一项意思下就行
        showFields.push({
          ...this.searchConfig?.commonFilters[0],
          needQuickSearch: false,
        });
      }

      showFields.forEach((f: any) => {
        items[f.fieldId] = {
          itemType: 'INPUT',
          visible: true,
          required: false,
          readOnly: false,
          condition: false,
          multiple: false,
          isVariableFields: false,
          ismain: false,
          isThirdColumn: false,
        };

        layout.push({
          id: f.id,
          label: f.showName,
          labelSpan: 6,
          groupId: 'commonGroup',
          needQuickSearch: f.needQuickSearch !== undefined ? f.needQuickSearch : true,
          hide: false,
          delete: false,
          custom: false,
          disableCustomConditions: false,
          selected: false,
          items: [f.fieldId],
        });
      });

      adParams = {
        ...adParams,
        layout: [layout],
        items,
      };
    } else {
      const { page, compId = '', config, masterComponent, pageId = '' } = this.props || {};
      const formData = new FormData();
      const paramsObj: any = {};

      let url = `/api/${this.comServicePath}/condition/getTableCondition`;

      if (page?.module === BIMODULE) {
        url = page?.isBiAnalyzeCom
          ? `/api/${this.comServicePath}/report/bi/getTableSearchCondition`
          : `/api/${this.comServicePath}/report/bi/getTableSearchConditionWithPage`;
      }

      if (page?.isBiAnalyzeCom) {
        appendParams(paramsObj, 'masterId', masterComponent!);
      } else {
        appendParams(paramsObj, 'pageId', pageId);
        appendParams(paramsObj, 'compId', compId);
      }
      appendParams(paramsObj, 'filtersName', this.searchConfigKey);
      appendParams(paramsObj, 'terminalType', page?.client || 'PC');
      const params = this.getTableConditionCustomParams?.(paramsObj) || paramsObj;

      Object.keys(params).forEach(key => {
        appendParams(formData, key, params[key]);
      });
      adParams = await ebdComsAjax({
        url,
        method: 'POST',
        data: formData,
        params: getUrlParams(page?.id || ''),
        ebBusinessId: page?.id || '',
        com: { config },
      });

      if (page?.client === 'MOBILE' && adParams) {
        /** sql 占位符和esb日期字段需要前端根据isRange来特殊处理 */
        Object.keys(adParams.items || {}).forEach(fieldId => {
          const it = (adParams.items || {})[fieldId];
          if (it.itemType === 'DATETIMEPICKER' && !it.otherParams.isRange && Array.isArray(it.value)) {
            it.value = it.value[0] || it.value[1] || '';
          }
        });
      }
    }
    const initParams = {
      ...adParams,
      module: this.comServicePath,
      conditionId: this.conditionId,
    };
    await this.searchAdvancedStore.init(initParams).then(() => {
      // if (this.isPreView && this.searchSetting.hasAd) {
      //   const datas = this.searchAdvancedStore.formStore.getFormDatas();
      //   const formDatas: { [_: string]: any } = {};
      //   Object.keys(datas).forEach(key => {
      //     if (key.indexOf('condition_type') < 0 && !isEmpty(toJS(datas[key]))) {
      //       formDatas[key] = [{ [key]: datas[key] }];
      //     }
      //   });
      //   this.onAdSearch({ formDatas }, 'fromSearchPanel', { isInit: true });
      // }
    });
  };
  onAdFormValid = async () => {
    const { validate, items } = this.searchAdvancedStore.formStore;
    const { errors }: any = await validate();
    // 为空不为空，当作有值
    const newErrors: any = {};
    Object.keys(errors).forEach(key => {
      if (!items[`${key}_condition_type`] || !conditionNullArr.includes(items[`${key}_condition_type`]?.value)) {
        newErrors[key] = errors[key];
      }
    });
    return newErrors;
  };
  /**
   * 修改高级搜索的，必填符号*，有值则隐藏
   * */
  changeRequired = (valObj: any, ruleFunc?: (key: string) => boolean) => {
    if (valObj) {
      const {
        searchAdvancedStore: { formStore },
      } = this;
      Object.keys(valObj).forEach((key: string) => {
        if (formStore.initItems[key]?.required) {
          // initItems为初始化的，required始终不会变
          const requiredShow: boolean = ruleFunc ? ruleFunc(key) : isValueEmpty(valObj[key]);
          formStore.setLayoutProps(key, {
            // 通过设置className修改必填符号*的显隐，直接修改required会有闪动问题
            className: `adSearch-required-mark-${requiredShow ? 'show' : 'hide'}`,
          });
          if (ruleFunc) {
            // 由于setLayoutProps首次不刷新，使用setItemProps刷新一下布局
            formStore.setItemProps(key, { className: 'default-required' });
          }
        }
      });
    }
  };
  /**
   * @desc 校验联动必填符号*，如果如果存在必填
   * */
  changeAllItemRequired = async (isFirst: boolean = false) => {
    const { items = {} } = this.searchAdvancedStore.formStore;
    // 第一次，将设置了必填的数据key缓存起来，
    if (isFirst) {
      Object.keys(items)
        .filter((key: string) => items[key]?.required)
        .forEach((key: string) => {
          this.recordRequiredItems[key] = key;
        });
    }
    // 后续只针对设置了必填的数据进行操作
    if (!isEmpty(this.recordRequiredItems)) {
      const errors: any = await this.onAdFormValid();
      // errors中会列举出不满足必填的字段，因此通过是否在errors中来判断
      this.changeRequired(this.recordRequiredItems, (key: string): boolean => key in errors);
    }
  };
  /**
   * 校验高级搜索有无必填未填写
   * */
  onAdCheck = async (passCall?: Function, isInit?: boolean) => {
    const { id: compId = '' } = this.props || {};
    const errors: any = await this.onAdFormValid();
    this.changeAllItemRequired(isInit);

    if (!dialogObj[compId]) {
      dialogObj[compId] = {};
    }

    if (!isEmpty(errors)) {
      if (!dialogObj[compId].errorsShow) {
        runInAction(() => {
          dialogObj[compId].errorsShow = true;
          dialogObj[compId].errorDialog = Dialog.message({
            type: 'info',
            content: getLabel('116152', '搜索条件有必填字段未填写，请填写后再试！'),
            delay: 0,
            destroy: () => {
              runInAction(() => {
                dialogObj[compId].errorsShow = false;
              });
            },
          });
        });
      }
      await passCall?.(false);
      return;
    }
    if (dialogObj[compId].errorsShow && dialogObj[compId].errorDialog?.destroy) {
      dialogObj[compId].errorDialog.destroy();
    }
    await passCall?.(true);
  };
  onCancel = () => {
    this.onVisibleChange(false);
  };
  emitSearch = (filters: any[], type: SearchType, needGetDate?: boolean) => {
    const { compId = '', events } = this.props || {};
    const typeKey = `${compId}_${type}${FilterNoCacheKey}`;
    events?.emit('filter', compId, filters, typeKey, needGetDate);
  };
  @action
  onAdSearch = ({ formDatas = {}, quickSearchDatas = {} }: SearchDatas, dataSource: string, otherParams?: any) => {
    const { config = {}, id: compId = '' } = this.props || {};
    const { commonFilters = [] } = this.searchConfig || {};
    const { filters, searchType } = getSearchTypeAndfilters(
      {
        compId,
        config,
        commonFilters,
        fields: this.fields,
      },
      this.searchAdvancedStore,
      { formDatas, quickSearchDatas },
      dataSource,
      otherParams
    );
    // 有必填条件时，需要校验是否已填写
    this.onAdCheck((pass: boolean) => {
      if (pass) {
        this.emitSearch(filters, searchType, true);
      }
      // if (pass) {
      //   // 路由更改在search执行之后，需要先等路由修改后再执行，否则无法获取正确的路径参数
      //   _setTimeout(() => {
      //     this.emitSearch(filters, searchType, true);
      //   });

      //   this.onCancel();
      // } else {
      //   // 路由更改在search执行之后，需要先等路由修改后再执行，否则无法获取正确的路径参数
      //   _setTimeout(() => {
      //     this.emitSearch(filters, searchType, false);
      //   });

      //   this.onVisibleChange(true);
      // }
    }, otherParams?.isInit);
  };

  @action
  onVisibleChange = async (visible?: boolean) => {
    if (visible && !this.firstLoaded) {
      this.firstLoaded = true;
      this.init();
    }
    await this.searchAdvancedStore.setVisible(visible!);
  };
}

export default SearchStore;
