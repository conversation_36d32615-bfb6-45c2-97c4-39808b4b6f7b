import { utils } from '@weapp/ui';
import { getLabel, qs, corsImport } from '@weapp/utils';
import axios from 'axios';
import { action, observable, runInAction, toJS, computed } from 'mobx';
import { EbuilderFormModule, ModuleType, KanBanDefaultPageSize, DEFAULT_LANE_ID } from '../../../constants';
import { H5GroupShieldFieldTypes } from '../../../constants/EtComponent';
import { ShowPosition, GroupType } from '../../../constants/common';
import { KanbanMockEmptyData } from '../../../constants/mock';
import { CommonSearchType } from '../../../types/common';
import { ajax } from '../../../utils/ajax';
import { getQuickParams, onAfterAdInit } from '../../../utils/advanceFn';
import { BaseSearchStore } from './BaseSearchStore';
import { BoardViewStore } from './store';
import { GroupItem, ListMenuActionType } from './types';
import { isEbFormDataV2 } from '../DataSet/utils';
import { handleGroupsWithCf, getLaneExtParams, getLaneById } from './utils';
import { timeStamp } from 'console';

const { isEmpty } = utils;
export class DesignerStore {
  initGroup = {
    id: '',
    name: getLabel('105298', '未分组'),
    type: 0,
    cards: [],
  };
  @observable dataStore: any;
  @observable optionsCancelToken = {} as any;

  constructor(dataStore: BoardViewStore) {
    runInAction(() => {
      this.dataStore = dataStore;
    });
  }
  @observable baseSearch = new BaseSearchStore(this);

  @action
  init = async () => {
    const datasetId = this.dataStore.config?.dataset?.id;
    if (!datasetId) {
      const { active = GroupType.custom, options = [] } = this.dataStore.config?.group! || {};
      if (`${active}` === GroupType.custom) {
        this.dataStore.groups = options[0]?.values || this.dataStore.initGroup;
      } else {
        this.dataStore.groups = KanbanMockEmptyData();
      }
      this.dataStore.loading = false;
      return;
    }
    // 表单数据源支持查分组信息
    if (isEbFormDataV2(this.dataStore.config)) {
      this.getBoardRight();
      // this.getGroupFilters();
      this.getOrders();
      await this.getGroupsControl();
    } else {
      this.getOrders();
      await this.getGroupsControl();
    }
    // 触发看板重新加载
    this.dataStore.resetViewportLoading('designerStore-init');
    // await this.getDefaultFilter(); // 初始化，需要先请求默认值
  };
  @action
  getBoardRight = async () => {
    const res = await ajax({
      url: `/api/${this.dataStore.comServicePath}/kanban/previewKanbanMenu`,
      method: 'POST',
      data: qs.stringify({
        pageId: this.dataStore.pageId,
        compId: this.dataStore.compId,
        config: JSON.stringify(this.dataStore.config),
        extParam: JSON.stringify({}),
      }),
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      ebBusinessId: this.dataStore.pageId,
    });
    const { addData, addGroup, name = '' } = res;
    runInAction(() => {
      this.dataStore.addGroup = addGroup;
      this.dataStore.addData = addData;
      this.dataStore.title = name;
    });
  };

  @action
  getOrders = () => {
    runInAction(() => {
      const {
        config: { orders = [] },
      } = this.dataStore;
      if (orders.length) {
        this.dataStore.orders = [
          {
            id: '',
            fieldName: getLabel('105948', '拖动排序'),
            selected: true,
          },
        ];
      } else {
        this.dataStore.orders = [];
      }
    });
  };
  @action
  getLanePageInfo = (laneGroupId: string, groupId: string) => {
    const laneGroups = toJS(this.dataStore.laneGroups),
      pageInfo = toJS(this.dataStore.laneBoardPageInfo);
    const laneGroupIdx = laneGroups.findIndex((i: any) => i.id === laneGroupId);
    if (laneGroupIdx <= -1) return {};
    const pageKey = `${laneGroups[laneGroupIdx].id}_${groupId}`;
    return pageInfo[pageKey] || { current: '1', pageSize: KanBanDefaultPageSize };
  };
  @action
  getGroupsControl = () => {
    this.dataStore.getGroupsControl(null, this.getGroups);
  };

  @action('获取自定义分组信息')
  getGroups = async (laneGroupId: string, cb?: Function): Promise<GroupItem[]> => {
    console.log('*👏👏👏***laneGroupId****', laneGroupId);
    if (!this.dataStore.compId || !this.dataStore.pageId) return [];
    if (this.optionsCancelToken?.compId === this.dataStore.compId && this.optionsCancelToken?.laneGroupId && this.optionsCancelToken?.laneGroupId === laneGroupId) {
      this.optionsCancelToken?.func?.cancel('Request canceled');
    }
    this.optionsCancelToken = {
      func: axios.CancelToken.source(),
      compId: this.dataStore.compId,
      laneGroupId,
    };
    this.dataStore.loading = true;

    const res = await ajax({
      url: `/api/${this.dataStore.comServicePath}/kanban/previewOptions`,
      method: 'post',
      data: qs.stringify({
        compId: this.dataStore.compId,
        pageId: this.dataStore.pageId,
        extParam: JSON.stringify(this.dataStore.extParams),
        config: JSON.stringify({ ...this.dataStore.config, timeStamp: new Date().getTime() }),
      }),
      cancelToken: this.optionsCancelToken?.func?.token,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      ebBusinessId: this.dataStore.pageId,
      error: (info: any) => {},
    });
    const groups = handleGroupsWithCf(res?.options || [], this.dataStore.config, this.dataStore.activeGroup.groupType, laneGroupId);
    if (this.dataStore.isLaneConfig) {
      return cb ? cb(res?.options || []) : res?.options || [];
    }
    runInAction(() => {
      this.dataStore.loading = false;
      if (!isEmpty(groups)) {
        this.dataStore.groups = groups;
        this.dataStore.descInfo[laneGroupId] = res?.descInfo;
        const { active: a = GroupType.custom, options: opts = [] } = this.dataStore.config.group!;
        if (a === GroupType.custom) {
          if (isEmpty(toJS(opts))) {
            this.dataStore.config.group = {
              active: GroupType.custom,
              options: [
                {
                  groupType: GroupType.custom,
                  values: res?.options,
                },
              ],
            };
          }
        }
      } else {
        this.dataStore.groups = [];
      }
    });
    return res?.options || [];
  };

  /** ***************************** preview ****************************** */
  @action
  previewDataControl = async ({ groupId, loadMore, laneId }: { groupId: string; loadMore?: boolean; laneId: string }) => {
    // 泳道看板获取数据
    if (this.dataStore.isLaneConfig) {
      const currentPageInfo = this.getLanePageInfo(laneId!, groupId);
      const fetcher = () => this.previewDataAjax(groupId, currentPageInfo.current, currentPageInfo.pageSize, laneId);
      this.dataStore.dealWithFetchData(groupId, loadMore, fetcher, laneId, () => {});
      return;
    }
    this.previewData(groupId, laneId, loadMore);
  };
  @action
  previewDataAjax = async (groupId: string, current: string = '1', pageSize: string = KanBanDefaultPageSize, laneId?: string) => {
    const params = {
      optionId: groupId || '',
      current: current,
      pageSize,
      ...this.dataStore.searchParams,
      searchParamData: {
        ...this.baseSearch.lastSearchParams,
        groupFields: !isEmpty(toJS(this.dataStore.groupSearchParams)) ? toJS(this.dataStore.groupSearchParams) : {},
      },
    };
    const sortParams = this.dataStore.orders.filter((o: any) => o.id && o.selected).map((o: any) => ({ orderKey: o.fieldKey, orderType: o.orderType }));
    const { pageFilter } = this.dataStore.RefreshComFunc.getFilter({
      config: this.dataStore.config,
      pageId: this.dataStore.pageId,
      comId: this.dataStore.compId,
    });
    const laneItem = getLaneById(this.dataStore.laneGroups, laneId);
    let _extPrams = {
      ...this.dataStore.extParamsExtra(groupId),
      pageFilter,
      optionId: groupId || '',
      sortParams,
      ...params,
      // @ts-ignore
      ...getLaneExtParams(this.dataStore.config?.laneConfig, laneItem),
    };
    const res = await ajax({
      url: `/api/${this.dataStore.comServicePath}/kanban/previewData`,
      method: 'POST',
      data: qs.stringify({
        pageId: this.dataStore.pageId,
        compId: this.dataStore.compId,
        extParam: JSON.stringify(_extPrams),
        pageSize,
        pageNo: current,
        ...this.dataStore.searchParams,
        config: JSON.stringify(this.dataStore.config),
      }),
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      ebBusinessId: this.dataStore.pageId,
    });
    return res;
  };
  @action
  previewData = async (groupId: string, laneId: string, loadMore: boolean = false) => {
    if (!groupId || this.dataStore.paginationData[groupId]?.isLoading) return;
    if (!loadMore || !this.dataStore.paginationData[groupId]) {
      // 初始化
      this.dataStore.paginationData[groupId] = {
        current: 1,
        pageSize: this.dataStore.pageSize,
        total: 0,
        hasMore: true,
        isLoading: true,
      };
    } else {
      // 加载更多
      const { current, total, hasMore } = this.dataStore.paginationData[groupId];
      if (!hasMore) return;
      this.dataStore.paginationData[groupId] = {
        pageSize: this.dataStore.pageSize,
        total,
        current: current + 1,
        hasMore: true,
        isLoading: true,
      };
    }

    const res = await this.previewDataAjax(groupId, this.dataStore.paginationData[groupId].current, this.dataStore.pageSize, laneId);
    const { fieldInfo = {}, count } = res;
    runInAction(() => {
      this.dataStore.setGroupDataById(groupId, res, loadMore);
      this.dataStore.paginationData[groupId] = {
        ...this.dataStore.paginationData[groupId],
        total: +count,
        isLoading: false,
      };
      const { current, pageSize, total } = this.dataStore.paginationData[groupId];
      if (current * pageSize >= total) {
        this.dataStore.paginationData[groupId].hasMore = false;
      }
      this.dataStore.setState({ cardSpecialField: fieldInfo });
    });
  };

  @action('获取默认值')
  getDefaultFilter = async (): Promise<any> => {
    const datasetId = this.dataStore.config?.dataset?.id;
    if (!datasetId) {
      return;
    }
    const res = await ajax({
      url: '/api/ebuilder/form/preview/viewFilter/getFilterDefault',
      method: 'post',
      data: {
        compId: this.dataStore.compId,
        pageId: this.dataStore.pageId,
        config: this.dataStore.config,
        viewType: ModuleType.Board,
        commonSearchType: this.dataStore.commonSearchType,
        ...this.dataStore.searchParams,
      },
      ebBusinessId: this.dataStore.pageId,
    });
    runInAction(async () => {
      const { groupFields = {}, ...params } = res?.searchParamData || {};
      // 分类是groupFields，
      this.dataStore.groupSearchParams = groupFields || {};
      const searchData = this.dataStore.formatDfData(res);
      this.dataStore.setFilterDefault(res, searchData);
      this.baseSearch.lastSearchParams = searchData;
      // 加载高级搜索,并给高级搜索设置默认值
      const needAd = this.dataStore.commonSearch === CommonSearchType.Advanced;
      const needTop = !needAd && !this.dataStore.isMobile;
      if (needTop) {
        this.getCommonFilters();
      }
      this.initAdvance(needAd || false, () => {
        if (!needTop) {
          onAfterAdInit(this.dataStore.baseSearch.searchAdvancedStore.formStore, params);
        }
      });
    });
  };

  @action
  initAdvance = async (needAd: boolean, onAfterInit?: () => void) => {
    const adCondition = await ajax({
      url: '/api/ebuilder/form/preview/viewFilter/getSearch',
      method: 'post',
      data: {
        compId: this.dataStore.compId,
        pageId: this.dataStore.pageId,
        config: this.dataStore.config,
        viewType: ModuleType.Board,
        commonSearchType: '0',
      },
      ebBusinessId: this.dataStore.pageId,
    });
    runInAction(() => {
      this.dataStore.conditionId = adCondition?.conditionId;
    });
    const params = {
      module: EbuilderFormModule,
      conditionId: this.dataStore.conditionId,
      data: {},
      items: adCondition.items,
      layout: adCondition.layout,
      groups: adCondition.groups,
    };
    const adQuickParams = getQuickParams(params, !needAd);
    runInAction(async () => {
      this.dataStore.baseSearch.adQuickParams = adQuickParams;
      if (adQuickParams) {
        await this.dataStore.baseSearch.searchAdvancedStore.init(params);
        await onAfterInit?.();
      }
    });
  };

  @action
  getCommonFilters = () => {
    ajax({
      url: '/api/ebuilder/form/preview/viewFilter/getSearch',
      method: 'post',
      data: {
        compId: this.dataStore.compId,
        pageId: this.dataStore.pageId,
        config: this.dataStore.config,
        viewType: ModuleType.Board,
        commonSearchType: '1',
      },
      ebBusinessId: this.dataStore.pageId,
    }).then(filters => {
      runInAction(() => {
        this.dataStore.baseSearch.filterData = filters; // 只使用常用筛选
      });
    });
  };
  @action('获取分类搜索配置（待废弃）')
  getGroupFilters = () => {
    this.dataStore.groupLoading = true;
    ajax({
      url: '/api/ebuilder/form/preview/viewFilter/getGroupSearch',
      method: 'post',
      data: {
        compId: this.dataStore.compId,
        pageId: this.dataStore.pageId,
        config: this.dataStore.config,
        viewType: ModuleType.Board,
      },
      ebBusinessId: this.dataStore.pageId,
    }).then(res => {
      runInAction(() => {
        this.dataStore.groupLoading = false;
        if (this.dataStore.isMobile) {
          const data = res.filter((dt: any) => H5GroupShieldFieldTypes.indexOf(dt.componentKey) < 0).map((d: any) => ({ ...d, content: d.showName, id: d.fieldName }));
          this.dataStore.groupSearchDatasH5 = data;
        } else {
          const data = res.map((d: any) => ({ ...d, content: d.showName, id: d.fieldName }));
          const leftMenuDatas = data.filter((g: any) => g.showPosition === ShowPosition.Left) || [];
          const groupTopDatas = data.filter((g: any) => g.showPosition === ShowPosition.Top) || [];
          this.dataStore.setState({
            leftMenuTreeDatas: leftMenuDatas,
            groupTopSearchDatas: groupTopDatas,
          });
        }
      });
    });
  };
}

export default DesignerStore;
