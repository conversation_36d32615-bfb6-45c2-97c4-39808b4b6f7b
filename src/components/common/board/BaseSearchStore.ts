import { SearchAdvancedStore } from '@weapp/ui';
import { getLabel, corsImport, cloneDeep, isObject, forEach, isEmpty } from '@weapp/utils';
import { action, observable, runInAction, toJS } from 'mobx';
import { Field } from '@weapp/ebdcoms/lib/components/charts/config/Field/types';
import { EbuilderFormModule, ListSearchCacheParams, conditionNullArr } from '../../../constants';
import { EtComponentKey, SelectType, DateDftNumberType, BrowserType, DateRangeType, AreaType } from '../../../constants/EtComponent';
import { ShowPosition } from '../../../constants/common';
import { AnyObj, SearchType, FilterData } from '../../../types/common';
import { ajax } from '../../../utils/ajax';
import { getBrowserTypeModule, getSystemFieldType, getLocaleValue, UUID } from '../../../utils';
import ebdcoms from '../../../utils/ebdcoms';
import ls from '../../../utils/ls';
import { getQuickParams } from '../../../utils/advanceFn';
import { addPhysicalParams, getActualDate, transformValue, formatSearchValue } from './utils';
import { DataType } from './types';

export enum OptionEnableType {
  Enable = '1',
  Disable = '0',
}
// 禁止filter缓存标识
export const FilterNoCacheKey = '_EXCLUDECACHE';
// 高级搜索中其他搜索条件 字段排序处理
function searchAdFiltersSort(arr: any) {
  let tempFieldArr: any[] = arr;

  tempFieldArr = tempFieldArr.filter((f, i) => {
    if (f.fieldOrder === undefined) {
      if (i === 0) {
        f.fieldOrder = -1;
      } else {
        f.fieldOrder = tempFieldArr[i - 1].fieldOrder;
      }
    }
    return f;
  });
  return tempFieldArr.sort((a: any, b: any) => a.fieldOrder - b.fieldOrder);
}
const onfilterChooseEmpVal = (data: any) => {
  const _data = cloneDeep(data);
  forEach(Object.keys(_data), (key: any) => {
    const target = _data[key][0][key];
    if (target && Array.isArray(target) && target[0] === 'chooseEmp') {
      let _targetList: any = [];
      forEach(target, (item: any) => {
        if (Array.isArray(item)) {
          _targetList = item;
        }
      });
      _data[key][0][key] = _targetList;
    }
  });
  return _data;
};

/**
 * <AUTHOR>
 * @createTime 2022 -02 -28
 */

export class BaseSearchStore {
  @observable dataStore: any = {};

  constructor(dataStore: any) {
    runInAction(() => {
      this.dataStore = dataStore;
    });
  }

  @observable lastSearchParams: any = {}; // 搜索参数

  @observable adQuickParams: any;

  @observable searchAdvancedStore: SearchAdvancedStore = new SearchAdvancedStore(); // 高级搜索

  @observable filterData: FilterData[] = []; // 常用筛选的字段数据
  @observable fields: any[] = [];

  @action
  getFilterParams = () => {
    let params: AnyObj = {};
    const currentSearch = `${ls.getItem(ListSearchCacheParams.CurrentSearchState)}`;
    /** ***
     * 读取本地缓存的上一步的搜索类型(高级，快捷，筛选独立)，带上前一次的参数
     */
    switch (currentSearch) {
      case SearchType.AdvanceSearch:
        params = {
          searchType: SearchType.AdvanceSearch,
          ...this.dataStore.advancedParams.formDatas,
          type: this.dataStore.advancedParams.type,
        };
        break;
      case SearchType.QuickSearch:
        params = {
          searchType: SearchType.QuickSearch,
          ...this.dataStore.advancedParams.quickSearchDatas,
        };
        break;
      case SearchType.FilterSearch:
        params = {
          searchType: SearchType.FilterSearch,
          ...this.dataStore.filterSearchStore.searchParams,
          type: this.dataStore.filterSearchStore.conditionType,
        };
        break;
    }
    return { ...this.dataStore.searchParams, ...params };
  };
  getField = async () => {
    const { config } = this.dataStore;
    const { dataset } = config;
    const { dsUtils } = await ebdcoms.get();
    await dsUtils.getFields(dataset).then((data: any) => {
      const fields: any[] = [];
      data.forEach((el: any) => {
        el.fields?.forEach((_field: any) => {
          const field = ebdcoms.get().transformFields(_field);
          // 系统字段类型转化表单类型
          field.compType = getSystemFieldType(field.id, field.compType as EtComponentKey);
          fields.push(field);
        });
      });
      runInAction(() => (this.fields = fields));
    });
  };

  @action
  getCommonFilters = () => {
    const { pageId, compId } = this.dataStore;
    const mobilePath = this.dataStore.isMobile ? '/app' : '';
    const designPath = this.dataStore.isDesign ? '/preview' : '';
    ajax({
      url: `/api${mobilePath}/ebuilder/form${designPath}/viewFilter/getSearch/${pageId}/${compId}?commonSearchType=1`,
      method: 'get',
      params: {
        isPhysical: this.dataStore?.config?.dataset?.isPhysical || false,
      },
      ebBusinessId: pageId,
    }).then(filters => {
      runInAction(() => {
        this.filterData = filters; // 只使用常用筛选
      });
    });
  };
  @action
  getCommonFiltersWithoutEb = async () => {
    const { config, client = 'PC' } = this.dataStore;
    const { filter, dataset } = config;
    const { transformParams } = await corsImport('@weapp/components');
    let value = {};
    let filterData: any[] = [];
    const promiseOpts: any[] = [];
    if (filter) {
      filterData = toJS(filter.commonFilters).map((el: any, i: number) => {
        // 后续需要从原始数据中覆盖的参数都从这里取,不要全量覆盖，
        // eg：conditionType需要多语言;format需要取最新
        const originField: any = this.fields.find((f: any) => f.id === el.fieldId) || {};
        el = {
          ...cloneDeep(el),
          // conditionType: originField.conditionType || el.conditionType || [],
          conditionType: el?.conditionType || [],
          config: {
            ...(el.config || {}),
            format: originField?.config?.format || el.config?.format,
          },
        };

        const { module, type } = getBrowserTypeModule(el.componentKey as EtComponentKey);
        const isField = el.componentKey !== EtComponentKey.GroupCustom;
        let options = isField ? [] : el.listFilterDefaultSets.listGroupSets;
        options.forEach((opt: any) => {
          if (opt.setName && !opt.name) {
            opt.name = opt.setName;
          }
        });
        if (!isField) {
          el.listGroupSets = el.listFilterDefaultSets.listGroupSets;
        } else {
          el.showName = el.text ? el.text : el.showName;
          el.fieldName = el.fieldId;
        }
        if ('startValue' in el.listFilterDefaultSets || !isField) {
          if (isField && el.listFilterDefaultSets.optionEnable === OptionEnableType.Enable) {
            el.filterSets = el.listFilterDefaultSets.listFilterSets;
          }
          const item: any = {
            fieldData: {
              dftValue: {
                startValue: el.listFilterDefaultSets.startValue,
                endValue: el.listFilterDefaultSets.endValue,
                startValueSpan: el.listFilterDefaultSets.startValueSpan,
                defaultType: el.listFilterDefaultSets.defaultType,
                listFilterSets: el.listFilterDefaultSets.listFilterSets,
                optionEnable: el.listFilterDefaultSets.optionEnable,
                isDateMenu: el.listFilterDefaultSets.isDateMenu,
                dateMenuGroup: el.listFilterDefaultSets.dateMenuGroup,
                isCurrentMax: el.listFilterDefaultSets.isCurrentMax,
                conditionTypeValue: el.listFilterDefaultSets.conditionTypeValue,
              },
              filterOpts: el.listFilterDefaultSets.listGroupSets,
            },
            id: el.fieldId,
            compType: el.componentKey,
            config: el.config,
          };
          if (el.componentKey === EtComponentKey.GroupCustom) {
            item.id = el.id;
          }
          // todo
          // value = setDftValue(value, isField, item, transformParams, 'PC');
        }
        // todo 选项动态取
        // if (isField && SelectType.includes(el.componentKey)) {
        //   if (el.fieldId === '8' || el.fieldId === '9') {
        //     options = getSystemFieldOpts({ id: el?.fieldId } as any) as any;
        //   } else {
        //     promiseOpts.push({
        //       index: i,
        //       data: Promise.resolve(
        //         getFieldOpts(dataset, { ...el, name: el.name || el.fieldName }, 'list'),
        //       ),
        //     });
        //   }
        // }
        // 动态获取级联选项
        if (isField && el.componentKey === EtComponentKey.Cascader) {
          const filterField = this.fields.find((f: any) => f.compType === EtComponentKey.Cascader);
          options = (filterField?.config as any)?.options || el?.config?.options || el?.listFilterDefaultSets?.listGroupSets;
        }
        if (client === 'MOBILE' && (BrowserType.includes(el.componentKey) || DateRangeType.includes(el.componentKey))) {
          // 移动端关联字段、日期类字段，全部放到更多内
          el.isQuick = '1';
        }

        /** 把listFilterDefaultSets内需要的值赋值到最外层原因：因为之前eb组件是后端返的，这里构造成eb的数据结构 */
        let { componentKey } = el;
        el.multiSelect = el.listFilterDefaultSets.multiSelect;
        el.allowSelectAll = el.listFilterDefaultSets.multiSelect;
        el.enableTime = el.listFilterDefaultSets.enableTime;

        if (
          SelectType.includes(el.componentKey) &&
          el.componentKey !== EtComponentKey.GroupCustom &&
          el.componentKey !== EtComponentKey.Cascader
        ) {
          if (componentKey != EtComponentKey.SelectMultiple) {
            componentKey = EtComponentKey.CheckBox;
          }
          if (!el.listFilterDefaultSets.multiSelect) {
            componentKey = el.componentKey;
          }
        }
        if ((DateDftNumberType.includes(el.componentKey) || el.componentKey === EtComponentKey.TimeComponent) && isObject(el?.config)) {
          el.config = JSON.stringify(el.config);
        }
        // 关联e-Builder字段的使用方module
        if ([EtComponentKey.Ebuilder].includes(el.componentKey)) {
          el.sModule = el?.module;
        }
        const data = {
          ...el,
          componentKey,
          module, // 关联类型module
          browserType: type || el.browserType, // 关联类型type
          options,
          isDateMenu: el.listFilterDefaultSets.isDateMenu,
          dateMenuGroup: el.listFilterDefaultSets.dateMenuGroup,
          isCurrentMax: el.listFilterDefaultSets.isCurrentMax,
          formId: el?.formId || el?.objId,
        };
        return data;
      });
    }
    // if (searchStore) {
    //   if (this.props.viewType === 'Design') {
    //     searchStore.setDfaultValues(value);
    //   } else {
    //     this.getFilterDefaultValue();
    //   }
    // }
    if (promiseOpts.length) {
      // 动态查询最新选项
      const pendingData = promiseOpts.map(el => el.data);
      const pendingIndex = promiseOpts.map(el => el.index);
      Promise.all(pendingData)
        .then(res => {
          pendingIndex.forEach((n, i: number) => {
            filterData[n].options = res[i];
          });
          runInAction(() => (this.filterData = filterData));
        })
        .catch(() => {
          runInAction(() => (this.filterData = filterData));
        });
    } else {
      runInAction(() => (this.filterData = filterData));
    }
  };
  @action('非eb筛选')
  getSearchParamsWithoutEb = (val: any, conditionType: string) => {
    const { config, client = 'PC', id, viewType } = this.dataStore;
    const { filter: searchConditions, dataset } = config;
    // const { dataset, searchConditions } = config;
    const { fields } = this;
    if (viewType === 'Design') return;
    const isEteams: boolean = dataset?.type === 'ETEAMS';
    const changes = onfilterChooseEmpVal(toJS(val));
    const cloneVal = cloneDeep(changes);
    const filters: any[] = [];
    Object.keys(changes).forEach(fieldId => {
      changes[fieldId].forEach((el: any) => {
        const field = fields.find(f => f.id === fieldId);
        let formatData: any = null;
        if (DateDftNumberType.includes(field?.compType as EtComponentKey)) {
          el[fieldId] = getActualDate(el[fieldId]);
        }
        if (isEteams && BrowserType.includes(field?.compType as EtComponentKey)) {
          formatData = transformValue(toJS(el), isEteams);
        } else if (
          // 行政字段
          [EtComponentKey.Country, EtComponentKey.Province, EtComponentKey.City, EtComponentKey.District, EtComponentKey.Cascader].includes(
            field?.compType as any
          )
        ) {
          // 行政字段以及级联不需要转换，保留数组格式
          Object.keys(toJS(el)).forEach((key: any) => {
            if (key === fieldId) {
              const _value = toJS(el)[fieldId];
              formatData = {
                [fieldId]: typeof _value === 'string' && _value.startsWith('[') ? JSON.parse(_value) : _value,
              };
            } else {
              formatData = {
                ...formatData,
                [key]: toJS(el)[key],
              };
            }
          });
        } else {
          formatData = transformValue(toJS(el));
        }
        if (!fieldId.includes(EtComponentKey.GroupCustom)) {
          let value = formatData[fieldId];
          if (typeof value === 'object' && ('min' in value || 'max' in value || value?.id?.includes('temp_'))) {
            const fieldfilter = searchConditions?.commonFilters.find((list: any) => list.fieldId === fieldId);
            const opt = fieldfilter?.listFilterDefaultSets?.listFilterSets.find((filterSet: any) => filterSet.id === value.id);
            if (opt) {
              value = `${opt.scope.min},${opt.scope.max}`;
            } else {
              value = '';
            }
          }
          if (AreaType.includes(field?.compType as EtComponentKey)) {
            value = toJS(el)[fieldId];
          }
          value = value === undefined ? '' : value;
          const filter = {
            value,
            filterFields: [
              {
                comId: id,
                dataset,
                id: fieldId,
                fieldName: field?.name,
                type: field?.type,
                compType: field?.compType,
                objId: field?.objId,
                formatType: field?.formatType || '',
              },
            ],
            conditionComId: id,
            dataType: DataType.Field,
            conditionType,
          };
          if ((typeof filter.value !== 'object' && filter.value.length) || typeof filter.value === 'object') {
            filters.push(filter);
          }
        } else if (formatData[fieldId] !== '') {
          const filter = {
            value: formatData[fieldId],
            filterFields: [],
            dataType: DataType.Classification,
            conditionComId: id,
            conditionType,
          };
          filters.push(filter);
        }
      });
    });
    // 移动端筛选模式，拿到高级搜索数据，合并去搜索
    // if (innerFilter.length) {
    //   innerFilter.forEach(f => {
    //     let hasInner = false;
    //     filters.forEach(wf => {
    //       if (wf.filterFields?.[0].id === f.filterFields?.[0].id) {
    //         wf.value = f.value;
    //         hasInner = true;
    //       }
    //     });
    //     if (!hasInner) {
    //       filters.push(f);
    //     }
    //   });
    // }
    // if (customOnchange) {
    //   customOnchange(cloneVal, conditionType);
    // } else {
    //   this.props.events?.emit('filter', id, filters, id);
    // }
    console.log('****filters****', filters);
    console.log('****id****', id);
    // this.setState({ wrapFilters: filters });
    this.dataStore.RefreshComFunc.setFilter(filters, id);
  };
  @action('非eb筛选')
  getFilterFields = (key: string) => {
    const { config, compId } = this.dataStore;
    const { dataset } = config;
    const field: Field | undefined = (this.fields || []).find(f => f.id === key);
    if (field) {
      const filterFields: any = {
        compId,
        compType: field?.compType,
        dataset,
        fieldName: field?.name,
        formatType: field?.formatType,
        id: field?.id,
        objId: field?.objId,
        type: field?.type,
      };

      // 数据源分组字段，shortKey是唯一标识
      if (field?.shortKey) {
        filterFields.shortKey = field.shortKey;
      }

      return filterFields;
    }
  };
  @action('非eb筛选')
  getSearchParamsWithoutEbV2 = (changes: any, conditionType: string) => {
    const { config, compId } = this.dataStore;
    const { filter: searchConditions } = config;
    const filters: any[] = [];
    console.log('****searchConditions.commonFilters****', toJS(searchConditions.commonFilters));
    Object.keys(changes).forEach(key => {
      changes[key].forEach((el: any) => {
        const value: any = toJS(el[key]);
        const funcValue = el[`${key}_condition_type`];
        // 值不为空或者 多条件包含为空不为空
        if (!isEmpty(value) || conditionNullArr.includes(funcValue)) {
          const dfFilterInfo = {
            conditionComId: `${compId}_filter${FilterNoCacheKey}`,
            conditionType,
            dataType: 'field',
            id: UUID(),
          };
          if (!key.includes(EtComponentKey.GroupCustom)) {
            filters.push({
              ...dfFilterInfo,
              value: conditionNullArr.includes(funcValue)
                ? funcValue
                : formatSearchValue(key, value, {
                    compId: compId!,
                    commonFilters: searchConditions.commonFilters,
                    config,
                  }),
              filterFields: [this.getFilterFields(key)].filter(Boolean),
              funcType: funcValue,
            });
          } else {
            filters.push({
              ...dfFilterInfo,
              value: formatSearchValue(key, value, {
                compId: compId!,
                commonFilters: searchConditions.commonFilters,
                config,
              }),
              filterFields: [],
              dataType: 'classification',
            });
          }
        }
      });
    });
    console.log('****filters****', filters);
    this.dataStore.RefreshComFunc.setFilter(filters, 'filter');
    // this.emitSearch(filters, SearchType.Filter);
  };

  /** ------------------------- 高级搜索相关  --------------------------- */
  @action
  initAdvance = async (needAd: boolean, onAfterInit?: () => void) => {
    const adCondition = await this.getAdvanceCondition();
    runInAction(() => {
      this.dataStore.conditionId = adCondition?.conditionId || this.dataStore?.config?.dataset?.id;
    });
    const params = {
      module: EbuilderFormModule,
      conditionId: this.dataStore.conditionId,
      data: {},
      items: adCondition.items,
      layout: adCondition.layout,
      groups: adCondition.groups,
    };
    const adQuickParams = getQuickParams(params, this.dataStore.isMobile ? false : !needAd);
    runInAction(async () => {
      this.adQuickParams = adQuickParams;
      if (this.adQuickParams) {
        await this.searchAdvancedStore.init(params);
        const data = searchAdFiltersSort(this.searchAdvancedStore.allFields);
        this.searchAdvancedStore.setAllFields(data);
        await onAfterInit?.();
      }
    });
  };

  @action('高级搜索')
  getAdvanceCondition = async () => {
    const mobilePath = this.dataStore.isMobile ? '/app' : '';
    const designPath = this.dataStore.isDesign ? '/preview' : '';
    return ajax({
      url: `/api${mobilePath}/ebuilder/form${designPath}/viewFilter/getSearch/${this.dataStore.pageId}/${this.dataStore.compId}?commonSearchType=0`,
      method: 'get',
      params: addPhysicalParams({}, this.dataStore?.config?.dataset),
      ebBusinessId: this.dataStore.pageId,
    });
  };

  @action
  onVisibleChange = (visible?: boolean) => {
    // 面板显示/隐藏状态切换回调事件
    this.searchAdvancedStore.setVisible(visible!);
  };

  onReset = () => {
    // 取消选中常用筛选项
    const { id, unCheckSnaps } = this.searchAdvancedStore.commonFilterStore;
    if (id) {
      unCheckSnaps();
    }
  };

  // 取消
  onCancel = () => {
    this.onVisibleChange(false);
  };
  customGetEbuilderList = ({ searchValue, fieldInfo }: any) => {
    const { formInfo = {} } = fieldInfo || {};
    const { pageId = '', compId = '', comServicePath } = this.dataStore || {};
    const formData = new FormData();
    formData.append('compId', compId);
    formData.append('pageId', pageId);
    formData.append(
      'otherParams',
      JSON.stringify({
        searchValue,
        formParam: JSON.stringify({
          formId: formInfo.formId,
          layoutId: formInfo.layoutId,
          fieldId: formInfo.fieldId,
          filterItems: [],
          module: 'ebuilderform',
          dataDetails: [],
        }),
        newly: 1,
        pageSize: 100,
        fieldId: formInfo.fieldId,
      }),
    );
    return ajax({
      url: `/api/${comServicePath}/condition/groupWithCount`,
      method: 'post',
      data: formData,
      ebBusinessId: pageId,
    });
  };
  @action('分类筛选条件处理') getGroupSearchFilter = async (isInit?: boolean, params?: any) => {
    const { config, pageId = '', compId = '', isMobile } = this.dataStore || {};
    if (!config) return
    const client = isMobile ? 'MOBILE' : 'PC'
    const groupFilters = config.filter?.groupFilters || [];
    let group: any[] = [];
    if (client === 'PC') {
      group = groupFilters || [];
    } else {
      group = groupFilters.filter((g: any) => SelectType.includes(g.type));
    }
    const left: any[] = [];
    const top: any[] = [];
    const values: { [_: string]: any } = {};
    const oldGroupDatas = [...(this.dataStore?.leftMenuTreeDatas || []), ...(this.dataStore?.groupTopSearchDatas || [])];
    group.forEach((g: any, ind: number) => {
      const {
        id,
        fieldId,
        fieldName,
        compType,
        componentKey,
        showName,
        fieldShowName,
        text,
        showPosition,
        tabStyle,
        objId,
        fieldType = '',
        type,
        listFilterDefaultSets: st,
      } = g;
      let { multiSelect } = st;
      if (
        (compType === EtComponentKey.Select || compType === EtComponentKey.RadioBox)
        && multiSelect === undefined
      ) {
        multiSelect = false;
      }
      const content = showName || text || fieldShowName || g.content;
      const newComponentKey = compType || componentKey;
      const originField: any = this.fields.find((f: any) => f.id === fieldId) || {};
      // 关联eb字段需要获取eb数据接口
      let customGetEbuilderList: any;
      if ([EtComponentKey.Ebuilder, EtComponentKey.RelateBrowser].includes(newComponentKey)) {
        customGetEbuilderList = this.customGetEbuilderList;
      }
      let newItem: any = {
        componentKey: newComponentKey,
        id,
        fieldId,
        // 按照id通行，进行实际搜索时，根据id需要获取对应的真实fieldName（数仓场景下）
        fieldName: id,
        // 分类-人员-需要fieldName
        realFieldName: fieldName,
        showName: content,
        content,
        showOrder: ind,
        showPosition,
        tabStyle,
        multiSelect,
        listFilterDefaultSets: {
          multiSelect,
          fieldInfo: {
            formInfo: st.fieldInfo?.formInfo || {},
            componentKey: newComponentKey,
            disableGroupSearch: false,
            fieldId,
            fieldName: id,
            fieldType: fieldType.toLowerCase(),
            objId,
            showName: content,
            type,
            customGetEbuilderList,
          },
          listGroupSets: [],
          tileOptionAllConfig: st.tileOptionAllConfig,
          tileOptionNoGroupConfig: st.tileOptionNoGroupConfig,
          enableFilter: st.enableFilter,
          enableCustomFilter: st.enableCustomFilter,
          customFilterOptions: st.customFilterOptions,
          // 显示统计数字
          enableShowCountNum: st.enableShowCountNum,
          // 是否跟随条件
          showCountNumType: st.showCountNumType,
          // 数据超过99显示99+
          enableShowCountNumLimit: st.enableShowCountNumLimit,
        },
        // 用于大分类-人员-筛选
        extroInfo: {
          pageId,
          compId,
        },
      };
      if (st.listGroupSets?.length > 0) {
        const _listGroupSets = this.regroupListGroupSets(st, tabStyle, newComponentKey);
        newItem.listFilterDefaultSets.listGroupSets = _listGroupSets.map((f: any) => {
          if (f.isChecked) {
            // 有其他选项，去掉全部默认选中
            if (!values[id] || values[id].includes(-1)) {
              values[id] = [f.optionId || f.id];
            } else {
              values[id] = [...values[id], f.optionId || f.id];
            }
          }
          const originOpt = (originField.options || []).find((opt: any) => opt.id === f.id) || {};
          return {
            ...f,
            // 需要用运行时最新的配置覆盖config的archive
            archive: originOpt.archive,
            optionName: f.optionName || f.name || getLocaleValue(f.setName),
            count: 0,
          };
        });
      }
      // 非首次：前端对【更新不跟随搜索】的分组字段进行覆盖
      if (!isInit && st.showCountNumType === 'notUpdate') {
        const noUpdateField = oldGroupDatas.find((_d) => _d.id === id);
        if (noUpdateField) {
          newItem = noUpdateField;
        }
      }

      if (showPosition === ShowPosition.Left) {
        left.push(newItem);
      } else {
        top.push(newItem);
      }
    });
    // 分类搜索顶部/左侧默认值
    let groupSearchDefaultParams = {} as any
    const validData = [...left, ...top]
    validData.forEach(i => {
      const fieldId = i.fieldId
      const checkedItems = (i.listFilterDefaultSets.listGroupSets ?? []).filter((k: any) => k.isChecked)
      if (!isEmpty(checkedItems)) {
        groupSearchDefaultParams[fieldId] = checkedItems.map((k: any) => k.id)
      }
    })
    return {leftMenuDatas: left, groupTopDatas: top, groupSearchDefaultParams}
  }
  /**
   * designer模式下，分类平铺存在全部、未分组选项前端处理
   * @param st
   * @param tabStyle
   * @returns
   */
  regroupListGroupSets = (
    st: {
      listGroupSets: any[];
      multiSelect?: boolean;
      tileOptionAllConfig?: AnyObj;
      tileOptionNoGroupConfig?: AnyObj;
    },
    tabStyle: string,
    componentKey: EtComponentKey,
  ) => {
    if (!this.dataStore.isDesign && tabStyle === 'tab' && componentKey !== EtComponentKey.GroupCustom) {
      const {
        tileOptionAllConfig = {
          isChecked: false,
          isShow: true,
          showOrder: 0,
        },
        tileOptionNoGroupConfig = {},
      } = st;

      const _datas = [...st.listGroupSets] || [];
      if (_datas.length > 0) {
        _datas.splice(tileOptionAllConfig?.showOrder, 0, {
          isChecked: tileOptionAllConfig?.isChecked,
          isShow: tileOptionAllConfig?.isShow,
          optionId: '0',
          optionName: getLabel('54218', '全部'),
        });
        // 未分组
        // eslint-disable-next-line max-len
        const _tileOptionNoGroupConfig: any = !tileOptionNoGroupConfig || isEmpty(tileOptionNoGroupConfig)
          ? {
            isChecked: false,
            isShow: true,
            showOrder: st.listGroupSets.length + 1,
          }
          : tileOptionNoGroupConfig;
        const _noGroupShoworder = _tileOptionNoGroupConfig?.showOrder === undefined
          ? st.listGroupSets.length + 1
          : _tileOptionNoGroupConfig?.showOrder;
        _datas.splice(_noGroupShoworder, 0, {
          isChecked: _tileOptionNoGroupConfig?.isChecked,
          isShow: _tileOptionNoGroupConfig?.isShow,
          optionId: '99999', // 虚拟id给大点
          optionName: getLabel('105298', '未分组'),
        });
      }
      return _datas;
    }

    return [...st.listGroupSets];
  };
}

export default BaseSearchStore;
