import { AnyObj } from '../../../types/common';
import { BoardConfigData } from '../../baseBoard/types';
import { PluginPackDataType } from '../../plugin-packages/loader';

export interface DataSet {
  id: string;
  text: string;
  type: string;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: any[];
  servicePath?: string;
  params?: string[];
  dataId?: string;
  ebuilderDataConfig?: any;
  data?: any[];
  /** 关联的组件id */
  refComId?: string;
  /** 数字面板，关联的数据项id */
  refPanelId?: string;
  isPhysical?: boolean;
}
export declare type StyleAttrs = {
  [key: string]: string;
};
/**
 * 样式配置信息
 */
export interface StyleConfigData {
  id: string;
  category: string;
  libStyle?: StyleAttrs;
  customStyle?: StyleAttrs;
  cssSnippet?: string;
  useCustom?: boolean;
  customType: string;
}
export declare enum EventsType {
  Click = 'CLICK',
  DidMount = 'DIDMOUNT',
}
export declare enum EventsAction {
  IF = 'IF',
  ELSEIF = 'ELSEIF',
  ELSE = 'ELSE',
  NEWPAGE = 'NewPage',
  JSCODE = 'JsCode',
  CLOSEPAGE = 'ClosePage',
  REFRESHCOMP = 'RefreshComp',
}
export declare enum ConditionType {
  Rule = 'Rule',
  Dialog = 'Dialog',
}
export declare type ConditionDataType = {
  conditionType: ConditionType;
  conditionTitle: string;
  conditionContent: string;
  confirmBtnText: string;
  cancelBtnText: string;
  conditions: any;
  conditionsText: string;
};
export declare type RefreshInfoType = {
  selectedCom: string[];
  conditionSet: any;
};
export declare type Events = {
  type: EventsAction;
  conditionData?: ConditionDataType;
  events?: Events[];
  linkInfo?: any;
  jsCode?: string;
  refreshInfo?: RefreshInfoType;
  id: string;
};
export declare type EvtMap = {
  type: EventsType;
  id: string;
  name: string;
  events: Events[];
  disableDelete?: boolean;
};
export declare type FillType = 'backgroundColor' | 'borderColor' | 'none';
export interface IconStyle {
  fontSize?: string;
  color?: string;
  bgColor?: string;
  radius?: string;
  fillType?: FillType;
  borderColor?: string;
}
export interface AssetsLibVal {
  path: string;
  style?: IconStyle;
  title?: any;
}
/**
 * 组件公共的配置
 */
export interface CommonConfigData {
  /** 类型 */
  type?: string;
  /** 标题 */
  title?: any;
  /** 是否启用标题 */
  titleEnabled?: boolean;
  /** 是否启用底部区域 */
  footerEnabled?: boolean;
  /** 顶部自定义按钮配置 */
  topOperatBtns?: any;
  /** 底部自定义按钮配置 */
  bottomOperatBtns?: any;
  /** 组件样式配置 */
  styles?: StyleConfigData[];
  /** 组件固定位置 */
  fixedArea?: any;
  /** 流式布局配置 */
  flow?: any;
  /** 事件动作组 */
  // eventGroup?: EvtMap[][];
  eventGroup?: any;
  /** 标题icon */
  titleIcon?: AssetsLibVal;
}
export interface DataSetItem extends DataSet {}
export interface ViewConfigData extends CommonConfigData {
  fromEbuilder?: boolean; // 是否为默认生成的数据
  showTitle?: boolean;
  name?: string;
}
export interface BoardPluginCenter {
  [x: string]: any;
}
export interface BaseParamsProps {
  compId: string;
  page?: any;
  pageId: string;
  config: BoardConfigData;
  isMobile: boolean;
  isDesign: boolean;
  isDoc?: boolean;
  ebParams?: any; // eb参数
  comServicePath?: string;
  onRef?: (ref: any) => void;
  events: AnyObj;
  ebStore: any;
  pluginCenter?: BoardPluginCenter; // 引入插件机制
  /** 组件插件包描述对象，内置属性，用于支持组件可插拔机制 */
  refPluginPackage?: PluginPackDataType;
}

export interface CardSpecialField {
  Date: string[];
  Employee: string[];
}

/** 卡片菜单操作按钮 */
export enum CardMenuActionType {
  edit = 'edit',
  delete = 'delete',
  follow = 'follow',
  unfollow = 'unfollow',
}

/** 分组选项菜单操作按钮 */
export enum ListMenuActionType {
  addData = 'addData',
  deleteOption = 'deleteOption',
  updateOption = 'updateOption',
  moveData = 'moveData',
}

export interface AllGroupData {
  [id: string]: any;
}

export interface GroupItem {
  id: string;
  laneId: string;
  name: any;
  type: string | number;
  cards: any[];
  statFields?: any[];
  groupCount?: any[];
  count?: string | number;
  desc?: string;
  baseTime?: string;
  uuid?: string;
  archive?: string; // 是否封存标识
  [x: string]: any
}
export interface YdGroupItemInfo {
  id: string;
  name: any;
}

export interface GroupPermission {
  cardDraggable: boolean;
  cardMenus: any[];
  id: string;
}

export enum OrderType {
  DESC = 'desc', // 降序
  ASC = 'asc', // 升序
}
export interface CustomMenuAction {
  actions: any[];
  iconPath: string;
  buttonType: string;
  name: string;
  objId: string;
  operateType: string;
  showPosition: string[];
  id: string;
  [x: string]: any;
}

export enum DateOpteLevel {
  DayLevel = 1,
  MonthLevel = 2,
  YearLevel = 3,
}

export enum DataType {
  Field = 'field',
  Classification = 'classification',
}

/**
 * 常用搜索-显示方式类型
 * '0' - 高级搜索
 * '1' - 筛选
 */
export type CommonSearchType = '0' | '1';
export interface SearchConditions {
  commonFilterGroups: any[];
  commonFilters: any[];
  commonSearchType: CommonSearchType;
  groupFilters: any[];
  manualFilter: any;
  needCondition: any;
  noneOfShow: any;
  quickFilters: any[];
  searchRelationEnable: any;
  type: any;
  fixedCol: any;
  fixedColCount: any;
}

/**
 * 搜索条件类型
 * 'filter' - 常用搜索：筛选
 * 'advance' - 常用搜索：高级搜索
 * 'quick' - 快捷搜搜
 * 'group' - 分类搜索
 */
export type SearchType = 'filter' | 'advance' | 'quick' | 'group';

/** 数据源类型 */
export type SourceType =
  /** 表单 */
  | 'FORM'
  /** 数据仓库 */
  | 'ETEAMS'
  /** 标准业务数据 */
  | 'BIZ'
  /** 自定义数据 */
  | 'CUSTOM'
  /** 逻辑表 */
  | 'LOGIC'
  /** 模拟数据源 */
  | 'mock'
  /** 数据集合 */
  | 'EXTERNALDATASET'
  /** 外部数据源 */
  | 'EXTERNAL';

/**
 * 数据源值类型
 */
export type DataSourceValueType = {
  id: string;
  text: string;
  type: SourceType;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: any[];
  servicePath?: string;
  params?: string[];
  dataId?: string;
  ebuilderDataConfig?: any;
  data?: any[];
  /** 关联的组件id */
  refComId?: string;
  /** 数字面板，关联的数据项id */
  refPanelId?: string;
  /** 数据源分组信息 数据中心提供 */
  dataGroupConfig?: any;
  /** 数据源分组更改时的时间戳，解决fields缓存问题 */
  dataGroupMemoTime?: number;
  /** 数据源分组原始过滤 */
  dataGroupFilter?: any;
  isPhysical?: boolean;
  /** 数据源支持类型 */
  usage?: string;
  appId?: string;
  comConfig?: any; // 组件配置信息
  objType?: string /** 数据源类型：vform（虚拟表） */;
  detailTable?: boolean /** 是否是明细数据源 */;
};