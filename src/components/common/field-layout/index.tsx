/**
 * <AUTHOR>
 * @createTime 2021-11-24
 * @desc  显示字段配置
 */
import { DataSet } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { NotUseFields } from '../../../constants';
import { hasLinkUrl } from './utils';
type ConfigType = {
  dataset: DataSet;
};

export type FieldConfig = {
  cardLayout: any;
  field: any;
  tableField: any;
};

interface FieldLayoutProps extends React.Attributes {
  config: ConfigType;
  appid: string;
  value: FieldConfig;
  onChange: (val: any) => void;
  onConfigChange?: (value?: FieldConfig) => void;
  externalElement?: (config: any, showDialog: any) => React.ReactNode;
  externalDftStyle?: any;
  dataset?: DataSet;
  needLink?: boolean; // 是否需要支持动作链接
  page?: any;
}

export enum ListModeType {
  TABLE = '1', // 表格 默认
  LIST = '2', // 列表
  Excel = '3', // 列表
}

@observer
export default class FieldLayout extends PureComponent<FieldLayoutProps> {
  fieldChange = (fields: FieldConfig | any) => {
    const { value } = this.props;
    const _value = typeof value === 'string' ? JSON.parse(value) : value;
    // 由于直接点击外部的删除，这个属性会丢失，因此这里取一下之前的
    let field = _value?.field || [];
    if (!isEmpty(fields.field)) {
      field = fields.field;
    }
    this.props.onChange({
      ...fields,
      // 最后再过滤掉序号字段 序号渲染是前端内部处理的 传给后端会报错
      field: field.filter((i: any) => i.id !== '-3'),
    });
  };

  fieldFilter = (field: any) => {
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id)) {
      return false;
    }
    return true;
  };

  render() {
    const { value = {}, config, externalElement, externalDftStyle = {}, dataset, needLink = true, appid = '', page = {} } = this.props;
    const _value = typeof value === 'string' ? JSON.parse(value) : value;

    const setEventActionProps: any = hasLinkUrl(_value)
      ? {}
      : {
          canSetEventAction: true,
          ebModuleType: '',
        };
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_x7skym`}
        // app="@weapp/ebdcoms"
        app="@weapp/ebdlist"
        compName="ListField"
        placeholder={getLabel('87540', '点击设置字段')}
        config={{
          ...config,
          mode: ListModeType.LIST,
          ..._value,
          cardLayout: toJS(_value?.cardLayout || {}),
          field: _value.field || [],
          dataset: config?.dataset || dataset || {},
        }}
        value={toJS(_value.cardLayout) || {}}
        onConfigChange={this.fieldChange}
        externalElement={externalElement}
        externalDftStyle={externalDftStyle}
        // 添加链接动作需要加的参数
        ebModuleType={needLink ? 'EBFORM' : ''}
        pageScope={needLink ? 'EB_FORM_VIEW' : ''}
        appid={appid || config?.dataset?.groupId}
        fieldFilter={this.fieldFilter}
        page={page}
        // 屏蔽掉明细子表
        needDetail={false}
        {...setEventActionProps}
      />
    );
  }
}
