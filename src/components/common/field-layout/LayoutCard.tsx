/**
 * <AUTHOR>
 * @createTime 2021-11-24
 * @desc  显示字段配置
 */
import { DataSet } from '@weapp/ebdcoms';
import { AnyObj, CorsComponent, ListData } from '@weapp/ui';
import { cloneDeep, forEach, isEmpty } from '@weapp/utils';
import { computed, toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { ComponentType, PureComponent, ReactNode } from 'react';
import { withRouter } from 'react-router-dom';
import { BrowserType, NumberType } from '../../../constants/EtComponent';
import ebdcoms from '../../../utils/ebdcoms';
import { isEntryEnable } from '../../../utils/encryUtils';
import { getSystemFieldType, isDetailPage } from '../../../utils';
// import { jumpLink } from '../../../utils/jumpLink';
import { SysFieldType } from '../../../constants/common';

const customFields = ['ImageComponent', 'PositionComponent', 'Raty', 'ProgressBar', 'Select', 'RadioBox', 'CheckBox', 'ComboSelect', 'TextArea', 'Employee', 'FileComponent', 'EinvoiceComponent', 'EmployeeScope', 'EmployeeOrganization', 'EmployeeShare', 'Switch', 'FVideoRecord', 'AudioRecord', 'SelectMultiple', 'RelateTag']
/**
 * 字段配置卡片的区域类型
 * */
export enum ListCardSplitType {
  MainArea = '0', //
  LeftFixedArea = '1',
  RightFixedArea = '2',
  HiddenArea = '3',
}

export interface SplitProps {
  id: string;
  index: number;
  type: ListCardSplitType;
  setting: {
    width: {
      type: 'left' | 'right' | '';
      width: number | string;
    };
  };
}

type ConfigType = {
  dataset: DataSet;
};

type FieldConfig = {
  cardLayout: any;
  field: any;
};

interface LayoutCardProps extends React.Attributes {
  config: ConfigType & FieldConfig;
  data: any;
  onClick?: (val: any) => void;
  sort?: any;
  isEbForm?: boolean;
  isDesign?: boolean;
  client?: 'PC' | 'MOBILE';
  objId?: any;
  originData?: any[];
  customRenderCell?: (field: any, formatValue: any, originValue: any) => React.ReactNode;
  customRenderCellRewrite?: (field: any, formatValue: any, originValue: any) => React.ReactNode;
  useFieldCustomRender?: boolean;
  history?: any; // 路由参数，支持点击链接可跳转
  match?: any; // 路由参数，支持点击链接可跳转
  location?: any; //
  compId: string; // 没有的话传''
  customFields?: string[];
  onCustomEventClick?: (e: any) => void;
  // 处理单格数据，代码块处理数据用的钩子函数，除密级外所有数据都会被劫持
  formatCellData?: (cellData: any, fieldId: string) => any;
  // 内容区域单格自定义方式，代码块处理自定义渲染用的钩子函数，除密级外所有渲染项都会被劫持
  customContent?: (v: ReactNode, fieldId: string) => ReactNode;
  needInterceptCustomRender?: boolean;
  dataset: DataSet;
}

@observer
export default class LayoutCard extends PureComponent<LayoutCardProps> {
  state = {
    numberObj: {} as AnyObj,
    fieldData: [],
  };

  componentDidMount = async () => {
    // 初始化的时候，将数字类型变成个数字对象传递给 ListCard 组件
    if (this.props.originData && this.props.originData.length) {
      forEach(toJS(this.props.config.field), (_field: any) => {
        if (NumberType.includes(_field.compType)) {
          const dataIndex = _field.id;
          const numberList = (this.props.originData || []).map((i: any) => (!!i[dataIndex] && Number(i[dataIndex])) || 0);
          const _numberObj = this.state.numberObj;
          _numberObj[dataIndex] = numberList;
          this.setState(() => ({ numberObj: _numberObj }));
        }
      });
    }
    const { objId, dataset } = this.props;
    if (objId || dataset?.id) {
      // 由于自定义文本，需要传listSourceField，否则解析不出来人员等字段
      const { dsUtils } = await ebdcoms.get();
      const mainData = await dsUtils.getFields({
        ...dataset,
        type: dataset?.type || 'FORM',
        id: objId || dataset?.id,
      });
      this.setState({
        fieldData: mainData,
      });
    }
  };

  // 给表单使用的字段点击
  onCellClick = (field: any, data: any) => async (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    const dataId = data?.id || '';
    let fields: any = { data, id: dataId };
    if (this.ifMainParamIsBrowserType(field.linkUrl)) {
      // 如果浏览框字段，不拼入id，取参数中的值id
      fields = { data };
    }
    const options: any = {
      clientType: this.props.client || 'PC',
      history: this.props.history,
      match: this.props.match,
      location: this.props.location,
      fields,
      compId: this.props?.compId,
    };
    // 详情页加上默认params参数
    if (isDetailPage(field.linkUrl.page)) {
      options.params = { id: dataId };
    }
    // jumpLink(field.linkUrl, options);
  };

  /**
   * 判断首个参数是否为浏览框类型
   * */
  ifMainParamIsBrowserType = (link: any) => {
    if (link?.params?.length) {
      const mainParam = link.params[0];
      const { type, value } = mainParam;
      if (type && type === 'FORMFIELDS' && value?.compType) {
        if (BrowserType.includes(value?.compType)) {
          return true;
        }
      }
    }
    return false;
  };

  handleCustomRenderCell = (field: any, formatValue: any, origionValue: any) => {
    const { data } = this.props;
    field = { ...field, compType: field.compType || getSystemFieldType(field.fieldId, field.type) };
    if (field.linkUrl?.page?.pageId) {
      // 链接动作拦截自定义前端
      return <span onClick={this.onCellClick(field, data)}>{formatValue}</span>;
    }
    return this.props.customRenderCellRewrite?.(field, formatValue, origionValue);
  };

  customHandleCellData = (data: ListData) => {
    const newData = cloneDeep(data);
    Object.keys(newData).forEach(key => {
      // 去掉脱敏字段
      if (isEntryEnable(newData[key])) {
        newData[key] = '';
      }
      // id和5兼容 谁有id就取哪一个
      newData.id = newData['5'] || newData.id;
    });
    return newData;
  };

  @computed
  get getConfig() {
    // 密级走自定义渲染，类型为Select会强制走内部逻辑
    const { config } = this.props;
    const _field = (toJS(config)?.field || []).map((item: any) => {
      if (item.name === SysFieldType.Classification) {
        item.compType = SysFieldType.Classification;
      }
      return item;
    });
    const _config = {
      ...toJS(config),
      field: _field,
    };
    let result = toJS(config)?.field ? _config : config;
    // * 兼容ListCard组件更新时未设置字段报错
    if (isEmpty(result)) {
      return {
        cardLayout: {
          grid: []
        },
      }
    }
    return result
  }
  onClick = ({ ...args }) => {
    const { onClick } = this.props;
    onClick && onClick(args)
  }

  render() {
    const {
      onClick,
      customRenderCell,
      customRenderCellRewrite,
      useFieldCustomRender = false,
      history,
      match,
      location,
      sort,
      ...restProps
    } = this.props;
    let props: any = restProps;
    if (customRenderCell || customRenderCellRewrite) {
      props = {
        ...props,
        customRenderCell: customRenderCell || this.handleCustomRenderCell,
      };
    }
    const isDesign = restProps.isEbForm || restProps.isDesign; // 控制是否马上回显 运行时不要传，不然缓存会被击穿 - wjm

    return (
      <CorsComponent
        {...props}
        weId={`${this.props.weId || ''}_n59kv5`}
        app="@weapp/ebdlist"
        compName="ListCard"
        sort={sort}
        useFieldCustomRender={useFieldCustomRender}
        customFields={customFields}
        config={this.getConfig}
        data={toJS(this.props.data)}
        onClick={this.onClick}
        dataset={{
          ...(restProps?.dataset || {}),
          id: this.props.objId,
        }}
        history={history}
        match={match}
        valueList={this.state.numberObj}
        location={location}
        // 链接地址点击复写
        customHanldeCellData={this.customHandleCellData}
        rowIndex={Number(sort) + 1} // 网格式通过 rowIndex 来判断表格格式奇偶行
        needInterceptCustomRender
        listSourceField={this.state.fieldData} /** 列表数据源字段 */
        design={isDesign}
      />
    );
  }
}

/**
 * 自动获取路由参数
 * */
export const LayoutCardWithRouter = withRouter(LayoutCard as any) as unknown as ComponentType<LayoutCardProps>;
