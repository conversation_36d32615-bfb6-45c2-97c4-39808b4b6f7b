/**
 * <AUTHOR>
 * @createTime 2022-02-18
 * @desc   : 分类搜索选择器
 */
import {
  AnyObj, Checkbox, Icon, Radio, Select, SelectOptionsType,
} from '@weapp/ui';
import { classnames, middleware } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { appName } from '../../../constants';
import { CheckedColor } from '../../../constants';
import '../select-quick/index.less';
import { getLocaleValueString } from '../../common/locale/utils';

export interface SearchSelecterProps extends Attributes {
  otherParams: {
    listGroupSets: any[];
    multiSelect: boolean;
    isOptionTile: boolean;
  };
  onChange: (data: any) => void;
  value: any;
  placeholder?: string;
}

@middleware(appName, 'SearchSelecter')
@observer
export default class SearchSelecter extends PureComponent<SearchSelecterProps> {
  getOptions = () => {
    const {
      otherParams: { listGroupSets = [] },
    } = this.props;
    const options: SelectOptionsType = listGroupSets.map((o) => ({
      ...o,
      content: getLocaleValueString(o.setName),
      color: CheckedColor,
    }));
    return options;
  };

  getValue = () => {
    const {
      otherParams: { multiSelect },
      value,
    } = this.props;
    const _v = toJS(value) === '-1' ? '' : toJS(value);
    return multiSelect ? _v : `${_v}`; // 下拉框单选时string,多选时string[]
  };

  getSingleCustom = () => {
    const params: AnyObj = {};
    if (this.getValue()) {
      params.inputIcon = (
        <>
          <Icon
            weId={`${this.props.weId || ''}_0mqdcz`}
            name="Icon-cancel"
            size="s"
            onClick={this.clearSelected}
          />
          <Icon weId={`${this.props.weId || ''}_k7k1d5`} name="Icon-Down-arrow01" size="s" />
        </>
      );
    }
    return params;
  };

  clearSelected = (e: React.MouseEvent) => {
    e.stopPropagation();
    this.props.onChange?.('');
  };

  render(): React.ReactNode {
    const {
      otherParams: { multiSelect, isOptionTile = false },
      onChange,
      value,
      placeholder,
    } = this.props;
    const singleSelect = multiSelect ? {} : this.getSingleCustom();
    return (
      <>
        <If weId={`${this.props.weId || ''}_ztzw61`} condition={isOptionTile}>
          <Then weId={`${this.props.weId || ''}_i5hzgf`}>
            <When weId={`${this.props.weId || ''}_kdmexz`} condition={multiSelect}>
              <Checkbox
                weId={`${this.props.weId || ''}_i6tfvm`}
                data={this.getOptions()}
                optionType="tag"
                hideCheck
                value={toJS(value)}
                onChange={onChange}
              />
            </When>
            <When weId={`${this.props.weId || ''}_g5m61i`} condition={!multiSelect}>
              <Radio
                weId={`${this.props.weId || ''}_7yfkn8`}
                data={this.getOptions()}
                optionType="tag"
                hideCheck
                value={`${toJS(value)}`}
                onChange={onChange}
              />
            </When>
          </Then>
          <Else weId={`${this.props.weId || ''}_tz3f9n`}>
            <Select
              weId={`${this.props.weId || ''}_qfj3bi`}
              placeholder={placeholder}
              data={this.getOptions() as SelectOptionsType}
              multiple={multiSelect}
              allowSelectAll={multiSelect}
              onChange={onChange}
              value={this.getValue()}
              allowCancel={!multiSelect}
              className={classnames({
                'ui-select-single-custom': !multiSelect,
                'ui-select-single-custom-active': !multiSelect && this.getValue(),
              })}
              {...singleSelect}
            />
          </Else>
        </If>
      </>
    );
  }
}
