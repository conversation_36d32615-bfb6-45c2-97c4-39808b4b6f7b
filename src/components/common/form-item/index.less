@import (reference) '../../../style/prefix.less';

.@{prefix}-form-item {
  margin-right: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  & > .@{uiPcClsPrefix}-layout-col-6 ,
  & > .@{uiPcClsPrefix}-layout-col-16{
    display: block;
    flex: none;
    max-width: 100%;
  }
  .ui-formItem-label{
    margin-right: var(--h-spacing-lg);
  }

  &.@{prefix}-fr .@{uiPcClsPrefix}-formItem-wrapper {
    justify-content: flex-end;
  }
  &.@{uiPcClsPrefix}-formItem-module {
    padding: 0;
  }
  .@{uiPcClsPrefix}-formItem-label {
    line-height: 32px;
    // min-width: 84px;
  }
  .@{uiPcClsPrefix}-formItem-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    line-height: 1;
  }
}
