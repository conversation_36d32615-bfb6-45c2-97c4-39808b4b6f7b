import { Layout } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React, { CSSProperties, ReactNode } from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

const { Row, Col } = Layout;
const uiFormItemPrefix = 'ui-formItem';
const formItemClsNameStr = `${ebdBClsPrefix}-form-item ${uiFormItemPrefix} ${uiFormItemPrefix}-module`;

interface FormItemProps extends React.Attributes {
  label?: string;
  labelTitle?: string; // 标签title
  align?: 'right' | 'left';
  labelSpan?: number;
  colSpan?: number;
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
}

const FormItem: React.FC<FormItemProps> = (props) => {
  const {
    label,
    labelTitle = '',
    labelSpan = 6,
    colSpan = 16,
    className = '',
    children,
    align = 'left',
    style,
  } = props;
  const clsNameStr = classnames(`${formItemClsNameStr} ${className}`, {
    [`${ebdBClsPrefix}-fr`]: align === 'right',
  });
  return (
    <Row weId={`${props.weId || ''}_kgstdz`} style={style} className={clsNameStr}>
      <When weId={`${props.weId || ''}_xccj54`} condition={label}>
        <Col
          weId={`${props.weId || ''}_y3buha`}
          className={`${uiFormItemPrefix}-label-col`}
          span={labelSpan}
        >
          <div className={`${uiFormItemPrefix}-label`} title={labelTitle || label}>
            {label}
          </div>
        </Col>
      </When>

      <Col
        weId={`${props.weId || ''}_tpma13`}
        span={colSpan}
        className={`${uiFormItemPrefix}-wrapper-col`}
      >
        <div className={`${uiFormItemPrefix}-wrapper`}>{children}</div>
      </Col>
    </Row>
  );
};

export default FormItem;
