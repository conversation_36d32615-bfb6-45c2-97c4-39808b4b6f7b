import {
  AnyObj,
  Checkbox,
  CheckboxValueType,
  Icon,
  Radio,
  RadioValueType,
  Select,
  SelectOptionData,
  SelectProps,
  SelectValueType,
} from '@weapp/ui';
import { OptionsType } from '@weapp/ui/lib/components/select';
import { OptionData } from '@weapp/ui/lib/components/select/types';
import { classnames } from '@weapp/utils';
import isArray from 'lodash-es/isArray';
import { observer } from 'mobx-react';
import React, { Attributes, useEffect, useState } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { CheckedColor } from '../../../constants';
import './index.less';

interface SelectQuickProps extends Attributes, SelectProps {
  onChange: (value: SelectValueType, option: OptionData | OptionsType) => void;
  selectProps?: SelectProps;
  isOptionTile?: boolean; // 是否平铺
}

export default observer((props: SelectQuickProps) => {
  const {
    placeholder,
    className,
    onChange,
    value,
    data,
    multiple,
    isOptionTile = false,
    selectProps = {},
  } = props;
  const [val, setVal] = useState<any>('-1');
  const [key] = useState(Date.now());

  const onSelectChange = (
    v: SelectValueType | CheckboxValueType | RadioValueType,
    option: OptionData | OptionsType,
  ) => {
    setVal(v);
    const _v = v === '-1' ? '' : v;
    if (onChange) {
      onChange(_v as any, option);
    }
  };

  useEffect(() => {
    setVal(value === '-1' ? '' : value);
  }, [value]);

  const newData: SelectOptionData[] = data?.map((f: any) => ({
    ...f,
    content: f.name,
    id: f.value || f.id, // 下拉框的字段是value, 整数配置的下拉是id
    color: CheckedColor,
  })) || [];

  /**
   * 页面需求：下拉单选时，添加x清空选项
   * @param e
   */
  const clearSelected = (e: React.MouseEvent) => {
    e.stopPropagation();
    setVal('');
    onChange?.('', { id: '' });
  };

  const getSingleCustom = () => {
    const params: AnyObj = {};
    if (val) {
      params.inputIcon = (
        <>
          <Icon
            weId={`${props.weId || ''}_o1zl91`}
            name="Icon-cancel"
            size="s"
            onClick={clearSelected}
          />
          <Icon weId={`${props.weId || ''}_7kyckn`} name="Icon-Down-arrow01" size="s" />
        </>
      );
    }
    return params;
  };

  const singleSelect = multiple ? {} : getSingleCustom();

  // 移动建模那边反馈，不需要请选择，allowCancel即可
  // if (!multiple && !isOptionTile) {
  //   // 多选不需要添加all类型   e10叫请选择，这种情况下展示placeholder
  //   newData.unshift({ id: '-1', content: getLabel('40502', '请选择') });
  // }
  return (
    <>
      <If weId={`${props.weId || ''}_g0ia2w`} condition={isOptionTile}>
        <Then weId={`${props.weId || ''}_zu3a1r`}>
          <When weId={`${props.weId || ''}_n03g33`} condition={multiple}>
            <Checkbox
              weId={`${props.weId || ''}_49vods`}
              data={newData}
              optionType="tag"
              hideCheck
              value={val}
              onChange={onSelectChange}
            />
          </When>
          <When weId={`${props.weId || ''}_s9tw2z`} condition={!multiple}>
            <Radio
              weId={`${props.weId || ''}_lpio10`}
              data={newData}
              optionType="tag"
              hideCheck
              // 兼容默认值：平铺下返回的都是数组
              value={isArray(val) ? val?.[0] : val}
              onChange={onSelectChange}
            />
          </When>
        </Then>
        <Else weId={`${props.weId || ''}_zyttme`}>
          <Select
            weId={`${props.weId || ''}_lbi4mt`}
            key={key}
            placeholder={placeholder}
            className={classnames(className, {
              'ui-select-single-custom': !multiple,
              'ui-select-single-custom-active': !multiple && val,
            })}
            onChange={onSelectChange}
            value={val}
            data={newData}
            multiple={multiple}
            allowSelectAll={multiple}
            {...selectProps}
            allowCancel
            {...singleSelect}
          />
        </Else>
      </If>
    </>
  );
});
