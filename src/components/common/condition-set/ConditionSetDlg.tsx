import { DataSet } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { specialFieldsFilter } from '../../../utils';

export interface ConditionSetDlgProps extends React.Attributes {
  visible: boolean;
  title?: string;
  filters?: any;
  dataset: DataSet;
  filter?: (item: any) => boolean;
  onCancel: () => void;
  onOk: (fitlers: any) => void;
}

/**
 * ConditionSet dialog
 */
@observer
export default class ConditionSetDlg extends React.PureComponent<ConditionSetDlgProps> {
  render() {
    const {
      dataset, onOk, visible, onCancel, title, filter = specialFieldsFilter,
    } = this.props;
    let { filters } = this.props;
    if (!filters || (Array.isArray(toJS(filters)) && toJS(filters).length === 0)) {
      filters = {};
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_lld9pr`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        dataSet={dataset}
        value={filters}
        onOk={onOk}
        onCancel={onCancel}
        visible={visible}
        title={title}
        fieldFilter={filter}
        showFilterType // 是否展示sql和自定义接口筛选类型
      />
    );
  }
}
