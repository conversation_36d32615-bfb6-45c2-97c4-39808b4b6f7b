import { DataSet, Filter } from '@weapp/ebdcoms';
import { Button, Icon } from '@weapp/ui';
import { BtnType } from '@weapp/ui/lib/components/button/types';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, {
  Attributes, ComponentType, useEffect, useState,
} from 'react';
import { ebdBClsPrefix } from '../../../constants';
import ConditionSetDlg from './ConditionSetDlg';

export interface ConditionSetBtnProps extends Attributes {
  value: Filter[];
  dataset: DataSet;
  onSave: (value: any) => void;
  btnClassName?: string;
  buttonType?: BtnType;
  btnTitle?: string;
  dlgTitle?: string;
}

/**
 * 带按钮的ConditionSet
 */
const ConditionSetBtn: ComponentType<ConditionSetBtnProps> = (props) => {
  const {
    dataset,
    onSave,
    value,
    btnClassName,
    buttonType = 'default',
    btnTitle = getLabel('53857', '数据过滤'),
    dlgTitle = getLabel('101756', '固定查询条件'),
  } = props;
  const [show, setShow] = useState<boolean>(false);
  const [val, setVal] = useState<Filter[]>(value);

  const onOk = (filters: Filter[]) => {
    setVal(filters);
    if (JSON.stringify(value) !== JSON.stringify(filters)) {
      onSave(JSON.stringify(filters));
    }
    setShow(false);
  };

  useEffect(() => {
    setVal(value);
  }, [value]);

  const onCancel = () => {
    setVal(value);
    setShow(false);
  };

  const triggerShow = () => {
    const visible = !show;
    setShow(visible);
  };

  return (
    <>
      <Button
        weId={`${props.weId || ''}_7e40jq`}
        onClick={triggerShow}
        className={`${ebdBClsPrefix}-calendar-style-set-button ${btnClassName}`}
        type={buttonType}
      >
        <Icon weId={`${props.weId || ''}_y8amo8`} className="btn-icon-left" name="Icon-screen" />
        {btnTitle}
      </Button>
      <ConditionSetDlg
        weId={`${props.weId || ''}_rlzl2e`}
        visible={show}
        dataset={dataset}
        title={dlgTitle}
        filters={val}
        onCancel={onCancel}
        onOk={onOk}
      />
    </>
  );
};

export default observer(ConditionSetBtn);
