import { DataSet, Filter } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';

export interface ConditionSetProps {
  value: Filter[];
  dataSet: DataSet;
  visible?: boolean;
  filter?: any; // 筛选数据函数
  onChange?: (fitlers: any[]) => void;
  onCancel?: () => void;
  title?: string;
  placeholder?: string;
}

/**
 * 带input框的ConditionSet
 */
@observer
export default class ConditionSet extends React.PureComponent<ConditionSetProps> {
  render() {
    const { value = [], dataSet, title } = this.props;
    const _value = typeof value === 'string' && value ? JSON.parse(value) : value;

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_2x02u5`}
        app="@weapp/components"
        compName="ConditionSet"
        title={title || getLabel('56236', '列表固定查询条件')}
        placeholder={getLabel('87541', '点击设置过滤条件')}
        {...this.props}
        value={_value}
        dataSet={dataSet}
      />
    );
  }
}
