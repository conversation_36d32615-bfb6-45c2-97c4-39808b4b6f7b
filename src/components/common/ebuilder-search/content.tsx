import {
  AnyObj, Button, Empty, Icon, Input, List, ListData, Spin, utils,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { Attributes, ReactText } from 'react';
import { Else, If, Then } from 'react-if';
import { ebdBClsPrefix, ModuleType } from '../../../constants';
import { ajax } from '../../../utils/ajax';
import { getEbBrowserTargetFormId } from '../../../utils';
import { GroupSearchField } from '../../../types/common';
import './index.less';

const { isEqual } = utils;
const ebuilderSearchClsPrefix = `${ebdBClsPrefix}-ebuilder-search`;

interface EBuilderSearchProps extends Attributes {
  selectedKeys: string[];
  fieldInfo: {
    fieldId: string;
    formId: string;
    layoutId: string;
    // 自定义获取eb列表数据
    customGetEbuilderList?: Function;
    [_: string]: any;
  };
  searchParam?: AnyObj;
  onChange: (id: Array<ListData> | Array<string>) => void;
  data: GroupSearchField;
  fieldId?: string;
}

interface State {
  searchContent: ReactText;
  dataSelectdList: Array<ListData> | Array<string>;
  dataList: any;
  loading: boolean;
}

@observer
class EBuilderSearch extends React.PureComponent<EBuilderSearchProps, State> {
  constructor(props: EBuilderSearchProps) {
    super(props);
    this.state = {
      dataList: [],
      dataSelectdList: props.selectedKeys || [],
      searchContent: '',
      loading: true, // 加载中
    };
  }

  componentDidMount() {
    this.getEbuilderList();
  }

  componentDidUpdate(oldProps: EBuilderSearchProps) {
    // 判断重新请求的时候不考虑当前数据本身
    // 和小武沟通，暂时不考虑分类之间的过滤，等后续需要在放开，但是要注意避免本身的自我比较；
    delete oldProps.searchParam?.groupFields;
    delete this.props.searchParam?.groupFields;
    if (!isEqual(oldProps.searchParam, this.props.searchParam)) {
      this.getEbuilderList();
    }
    if (!isEqual(this.props.selectedKeys, oldProps.selectedKeys)) {
      this.updateSelectList();
    }
  }

  getEbuilderList = async () => {
    // 获取部门
    const { fieldInfo, searchParam, fieldId } = this.props;
    const { customGetEbuilderList } = fieldInfo;
    let newRes: any;
    if (customGetEbuilderList) {
      newRes = await customGetEbuilderList({
        searchValue: this.state.searchContent,
        fieldInfo,
      });
    } else {
      // 表格视图识别listId
      if (searchParam?.viewType === ModuleType.Table && !searchParam?.listId) {
        return;
      }
      newRes = await ajax({
        url: '/api/ebuilder/form/browser/groupWithCount',
        method: 'post',
        data: {
          searchValue: this.state.searchContent,
          formParam: JSON.stringify({
            formId: fieldInfo.formId,
            layoutId: fieldInfo.layoutId,
            fieldId: fieldInfo.fieldId,
            filterItems: [],
            module: 'ebuilderform',
            dataDetails: [],
          }),
          newly: 1,
          pageSize: 100,
          // formId给后端分流用，不用pageId
          searchParam: {
            ...searchParam,
            fieldId,
            formId: fieldInfo.formId,
          } || {
            formId: fieldInfo.formId,
          },
        },
        ebBusinessId: getEbBrowserTargetFormId(fieldInfo),
      });
    }
    if (newRes) {
      this.setState({ dataList: newRes, loading: false });
    } else {
      this.setState({ loading: false });
    }
  };

  updateSelectList = () => {
    this.setState({ dataSelectdList: this.props.selectedKeys });
  };

  onRowSelect = (arr: Array<ListData> | Array<string>) => {
    this.setState({ dataSelectdList: arr });
    this.props.onChange?.(arr);
  };

  customRenderItem = (rowData: ListData) => {
    const obj = rowData;
    return (
      <>
        <span className="name" key={obj.id} title={obj.name}>
          {obj.name}
        </span>
        <Icon weId={`${this.props.weId || ''}_gkukrp`} name="Icon-correct01" />
      </>
    );
  };

  onClickSearch = () => {
    this.getEbuilderList();
  };

  onSearchContentChange = (value: ReactText) => {
    this.setState({ searchContent: value });
  };

  render() {
    const { dataList = [], dataSelectdList = [], loading } = this.state;
    const { multiSelect = true } = this.props.data;
    return (
      <div className={`${ebuilderSearchClsPrefix}-content`}>
        <div className={`${ebuilderSearchClsPrefix}-content-filter`}>
          <Input
            weId={`${this.props.weId || ''}_c3llbj`}
            suffix={
              <Button weId={`${this.props.weId || ''}_bmyo0w`} type="link">
                <Icon
                  weId={`${this.props.weId || ''}_ch0nm4`}
                  name="Icon-global-search-o"
                  onClick={this.onClickSearch}
                />
              </Button>
            }
            allowClear
            onPressEnter={this.onClickSearch}
            onChange={this.onSearchContentChange}
          />
        </div>
        <Spin
          weId={`${this.props.weId || ''}_q84tj2`}
          spinning={loading}
          text={getLabel('116168', '加载中...')}
        >
          <If weId={`${this.props.weId || ''}_t0t16c`} condition={!dataList.length && !loading}>
            <Else weId={`${this.props.weId || ''}_x9uh7z`}>
              <List
                weId={`${this.props.weId || ''}_4t0634`}
                multipleCheck={multiSelect}
                checkboxPosition="right"
                data={dataList}
                rowSelect
                customRenderContent={this.customRenderItem}
                selectedRowKeys={toJS(dataSelectdList)}
                onRowSelect={this.onRowSelect}
                stopCheckboxPropagation
              />
            </Else>

            <Then weId={`${this.props.weId || ''}_6wsinz`}>
              <Empty
                weId={`${this.props.weId || ''}_ld23gh`}
                description={getLabel('54023', '暂无数据')}
                style={{ paddingTop: 80 }}
                image={
                  <Icon
                    weId={`${this.props.weId || ''}_sxfldj`}
                    style={{ width: 100, height: 100 }}
                    name="Icon-empty-file"
                  />
                }
              />
            </Then>
          </If>
        </Spin>
      </div>
    );
  }
}

export default EBuilderSearch;
