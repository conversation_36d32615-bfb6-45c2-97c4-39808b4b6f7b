@import (reference) '../../../style/prefix.less';

.@{prefix}-ebuilder-search-content{
  height: 100%;
  flex: 1 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  &-filter{
    display: flex;
    align-items: center;
    padding:4px 8px;
  }
  &-filter+div {
    flex: 1 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .ui-list-body{
      .ui-list-item{
        &:hover {
          cursor: pointer;
        }
        .ui-list-content {
          padding: 10px 0 10px 10px;
          border: none!important;
          .name{
            margin-left: 8px;
            flex: 1 1;
            overflow: hidden;
            font-size: var(--font-size-12);
            text-overflow: ellipsis;
          }
          .ui-icon{
            width: 30px;
            opacity: 0;
          }
        }
      }
      .ui-list-item:hover {
        .ui-list-content{
          color: var(--primary);
        }
      }
      .ui-list-item-checked{
        .ui-list-content{
          color: var(--primary);
          .ui-icon{
            opacity: 1;
          }
        }
      }
    }
  }

  &>.ui-spin-nested-loading{
    height: calc(100% - 38px);
    .ui-spin-container{
      height: 100%;
      overflow: auto;
    }
  }
}