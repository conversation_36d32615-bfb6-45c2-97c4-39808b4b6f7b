import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rows<PERSON>, MSelect, Select,
} from '@weapp/ui';
import { classnames, middleware } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { When } from 'react-if';
import { appName, ebdBClsPrefix } from '../../../constants';
import { stopPropagation } from '../../../utils';
import { CheckedColor } from '../../../constants';
import './index.less';

@middleware(appName, 'PersonSelect')
@observer
export default class PersonSelect extends React.PureComponent<any> {
  getOptions = () => {
    const { datas = [], isFilter = false } = this.props;
    // 筛选
    if (datas.length === 0 && isFilter) {
      const {
        field: { filterSets = [] },
      } = this.props;
      return filterSets.map(({ dateScope, name }: any, ind: number) => ({
        id: dateScope,
        content: name,
        color: CheckedColor,
        isLast: ind === datas.length - 1,
      }));
    }
    return datas.map(({ dateScope, name }: any, ind: number) => ({
      id: dateScope,
      content: name,
      color: CheckedColor,
      isLast: ind === datas.length - 1,
    }));
  };

  onDateChange = (value: any) => {
    const { datas = [] } = this.props;
    const personValue = [value];
    if (datas.length >= 0) {
      personValue.unshift('chooseEmp');
    }
    this.props.onChange(personValue);
  };

  transValue = () => {
    let { value } = this.props;
    // 组件日期保存的格式value.value才是实际的日期范围
    if (Object.prototype.toString.call(value) === '[object Object]') {
      value = (value as any).value;
    }

    return value || [];
  };

  onSelect = (value: any) => {
    this.props?.onChange?.([value]);
  };

  // 阻止冒泡
  dropdownRender = (nodes: any) => <div onMouseLeave={stopPropagation}>{nodes}</div>;

  clearSelected = (e: React.MouseEvent) => {
    e.stopPropagation();
    this.props?.onChange?.([]);
  };

  render() {
    const { isMobile = false, value = [] } = this.props;
    const Value = toJS(value);
    let empValue = Value[1];

    const options = this.getOptions();
    if (!Array.isArray(Value[1]) && Value[1]) {
      empValue = Value.filter((item: any, index: any) => index > 0);
    }
    const selectValue = options.filter((f: any) => f.id === Value[0] || f.id === Value[0]?.id);
    // 移动端
    if (isMobile) {
      return (
        <div className={`${ebdBClsPrefix}-MpersonSelect`}>
          <MSelect
            weId={`${this.props.weId || ''}_gts6r5`}
            data={options}
            value={selectValue || ''}
            onChange={this.onSelect}
            dropdownRender={this.dropdownRender}
            className={classnames(`${ebdBClsPrefix}-personSelect-select`, {
              'ui-select-single-custom': true,
            })}
            inputIcon={
              <>
                <When
                  weId={`${this.props.weId || ''}_adfbly`}
                  condition={selectValue?.length !== 0}
                >
                  <Icon
                    weId={`${this.props.weId || ''}_b77qht`}
                    name="Icon-cancel"
                    size="s"
                    onClick={this.clearSelected}
                  />
                </When>
                <Icon weId={`${this.props.weId || ''}_uaizbf`} name="Icon-Down-arrow01" size="s" />
              </>
            }
          />

          {/* 如果是选择人员 */}
          <When weId={`${this.props.weId || ''}_zy9rlm`} condition={Value[0] === 'chooseEmp'}>
            <MBrowser
              weId={`${this.props.weId || ''}_9kc3cv`}
              type="resource"
              module="hrm"
              hasAdvanceSearch
              browserAssociativeProps={{
                enableAddData: false,
                enableExtendButton: false,
              }}
              multiple
              onChange={this.onDateChange}
              value={empValue || []}
            />
          </When>
        </div>
      );
    }
    return (
      <div className={`${ebdBClsPrefix}-personSelect`}>
        <Select
          weId={`${this.props.weId || ''}_gts6r5`}
          data={options}
          value={selectValue || ''}
          onChange={this.onSelect}
          dropdownRender={this.dropdownRender}
          className={classnames(`${ebdBClsPrefix}-personSelect-select`, {
            'ui-select-single-custom': true,
          })}
          inputIcon={
            <>
              <When weId={`${this.props.weId || ''}_adfbly`} condition={selectValue?.length !== 0}>
                <Icon
                  weId={`${this.props.weId || ''}_b77qht`}
                  name="Icon-cancel"
                  size="s"
                  onClick={this.clearSelected}
                />
              </When>
              <Icon weId={`${this.props.weId || ''}_uaizbf`} name="Icon-Down-arrow01" size="s" />
            </>
          }
        />

        {/* 如果是选择人员 */}
        <When weId={`${this.props.weId || ''}_zy9rlm`} condition={Value[0] === 'chooseEmp'}>
          <Browser
            weId={`${this.props.weId || ''}_9kc3cv`}
            type="resource"
            module="hrm"
            hasAdvanceSearch
            multiple
            browserAssociativeProps={{
              enableAddData: false,
              enableExtendButton: false,
              wrapDisplay: true,
            }}
            onChange={this.onDateChange}
            value={empValue || []}
          />
        </When>
      </div>
    );
  }
}
