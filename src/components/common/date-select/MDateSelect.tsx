import { MDateTimePicker, MSelect } from '@weapp/ui';
import { observer } from 'mobx-react';
import React from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';
import { MDateSelectProps } from './types';

@observer
export default class MDateSelect extends React.PureComponent<MDateSelectProps> {
  render() {
    const {
      options, value, onDateChange, onSelect, type, otherParams = {},
    } = this.props;
    const [startDate = '', endDate = '', selectValue = ''] = value || [];

    // 显示成日期
    if (options.length === 0) {
      return (
        <MDateTimePicker
          weId={`${this.props.weId || ''}_po4zox`}
          isRange
          type={type as any}
          value={[startDate || '', endDate || '']}
          onChange={onDateChange}
          {...otherParams}
        />
      );
    }

    // 显示成 下拉框或者单选框+（自定义时）日期
    return (
      <div className={`${ebdBClsPrefix}-mdateSelect`}>
        <MSelect
          weId={`${this.props.weId || ''}_7j7crg`}
          data={options}
          value={selectValue}
          onChange={onSelect}
          className={`${ebdBClsPrefix}-mdateSelect-select`}
        />
        <When weId={`${this.props.weId || ''}_zy9rlm`} condition={selectValue === 'customDate'}>
          <MDateTimePicker
            weId={`${this.props.weId || ''}_po4zox`}
            isRange
            type={type as any}
            value={[startDate, endDate]}
            onChange={onDateChange}
            primaryKey={[]}
            className={`${ebdBClsPrefix}-mdateSelect-datePicker`}
            {...otherParams}
          />
        </When>
      </div>
    );
  }
}
