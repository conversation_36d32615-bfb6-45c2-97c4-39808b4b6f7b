import {
  DatePicker, DatePickerDateType, Icon, Radio, Select,
} from '@weapp/ui';
import { PrimaryKeyType } from '@weapp/ui/lib/components/date-picker/types';
import { classnames, dayjs, middleware } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { appName, ebdBClsPrefix, CheckedColor } from '../../../constants';
import { stopPropagation } from '../../../utils';
import { DateCom } from './DateCom';
import './index.less';
import MDateSelect from './MDateSelect';
import { DateSelectProps } from './types';
import { getDatePickPrimaryAry } from './utils';

@middleware(appName, 'DateSelect')
@observer
export default class DateSelect extends React.PureComponent<DateSelectProps> {
  getOptions = () => {
    const { datas = [] } = this.props;
    return datas.map(({ dateScope, name }: any, ind: number) => ({
      id: dateScope,
      content: name,
      color: CheckedColor,
      isLast: ind === datas.length - 1,
    }));
  };

  onDateChange = (value: any) => {
    const { datas = [] } = this.props;
    const [startDate = '', endDate = ''] = value;
    const newValue = [startDate, endDate];
    if (datas.length > 0) {
      newValue.push('customDate');
    }
    this.props.onChange(newValue);
  };

  transDefaultDate = (
    startDate: DatePickerDateType,
    endDate: DatePickerDateType,
    format: string,
  ) => [dayjs?.(startDate)?.format(format), dayjs?.(endDate)?.format(format)];

  transValue = () => {
    let { value } = this.props;
    // 组件日期保存的格式value.value才是实际的日期范围
    if (Object.prototype.toString.call(value) === '[object Object]') {
      value = (value as any).value;
    }
    return value || [];
  };

  onSelect = (value: any) => {
    const dateVal = DatePicker.getRangeInfoByPrimaryKey(value, 1);
    let dataFormatValue = ['', ''];
    if (dateVal !== -1) {
      // 除了区间组件的toady等数据，其余均为-1
      dataFormatValue = this.transDefaultDate(
        dateVal.startDate as DatePickerDateType,
        dateVal.endDate as DatePickerDateType,
        'YYYY-MM-DD',
      );
    }
    this.props?.onChange?.([...dataFormatValue, value]);
  };

  // 阻止冒泡
  dropdownRender = (nodes: any) => <div onMouseLeave={stopPropagation}>{nodes}</div>;

  clearSelected = (e: React.MouseEvent) => {
    e.stopPropagation();
    this.props?.onChange?.([]);
  };

  customOptionRender = (_: any, ele: any) => {
    const {
      type, innerRadio = false, otherParams = {},
    } = this.props;
    const [startDate = '', endDate = '', selectValue = ''] = this.transValue();

    // innerRadio+自定义日期 复写最后一项 日期组件也跟随flex布局
    if (selectValue === 'customDate' && innerRadio && _.option.isLast) {
      return (
        <div>
          <span>{ele}</span>
          <DateCom
            weId={`${this.props.weId || ''}_s01adn`}
            type={type}
            value={[startDate, endDate]}
            primaryKey={getDatePickPrimaryAry(1).map((item) => item.type as PrimaryKeyType)}
            onChange={this.onDateChange}
            className={`${ebdBClsPrefix}-dateSelect-datePicker`}
            {...otherParams}
          />
        </div>
      );
    }

    return <span>{ele}</span>;
  };

  render() {
    const {
      isOptionTile,
      isMobile = false,
      placeholder,
      type,
      innerRadio = false,
      value,
      otherParams = {},
    } = this.props;
    const [startDate = '', endDate = '', selectValue = ''] = this.transValue();
    const options = this.getOptions();

    // 移动端
    if (isMobile) {
      return (
        <MDateSelect
          weId={`${this.props.weId || ''}_0yzkrd`}
          options={options}
          type={type}
          value={value}
          onDateChange={this.onDateChange}
          onSelect={this.onSelect}
          otherParams={otherParams}
        />
      );
    }

    // 显示成日期
    if (options.length === 0) {
      return (
        <DateCom
          weId={`${this.props.weId || ''}_zrmtfo`}
          type={type}
          value={value || ['', '']}
          onChange={this.onDateChange}
          primaryKey={getDatePickPrimaryAry(1).map((item) => item.type as PrimaryKeyType)}
          {...otherParams}
        />
      );
    }

    // 显示成 下拉框或者单选框+（自定义时）日期
    return (
      <div className={`${ebdBClsPrefix}-dateSelect`}>
        <If weId={`${this.props.weId || ''}_mdq0jx`} condition={isOptionTile}>
          <Then weId={`${this.props.weId || ''}_a6jnhd`}>
            <Radio
              weId={`${this.props.weId || ''}_gr86w2`}
              data={options}
              value={selectValue}
              optionType="tag"
              hideCheck
              onChange={this.onSelect}
              customOptionRender={this.customOptionRender}
            />
          </Then>
          <Else weId={`${this.props.weId || ''}_rtnejz`}>
            <Select
              weId={`${this.props.weId || ''}_91fyxf`}
              data={options}
              value={selectValue}
              onChange={this.onSelect}
              dropdownRender={this.dropdownRender}
              placeholder={placeholder}
              className={classnames(`${ebdBClsPrefix}-dateSelect-select`, {
                'ui-select-single-custom': true,
                'ui-select-single-custom-active': selectValue,
              })}
              inputIcon={
                <>
                  <When weId={`${this.props.weId || ''}_adfbly`} condition={selectValue}>
                    <Icon
                      weId={`${this.props.weId || ''}_b77qht`}
                      name="Icon-cancel"
                      size="s"
                      onClick={this.clearSelected}
                    />
                  </When>
                  <Icon
                    weId={`${this.props.weId || ''}_uaizbf`}
                    name="Icon-Down-arrow01"
                    size="s"
                  />
                </>
              }
            />
          </Else>
        </If>
        <When
          weId={`${this.props.weId || ''}_zy9rlm`}
          condition={selectValue === 'customDate' && (!isOptionTile || !innerRadio)}
        >
          <DateCom
            weId={`${this.props.weId || ''}_s01adn`}
            type={type}
            value={[startDate, endDate]}
            primaryKey={getDatePickPrimaryAry(1).map((item) => item.type as PrimaryKeyType)}
            onChange={this.onDateChange}
            className={`${ebdBClsPrefix}-dateSelect-datePicker`}
            {...otherParams}
          />
        </When>
      </div>
    );
  }
}
