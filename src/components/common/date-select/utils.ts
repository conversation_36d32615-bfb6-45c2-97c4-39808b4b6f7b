import { dayjs, getLabel } from '@weapp/utils';
import { CheckedColor } from '../../../constants';
import { MultiBtnDataType, PrimaryKeyType } from './types';

/**
 * @description: 动态产生原生数组（只有点击的时候才会开始计算）
 * @param {number} weekStart
 * @return {*}
 */
export function getDatePickPrimaryAry(weekStart: number = 0) {
  const today = dayjs().day();
  const diff = weekStart <= today ? weekStart - today : weekStart - today - 7; // 每周开始日调整

  const season = dayjs().quarter(); // 本季度开始日
  const primaryAry: MultiBtnDataType[] = [
    {
      type: 'today',
      title: getLabel('55076', '今天'),
      startDate: dayjs(),
      endDate: dayjs(),
    },
    {
      type: 'yesterday',
      title: getLabel('54358', '昨天'),
      startDate: dayjs().subtract(1, 'day'),
      endDate: dayjs().subtract(1, 'day'),
    },
    {
      type: 'tomorrow',
      title: getLabel('55184', '明天'),
      startDate: dayjs().add(1, 'day'),
      endDate: dayjs().add(1, 'day'),
    },
    {
      type: 'thisWeek',
      title: getLabel('55077', '本周'),
      startDate: dayjs().add(diff, 'day'),
      endDate: dayjs().add(diff, 'day').add(6, 'day'),
    },
    {
      type: 'lastWeek',
      title: getLabel('55078', '上一周'),
      startDate: dayjs().add(diff, 'day').subtract(7, 'day'),
      endDate: dayjs().add(diff, 'day').add(6, 'day').subtract(7, 'day'),
    },
    {
      type: 'nextWeek',
      title: getLabel('222261', '下一周'),
      startDate: dayjs().add(diff, 'day').add(7, 'day'),
      endDate: dayjs().add(diff, 'day').add(6, 'day').add(7, 'day'),
    },
    {
      type: 'thisMonth',
      title: getLabel('222262', '本月'),
      startDate: dayjs().set('date', 1),
      endDate: dayjs()
        .set('month', dayjs().month() + 1)
        .set('date', 0),
    },
    {
      type: 'lastMonth',
      title: getLabel('206825', '上一月'),
      startDate: dayjs()
        .set('month', dayjs().month() - 1)
        .set('date', 1),
      endDate: dayjs()
        .set('month', dayjs().month() - 1 + 1)
        .set('date', 0),
    },
    {
      type: 'nextMonth',
      title: getLabel('222263', '下一月'),
      startDate: dayjs()
        .set('month', dayjs().month() + 1)
        .set('date', 1),
      endDate: dayjs()
        .set('month', dayjs().month() + 1 + 1)
        .set('date', 0),
    },
    {
      type: 'thisSeason',
      title: getLabel('55182', '本季'),
      startDate: dayjs()
        .set('month', (season - 1) * 3)
        .set('date', 1),
      endDate: dayjs()
        .set('month', season * 3)
        .set('date', 0),
    },
    {
      type: 'lastSeason',
      title: getLabel('222264', '上一季'),
      startDate: dayjs()
        .set('month', (season - 1 - 1) * 3)
        .set('date', 1),
      endDate: dayjs()
        .set('month', (season - 1) * 3)
        .set('date', 0),
    },
    {
      type: 'nextSeason',
      title: getLabel('206828', '下一季'),
      startDate: dayjs()
        .set('month', (season - 1 + 1) * 3)
        .set('date', 1),
      endDate: dayjs()
        .set('month', (season + 1) * 3)
        .set('date', 0),
    },
    {
      type: 'thisYear',
      title: getLabel('222265', '今年'),
      startDate: dayjs().set('month', 0).set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() + 1)
        .set('month', 0)
        .set('date', 0),
    },
    {
      type: 'firstHalfYear',
      title: getLabel('189886', '上半年'),
      startDate: dayjs().set('year', dayjs().year()).set('month', 0).set('date', 1),
      endDate: dayjs().set('year', dayjs().year()).set('month', 6).set('date', 0),
    },
    {
      type: 'nextHalfYear',
      title: getLabel('189887', '下半年'),
      startDate: dayjs().set('year', dayjs().year()).set('month', 6).set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() + 1)
        .set('month', 0)
        .set('date', 0),
    },
    {
      type: 'lastYear',
      title: getLabel('189888', '去年'),
      startDate: dayjs()
        .set('year', dayjs().year() - 1)
        .set('month', 0)
        .set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() - 1 + 1)
        .set('month', 0)
        .set('date', 0),
    },
    {
      type: 'nextYear',
      title: getLabel('189889', '明年'),
      startDate: dayjs()
        .set('year', dayjs().year() + 1)
        .set('month', 0)
        .set('date', 1),
      endDate: dayjs()
        .set('year', dayjs().year() + 1 + 1)
        .set('month', 0)
        .set('date', 0),
    },
  ];
  primaryAry.map((item: MultiBtnDataType) => {
    item.startDate = (item.startDate as dayjs.Dayjs).hour(0).minute(0).second(0);
    item.endDate = (item.endDate as dayjs.Dayjs).hour(23).minute(59).second(59);
    return item;
  });
  return primaryAry.map((item) => ({
    ...item,
    id: item.type,
    content: item.title,
    color: CheckedColor,
  }));
}

// 创建日期快捷选项
export const careateTimeKeys:PrimaryKeyType[] = [
  'today',
  'yesterday',
  'thisWeek',
  'lastWeek',
  'thisMonth',
  'lastMonth',
  'thisSeason',
  'lastSeason',
  'thisYear',
  'firstHalfYear',
  'nextHalfYear',
  'lastYear',
];

export default {};
