/**
 * <AUTHOR>
 * @createTime 2022-05-18
 * 日期选择框:平铺下拉
 * 高级搜索和顶部搜索使用
 * pc、移动端公用
 */
import { ComponentType } from 'react';
import Loadable from '../../react-loadable';
import { DateSelectProps } from './types';

const DateSelect = Loadable({
  name: 'DateSelect',
  loader: () => import(
    /* webpackChunkName: "ebdfpage_DateSelect" */
    './DateSelect'
  ),
}) as ComponentType<DateSelectProps>;

export type { DateSelectType } from './types';

export default DateSelect;
