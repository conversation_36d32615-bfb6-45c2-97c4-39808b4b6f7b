import { DatePickerPrimaryKeyType } from "@weapp/ui";
import { DateType, PickerType } from "@weapp/ui/lib/components/date-picker/types";

export type DateOtherParamsType = {
  primaryKey?: DatePickerPrimaryKeyType[];//复写快捷日期展示逻辑
  timeFormat?:'HH:mm:ss'|'HH:mm';//日期时分秒类型
  [_: string]: any;
}

export interface DateSelectProps {
  isOptionTile: boolean; //是否平铺
  datas: any[]; //选项
  value: Array<string>; //[startValue,endValue,selectOpt]
  onChange: (value: Array<string>) => void;
  isMobile?: boolean;
  placeholder?: string;
  type?: PickerType;
  format?: string | 'timestamp' | 'Date';
  innerRadio?: boolean;//是否放到radio内部
  otherParams?: DateOtherParamsType;//日期其他配置
}

export interface MDateSelectProps {
  options: Array<{ id: string; content: string; [_: string]: any }>;
  value: Array<string>; // [startValue,endValue,selectOpt];
  onDateChange: (value: any) => void; // 日期onchange
  onSelect: (value: any) => void; // 下拉框onchange
  type?: PickerType;
  format?: string | 'timestamp' | 'Date';
  otherParams?: DateOtherParamsType;//日期其他配置
}

export type MultiBtnDataType = {
  type: string;
  title: string;
  startDate: DateType | (() => DateType);
  endDate: DateType | (() => DateType);
};

export type PrimaryKeyType =
  | 'today'
  | 'yesterday'
  | 'tomorrow'
  | 'thisWeek'
  | 'lastWeek'
  | 'nextWeek'
  | 'thisMonth'
  | 'lastMonth'
  | 'nextMonth'
  | 'thisSeason'
  | 'lastSeason'
  | 'nextSeason'
  | 'thisYear'
  | 'lastYear'
  | 'nextYear'
  | 'firstHalfYear'
  | 'nextHalfYear';

export type DateSelectType = DateSelectProps;
