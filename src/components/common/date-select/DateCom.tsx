import {
  DatePicker, DateTimePicker,
} from '@weapp/ui';
import React from 'react';
import './index.less';

/**
 * 根据表单配置format渲染DatePicker还是DateTimePicker
 */
export const DateCom = (props:any) => {
  const { type, isRange = true } = props;
  let { value } = props;

  const onChange = (val: any) => {
    let newValue: string[] = [];
    /**
     * 兼容单个日期和范围
     */
    if (!isRange) {
      newValue = [val, val];
    } else {
      const [startDate = '', endDate = ''] = val;
      newValue = [startDate, endDate];
    }
    props.onChange(newValue);
  };

  /** 兼容单个日期回显值 */
  if (!isRange) {
    value = props.value?.[0] || '';
  }
  if (!type) {
    return (
      <DateTimePicker
        weId={`${props.weId || ''}_1owe9d`}
        isRange
        {...props}
        value={value}
        onChange={onChange}
      />
    );
  }
  return (
    <DatePicker
      weId={`${props.weId || ''}_h9ttol`}
      type={type}
      isRange
      {...props}
      value={value}
      onChange={onChange}
    />
  );
};

export default DateCom;
