import {
  DatePicker, DatePickerDateType, DatePickerProps, Radio, RadioValueType,
} from '@weapp/ui';
import { dayjs } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../constants';
import { DateCom } from './DateCom';
import './index.less';
import { DateOtherParamsType, PrimaryKeyType } from './types';
import { getDatePickPrimaryAry } from './utils';

interface DatePickerMainProps extends DatePickerProps, Attributes {
  otherParams: {
    quickOptions: string[];
    isOptionTile: boolean;
  } & DateOtherParamsType;
  innerRadio?:boolean;// 是否放到radio内部
}

// 老版本日期选项
@observer
export default class DatePickerMain extends PureComponent<DatePickerMainProps> {
  getOptions = () => {
    const {
      otherParams: { quickOptions = [] },
    } = this.props;
    const options = getDatePickPrimaryAry(1).filter((d) => quickOptions.indexOf(d.id) > -1);
    return options.map((item, ind) => ({ ...item, isLast: ind === options.length - 1 }));
  };

  getPrimarykey = () => {
    const {
      otherParams: { quickOptions = [] },
    } = this.props;
    const primaryKeyArr: PrimaryKeyType[] = [];
    getDatePickPrimaryAry(1).forEach((item) => {
      if (quickOptions.indexOf(item.type) < 0) {
        primaryKeyArr.push(item.type as PrimaryKeyType);
      }
    });
    return primaryKeyArr as PrimaryKeyType[];
  };

  transDefaultDate = (
    startDate: DatePickerDateType,
    endDate: DatePickerDateType,
    format: string,
  ) => [dayjs?.(startDate)?.format(format), dayjs?.(endDate)?.format(format)];

  onRadioChange = (type: RadioValueType) => {
    const { onChange } = this.props;
    const dateVal = DatePicker.getRangeInfoByPrimaryKey(type as PrimaryKeyType, 1);
    let dataFormatValue = ['', ''];
    if (dateVal !== -1) {
      // 除了区间组件的toady等数据，其余均为-1
      dataFormatValue = this.transDefaultDate(
        dateVal.startDate as DatePickerDateType,
        dateVal.endDate as DatePickerDateType,
        'YYYY-MM-DD',
      );
    }
    onChange?.(dataFormatValue);
  };

  customOptionRender = (_: any, ele: any) => {
    const {
      value,
      onChange,
      otherParams: { isOptionTile, quickOptions, ...restParams },
      type,
      innerRadio = false,
    } = this.props;
    const isTile = isOptionTile && quickOptions?.length > 0;

    // innerRadio+自定义日期 复写最后一项 日期组件也跟随flex布局
    if (innerRadio && _.option.isLast) {
      return (
        <div>
          <span>{ele}</span>
          <DateCom
            weId={`${this.props.weId || ''}_8rof3v`}
            type={type}
            value={value || ['', '']}
            className={isTile ? `${ebdBClsPrefix}-dateSelect-datePicker` : ''}
            onChange={onChange}
            primaryKey={this.getPrimarykey()}
            {...restParams}
          />
        </div>
      );
    }

    return <span>{ele}</span>;
  }

  render(): React.ReactNode {
    const {
      value,
      onChange,
      otherParams: { isOptionTile, quickOptions, ...restParams },
      type,
      innerRadio = false,
    } = this.props;
    const isTile = isOptionTile && quickOptions?.length > 0;

    return (
      <div className={`${ebdBClsPrefix}-dateSelect`}>
        <When weId={`${this.props.weId || ''}_5fcnsx`} condition={isTile}>
          <Radio
            weId={`${this.props.weId || ''}_gr86w2`}
            data={this.getOptions()}
            optionType="tag"
            hideCheck
            onChange={this.onRadioChange}
            customOptionRender={this.customOptionRender}
          />
        </When>
        <When weId={`${this.props.weId || ''}_zy9rlm`} condition={!innerRadio}>
          <DateCom
            weId={`${this.props.weId || ''}_ieqm9v`}
            type={type}
            value={value || ['', '']}
            className={isTile ? `${ebdBClsPrefix}-dateSelect-datePicker` : ''}
            onChange={onChange}
            primaryKey={this.getPrimarykey()}
            {...restParams}
          />
        </When>
      </div>
    );
  }
}
