/**
 * <AUTHOR>
 * @createTime 2021-12-17
 */
import { <PERSON><PERSON><PERSON>, BrowserValueType } from '@weapp/ui';
import { isArray } from '@weapp/utils';
import React from 'react';
import { getEbBrowserTargetFormId } from '../../../utils';
import { BrowserBeans, BrowserNeedExtraParams } from './constants';
import { FieldItemProps } from './types';
import { getEbBrowserOtherParams, isTrue } from './utils';

interface EbBrowserProps extends React.Attributes {
  field: FieldItemProps;
  onChange?: (value: BrowserValueType) => void;
  onBrowserChange?: (ids: string, names: string) => void;
  value?: BrowserValueType;
}

export class EbBrowser extends React.PureComponent<EbBrowserProps> {
  render() {
    const { field, value = [], onChange, onBrowserChange } = this.props;

    let allParams: any = {};
    const { module, browserType } = BrowserBeans[field.componentKey];
    if (BrowserNeedExtraParams.includes(field.componentKey)) {
      allParams = getEbBrowserOtherParams(field);
    }
    // 独立部署相关
    if (module === 'ebuilder/form') {
      allParams.requestHeaderParams = { ebBusinessId: getEbBrowserTargetFormId(field) };
    }

    const handleChange = (data: any) => {
      onChange && onChange(data);
      if (onBrowserChange) {
        if (!isArray(data)) {
          data = data ? [data] : [];
        }
        const [ids, names] = data.reduce(
          (p: any[], c: any) => {
            p[0].push(c.id);
            p[1].push(c?.name || c?.content);
            return p;
          },
          [[], []]
        );
        onBrowserChange?.(ids.join(','), names.join(','));
      }
    };

    return (
      <Browser
        weId={`${this.props.weId || ''}_akt4z9`}
        key={`${field.fieldId}`}
        // multiple={isTrue(field?.relateMultiple)}
        type={browserType}
        module={module}
        {...allParams}
        browserDialogProps={{
          title: field.placeholder || field.title,
        }}
        browserAssociativeProps={{
          placeholder: field.title,
          enableAddData: false,
          enableExtendButton: false,
        }}
        placeholder={field.title}
        onChange={handleChange}
        defaultValue={value}
        browserTableProps={{
          scrollX: 200, // 后端暂时无法获取具体的宽度,先写死200用于开启滚动属性
        }}
      />
    );
  }
}

export default EbBrowser;
