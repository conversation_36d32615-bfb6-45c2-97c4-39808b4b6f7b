/**
 * <AUTHOR>
 * @createTime 2021 -12 -17
 */

import { EtComponentKey } from '../../../constants/EtComponent';

// 关联浏览框类型
export const BrowserBeans: any = {
  [EtComponentKey.Employee]: {
    module: 'hrm',
    browserType: 'resource',
  },
  [EtComponentKey.Department]: {
    module: 'hrm',
    browserType: 'department',
  },
  [EtComponentKey.Ebuilder]: {
    module: 'ebuilder/form',
    browserType: 'ebuilder',
  },
  [EtComponentKey.FormComponent]: {
    module: 'formdatareport',
    browserType: 'frpt_component',
  },
  [EtComponentKey.Mainline]: {
    module: 'project',
    browserType: 'mainlineBrowser',
  },
  [EtComponentKey.Task]: {
    module: 'task',
    browserType: 'taskBrowser',
  },
  [EtComponentKey.Document]: {
    module: 'doc',
    browserType: 'document',
  },
  [EtComponentKey.Workflow]: {
    module: 'workflow/core',
    browserType: 'wfcRequest',
  },
  [EtComponentKey.AgendaComponent]: {
    module: 'calendar',
    browserType: 'calendarBrowser',
  },
  [EtComponentKey.CustomerComponent]: {
    module: 'crm',
    browserType: 'customer',
  },
  [EtComponentKey.ClueComponent]: {
    module: 'crm',
    browserType: 'clue',
  },
  [EtComponentKey.OrderComponent]: {
    module: 'crm',
    browserType: 'orderform',
  },
  [EtComponentKey.ContactComponent]: {
    module: 'crm',
    browserType: 'contact',
  },
  [EtComponentKey.ChanceComponent]: {
    module: 'crm',
    browserType: 'saleChance',
  },
  [EtComponentKey.ProductionComponent]: {
    module: 'crm',
    browserType: 'production',
  },
  [EtComponentKey.ContractComponent]: {
    module: 'crm',
    browserType: 'contract',
  },
  [EtComponentKey.CompetitorComponent]: {
    module: 'crm',
    browserType: 'competitor',
  },
  [EtComponentKey.KpiFlowComponent]: {
    module: 'performance',
    browserType: 'performanceBrowser',
  },
  [EtComponentKey.QuoteComponent]: {
    module: 'crm',
    browserType: 'quote',
  },
  [EtComponentKey.AttendComponent]: {
    module: 'attend/web',
    browserType: 'attendPermissionEmpsBrowser',
  },
  [EtComponentKey.DataSource]: {
    module: 'attend/web',
    browserType: 'vacationTypeBrowser',
  },
  [EtComponentKey.WorkreportComponent]: {
    module: 'plan',
    browserType: 'planBrowser',
  },
  [EtComponentKey.ActivityComponent]: {
    module: 'crm',
    browserType: 'marketactivity',
  },
  [EtComponentKey.Subcompany]: {
    module: 'hrm/common',
    browserType: 'subcompany',
  },
};

// 需要额外传参的浏览框
export const BrowserNeedExtraParams = [
  EtComponentKey.Ebuilder,
  EtComponentKey.FormComponent,
  EtComponentKey.Workflow,
  EtComponentKey.Task,
];
