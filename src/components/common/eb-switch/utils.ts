import { toJS } from 'mobx';
import { isArray, isEmpty } from '@weapp/utils';
import { FieldItemProps } from './types';

/**
 * <AUTHOR>
 * @createTime 2021 -12 -17
 */

export const isTrue = (val: string | boolean | undefined | null) => `${val}` === 'true';

/**
 * 获取关联eb浏览按钮的额外参数
 * @param field
 * @returns
 */
export const getEbBrowserOtherParams = (field: FieldItemProps) => {
  const formParam = {
    formId: field.formId,
    layoutId: field?.layoutId || '',
    fieldId: field.fieldId,
    module: 'ebuilderform',
    filterItems: [],
    dataDetails: [],
  };
  const params = { formParam: JSON.stringify(formParam), fromSearch: '1' };
  return {
    dataParams: params,
    destDataParams: params,
    completeParams: params,
    leftDataParams: params,
    propParams: params,
    historyParams: params,
    addDataParams: params,
    searchParams: params,
    commonParams: params,
    cardDetailDialogParams: { objId: field.objId },
  };
};

/**
 * widgetValue 表单：单组件赋值【来自表单的setFieldValue方法的注释】
 * options包含:
 * 1、fieldId: 组件Id。必传。
 * 2、data: 组件数据。必传。格式:
 *      a. 文本组件{content: '123'};
 *      b. 选项组件{dataOptions: [{optionId: 123,content: '123'}]};c
 *      c. 多行文本组件{dataText: {content: '123'}}
 *      d. 日期区间，[{content: '', formField: {id: start.id}},{content: '', formField: {id: end.id}}]
 *      e. 地理位置 {content: '',formField: {id:''},latitude: '',longitude: '',}
 * 3、subFormId: 明细表Id。非必传
 * 4、dataIndex: 行号。非必传
 */
export const getFormSwitchValue = (valObj: any, keepResource?: boolean): any => {
  if (isArray(valObj)) {
    // 区间
    return valObj;
  }
  if (typeof valObj === 'object') {
    if (valObj.dataText) {
      // 多行文本
      if (keepResource) {
        return valObj.dataText;
      }
      return valObj.dataText?.content || valObj.dataText?.richContent;
    }
    if (valObj.dataOptions && isArray(toJS(valObj.dataOptions))) {
      if (valObj.dataOptions.length === 0 && valObj.content) {
        return valObj.content;
      }
      // 选择框相关
      return valObj.dataOptions.map((opt: any) => ({
        ...opt,
        id: opt.optionId,
        content: opt.content,
        type: opt.type,
      }));
    }
    if ('content' in valObj) {
      if (valObj.latitude && valObj.longitude) {
        // 地理位置
        return {
          address: valObj.content,
          location: `${valObj.latitude},${valObj.longitude}`,
        };
      }
      return valObj.content;
    }
  }
  return valObj;
};

export const hasFormSwitchValue = (valObj: any): any => {
  if (isArray(valObj)) {
    // 区间
    return valObj.length > 0;
  }
  if (typeof valObj === 'object') {
    if (valObj.dataText) {
      return !!valObj.dataText?.content || !!valObj.dataText?.richContent;
    }
    if (valObj.dataOptions && isArray(toJS(valObj.dataOptions))) {
      if (valObj.dataOptions.length === 0 && valObj.content) {
        return !!valObj.content;
      }
      // 选择框相关
      return valObj.dataOptions.length > 0;
    }
    if ('content' in valObj) {
      if (valObj.latitude && valObj.longitude) {
        return true; // 地理位置且有值，true
      }
      return !!valObj.content;
    }
    return !isEmpty(valObj);
  }
  return !isEmpty(valObj);
};

export const getFormValueString = (valObj: any): string => {
  if (!valObj) return '';
  if (isArray(valObj)) {
    // 区间
    return valObj.join(',');
  }
  if (typeof valObj === 'object') {
    if (valObj.dataText) {
      // 多行文本
      return valObj.dataText?.content || valObj.dataText?.richContent || valObj.content;
    }
    if (valObj.dataOptions && isArray(toJS(valObj.dataOptions))) {
      if (valObj.dataOptions.length === 0 && valObj.content) {
        return valObj.content;
      }
      // 选择框相关
      return valObj.dataOptions.map((opt: any) => opt.content).join(',');
    }
    if ('content' in valObj) {
      return valObj.content;
    }
  }
  return '';
};

export default {
  isTrue,
  hasFormSwitchValue,
  getEbBrowserOtherParams,
};
