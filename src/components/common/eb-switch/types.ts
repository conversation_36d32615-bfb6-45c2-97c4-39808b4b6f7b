import { Attributes } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { EtComponentKey } from '../../../constants/EtComponent';

export interface FormSwitchData {
  formField: {
    id: string;
  };
  content?: any;
  dataOptions?: any;
  error?: {
    content: string;
    type: string;
  };
}

export interface FieldItemProps {
  fieldId: string;
  componentKey: EtComponentKey;
  title: string;
  options?: any[];
  value?: any;
  /** * 其他属性 */
  fieldShowName?: string;
  fieldName?: string;
  content?: string;
  customcheck?: string;
  isReadOnly?: string;
  readOnly?: string; // 新表单生效
  describe?: string;
  express?: string;
  formId?: string;
  index?: string;
  isAddForRelation?: string;
  isDefault?: string;
  isHideTitle?: string;
  isMust?: string;
  language?: string;
  maxlen?: string;
  order?: string;
  placeholder?: string;
  position?: string;
  regular?: string;
  relateMultiple?: string;
  required?: string;
  scan?: string;
  size?: string;
  tempId?: string;
  titleLayout?: string;
  unique?: string;
  isCurrentEmployee?: boolean | string;
  /** 数值 */
  maxNum?: string;
  minNum?: string;
  numberType?: 'percentage';
  /** 日期 */
  format?: string;
  /** 时间 */
  hourStep?: string;
  minuteStep?: string;
  secondStep?: string;
  maxHours?: string;
  minTime?: string;
  showSeconds?: string;
  /** 关联EB浏览框 */
  layoutId?: string;
  objId?: string;
  /** 上传 */
  isSingle?: string;
  /** 评分 */
  half?: string;
  ratypic?: string;
  maxstar?: string;
  defaultStar?: string;
  /** 地图 */
  defaultAddress?: string;
  defaultLat?: string;
  defaultLng?: string;
  /** */
  formStore?: any;
  uid?: string;
  /** 选择框 */
  OptionBackgroundStyle?: any;
  optionBackgroundStyle?: any;
  isUnique?: boolean;

  /** 新表单设计器组件存在的属性 */
  dataKey?: string;
  type?: string;
  groupId?: string;

  sformId?: string; // 关联eb目标id
  requestHeaderParams?: any; // 浏览按钮支持支持独立部署添加header
}

export interface FieldSwitchProps extends Attributes, RouteComponentProps {
  value: any;
  fieldId: string;
  componentKey: EtComponentKey;
  onChange?: (value: any) => void;
  onBlur?: (value: any) => void;
  onChangeComplete?: (value: any) => void;
  data: FieldItemProps;
  isMobile?: boolean;
  useDefaultProp?: boolean;

  objId: string;
}
