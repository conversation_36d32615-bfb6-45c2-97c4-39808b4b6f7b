/**
 * <AUTHOR>
 * @createTime 2021-12-27
 */
import { CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { EbuilderModule } from '../../../constants';
import { FieldSwitchProps, FormSwitchData } from './types';
import { getAppDocDiridRpc } from '../../../api/common';

/**
 * widgetValue 表单：单组件赋值【来自表单的setFieldValue方法的注释】
 * @param options 参数
 * options包含:
 * 1、fieldId: 组件Id。必传。
 * 2、data: 组件数据。必传。格式:
 *      a. 文本组件{content: '123'};
 *      b. 选项组件{dataOptions: [{optionId: 123,content: '123'}]};c
 *      c. 多行文本组件{dataText: {content: '123'}}
 *      d. 日期区间，[{content: '', formField: {id: start.id}},{content: '', formField: {id: end.id}}]
 *      e. 地理位置 {content: '',formField: {id:''},latitude: '',longitude: '',}
 * 3、subFormId: 明细表Id。非必传
 * 4、dataIndex: 行号。非必传
 */

@observer
class EbFormSwitch extends PureComponent<FieldSwitchProps, any> {
  constructor(props: FieldSwitchProps) {
    super(props);
    this.state = {
      docDirid: '',
    };
  }

  componentDidMount() {
    this.getAppDocDirid();
  }

  componentDidUpdate(prevProps: Readonly<FieldSwitchProps>) {
    if (prevProps?.objId !== this.props.objId) {
      this.getAppDocDirid();
    }
  }

  getAppDocDirid = () => {
    const { objId = '' } = this.props;
    if (!objId) {
      return;
    }
    getAppDocDiridRpc(objId, 'obj').then((dirid) => {
      if (dirid) {
        this.setState({ docDirid: dirid });
      }
    });
  };

  handleCompleteChange = (formSwitchData: FormSwitchData) => {
    this.props.onChangeComplete?.(formSwitchData);
  };

  handleChange = (formSwitchData: FormSwitchData) => {
    this.props.onChange?.(formSwitchData);
  };

  handleNewChange = (formSwitchData: FormSwitchData) => {
    this.props.onChange?.(formSwitchData);
    this.props.onChangeComplete?.(formSwitchData);
  };

  getWeightProps = () => {
    const {
      data, value, isMobile = false, useDefaultProp, ...restProps
    } = this.props;
    /**
     * 为了适配新表单设计器你将处理一下属性，包括但不限于：
     * */
    let widgetProps: any = {
      formLayout: {},
      ...restProps,
      widgetValue: value, // 回写值，
      isMobile,
      widgetRequired: `${data?.required}` === 'true', // 必填
      widgetReadOnly: `${data?.isReadOnly}` === 'true' || `${data?.readOnly}` === 'true', // 只读
      isUnique: data?.isUnique ? data.isUnique : data.isSingle, // 是否单选
      settingParam: {
        uploadParam: {
          folderId: this.state.docDirid,
        },
      },
    };

    let comProps: any = data;
    if ('options' in data && data.options) {
      // 兼容新表单
      comProps = {
        ...data,
        OptionBackgroundStyle: data.OptionBackgroundStyle || data.optionBackgroundStyle,
        options: data.options.map((opt: any) => ({
          ...opt,
          componentKey: 'Option',
          fieldId: data.fieldId,
          selectionId: opt?.selectionId || opt.uid,
        })),
      };
    }
    if (!useDefaultProp) {
      widgetProps = {
        ...comProps,
        ...widgetProps,
        isHideTitle: true,
        required: 'false',
        isReadOnly: 'false',
        unique: 'false',
        widgetRequired: false,
        widgetReadOnly: false,
      };
    } else {
      widgetProps = {
        isHideTitle: true,
        required: 'false',
        isReadOnly: 'false',
        unique: `${comProps?.unique}` === 'true' ? 'true' : 'false',
        ...comProps,
        ...widgetProps,
      };
    }

    if (comProps.componentKey === 'PositionComponent') {
      // 不使用自动定位
      widgetProps = { ...widgetProps, isAutoPosition: 'false' };
    }
    return widgetProps;
  };

  customRenderOld = (module: any) => {
    const { FormSwitch, WidgetStore, utils } = module;
    const widgetProps: any = {
      ...this.getWeightProps(),
      onChangeComplete: this.handleCompleteChange,
      onChange: this.handleChange,
      onBlur: this.props?.onBlur,
    };
    let formItem = null;
    if (WidgetStore && utils) {
      widgetProps.module = EbuilderModule;
      const widgetKey = utils.parseWidgetKey(widgetProps.componentKey);
      if (widgetKey) {
        const widgetStore = new WidgetStore[widgetKey]();
        formItem = (
          <FormSwitch
            weId={`${this.props.weId || ''}_if4dun`}
            {...widgetProps}
            store={widgetStore}
          />
        );
      }
    }
    return formItem;
  };

  customRender = (module: any) => {
    const { value } = this.props;
    const { FormSwitch } = module;
    let widgetProps = {
      ...this.getWeightProps(),
      config: this.getWeightProps(),
      value,
      onChangeComplete: this.handleCompleteChange,
      onChange: this.handleNewChange,
      onBlur: this.props?.onBlur,
      module: EbuilderModule,
    };
    if (this.getWeightProps().secretLevel) {
      widgetProps = {
        ...widgetProps,
        ...{
          secretProtect: this.getWeightProps()?.secretProtect,
          secretLevel: this.getWeightProps()?.secretLevel,
          secretLevelValidity: this.getWeightProps()?.secretLevelValidity,
        },
      };
    }
    return <FormSwitch weId={`${this.props.weId || ''}_f3vawf`} {...widgetProps} />;
  };

  render() {
    const { data } = this.props;
    const { dataKey } = data;
    if (dataKey) {
      // 新的表单设计器的单组件渲染
      return (
        <CorsComponent
          weId={`${this.props.weId || ''}_x5fehf`}
          app="@weapp/formbuilder"
          module={EbuilderModule}
          compName="FormSwitch"
          customRender={this.customRender}
        />
      );
    }
    // 老表单设计器单组件
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_p3y3hn`}
        app="@weapp/form"
        module={EbuilderModule}
        compName="FormSwitch"
        customRender={this.customRenderOld}
      />
    );
  }
}

export default withRouter(EbFormSwitch);
