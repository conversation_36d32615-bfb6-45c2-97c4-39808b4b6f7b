/**
 * @desc    : 看板通用无数据
 */
import React, { Attributes, PureComponent } from 'react';
import { Empty, Icon, AnyObj } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

interface IProps extends Attributes {
  extraProps?: AnyObj;
  iconSize?: AnyObj;
  onlyText?: Boolean; // 文字形式
}
export class BoardEmpty extends PureComponent<IProps> {
  render() {
    const { extraProps, iconSize = {}, onlyText } = this.props;
    if (onlyText) {
      return <div className={`${ebdBClsPrefix}-board-emptyText`}>
        <div>{getLabel('54023', '暂无数据')}</div>
      </div>;
    }
    return (
      <div className={`${ebdBClsPrefix}-board-empty`}>
        <Empty
          weId={`${this.props.weId || ''}_td2kho`}
          className={`${ebdBClsPrefix}-list-empty-icon`}
          description={getLabel('54023', '暂无数据')}
          image={
            <Icon weId={`${this.props.weId || ''}_o7r2yf`} style={{ width: 100, height: 100, ...iconSize }} name="Icon-NDboard-mcolor" />
          }
          {...extraProps}
        />
      </div>
    );
  }
}

export default BoardEmpty;
