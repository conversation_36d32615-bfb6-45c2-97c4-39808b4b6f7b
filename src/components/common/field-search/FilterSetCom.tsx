/**
 * 筛选器-content
 */
import { DataSet } from '@weapp/ebdcoms';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import FieldSearch from '.';
/**
 * 视图类型
 * */
export enum ModuleType {
  Form = 'formView', // 表单视图
  Table = 'tableView', // 表格
  Gantt = 'ganttView', // 甘特图
  Calendar = 'calendarView', // 日历
  Board = 'kanbanView', // 看板
  MindMap = 'mindMapView', // 思维导图
  Grid = 'gridView', // 网格视图
  Occupy = 'occupyView', // 网格视图
}
export interface FilterSetProps extends React.Attributes {
  dataset: DataSet;
  onSave: (key: string, value: any) => void;
  checked?: boolean;
  value: any;
  customBtnEle?: (fn: Function) => void;
  buttonType?: any;
  module?: ModuleType;
  ebBusinessId: string;
  showMenu?: string[];
}

@observer
class FilterSetCom extends PureComponent<FilterSetProps> {
  onOk = (data: any) => {
    this.props.onSave?.('filter', data);
  };

  render(): React.ReactNode {
    const {
      customBtnEle,
      dataset,
      checked = false,
      value = {
        commonFilters: [],
        quickFilters: [],
        groupFilters: [],
        commonSearchType: '0',
        searchRelationEnable: '1',
      },
      buttonType,
      module = ModuleType.Board,
      ebBusinessId,
      showMenu
    } = this.props;
    return (
      <FieldSearch
        weId={`${this.props.weId || ''}_ao6gzf`}
        module={module}
        dataset={dataset as DataSet}
        dataProps={{
          commonFilters: value?.commonFilters || [],
          quickFilters: value?.quickFilters || [],
          // groupFilters: value?.groupFilters || [],
          commonSearchType: value.commonSearchType || '0',
          searchRelationEnable: value.searchRelationEnable || '1',
          commonFilterGroups: value?.commonFilterGroups || [],
          hideCommonSearchType: [],
          // showMenu: ['commonFilters', 'quickFilters', 'groupFilters'],
          // 暂时屏蔽分类搜索
          showMenu: showMenu || ['commonFilters', 'quickFilters'],
        }}
        otherProps={{ checked, buttonType }}
        onChange={this.onOk}
        externalElement={customBtnEle}
        ebBusinessId={ebBusinessId}
      />
    );
  }
}

export default observer(FilterSetCom);
