import { AnyObj, CorsComponent } from '@weapp/ui';
import React, { PureComponent } from 'react';

interface DataSetFieldSearchProps {
  onChange: (val: any) => void;
  config?: any;
  value?: any;
  pageId?: string;
  dataProps?:AnyObj;
}

export enum ShowType {
  Advance = '0',
  Filter = '1',
  ConditionGroup = '2'
}

export default class DataSetFieldSearch extends PureComponent<DataSetFieldSearchProps> {
  render() {
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_3766hu`}
        app="@weapp/ebdcoms"
        compName="SearchFields"
        {...this.props}
      />
    );
  }
}
