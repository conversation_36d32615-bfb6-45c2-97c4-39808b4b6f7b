import { AnyObj, CorsComponent } from '@weapp/ui';
import React from 'react';
import { toJS } from 'mobx';
import { ModuleType } from '../../../constants';
import { EtComponentKey } from '../../../constants/EtComponent';
import { UUID } from '../../../utils';
import { DataSet } from '../../../types/common';

export interface FieldSearchProps extends React.Attributes {
  module: ModuleType;
  dataset: DataSet;
  dataProps: any;
  otherProps?: any;
  className?: string;
  externalElement?: (fn: Function) => void;
  onChange?: (filter: any) => void;
  ebBusinessId: string;
}
// 屏蔽字段
export const NotUseFields = [
  EtComponentKey.ComboSelect,
  EtComponentKey.MatrixComponent,
  EtComponentKey.FileComponent,
  EtComponentKey.ImageComponent,
  EtComponentKey.TreeSelect,
  EtComponentKey.radioboximg,
  EtComponentKey.checkboximg,
  EtComponentKey.ImageCheckBox,
  EtComponentKey.ImageRadioBox,
  EtComponentKey.HandwritingSignature,
];
export default class FieldSearch extends React.PureComponent<FieldSearchProps> {
  onConfirm = (data: any, type: string, callBack?: Function, otherConfig?: AnyObj) => {
    const { commonFilters, quickFilters, groupFilters, commonFilterGroups } = data;
    const { needCondition, noneOfShow, searchRelationEnable } = otherConfig || {};
    const newData = {
      // 简化常用搜素
      commonFilters: toJS(commonFilters).map(({ listFilterDefaultSets: st, ...el }: any) => {
        const isGroupCustom = el.componentKey === EtComponentKey.GroupCustom;
        /**
         * 本身id和name就行了，但是一些接口没改造，不传fieldId和fieldName会导致运行时获取数据不对
         * 因此这里两个都传一下
         *  id、fieldId：作为搜素key，字段选择组件将id转换成了fieldId；小分类用前端生成的id
         *  name、fieldName: 数仓的 "data_status"这种别称
         */
        let id = el.fieldId;
        let _fieldId = id;
        if (isGroupCustom) {
          // id = el.id.indexOf(EtComponentKey.GroupCustom) < 0 ? `${EtComponentKey.GroupCustom}_${el.id}` : el.id;
          id = el.id;
          _fieldId = '-1'//自定义类型须为-1 建模后端武宇敬
        }
        return {
          id,
          fieldId: _fieldId,
          name: el.name,
          filterId: UUID(),
          fieldName: isGroupCustom ? id : el.name || el.fieldShowName || el.showName,
          componentKey: el.componentKey,
          // 多条件
          conditionType: el.conditionType,
          conditionTypeValue: el.conditionTypeValue,
          // 字段配置:eg format 时分秒等
          config: el.config,
          fieldShowName: el.fieldShowName || el.text || el.showName,
          fieldType: el.fieldType,
          // 显示位置
          isQuick: el.isQuick,
          objId: el.objId,
          showName: el.showName,
          text: el.text,
          type: el.type,
          // 是否选项平铺
          isOptionTile: el.isOptionTile,
          // 下拉框等选项
          options: el.options,
          listFilterDefaultSets: {
            // 是否开启选项
            optionEnable: st.optionEnable,
            // 日期和金额开启选项后配置
            listGroupSets: st.listGroupSets || [],
            listFilterSets: st.listFilterSets || [],
            // 是否多选
            multiSelect: st.multiSelect,
            // 默认值类型
            defaultType: st.defaultType,
            // 默认值
            startValue: st.startValue || '',
            startValueSpan: st.startValueSpan || '',
            // 行政字段回显值
            startDefaultLabel: st.startDefaultLabel || '',
            endValue: st.endValue || '',
            // 是否开启按月拆分
            isDateMenu: st.isDateMenu,
            dateMenuGroup: st.dateMenuGroup,
            isCurrentMax: st.isCurrentMax,
          },
          // 平铺分组
          groupJson: el.groupJson,
          // 重命名显示名称
          configNameJson: el.configNameJson,
        };
      }),
      // 简化快捷
      quickFilters: quickFilters.map(({ id, fieldId, showName, needQuickSearch, fieldName }: any) => ({
        id,
        fieldId,
        showName,
        needQuickSearch,
        fieldName: fieldName || showName,
      })),
      // 简化大分类
      groupFilters: groupFilters.map(({ listFilterDefaultSets: st, ...g }: any) => {
        const isGroupCustom = g.componentKey === EtComponentKey.GroupCustom;
        /**
         * 本身id和name就行了，但是一些接口没改造，不传fieldId和fieldName会导致运行时获取数据不对
         * 因此这里两个都传一下
         *  id、fieldId：作为搜素key，数仓将id转换成了fieldId；小分类用前端生成的id
         *  name、fieldName: 数仓的 "data_status"这种别称
         */
        let id = g.fieldId;
        if (isGroupCustom) {
          // id = g.id.indexOf(EtComponentKey.GroupCustom) < 0 ? `${EtComponentKey.GroupCustom}_${g.id}` : g.id;
          id = g.id;
        }
        return {
          id,
          fieldId: id,
          name: g.name || g.fieldShowName || g.showName,
          fieldName: g.name || g.fieldShowName || g.showName,
          compType: g.compType,
          componentKey: g.componentKey,
          showName: g.showName,
          fieldShowName: g.fieldShowName,
          text: g.text,
          // 显示位置
          showPosition: g.showPosition,
          // 显示方式
          tabStyle: g.tabStyle,
          objId: g.objId,
          fieldType: g.fieldType,
          type: g.type,
          listFilterDefaultSets: {
            // 配置选项
            listGroupSets: st.listGroupSets,
            // 是否多选
            multiSelect: st.multiSelect || false, //默认返回可能为空，增加默认值
          },
        };
      }),
      commonFilterGroups,
      commonSearchType: type,
      needCondition,
      noneOfShow,
      searchRelationEnable,
    };
    this.props.onChange?.(newData);
  };
  fieldFilter = (field: any) => {
    // 屏蔽不支持的字段
    if (NotUseFields.includes(field.componentKey)) {
      return false;
    }
    return true;
  };

  render() {
    const { dataset, dataProps = {}, otherProps = {}, externalElement, module, ebBusinessId } = this.props;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_9lxdvj`}
        app="@weapp/ebdform"
        compName="FieldSearch"
        module={module} // 屏蔽明细表用，内部判断
        dataset={dataset}
        configType="ebDesigner"
        type="ebDesigner"
        // configType="formView" // 配置类型，使用的场景，默认式表单
        onConfirm={this.onConfirm} // 确定回调，两个参数
        enableSqlEncry
        data={{
          showMenu: ['commonFilters', 'quickFilters', 'groupFilters'], // 左侧展示的菜单类别，这里不需要分类搜索
          hideCommonSearchType: [], // 常用搜索隐藏的类型，这里隐藏高级搜索
          commonSearchType: '1', // 常用搜索的类型，这里默认选中筛选
          filterHidePosition: false, // 常用搜索类型，筛选隐藏显示位置的配置，默认仅有下拉
          needId: true,
          ...dataProps,
        }}
        ebBusinessId={ebBusinessId}
        // configParams={{ RIU: true, needDetail: false }} // 表单内部获取字段接口走components的Fields接口 并且屏蔽掉明细表相关字段
        customBtnRender={externalElement} // 外部定义点击按钮
        {...otherProps}
        needNewDateOpts
        needCommonFilterGroups
        needSearchRelationEnable
        needRename
        needDateMenu
        fieldFilter={this.fieldFilter}
      />
    );
  }
}
