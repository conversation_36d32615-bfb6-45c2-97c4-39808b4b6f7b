import React, { PureComponent } from 'react';
import { ViewProps } from '../types';
import BoardView from '../view/View';

// class BoardView2 extends PureComponent<ViewProps> {
//   render() {
//     const {
//       id, page, config, comServicePath, onRef,events, store, pluginCenter
//     } = this.props;
//     return (
//       <MBoardView
//         weId={`${this.props.weId || ''}_2hrrq5`}
//         onRef={onRef}
//         comServicePath={comServicePath}
//         compId={id as string}
//         pageId={page.id}
//         isDesign={false}
//         isMobile
//         config={config}
//         events={events!}
//         ebStore={store}
//         pluginCenter={pluginCenter}
//       />
//     );
//   }
// }

export default class MBoardView extends React.Component<ViewProps> {
  render() {
    return (
      <BoardView
        weId={`${this.props.weId || ''}_0b0vk0`}
        {...this.props}
        client='MOBILE'
      />
    );
  }
}