import { ClientType, CommonConfigData, ComConfigData, TransformHandler } from '@weapp/ebdcoms';
import { AnyObj } from '@weapp/ui';
import { DataSet } from '@weapp/ebdcoms';
import { BoardPluginCenter } from '../common/board/types';
import { PageSize, PageMode, MarkConfig } from '../baseBoard/config/cmps/types';
import { SourceType } from '../../constants/common';
import { PluginPackDataType } from '../plugin-packages/loader';
import { GroupDataProps } from './engine/func-groups/types';
import { LaneConfigProps } from './config/cmps/yd/types';

export interface DataSetItem extends DataSet {}
export interface ViewConfigData extends CommonConfigData {
  fromEbuilder?: boolean; // 是否为默认生成的数据
  showTitle?: boolean;
  name?: string;
}
export enum DataSetType {
  Current = 'current',
  Other = 'other',
}
export type DataSetTypeItem = (typeof DataSetType)[keyof typeof DataSetType];
export interface BoardConfigData extends ViewConfigData {
  dataset?: DataSetItem;
  datasetType?: DataSetTypeItem;
  condition?: any;
  filter?: any;
  layout?: any;
  group?: GroupDataProps;
  card?: any;
  orders?: any[];
  statistics?: any;
  pageSize?: PageSize;
  pageMode?: PageMode;
  markSetting?: MarkConfig;
  laneConfig?: LaneConfigProps
  diffGroupCustomId?: string; // 差异化分组自定义分组id
}

export interface DesignProps extends React.Attributes {
  config: BoardConfigData;
  page?: any;
  id?: string;
  pid?: string;
  readOnly?: boolean;
  pageId?: string;
  client?: ClientType;
  comServicePath?: string;
  onRef?: (ref: any) => void;
  events?: AnyObj;
  store?: AnyObj;
    /** 插件处理中心 */
  pluginCenter?: BoardPluginCenter;
  /** 组件插件包描述对象，内置属性，用于支持组件可插拔机制 */
  refPluginPackage?: PluginPackDataType;
  pluginCenterOpts?: any;
  /**
   * 列表配置中的属性变更的回调
   * @param key 字段的key
   * @param val 字段更改后的值
   */
  onPropChange?(key: string, val: any): void;
  /** 组件mount前的回调 */
  onBeforeMount?: (store: any) => void;
  useForm?: boolean;
}

export interface DesignComProps extends DesignProps {
  onChange: (changes: any, ...args: any[]) => void;
  onConfigChange?: (changes: any) => void;
  value: any;
  appid?: string;
  comId?: string;
  isIconBtn?: boolean;
}

export interface ViewProps extends DesignProps {
  isDesign: boolean;
  isMobile: boolean;
  isDoc?: boolean; // 是否为文档模式
  pluginCenter?: BoardPluginCenter; // 引入插件机制
}

export interface GroupStatisticsProps extends DesignProps {
  onChange: (changes: any, ...args: any[]) => void;
  onConfigChange?: (changes: any) => void;
  value: any;
  appid?: string;
  comId?: string;
  statisticsData?: groupStatisticsValue[];
  hideWithUnOpen?: boolean; //关闭后隐藏列表
}

export interface groupStatisticsValue {
  /** 分组统计: id */
  id?: string;
  inputValue?: any;
  eBFilterData?: any;
}

export interface statisticsItemProps {
  id?: string;
  pageId?: string;
  data?: groupStatisticsValue;
  allDatas?: groupStatisticsValue[];
  dataset?: DataSetItem;
  index: number;
  onChange: (changes: any) => void;
  onItemDelete: (index: number) => void;
  useForm?: boolean; // 走表单的多语言接口
}

export interface GroupStatisticsProps extends DesignProps {
  onChange: (changes: any, ...args: any[]) => void;
  onConfigChange?: (changes: any) => void;
  value: any;
  appid?: string;
  comId?: string;
  statisticsData?: groupStatisticsValue[];
}

export interface groupStatisticsValue {
  /** 分组统计: id */
  id?: string;
  inputValue?: any;
  eBFilterData?: any;
}

export interface statisticsItemProps {
  id?: string;
  pageId?: string;
  data?: groupStatisticsValue;
  allDatas?: groupStatisticsValue[];
  dataset?: DataSetItem;
  index: number;
  onChange: (changes: any) => void;
  onItemDelete: (index: number) => void;
}

export interface bottomBtnNameValue {
  inputValue?: any;
}

export interface bottomBtnNameSetProps {
  config?: any;
  pageId?: string;
  data?: bottomBtnNameValue;
  onChange?: any;
}
/**
 * 数据源值类型
 */
export type DataSourceValueType = {
  id: string;
  text: string;
  type: SourceType;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: any[];
  servicePath?: string;
  params?: string[];
  dataId?: string;
  ebuilderDataConfig?: any;
  data?: any[];
  /** 关联的组件id */
  refComId?: string;
  /** 数字面板，关联的数据项id */
  refPanelId?: string;
  /** 数据源分组信息 数据中心提供 */
  dataGroupConfig?: any;
  /** 数据源分组更改时的时间戳，解决fields缓存问题 */
  dataGroupMemoTime?: number;
  /** 数据源分组原始过滤 */
  dataGroupFilter?: any;
  isPhysical?: boolean;
  /** 数据源支持类型 */
  usage?: string;
  appId?: string;
  comConfig?: any; // 组件配置信息
  objType?: string /** 数据源类型：vform（虚拟表） */;
  detailTable?: boolean /** 是否是明细数据源 */;
};

export interface BaseConfigDataType extends ComConfigData {
  /** 插件包信息 */
  _plugins?: any[];
}
