// MLazyLoader.tsx
import React, { useEffect, useRef } from 'react';
import { observer } from 'mobx-react';
import '../../../../utils/polyfill/intersection-observer';
import { GroupItem } from '../../../common/board/types';

interface MLazyLoaderProps {
  onVisibilityChange: (item: any) => void;
  children: React.ReactNode;
  compId: string;
  id: string;
  loadedBoardIds: Set<string>;
  setBoardLoaded: (boardId: string) => void;
  loadingTimestamp: number;
  onViewportLoaded?: () => void;
  groups: GroupItem[]
}

const MLazyLoader: React.FC<MLazyLoaderProps> = observer(
  ({ onVisibilityChange, children, compId, loadingTimestamp, loadedBoardIds, setBoardLoaded, id, groups }) => {
    const ref = useRef<HTMLDivElement>(null);

    const getContainer = () => {
      const container = document.querySelector(`#${compId}_${id}`) as HTMLElement;
      return container;
    };

    const getActiveBulletIdx = () => {
      const container = getContainer();
      if (!container) return -1;
      let activeIndex = -1;
      container.querySelectorAll('.weapp-ebdboard-m-bullet').forEach((bullet, index) => {
        if (bullet.classList.contains('weapp-ebdboard-m-bullet-active')) {
          activeIndex = index;
        }
      });
      return activeIndex;
    };
    const handleVisibilityChange = () => {
      const bulletIdx = getActiveBulletIdx();
      if (bulletIdx === -1) return;
      const activeItem = groups[bulletIdx];
      if (activeItem && !loadedBoardIds.has(activeItem.id)) {
        setBoardLoaded(activeItem.id);
        // 获取元素位置信息
        if (ref.current) {
          onVisibilityChange(activeItem);
        }
      }
    };

    useEffect(() => {
      const currentRefValue = ref.current;
      const container = getContainer();
      if (!currentRefValue || !container) return;
      // 初始化检测
      handleVisibilityChange();

      const observer = new MutationObserver(mutationsList => {
        for (const mutation of mutationsList) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const target = mutation.target as any;
            if (target && target.classList.contains('weapp-ebdboard-m-bullet-active')) {
              // 在这里执行你需要的操作
              handleVisibilityChange();
            }
          }
        }
      });
      const config = {
        attributes: true, // 监听属性变化
        subtree: true, // 监听所有子节点的变化
        attributeFilter: ['class'], // 只监听 class 属性的变化
      };
      observer.observe(container, config);

      return () => {
        observer.disconnect();
      };
    }, [compId, loadingTimestamp]);

    return (
      <div ref={ref} style={{ height: '100%', width: '100%' }}>
        {children}
      </div>
    );
  }
);

export default MLazyLoader;
