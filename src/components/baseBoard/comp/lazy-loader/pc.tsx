// LazyLoader.tsx
import React, { useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react';
import '../../../../utils/polyfill/intersection-observer';

interface LazyLoaderProps {
  onVisibilityChange: (item: any) => void;
  children: React.ReactNode;
  item: any;
  compId: string;
  id: string;
  loadedBoardIds: Set<string>;
  setBoardLoaded: (boardId: string) => void;
  loadingTimestamp: number;
  onViewportLoaded?: () => void;
}
export const getElementPosition = (element: HTMLElement, container: HTMLElement) => {
  // 获取元素和容器的完整位置信息
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  // 计算元素相对于容器的位置
  const relativePosition = {
    left: elementRect.left - containerRect.left + container.scrollLeft,
    top: elementRect.top - containerRect.top + container.scrollTop,
    right: elementRect.right - containerRect.left + container.scrollLeft,
    bottom: elementRect.bottom - containerRect.top + container.scrollTop,
  };

  return {
    // 相对位置
    ...relativePosition,
    // 元素尺寸
    width: elementRect.width,
    height: elementRect.height,
    // 是否在视口内
    isInViewport: isElementInContainer(elementRect, containerRect, container),
  };
};

export const isElementInContainer = (elementRect: DOMRect, containerRect: DOMRect, container: HTMLElement, threshold: number = 0) => {
  // 获取容器的可视区域信息
  const containerVisibleWidth = container.clientWidth;
  const containerVisibleHeight = container.clientHeight;

  // 计算元素相对于容器可视区域的位置（考虑容器的边框和内边距）
  const containerStyle = window.getComputedStyle(container);
  const borderLeft = parseFloat(containerStyle.borderLeftWidth) || 0;
  const borderTop = parseFloat(containerStyle.borderTopWidth) || 0;
  const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
  const paddingTop = parseFloat(containerStyle.paddingTop) || 0;

  // 容器内容区域的实际边界
  const containerContentLeft = containerRect.left + borderLeft + paddingLeft;
  const containerContentTop = containerRect.top + borderTop + paddingTop;
  const containerContentRight = containerContentLeft + containerVisibleWidth - paddingLeft * 2;
  const containerContentBottom = containerContentTop + containerVisibleHeight - paddingTop * 2;

  // 计算元素与容器内容区域的重叠部分
  const overlapLeft = Math.max(elementRect.left, containerContentLeft);
  const overlapTop = Math.max(elementRect.top, containerContentTop);
  const overlapRight = Math.min(elementRect.right, containerContentRight);
  const overlapBottom = Math.min(elementRect.bottom, containerContentBottom);

  // 检查是否有重叠
  const hasOverlap = overlapLeft < overlapRight && overlapTop < overlapBottom;

  if (!hasOverlap) {
    return false;
  }

  // 如果设置了阈值，计算可见比例
  if (threshold > 0) {
    const overlapWidth = overlapRight - overlapLeft;
    const overlapHeight = overlapBottom - overlapTop;
    const overlapArea = overlapWidth * overlapHeight;
    const elementArea = elementRect.width * elementRect.height;
    const visibleRatio = elementArea > 0 ? overlapArea / elementArea : 0;

    return visibleRatio >= threshold;
  }

  return true;
};

// 调试工具函数
export const debugElementVisibility = (elementRect: DOMRect, containerRect: DOMRect, container: HTMLElement, elementId?: string) => {
  const result = isElementInContainer(elementRect, containerRect, container, 0.1);

  console.group(`🔍 Element Visibility Debug ${elementId ? `(${elementId})` : ''}`);
  console.log('Element Rect:', {
    left: elementRect.left,
    top: elementRect.top,
    right: elementRect.right,
    bottom: elementRect.bottom,
    width: elementRect.width,
    height: elementRect.height,
  });
  console.log('Container Rect:', {
    left: containerRect.left,
    top: containerRect.top,
    right: containerRect.right,
    bottom: containerRect.bottom,
    width: containerRect.width,
    height: containerRect.height,
  });
  console.log('Container Scroll:', {
    scrollLeft: container.scrollLeft,
    scrollTop: container.scrollTop,
    clientWidth: container.clientWidth,
    clientHeight: container.clientHeight,
  });
  console.log('Visibility Result:', result);
  console.groupEnd();

  return result;
};

const LazyLoader: React.FC<LazyLoaderProps> = observer(
  ({ onVisibilityChange, children, item, compId, onViewportLoaded, loadingTimestamp, loadedBoardIds, setBoardLoaded, id }) => {
    const ref = useRef<HTMLDivElement>(null);
    console.log('*👏👏👏***loadedBoardIds****', loadedBoardIds);

    const getContainer = useCallback(() => {
      const container = document.querySelector(`#${compId}_${id}`) as HTMLElement;
      return container;
    }, [compId, id]);

    const checkViewportBoardsLoaded = useCallback(() => {
      const container = getContainer();
      if (!container) return;

      const boardIdName = 'data-id';
      const allBoards = container.querySelectorAll(`#${compId}_${id} .ui-board-list.is-custom[${boardIdName}]`);

      const visibleUnloadedBoards = Array.from(allBoards).filter(board => {
        const boardId = board.getAttribute(boardIdName);
        if (!boardId || loadedBoardIds.has(`${id}_${boardId}`) || boardId === 'fake-add') return false;

        const boardRect = board.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // 调试信息（可以根据需要移除）
        if (id === '-1') {
          console.log('*👏👏👏***allBoards****', allBoards);
          console.log('*👏👏👏*board**boardRect****', board, boardRect);
          console.log('*👏👏👏***containerRect****', containerRect);
          console.log('*👏👏👏***isVisible****', isElementInContainer(boardRect, containerRect, container, 0.1));
        }

        // 使用更严格的可视性检测，要求至少10%的元素可见
        return isElementInContainer(boardRect, containerRect, container, 0.1);
      });

      if (visibleUnloadedBoards.length === 0) {
        onViewportLoaded?.();
      }
    }, [getContainer, loadedBoardIds, onViewportLoaded, compId, id]);

    const handleVisibilityChange = useCallback(() => {
      if (!loadedBoardIds.has(`${id}_${item.id}`)) {
        setBoardLoaded(`${id}_${item.id}`);
        // 获取元素位置信息
        if (ref.current) {
          const container = getContainer();
          if (container) {
            onVisibilityChange(item);
          }
        }

        checkViewportBoardsLoaded();
      }
    }, [checkViewportBoardsLoaded, getContainer, item, loadedBoardIds, onVisibilityChange, setBoardLoaded]);

    useEffect(() => {
      const currentRefValue = ref.current;
      const container = getContainer();
      if (!currentRefValue || !container) return;

      let isObserverActive = true;
      let scrollTimer: NodeJS.Timeout | null = null;

      const checkVisibility = () => {
        if (!isObserverActive || !currentRefValue) return;

        const elementRect = currentRefValue.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // 使用更精确的可视性检测，要求至少10%的元素可见
        if (isElementInContainer(elementRect, containerRect, container, 0.1)) {
          handleVisibilityChange();
          isObserverActive = false; // 触发后停止检测
        }
      };

      // 防抖的滚动检查函数
      const debouncedScrollCheck = () => {
        if (scrollTimer) {
          clearTimeout(scrollTimer);
        }
        scrollTimer = setTimeout(checkVisibility, 100);
      };

      // 初始检查
      checkVisibility();

      // 使用 IntersectionObserver 作为主要检测方式
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && isObserverActive) {
            // 当元素进入视口时，进行精确检查
            setTimeout(checkVisibility, 50); // 延迟一点确保DOM更新完成
          }
        },
        {
          root: container,
          threshold: [0, 0.1, 0.5], // 多个阈值，提高检测精度
          rootMargin: '10px', // 提前一点触发检测
        }
      );

      observer.observe(currentRefValue);

      // 监听容器滚动（使用防抖）
      container.addEventListener('scroll', debouncedScrollCheck, { passive: true });

      // 监听容器大小变化
      const resizeObserver = new ResizeObserver(() => {
        if (isObserverActive) {
          debouncedScrollCheck();
        }
      });
      resizeObserver.observe(container);

      return () => {
        isObserverActive = false;
        if (scrollTimer) {
          clearTimeout(scrollTimer);
        }
        observer.disconnect();
        resizeObserver.disconnect();
        container.removeEventListener('scroll', debouncedScrollCheck);
      };
    }, [compId, item.id, loadedBoardIds, getContainer, handleVisibilityChange]);

    // 监听 loadingTimestamp 变化 强制刷新
    useEffect(() => {
      const container = getContainer();
      if (container && ref.current && loadingTimestamp !== -1) {
        const elementRect = ref.current.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // 使用更严格的可视性检测
        if (isElementInContainer(elementRect, containerRect, container, 0.1)) {
          handleVisibilityChange();
        }
      }
    }, [loadingTimestamp, getContainer, handleVisibilityChange]);

    return (
      <div ref={ref} style={{ height: '100%', width: '100%' }} data-board-id={item.id}>
        {children}
      </div>
    );
  }
);

export default LazyLoader;
