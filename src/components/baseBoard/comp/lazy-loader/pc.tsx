// LazyLoader.tsx
import React, { useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react';
import '../../../../utils/polyfill/intersection-observer';

interface LazyLoaderProps {
  onVisibilityChange: (item: any) => void;
  children: React.ReactNode;
  item: any;
  compId: string;
  id: string;
  loadedBoardIds: Set<string>;
  setBoardLoaded: (boardId: string) => void;
  loadingTimestamp: number;
  onViewportLoaded?: () => void;
}
const THRESHOLD = 0.3; // 可见检测范围
// 简化的可视性检测函数
export const isElementInContainer = (elementRect: DOMRect, containerRect: DOMRect, container: HTMLElement, threshold: number = THRESHOLD) => {
  // 计算元素与容器的重叠区域
  const overlapLeft = Math.max(elementRect.left, containerRect.left);
  const overlapTop = Math.max(elementRect.top, containerRect.top);
  const overlapRight = Math.min(elementRect.right, containerRect.right);
  const overlapBottom = Math.min(elementRect.bottom, containerRect.bottom);

  // 检查是否有重叠
  const hasOverlap = overlapLeft < overlapRight && overlapTop < overlapBottom;
  if (!hasOverlap) return false;

  // 计算可见比例
  const overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop);
  const elementArea = elementRect.width * elementRect.height;
  const visibleRatio = elementArea > 0 ? overlapArea / elementArea : 0;

  return visibleRatio >= threshold;
};

// 懒加载的严格可视性检测：要求THRESHOLD%可见且中心点在容器内
export const isElementVisibleForLazyLoad = (elementRect: DOMRect, containerRect: DOMRect, container: HTMLElement, threshold: number = THRESHOLD) => {
  if (!isElementInContainer(elementRect, containerRect, container, threshold)) return false;

  // 检查中心点在容器内
  const centerX = elementRect.left + elementRect.width / 2;
  const centerY = elementRect.top + elementRect.height / 2;

  return centerX >= containerRect.left && centerX <= containerRect.right && centerY >= containerRect.top && centerY <= containerRect.bottom;
};

const LazyLoader: React.FC<LazyLoaderProps> = observer(({ onVisibilityChange, children, item, compId, onViewportLoaded, loadingTimestamp, loadedBoardIds, setBoardLoaded, id }) => {
  const ref = useRef<HTMLDivElement>(null);

  const getContainer = useCallback(() => document.querySelector(`#${compId}`) as HTMLElement, [compId]);
  // 检查视口内是否还有未加载的看板
  const checkViewportBoardsLoaded = useCallback(() => {
    const container = getContainer();
    if (!container) return;

    const allBoards = container.querySelectorAll(`#${compId}_${id} .ui-board-list.is-custom[data-id]`);
    const containerRect = container.getBoundingClientRect();

    const hasUnloadedVisible = Array.from(allBoards).some(board => {
      const boardId = board.getAttribute('data-id');
      if (!boardId || loadedBoardIds.has(`${id}_${boardId}`) || boardId === 'fake-add') return false;

      const boardRect = board.getBoundingClientRect();
      return isElementInContainer(boardRect, containerRect, container);
    });

    if (!hasUnloadedVisible) {
      onViewportLoaded?.();
    }
  }, [getContainer, loadedBoardIds, onViewportLoaded, compId, id]);

  const handleVisibilityChange = useCallback(() => {
    const boardKey = `${id}_${item.id}`;

    if (!loadedBoardIds.has(boardKey)) {
      setBoardLoaded(boardKey);
      onVisibilityChange(item);
      checkViewportBoardsLoaded();
    }
  }, [checkViewportBoardsLoaded, item, loadedBoardIds, onVisibilityChange, setBoardLoaded, id]);

  useEffect(() => {
    const element = ref.current;
    const container = getContainer();
    if (!element || !container) return;

    let isActive = true;
    let timer: NodeJS.Timeout | null = null;

    const checkVisibility = () => {
      if (!isActive || !element) return;

      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      if (isElementVisibleForLazyLoad(elementRect, containerRect, container)) {
        handleVisibilityChange();
        isActive = false; // 触发后停止检测
      }
    };

    const debouncedCheck = () => {
      if (timer) clearTimeout(timer);
      timer = setTimeout(checkVisibility, 100);
    };

    // IntersectionObserver 检测
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && isActive) {
          setTimeout(checkVisibility, 200);
        }
      },
      { root: container, threshold: THRESHOLD, rootMargin: '0px' }
    );

    observer.observe(element);
    container.addEventListener('scroll', debouncedCheck, { passive: true });

    return () => {
      isActive = false;
      if (timer) clearTimeout(timer);
      observer.disconnect();
      container.removeEventListener('scroll', debouncedCheck);
    };
  }, [getContainer, handleVisibilityChange]);

  // 监听 loadingTimestamp 变化
  useEffect(() => {
    if (loadingTimestamp === -1 || !ref.current) return;

    const container = getContainer();
    if (!container) return;

    const elementRect = ref.current.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    if (isElementVisibleForLazyLoad(elementRect, containerRect, container)) {
      handleVisibilityChange();
    }
  }, [loadingTimestamp, getContainer, handleVisibilityChange]);

  return (
    <div ref={ref} style={{ height: '100%', width: '100%' }} data-board-id={item.id}>
      {children}
    </div>
  );
});

export default LazyLoader;
