// LazyLoader.tsx
import React, { useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react';
import '../../../../utils/polyfill/intersection-observer';

interface LazyLoaderProps {
  onVisibilityChange: (item: any) => void;
  children: React.ReactNode;
  item: any;
  compId: string;
  id: string;
  loadedBoardIds: Set<string>;
  setBoardLoaded: (boardId: string) => void;
  loadingTimestamp: number;
  onViewportLoaded?: () => void;
}
export const getElementPosition = (element: HTMLElement, container: HTMLElement) => {
  // 获取元素和容器的完整位置信息
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  // 计算元素相对于容器的位置
  const relativePosition = {
    left: elementRect.left - containerRect.left + container.scrollLeft,
    top: elementRect.top - containerRect.top + container.scrollTop,
    right: elementRect.right - containerRect.left + container.scrollLeft,
    bottom: elementRect.bottom - containerRect.top + container.scrollTop,
  };

  return {
    // 相对位置
    ...relativePosition,
    // 元素尺寸
    width: elementRect.width,
    height: elementRect.height,
    // 是否在视口内
    isInViewport: isElementInContainer(elementRect, containerRect, container),
  };
};

export const isElementInContainer = (elementRect: DOMRect, containerRect: DOMRect, container: HTMLElement) => {
  // 考虑容器的滚动位置
  const containerVisibleWidth = container.clientWidth;
  const containerVisibleHeight = container.clientHeight;
  const scrollLeft = container.scrollLeft;
  const scrollTop = container.scrollTop;

  // 计算元素相对于容器可视区域的位置
  const relativeLeft = elementRect.left - containerRect.left;
  const relativeTop = elementRect.top - containerRect.top;

  // 判断元素是否在容器的可视区域内
  return (
    relativeLeft + elementRect.width > scrollLeft && // 右边界在可视区左侧
    relativeLeft < scrollLeft + containerVisibleWidth && // 左边界在可视区右侧
    relativeTop + elementRect.height > scrollTop && // 下边界在可视区上方
    relativeTop < scrollTop + containerVisibleHeight // 上边界在可视区下方
  );
};

const LazyLoader: React.FC<LazyLoaderProps> = observer(
  ({ onVisibilityChange, children, item, compId, onViewportLoaded, loadingTimestamp, loadedBoardIds, setBoardLoaded, id }) => {
    const ref = useRef<HTMLDivElement>(null);
    console.log('*👏👏👏***loadedBoardIds****', loadedBoardIds);

    const getContainer = useCallback(() => {
      const container = document.querySelector(`#${compId}_${id}`) as HTMLElement;
      return container;
    }, [compId, id]);

    const checkViewportBoardsLoaded = useCallback(() => {
      const container = getContainer();
      if (!container) return;
      const boardIdName = 'data-id';
      const allBoards = container.querySelectorAll(`#${compId}_${id} .ui-board-list.is-custom[${boardIdName}]`);
      console.log('*👏👏👏***allBoards****', allBoards);
      const visibleUnloadedBoards = Array.from(allBoards).filter(board => {
        const boardId = board.getAttribute(boardIdName);
        if (!boardId || loadedBoardIds.has(`${id}_${boardId}`) || boardId === 'fake-add') return false;

        const boardRect = board.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
          if (id === '4260253990411695750') {
          console.log('*👏👏👏*board**boardRect****', board, boardRect);
          console.log('*👏👏👏***containerRect****', containerRect);
        }
        return isElementInContainer(boardRect, containerRect, container);
      });
      if (visibleUnloadedBoards.length === 0) {
        onViewportLoaded?.();
      }
    }, [getContainer, loadedBoardIds, onViewportLoaded]);

    const handleVisibilityChange = useCallback(() => {
      if (!loadedBoardIds.has(`${id}_${item.id}`)) {
        setBoardLoaded(`${id}_${item.id}`);
        // 获取元素位置信息
        if (ref.current) {
          const container = getContainer();
          if (container) {
            onVisibilityChange(item);
          }
        }

        checkViewportBoardsLoaded();
      }
    }, [checkViewportBoardsLoaded, getContainer, item, loadedBoardIds, onVisibilityChange, setBoardLoaded]);

    useEffect(() => {
      const currentRefValue = ref.current;
      const container = getContainer();
      if (!currentRefValue || !container) return;

      const checkVisibility = () => {
        const elementRect = currentRefValue.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        if (isElementInContainer(elementRect, containerRect, container)) {
          handleVisibilityChange();
        }
      };

      // 初始检查
      // checkVisibility();

      // 使用 IntersectionObserver
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            checkVisibility();
            observer.disconnect();
          }
        },
        {
          root: container,
          threshold: 0,
        }
      );

      observer.observe(currentRefValue);

      // 监听容器滚动
      container.addEventListener('scroll', checkVisibility);

      return () => {
        observer.disconnect();
        container.removeEventListener('scroll', checkVisibility);
      };
    }, [compId, item.id, loadedBoardIds, getContainer, handleVisibilityChange]);

    // 监听 loadingTimestamp 变化 强制刷新
    useEffect(() => {
      const container = getContainer();
      if (container && ref.current && loadingTimestamp !== -1) {
        const elementRect = ref.current.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        if (isElementInContainer(elementRect, containerRect, container)) {
          handleVisibilityChange();
        }
      }
    }, [loadingTimestamp]);

    return (
      <div ref={ref} style={{ height: '100%', width: '100%' }} data-board-id={item.id}>
        {children}
      </div>
    );
  }
);

export default LazyLoader;
