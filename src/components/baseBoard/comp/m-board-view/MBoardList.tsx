import { Spin, Icon, MActionSheet, MDialog, Menu, MList, MListData, PullToRefresh, MenuItemData, Popover } from '@weapp/ui';
import { classnames, getLabel, middleware, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { Else, If, Then, When } from 'react-if';
import { withRouter } from 'react-router-dom';
import { appName, mBoardClsPrefix, DEFAULT_LANE_ID } from '../../../../constants';
import { EbdBPlKeys } from '../../../../types/common';
import { stopPropagation, invoke } from '../../../../utils';
import { ListMenuActionType, GroupItem } from '../../../common/board/types';
import { getBoardBg } from '../../../common/board/utils';
import { LayoutCardWithRouter } from '../../../common/field-layout/LayoutCard';
import MenuSheet from './MenuSheet';
import { MBoardListProps } from './types';
import { MarkItemProps, PageModeType } from '../../config/cmps/types';
import { renderVertical, renderCorner } from '../../config/cmps/lineMarkSet';
import MBoardEmpty from './empty';
import './index.less';

const formatMenus = (menu: any[]) => {
  return menu.map(i => {
    return {
      ...i,
      id: i.key,
      content: i.name || i.text,
    };
  });
};
// 描述
const renderDesc = (desc: string) => {
  if (isEmpty(desc)) return null;
  const pop = desc;
  return (
    <Popover weId={`d05yaa`} popup={pop} placement={'top'} popoverType="tooltip" action={['click']}>
      <div className={`${mBoardClsPrefix}-card-header-left-desc`} dangerouslySetInnerHTML={{ __html: desc || '' }} />
    </Popover>
  );
};
@middleware(appName, 'MBoardList')
@observer
class MBoardList extends PureComponent<MBoardListProps> {
  onEndReached = () => {
    const { store } = this.props;
    const { toLoadMore, pageMode } = store;
    if (pageMode === PageModeType.More) return;
    toLoadMore(this.props.data.id, DEFAULT_LANE_ID);
  };

  onRefresh = () => {
    const { store } = this.props;
    const { getData } = store;
    getData({ groupId: this.props.data.id });
  };

  openSortAction = (items: any = [], nowGroupId: string) => {
    const { getCurrentGroup, transferGroupCardBefore } = this.props.store;
    const groups = getCurrentGroup(DEFAULT_LANE_ID) as GroupItem[];
    const buttons = items.map((it: any) => ({
      content: (
        <div className={classnames({ [`${mBoardClsPrefix}-order`]: true })}>
          <span className={`${mBoardClsPrefix}-order-name`}>{it.content}</span>
        </div>
      ),
      id: it.id,
      onClick: () => {
        const groupId = it.id.split('_')[1];
        const nowGroup = groups.find((g: any) => g.id === nowGroupId);
        transferGroupCardBefore(nowGroup, DEFAULT_LANE_ID, groupId);
      },
    }));
    MActionSheet.showActionSheetWithOptions({
      options: buttons,
      maskClosable: true,
    });
  };

  onHeadMenuClick = (e: React.MouseEvent, menuItem: any) => {
    const { store, data, parentProps } = this.props;
    if (menuItem.id === ListMenuActionType.moveData) {
      this.openSortAction(formatMenus(menuItem.children), data.id);
    } else {
      const otherParams = { itemData: menuItem, e, props: parentProps };
      store.onListMenuChange(menuItem.id, this.props.data, otherParams);
    }
  };
  getColorStyle = () => {
    const { data, store } = this.props;
    let { color } = data as any;
    // 先判定是否开始了背景色自定义
    const { backgroundColor, fontColor } = getBoardBg(store?.activeGroup?.enableColor!, color);
    return {
      fontColor,
      bgColor: backgroundColor,
    };
  };

  getGroupData = () => {
    const { data } = this.props;
    const { groupCount = [] } = data!;
    let name = '',
      count = '',
      showgroup = true;
    if (groupCount.length === 0) return { name, count, showgroup: false };
    groupCount.map((el: any, ind: number) => {
      if (ind === groupCount.length - 1) {
        count = count + el.count;
        name = name + el?.name?.valueLocale;
      } else {
        count = count + el.count + '/';
        name = name + el?.name?.valueLocale + '/';
      }
    });
    return { name, count, showgroup };
  };

  renderHeader = () => {
    const { store, data } = this.props;
    const _data = toJS(data);
    const menus = store.getRealTimeGroupMenu(DEFAULT_LANE_ID, _data.id).filter((m: any) => m.id !== ListMenuActionType.updateOption);
    const csStyle = this.getColorStyle();
    const groupData = this.getGroupData();
    return (
      <div
        className={`${mBoardClsPrefix}-card-header ${_data?.desc || `${_data?.desc}` === '0' ? '' : 'no-desc'}`}
        style={{ background: csStyle.bgColor, color: csStyle.fontColor }}
      >
        <div className={`${mBoardClsPrefix}-card-header-left`}>
          <div className={`${mBoardClsPrefix}-card-header-left-title`}>
            <span className={`${mBoardClsPrefix}-card-header-left-title-name`}>
              {_data.name?.nameAlias ? _data.name?.nameAlias : _data.name}
            </span>
            <When weId={`${this.props.weId || ''}_wrq6fi`} condition={groupData?.showgroup}>
              <span className={`${mBoardClsPrefix}-header-left-title-count`}>{`（${groupData?.count}）`}</span>
            </When>
          </div>
          <When weId={`${this.props.weId || ''}_quqipr`} condition={_data?.desc || `${_data?.desc}` === '0'}>
            {renderDesc(_data?.desc || '')}
          </When>
        </div>
        <When weId={`${this.props.weId || ''}_f8a54o`} condition={menus.length}>
          <MenuSheet weId={`${this.props.weId || ''}_yfp4ds`} menus={formatMenus(menus)} onClick={this.onHeadMenuClick} />
        </When>
      </div>
    );
  };

  customRenderItem = (rowData: MListData, index: number) => {
    const { store, compId, pluginCenter, parentProps } = this.props;
    const { onCardMenuClick, config } = store;
    const { cardDraggable, cardMenus = [], extra = {}, ...restCardData } = rowData;
    const { id, ...showDatas } = restCardData;
    const isDataTemp: boolean = extra?.data_status === '0';

    const onChange = (value: string, item: MenuItemData, e: React.MouseEvent) => {
      onCardMenuClick(value, rowData, this.props, e.currentTarget as any);
    };
    const cornerSettingInfo: MarkItemProps[] = rowData.cornerSettingInfo ?? [];
    const verticalSettingInfo: MarkItemProps[] = rowData.verticalSettingInfo ?? [];

    const menus = cardMenus.filter((c: any) => c.visible);
    const csStyle = this.getColorStyle();
    const renderContent = () => {
      const renderCard = (_config: any, data: any) => {
        return (
          <LayoutCardWithRouter
            weId={`${this.props.weId || ''}_qaani9`}
            {...parentProps}
            data={toJS(restCardData)}
            config={_config.card}
            sort={index + 1}
            client="MOBILE"
            compId={compId}
            dataset={_config?.dataset!}
          />
        );
      };
      return (
        <div className={`${mBoardClsPrefix}-card`} key={id} style={{ background: csStyle.bgColor }}>
          <div className={`${mBoardClsPrefix}-card-main`}>
            {/* 竖线 */}
            {renderVertical(verticalSettingInfo)}
            <When weId={`${this.props.weId || ''}_bwc93v`} condition={isDataTemp}>
              <div className="data-temp" title={getLabel('55840', '暂存')} />
            </When>
            <div onClick={stopPropagation}>
              <Menu
                weId={`${this.props.weId || ''}_996fhp`}
                data={menus}
                type="select"
                selectType="iconOverlay"
                customSelectContent={<Icon weId={`${this.props.weId || ''}_c09pgs`} name="Icon-Down-arrow01" />}
                className={`${mBoardClsPrefix}-card-main-icon-down`}
                triggerProps={{
                  popupPlacement: 'bottomRight',
                  popupClassName: 'board-groupMenu-select-overlay',
                }}
                childTriggerProps={{
                  popupPlacement: 'leftTop',
                  popupClassName: 'board-card-select-overlay-child',
                }}
                onChange={onChange}
              />
            </div>
            <When weId={`${this.props.weId || ''}_t5ejpr`} condition={isEmpty(showDatas)}>
              <span className={`${mBoardClsPrefix}-card-main-noTemplate`}>{getLabel('105950', '未设置卡片显示字段')}</span>
            </When>
            <When weId={`${this.props.weId || ''}_tj1kqn`} condition={!isEmpty(showDatas)}>
              {invoke(pluginCenter, EbdBPlKeys.renderCardBefore, {
                args: [rowData],
              })}
              {invoke(pluginCenter, EbdBPlKeys.renderCardContent, {
                args: [config, toJS(rowData)],
                hook: renderCard,
              })}
              {invoke(pluginCenter, EbdBPlKeys.renderCardAfter, {
                args: [rowData],
              })}
            </When>
            {/* 角标 */}
            {renderCorner(cornerSettingInfo)}
          </div>
        </div>
      );
    };
    if (pluginCenter) {
      return pluginCenter.invoke(EbdBPlKeys.renderCard, {
        // 默认视图
        hook: renderContent,
        args: [
          {
            item: rowData,
            onBoardMenuClick: onChange,
            boardMenus: menus,
          },
        ],
      });
    }
    return renderContent();
  };

  onAdd = () => {
    const { data, store } = this.props;
    store.onAddNewData(DEFAULT_LANE_ID, data);
  };
  // 点击加载更多
  renderClickMore = (item: any, pageInfo: any) => {
    const { store } = this.props;
    // 渲染加载更多
    // 点击加载更多模式 无数据、无更多、数量少于分页不显示
    const { hasMore, isLoading } = pageInfo;
    return (
      <When weId={`${this.props.weId || ''}_8ul5ev`} condition={!isLoading}>
        <If weId={`${this.props.weId || ''}_rl99dy`} condition={hasMore}>
          <Then weId={`${this.props.weId || ''}_l6apye`}>
            <div className={`${mBoardClsPrefix}-list-more load-more-text`} onClick={() => store.toLoadMore(item.id, DEFAULT_LANE_ID)}>
              <span>{getLabel('76148', '加载更多')}</span>
              <Icon weId={`${this.props.weId || ''}_yowfvr`} name="Icon-Down-arrow01" />
            </div>
          </Then>
          <Else weId={`${this.props.weId || ''}_0u7e14`}>
            <div className={`${mBoardClsPrefix}-list-more`}>
              <span>{getLabel('82180', '已加载所有数据')}</span>
            </div>
          </Else>
        </If>
      </When>
    );
  };
  render() {
    const { data, store } = this.props;
    const { paginationData, hasAddCardRight, onCardClick, pageMode } = store;
    let { cards = [] } = data as any;
    const csStyle = this.getColorStyle();
    const _paginationData = toJS(paginationData);
    const getExtraEle = () => {
      if (isEmpty(_paginationData) || isEmpty(data.id)) return null;
      const { total = 0, pageSize = 0, isMoreLoading } = _paginationData[data.id] || {};
      if (isMoreLoading) {
        return (
          <div className={`${mBoardClsPrefix}-list-moreLoading`}>
            <Spin weId={`${this.props.weId || ''}_13xgd8`} size="small" />
          </div>
        );
      }
      if (pageMode === PageModeType.More && total > pageSize) {
        return this.renderClickMore(data, _paginationData[data.id]);
      }
      return null;
    };

    return (
      <div className={`${mBoardClsPrefix}-board-list-container`}>
        <div className={`${mBoardClsPrefix}-board-list-wrapper`}>
          <If weId={`${this.props.weId || ''}_qtyd2t`} condition={cards?.length === 0 && !paginationData[data.id]?.isLoading}>
            <Then weId={`${this.props.weId || ''}_ryyvi6`}>
              <>{this.renderHeader()}</>
              <MBoardEmpty weId={`${this.props.weId || ''}_e8df4t`} csStyle={csStyle} />
            </Then>
            <Else weId={`${this.props.weId || ''}_yoxw5k`}>
              <MList
                weId={`${this.props.weId || ''}_4caj6r@${data.id}`}
                sortable
                direction="column"
                sortableOptions={{ handle: '.my-handle', delay: '200' }}
                data={cards}
                renderHeader={this.renderHeader}
                customRenderContent={this.customRenderItem}
                className={`${mBoardClsPrefix}-list`}
                showImg
                onEndReached={this.onEndReached}
                onRowClick={onCardClick}
                // hasMore={paginationData[data.id]?.hasMore}
                pullToRefresh={
                  <PullToRefresh
                    weId={`${this.props.weId || ''}_ab0tje!${data.id}`}
                    direction="down"
                    refreshing={paginationData[data.id]?.isLoading}
                    onRefresh={this.onRefresh}
                  />
                }
                extraEle={getExtraEle()}
              />
            </Else>
          </If>
          <When weId={`${this.props.weId || ''}_8ul5ev`} condition={hasAddCardRight}>
            <div className={`${mBoardClsPrefix}-list-bottom`} onClick={this.onAdd}>
              <span>+{getLabel('105951', '新增数据')}</span>
            </div>
          </When>
        </div>
      </div>
    );
  }
}

export default withRouter(MBoardList) as unknown as ComponentType<any>;
