import { FormSwitchProps, Icon, MActionSheet, MBrowser, MCascader, MCityPicker, MIconDropdown, MSearchAdvanced, MSelect, MTypesBrowser } from '@weapp/ui';
import { classnames, getLabel, middleware } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { Else, If, Then, When } from 'react-if';
import { appName, ebdBClsPrefix, EbuilderFormModule, mBoardClsPrefix } from '../../../../constants';
import { OrderType } from '../../../../constants/common';
import { browserTypes, EtComponentKey, typeBrowserProps } from '../../../../constants/EtComponent';
import { formatParentPath, judgeIsPC } from '../../../../utils';
import DateSelect from '../../../common/date-select';
import PersonSelect from '../../../common/person-select';
import TopNav from './top-nav';
import { MBoardHeaderProps } from './types';
import './index.less';

const { MMultiCascader } = MCascader as any;
@middleware(appName, 'MBoardHeader')
@observer
class MBoardHeader extends React.PureComponent<MBoardHeaderProps, any> {
    parentPath: string = formatParentPath(this.props);

    // 根据权限获取高级搜索常用筛选
    getPanelParams = () => {
        const { appManager, getAdPanelParams } = this.props.store;
        return getAdPanelParams(appManager);
    };

    onViewRowClick = (data?: any) => {
        const menu = data?.id;
        switch (menu) {
            case 'sortMenu':
                this.openSortAction();
                break;
        }
    };

    openSortAction = () => {
        const { orders, onOrderClick } = this.props.store;
        const buttons = orders.map((data: any) => ({
            content: (
                <div className={classnames({ [`${mBoardClsPrefix}-order`]: true, selected: data?.selected })}>
                    <span className={`${ebdBClsPrefix}-order-name`}>{data.fieldName}</span>
                    <When weId={`${this.props.weId || ''}_333xho@${data.id}`} condition={data.id}>
                        <span className={`${mBoardClsPrefix}-order-icon`}>
                            <When weId={`${this.props.weId || ''}_z5og5q@${data.id}`} condition={data.orderType === OrderType.DESC}>
                                <Icon weId={`${this.props.weId || ''}_c02lin@${data.id}`} size="md" name="Icon-sort" />
                            </When>
                            <When weId={`${this.props.weId || ''}_l97eyt@${data.id}`} condition={data.orderType === OrderType.ASC}>
                                <Icon weId={`${this.props.weId || ''}_ii6pu2@${data.id}`} size="md" name="Icon-sort" style={{ transform: 'rotate(180deg)', transformOrigin: 'center' }} />
                            </When>
                        </span>
                    </When>
                </div>
            ),
            id: data.id,
            onClick: () => {
                onOrderClick(data.id);
            },
        }));
        MActionSheet.showActionSheetWithOptions({
            options: buttons,
            maskClosable: true,
        });
    };

    customMoreListItem = (rowData: any) => {
        const itemPrefix = `${mBoardClsPrefix}-menu-moreMenuItem`;
        return (
            <div className={`${itemPrefix}`}>
                <span className={`${itemPrefix}-left`}>
                    <Icon weId={`${this.props.weId || ''}_etfexf`} size="sm" className="leftIcon" name={rowData.icon} />
                    {rowData.title}
                </span>
                {rowData.noArrow ? '' : <Icon className={`${itemPrefix}-right`} size="xs" weId={`${this.props.weId || ''}_k55olt`} name="Icon-Right-arrow01" />}
            </div>
        );
    };

    render() {
        const { store, compId } = this.props;
        const { onSearch, conditionId, baseSearch, orders, groupSearchDatasH5, pageId } = store;
        const { adQuickParams, onVisibleChange, onReset, searchAdvancedStore } = baseSearch;
        const triggerProps: any = judgeIsPC() && {
            popupPlacement: 'bottomRight', // 解决PC iframe里位置显示在看不见的外面的问题
        }
        const searchDom = (
            <>
                <div className={`${mBoardClsPrefix}-header-search`}>
                    <When weId={`${this.props.weId || ''}_9x4hwu`} condition={adQuickParams}>
                        <MSearchAdvanced
                            weId={`${this.props.weId || ''}_gpo3rb`}
                            onVisibleChange={onVisibleChange}
                            conditionId={conditionId}
                            searchAdvancedStore={searchAdvancedStore}
                            onSearch={onSearch}
                            onReset={onReset}
                            path={`${this.parentPath}/${compId}`}
                            needRange
                            module={EbuilderFormModule}
                            formProps={{ customRenderFormSwitch: this.customRenderFormSwitch }}
                            {...adQuickParams}
                            {...this.getPanelParams()}
                            searchBarProps={adQuickParams?.quickSearchInputProps}
                            disableCommonFilter={adQuickParams?.quickSearchInputProps?.suffix === null}
                            className={`${mBoardClsPrefix}-searchAd-panel`}
                            requestHeaderParams={{ ebBusinessId: pageId }}
                        />
                    </When>
                </div>
                <div>
                    <When weId={`${this.props.weId || ''}_fzcbf0`} condition={orders.length > 0}>
                        <MIconDropdown
                            weId={`${this.props.weId || ''}_1f8nxa`}
                            key="m-board-header"
                            triggerProps={triggerProps}
                            data={[{ id: 'sortMenu', title: getLabel('19397', '排序'), icon: 'Icon-sort02-o' }]}
                            onClick={this.onViewRowClick}
                            icon="Icon-more02"
                            iconProps={{ size: 'lg' }}
                            customRenderContent={this.customMoreListItem}
                        />
                    </When>
                </div>
            </>
        );
        if (!adQuickParams && !orders.length && !groupSearchDatasH5.length) return null;
        return (
            <div className={`${mBoardClsPrefix}-header`}>
                <If weId={`${this.props.weId || ''}_se76er`} condition={groupSearchDatasH5.length > 0}>
                    <Then weId={`${this.props.weId || ''}_v7ltbf`}>
                        <TopNav
                            weId={`${this.props.weId || ''}_rfr9ql`}
                            data={toJS(groupSearchDatasH5)}
                            selectedKeys={store.groupSearchParams}
                            onChange={store.updateGroupSearchParams}
                            extraContent={searchDom}
                            className={`${mBoardClsPrefix}-searchAd-menu`}
                            targetDomId={store.compId}
                        />
                    </Then>
                    <Else weId={`${this.props.weId || ''}_lly9hi`}>{searchDom}</Else>
                </If>
            </div>
        );
    }

    /**
     * 高级搜索面板自定义组件
     * @param id
     * @param props
     */
    customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
        const { otherParams, value, onChange } = props.props;
        if (key.indexOf(EtComponentKey.GroupCustom) > -1) {
            const { multiSelect, listGroupSets } = otherParams;
            const options = listGroupSets.map((o: any) => ({
                ...o,
                content: o.setName,
            }));
            return <MSelect weId={`${this.props.weId || ''}_wwwd91`} data={options} value={toJS(value)} allowCancel multiple={multiSelect} placeholder={getLabel('40502', '请选择')} onChange={onChange} />;
        }
        if (otherParams?.componentKey === EtComponentKey.DateComponent) {
            const { dateVal, type } = otherParams;
            return <DateSelect weId={`${this.props.weId || ''}_7q4qiq`} datas={dateVal} value={value} isOptionTile={false} onChange={onChange} isMobile type={type} />;
        }

        if (otherParams?.componentKey === EtComponentKey.Employee) {
            const { dateVal } = otherParams;
            return <PersonSelect weId={`${this.props.weId || ''}_cb7cf2`} datas={dateVal} value={value} isOptionTile={false} onChange={onChange} isMobile />;
        }
        // 人员范围选择浏览框
        if (otherParams?.componentKey === EtComponentKey.EmployeeScope) {
            const onEmployeeScopeChange = (val: any) => {
                onChange(val);
            };
            return <MTypesBrowser weId={`${props.weId || ''}_ebz3w9`} module="hrm" type="hrmcombination" value={toJS(value)} options={browserTypes()} browsers={typeBrowserProps} onChange={onEmployeeScopeChange} />;
        }

        // 人员组织多选浏览框
        if (otherParams?.componentKey === EtComponentKey.EmployeeOrganization) {
            const onEmployeeOrganizationChange = (val: any) => {
                onChange(val);
            };
            return (
                <MBrowser
                    weId={`${props.weId || ''}_zddg5r`}
                    type="combinationResource"
                    module="hrm"
                    multiple
                    searchAtTab
                    isMultType
                    value={toJS(value)}
                    hasAdvanceSearch
                    disabledTabCache
                    onChange={onEmployeeOrganizationChange}
                />
            );
        }

        if (otherParams?.componentKey === EtComponentKey.Cascader) {
            return <MMultiCascader weId={`${this.props.weId || ''}_n9d59f`} options={props.props.options} value={value || []} onChange={onChange} />;
        }

        // 行政字段国家、省份、城市、区域
        if ([EtComponentKey.Country, EtComponentKey.Province, EtComponentKey.City, EtComponentKey.District].includes(otherParams?.componentKey)) {
            const defaultLabel = toJS(otherParams?.defaultLabel);
            const multiSelect = otherParams?.multiple;
            return (
                <MCityPicker
                    weId={`${props.weId || ''}_nyowf0`}
                    value={toJS(value) || []}
                    onChange={onChange}
                    multiple={multiSelect}
                    level={(otherParams?.componentKey || '').toLowerCase()}
                    cascaderProps={{
                        defaultLabel: toJS(value) && defaultLabel ? defaultLabel : [],
                    }}
                />
            );
        }
        return <span>The current custom type is supported. Please contact the developer</span>;
    };
}

export default MBoardHeader;
