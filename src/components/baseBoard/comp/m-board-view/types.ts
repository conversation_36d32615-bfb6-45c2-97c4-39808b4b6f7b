/**
 * <AUTHOR>
 * @createTime 2022 -02 -22
 */
import React, { Attributes } from 'react';
import { RouteComponentProps } from "react-router-dom";
import { AnyObj } from "@weapp/ui";
import { BaseParamsProps, GroupItem, BoardPluginCenter } from "../../../common/board/types";
import { BoardViewStore } from '../../../common/board/store'
import { EtComponentKey } from '../../../../constants/EtComponent';
import { ShowPosition } from '../../../../constants/common';

export interface MBoardViewMain extends RouteComponentProps, BaseParamsProps, Attributes {
  store: BoardViewStore;
  contentHeight?: number;
  urlParams: any;
  singlePageData?: any
  /** 组件mount前的回调 */
  onBeforeMount?: (store: any) => void;
}

export interface MBoardListProps extends RouteComponentProps, React.Attributes {
  store: BoardViewStore,
  config: any,
  data: GroupItem,
  wrapperRef?: HTMLDivElement
  compId: string
  pluginCenter?: BoardPluginCenter // 引入插件机制
  parentProps?: MBoardViewMain
}

export interface MBoardHeaderProps extends RouteComponentProps, React.Attributes {
  store: BoardViewStore,
  config: any,
  compId: string
}

export interface MGroupAddProps extends React.Attributes {
  onOk: (val: any) => void,
  createGroupDataByEbuilder: (type: 'add' | 'edit', dataId?: string) => Promise<void>,
  isEbuilderField?: boolean
}
/**
 * 分组搜索字段
 */
export type GroupSearchField = {
  groupField: string;
  id: string;
  listFilterDefaultSets: AnyObj;
  multiSelect: boolean;
  showName: string;
  showPosition: ShowPosition;
  fieldName: string; // 后端搜索key值
  componentKey: EtComponentKey;
  tabStyle?: string; // "select" | tab
};