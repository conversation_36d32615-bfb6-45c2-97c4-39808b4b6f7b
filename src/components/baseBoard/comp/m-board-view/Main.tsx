import React, { ComponentProps, createRef, PureComponent } from 'react';
import { MCarousel, MDialog, Spin } from '@weapp/ui';
import { middleware, eventEmitter, isEqual, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import { withRouter } from 'react-router-dom';
import { appName, mBoardClsPrefix, DEFAULT_LANE_ID, DEFAULT_LANE_ITEM } from '../../../../constants';
import { EbdBPlKeys } from '../../../../types/common';
import { formatParentPath, isNeedRefreshByConfig, hasRegisterFuncInPlugin, invoke } from '../../../../utils';
import { CustomEventName } from '../../../../utils/event-emitter/types';
import withAuthorized from '../../../common/withAuthorized';
import MBoardHeader from './MBoardHeader';
import MBoardList from './MBoardList';
import MGroupAdd from './MGroupAdd';
import MGroupEdit from './MGroupEdit';
import { MBoardViewMain } from './types';
import { EbMCardDetailView } from './EbMCardDetailView';
import MBoardEmpty from './empty';
import MLazyLoader from '../lazy-loader/m';
import { GroupItem } from '../../../common/board/types';
import './index.less';

@inject('store')
@withAuthorized('store')
@middleware(appName, 'MBoardMain')
@observer
class MBoardMain extends PureComponent<MBoardViewMain> {
  parentPath: string = formatParentPath(this.props);
  wrapperRef = createRef<HTMLDivElement>();

  componentDidMount() {
    const { store, compId } = this.props;
    this.props.onRef?.(this.wrapperRef);
    store.setRouteProps(this.props);
    this.initBoard();
    eventEmitter.on('weappEbdfpage', CustomEventName.mCardAdd, this.onMCardAdd, compId);
    eventEmitter.on('weappEbdfpage', CustomEventName.mFlowAdd, this.onMFlowAdd, compId);
    eventEmitter.on('weappEbdfpage', CustomEventName.mCardEdit, this.onMCardReload, compId);
    const wrapper: any = document.querySelector('.ebpage');
    const dom: any = document.querySelector(`#${compId}.ebcom-kanban`);
    const listDom: any = document.querySelector(`#${compId}.ebcom-kanban .weapp-ebdboard-m`);
    if (wrapper && dom) {
      dom.style.height = `${wrapper.clientHeight - 30}px`;
      listDom.style.height = `${wrapper.clientHeight - 90}px`;
    }
    this.handleMount();
  }

  componentWillUnmount() {
    const { compId } = this.props;
    eventEmitter.off('weappEbdfpage', CustomEventName.mCardAdd, this.onMCardAdd, compId);
    eventEmitter.off('weappEbdfpage', CustomEventName.mFlowAdd, this.onMFlowAdd, compId);
    eventEmitter.off('weappEbdfpage', CustomEventName.mCardEdit, this.onMCardReload, compId);
  }
  componentWillReceiveProps(nextProps: Readonly<MBoardViewMain>) {
    // 插件包新注入
    if (!isEqual(nextProps.pluginCenter, this.props.pluginCenter) && !isEmpty(nextProps.pluginCenter)) {
      this.initBoard(nextProps);
      this.handleMount(nextProps);
      return;
    }
  }

  componentDidUpdate(prevProps: MBoardViewMain) {
    const { compId: _compId, pageId: _pageId, config: _config, isMobile: _isMobile, urlParams: _urlParams } = prevProps;
    const { compId, pageId, config, isMobile, urlParams } = this.props;
    if (
      !isEqual(compId, _compId) ||
      !isEqual(pageId, _pageId) ||
      !isEqual(isMobile, _isMobile) ||
      // !isEqual(urlParams, _urlParams) ||
      isNeedRefreshByConfig(_config, config)
    ) {
      this.initBoard();
    }
    const wrapper: any = document.querySelector('.ebpage');
    const dom: any = document.querySelector('.ebcom-kanban');
    if (wrapper && dom) {
      dom.style.height = `${wrapper.clientHeight - 30}px`;
    }
  }
  initBoard = (props = this.props) => {
    this.initConfig(props);
    const { store, pluginCenter, isDoc, refPluginPackage } = props;
    // 插件包等待pluginCenter注入
    if (!isEmpty(refPluginPackage) && !pluginCenter) return;
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.onCustomDidMount)) {
      invoke(pluginCenter, EbdBPlKeys.onCustomDidMount, { args: [props] });
      return;
    }
    if (!isDoc) {
      store.initData();
    }
  };

  initConfig = (props = this.props) => {
    const { store, compId, pageId, config, isMobile, isDesign, urlParams, events, ebStore, pluginCenter } = props;
    store.initConfig(
      {
        compId,
        pageId,
        config,
        isMobile,
        isDesign,
        events,
        ebStore,
        pluginCenter,
      },
      urlParams
    );
  };
  handleMount = (props = this.props) => {
    invoke(props.pluginCenter, EbdBPlKeys.onBeforeMount, {
      args: [props.store],
    });
    // 兼容新拉出插件包场景
    props.onBeforeMount?.(props.store);
    // 处理筛选事件
    if (!props.events) {
      return;
    }
    // 先解绑
    props.events.off('filter', props.compId);
    if (hasRegisterFuncInPlugin(props.pluginCenter, EbdBPlKeys.onFilter)) {
      invoke(props.pluginCenter, EbdBPlKeys.onFilter, { args: [this.props] });
      return;
    }
    props.events.on('filter', props.compId, (val: any[], filterId?: string) => {
      props.store.setFilter(val, filterId);
      props.store.initData();
    });
  };

  onMCardAdd = (data: any) => {
    const { store } = this.props;
    const { isEbuilderField, groupEditingData } = store;
    store.addCard(data.dataid, false, () => {
      // 如果当前是关联ebuilder字段新建分组 则需要刷新外部分组
      if (isEbuilderField && groupEditingData.objId) {
        store.initData();
        store.onClearNewData();
      }
    });
  };
  renderMDetailCardDialog = () => {
    return (
      <MDialog weId={`${this.props.weId || ''}_jxpa5z`} destroyOnClose path={`${this.parentPath}/mCardDetailView`} isRouteLayout>
        <EbMCardDetailView weId={`${this.props.weId || ''}_g1izic`} {...this.props} />
      </MDialog>
    );
  };

  onMFlowAdd = (data: any) => {
    const { store } = this.props;
    store.addCard(data.requestId, true);
  };

  onMCardReload = () => {
    const { store } = this.props;
    store.resetViewportLoading('onMCardReload');
  };

  onAddGroup = (v: string) => {
    const { store } = this.props;
    store.addOrEditGroup({ name: v });
  };

  getItems = () => {
    const { store, config, compId, pluginCenter } = this.props;
    const { loadedBoardIds, loadingTimestamp, setBoardLoaded, isEbuilderField, getCurrentGroup } = store;
    // 移动端无泳道 用默认的
    const groups = getCurrentGroup(DEFAULT_LANE_ID) as GroupItem[];
    const items = groups.map((g: any) => {
      return (
        <MLazyLoader
          weId={`${this.props.weId || ''}_keyyhs`}
          onVisibilityChange={this.onBoardEntryView}
          compId={compId}
          key={g.id}
          id={DEFAULT_LANE_ID}
          loadedBoardIds={loadedBoardIds}
          loadingTimestamp={loadingTimestamp}
          setBoardLoaded={setBoardLoaded}
          groups={toJS(groups)}
        >
          <Spin weId={`${this.props.weId || ''}_94xijl`} spinning={false} style={{ height: '100%' }} wrapperClassName={`${mBoardClsPrefix}-board-spin-list`}>
            <MBoardList weId={`${this.props.weId || ''}_w4ghbc!${g.id}`} key={g.id} store={store} data={g} config={config} compId={compId} pluginCenter={pluginCenter} parentProps={this.props} />
          </Spin>
        </MLazyLoader>
      );
    });
    if (store.hasAddGroupRight) {
      items.push(<MGroupAdd weId={`${this.props.weId || ''}_we0yho`} key="add" onOk={this.onAddGroup} isEbuilderField={isEbuilderField} createGroupDataByEbuilder={store.createGroupDataByEbuilder} />);
    }
    return items;
  };
  onBoardEntryView = (item: any) => {
    const { store, pluginCenter } = this.props;
    const { getData } = store;
    // 移动端无泳道  默认就是DEFAULT_LANE_ID
    const fetchData = () => {
      if (store.isDesign) {
        store?.designerStore?.previewData(item.id, DEFAULT_LANE_ID);
      } else {
        getData({ groupId: item.id, laneId: DEFAULT_LANE_ID });
      }
    };
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.getBoardData)) {
      return invoke(pluginCenter, EbdBPlKeys.getBoardData, {
        args: [
          {
            item: item,
            fetchData,
          },
        ],
      });
    }
    fetchData();
  };
  renderMain = (props = this.props) => {
    const { store, compId } = props;
    const { groupRefreshKey, getCurrentGroup } = store;
    // 移动端无泳道 用默认的
    const groups = getCurrentGroup(DEFAULT_LANE_ID) as GroupItem[];
    if (isEmpty(groups)) {
      return <MBoardEmpty weId={`${this.props.weId || ''}_nco87a`} />;
    }
    return (
      <div className={`${mBoardClsPrefix}-content`} id={`${compId}_${DEFAULT_LANE_ID}`}>
        <MCarousel
          weId={`${this.props.weId || ''}_g623ly_${compId}`}
          navigation={groups.length > 1}
          fillWays="backgroundFill"
          pagination={{
            type: 'bullets',
            bulletClass: `${mBoardClsPrefix}-bullet`,
            bulletActiveClass: `${mBoardClsPrefix}-bullet-active`,
          }}
          key={groupRefreshKey}
        >
          {this.getItems()}
        </MCarousel>
      </div>
    );
  };

  render() {
    const { store, config, pageId, pluginCenter } = this.props;
    const { groupEditVisible, groupEditingData, boardColorStyle } = store;
    const editGroupData = { name: groupEditingData.name, color: groupEditingData?.color };
    return (
      <div className={mBoardClsPrefix} ref={this.wrapperRef}>
        <MBoardHeader weId={`${this.props.weId || ''}_5bimls`} {...this.props} store={store} config={config} />
        {invoke(pluginCenter, EbdBPlKeys.renderContentTop)}
        {invoke(pluginCenter, EbdBPlKeys.renderMain, { hook: this.renderMain, args: [this.props] })}
        <MGroupEdit
          weId={`${this.props.weId || ''}_9ei55c`}
          visible={groupEditVisible}
          onOk={store.addOrEditGroup}
          onCancel={store.closeGroupEdit}
          datas={editGroupData}
          boardColorStyle={boardColorStyle}
          ebBusinessId={pageId}
        />
        {this.renderMDetailCardDialog()}
        {/*解决移动端循环嵌套渲染 */}
        {/* <CorsComponent weId={`${this.props.weId || ''}_dv3rwh`} app="@weapp/ebdpage" compName="RoutePageView"
                       type="EB_FORM_VIEW"/> */}
      </div>
    );
  }
}

export default withRouter(MBoardMain) as unknown as ComponentProps<any>;
