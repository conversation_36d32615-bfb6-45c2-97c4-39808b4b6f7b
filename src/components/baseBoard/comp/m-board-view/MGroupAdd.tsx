/**
 * <AUTHOR>
 * @createTime 2022-03-02
 */
import React from 'react';
import { getLabel, trim } from '@weapp/utils';
import { MButton, Icon, Input, MDialog } from '@weapp/ui';
import { If, Then, Else } from 'react-if';
import { MGroupAddProps } from './types';
import { mBoardClsPrefix } from '../../../../constants';

export default class MGroupAdd extends React.PureComponent<MGroupAddProps, any> {
  constructor(props: MGroupAddProps) {
    super(props);
    this.state = {
      editing: false,
      addFormGroupPending: false,
      value: '',
    };
  }
  handleCreateGroupDataByEbuilder = async () => {
    const { createGroupDataByEbuilder } = this.props;
    const { addFormGroupPending } = this.state;
    if (addFormGroupPending || !createGroupDataByEbuilder) return;
    try {
      this.setState({ addFormGroupPending: true });
      await createGroupDataByEbuilder('add');
    } catch (error) {
    } finally {
      this.setState({ addFormGroupPending: false });
    }
  };
  enterEditing = () => {
    const { isEbuilderField } = this.props;
    if (isEbuilderField) {
      this.handleCreateGroupDataByEbuilder();
      return;
    }
    this.setState({ editing: true });
  };

  exitEditing = () => {
    this.setState({ editing: false, value: '' });
  };

  onInputChange = (v: React.ReactText) => {
    this.setState({ value: v });
  };

  onSave = () => {
    const value = trim(this.state.value);
    if (value) {
      this.props.onOk(this.state.value);
      this.exitEditing();
    } else {
      MDialog.toast({
        type: 'fail',
        content: getLabel('221898', '分组名称不能为空'),
        mask: true,
        delay: 750,
      });
    }
  };
  getAddComp = () => {
    const { addFormGroupPending } = this.state;
    return (
      <div onClick={this.enterEditing} className={`${mBoardClsPrefix}-group-add-area-text`}>
        {addFormGroupPending ? <Icon weId={`${this.props.weId || ''}_e6z2xl`} name="Icon-Loading" spin moduleColor="blue" size="sm" /> : <Icon weId={`${this.props.weId || ''}_hv199n`} size="sm" name="Icon-add-to01" />}
        <span>{getLabel('221899', '新建分组')}</span>
      </div>
    );
  };
  render() {
    const { editing } = this.state;
    return (
      <div className={`${mBoardClsPrefix}-group-add-area`} data-board-id="fake-add">
        <If weId={`${this.props.weId || ''}_cy5rx3`} condition={!editing}>
          <Then weId={`${this.props.weId || ''}_iaf0ki`}>{this.getAddComp()}</Then>
          <Else weId={`${this.props.weId || ''}_k3g3z2`}>
            <>
              <Input
                weId={`${this.props.weId || ''}_0ssq32`}
                onChange={this.onInputChange}
                onPressEnter={this.onSave}
                placeholder={getLabel('221900', '请输入分组名称')}
                className={`${mBoardClsPrefix}-group-add-area-input`}
              />
              <div className={`${mBoardClsPrefix}-group-add-area-btns`}>
                <MButton weId={`${this.props.weId || ''}_ymmebj`} type="primary" size="small" onClick={this.onSave}>
                  {getLabel('221901', '确定')}
                </MButton>
                <MButton weId={`${this.props.weId || ''}_vtl7yd`} type="default" size="small" onClick={this.exitEditing}>
                  {getLabel('53937', '取消')}
                </MButton>
              </div>
            </>
          </Else>
        </If>
      </div>
    );
  }
}
