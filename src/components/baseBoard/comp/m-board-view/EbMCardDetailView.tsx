import { CorsComponent } from '@weapp/ui';
import React from 'react';
import { RouteComponentProps } from 'react-router-dom';
import {getUrlParams} from '../../../../utils';

interface EbMCardDetailViewProps extends RouteComponentProps {
  location: any;
}

export class EbMCardDetailView extends React.PureComponent<EbMCardDetailViewProps> {
  paramsCacheLast = {};

  render() {
    const params = this.props.location.state || {}; //type, dataid, objid
    const urlParams = getUrlParams();

    const pathname = this.props.location.pathname;
    if (pathname.endsWith('mCardDetailView')) {
      this.paramsCacheLast = { ...urlParams, ...params };
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_6ahgeq`}
        app={'@weapp/ebdfpage'}
        compName="EbMCardDetailView"
        paramsFromParent={this.paramsCacheLast}
      />
    );
  }
}
