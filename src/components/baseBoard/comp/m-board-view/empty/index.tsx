import { getLabel } from '@weapp/utils';
import { Empty, Icon, AnyObj } from '@weapp/ui';
import { mBoardClsPrefix } from '../../../../../constants';
import './index.less';

export interface MBoardEmptyProps {
  description?: string;
  csStyle?: AnyObj;
  [x: string]: any;
}

const MBoardEmpty = (props: MBoardEmptyProps) => {
  const { csStyle, description = getLabel('54023', '暂无数据') } = props;
  return (
    <div className={`${mBoardClsPrefix}-empty`}>
      <Empty
        weId={`${props.weId || ''}_2mop4o`}
        description={description}
        style={{ color: csStyle?.fontColor === 'var(--base-white)' ? '#999' : csStyle?.fontColor }}
        image={<Icon weId={`${props.weId || ''}_orgs7q`} style={{ width: 100, height: 100 }} name="Icon-empty-file" />}
      />
    </div>
  );
};
export default MBoardEmpty;
