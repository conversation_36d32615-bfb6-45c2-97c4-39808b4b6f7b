/**
 * <AUTHOR>
 * @createTime 2022-03-10
 */
import React from 'react';
import { observer } from 'mobx-react';
import { MIconDropdown, Icon, CorsComponent } from '@weapp/ui';
import { mBoardClsPrefix } from '../../../../constants';
import { judgeIsPC } from '../../../../utils';

export interface MenuSheetProps {
  onClick: (e: React.MouseEvent, data: any) => void;
  menus: Array<{ id: string; title: string; icon: any }>;
}

@observer
export default class MenuSheet extends React.PureComponent<MenuSheetProps, any> {
  customMoreListItem = (rowData: any) => {
    const itemPrefix = `${mBoardClsPrefix}-menu-moreMenuItem`;
    let iconPath = rowData.iconPath ? JSON.parse(rowData.iconPath) : '';
    return (
      <div className={`${itemPrefix}`} onClick={(e) => this.onRowClick(e, rowData)}>
        <span className={`${itemPrefix}-left`}>
          {!!iconPath && (
            <CorsComponent
              weId={`$mq4b2m`}
              app="@weapp/ebdcoms"
              compName="AssetsItem"
              path=""
              style={{
                borderRadius: 'var(--border-radius-xs)',
              }}
              size="s"
              {...iconPath}
            />
          )}
          {rowData?.icon && <Icon weId={`${this.props.weId || ''}_etfexf`} size="sm" className="leftIcon" name={rowData.icon} />}
          {rowData?.title || rowData?.content}
        </span>
        {rowData.noArrow ? (
          ''
        ) : (
          <Icon className={`${itemPrefix}-right`} size="xs" weId={`${this.props.weId || ''}_k55olt`} name="Icon-Right-arrow01" />
        )}
      </div>
    );
  };

  onRowClick = (e: React.MouseEvent, data: any) => {
    this.props?.onClick(e, data);
  };

  render() {
    const { menus = [] } = this.props;
    const triggerProps: any = judgeIsPC() && {
      popupPlacement: 'bottomRight', // 解决PC iframe里位置显示在看不见的外面的问题
      mask: true,
      maskClosable: true,
    };
    return (
      <MIconDropdown
        weId={`${this.props.weId || ''}_1f8nxa`}
        key="board-menu-sheet"
        triggerProps={triggerProps}
        data={menus}
        icon="Icon-more-o"
        iconProps={{ size: 'lg' }}
        customRenderContent={this.customMoreListItem}
      />
    );
  }
}
