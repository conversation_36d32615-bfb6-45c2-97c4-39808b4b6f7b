/**
 * <AUTHOR>
 * @createTime 2022-02-22
 */
import { observer, Provider } from 'mobx-react';
import React, { PureComponent, Suspense } from 'react';
import { withRouter } from 'react-router-dom';
import { AnyObj } from '../../../../types/common';
import { getUrlParams } from '../../../../utils';
import { BoardViewStore } from '../../../common/board/store';
import withEbParams from '../../../common/withEbParams';
import { BoardViewProps } from '../board-view/types';
import Main from './Main';

@withEbParams
@observer
class MBoardView extends PureComponent<BoardViewProps & React.Attributes> {
  render() {
    const { compId, pageId, config, ebParams = {}, onRef, ebStore, pluginCenter } = this.props;
    const urlParams: AnyObj = { ...getUrlParams(), ...ebParams };
    return (
      <Provider weId={`${this.props.weId || ''}_38axof`} store={new BoardViewStore()}>
        <Suspense weId={`${this.props.weId || ''}_4kyviz`} fallback={<div />}>
          <Main
            weId={`${this.props.weId || ''}_1qf2vu`}
            {...this.props}
            onRef={onRef}
            compId={compId}
            pageId={pageId}
            config={config}
            urlParams={urlParams}
            ebStore={ebStore}
            pluginCenter={pluginCenter}
          />
        </Suspense>
      </Provider>
    );
  }
}
export default withRouter(MBoardView);
