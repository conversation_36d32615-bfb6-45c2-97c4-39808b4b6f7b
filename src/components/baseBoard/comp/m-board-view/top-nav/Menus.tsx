import {
  AnyObj, I<PERSON>, MList, MListData,
} from '@weapp/ui';
import { find } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { groupSearchClsPrefix } from './index';
import { GroupSearchField } from '../types';

interface MenuDataField extends GroupSearchField {
  optionName: string;
  children: Array<any>;
  multiSelect: boolean;
}
interface CustomMenusProps {
  data: MenuDataField[];
  selectedKeys: AnyObj;
  leftKeys?: Array<MListData> | Array<string>; // 多项分类，第一列默认值展示
  onChange: (data: any) => void;
}

interface State {
  selectedLeftKeys: Array<MListData> | Array<string>;
  selectedKeys: AnyObj; // 存储所有的默认值
  isClick: boolean; // 记录当前第一列是否选中
  leftActiveKey: string; // 记录当前第一列选中的key
}
@observer
export default class CustomMenus extends PureComponent<CustomMenusProps, State> {
  constructor(props: CustomMenusProps) {
    super(props);
    this.state = {
      selectedLeftKeys: [],
      selectedKeys: props.selectedKeys || {},
      isClick: false,
      leftActiveKey: '',
    };
  }

  // 获取当前选项的数据对象
  getRightListDataObj = () => {
    const { leftActiveKey } = this.state;
    const { data } = this.props;
    const dataObj: any = find(data, (o) => o.id === leftActiveKey) || {};
    return dataObj;
  };

  // 分类只有一项时点击事件
  onRowSelect = (arr: Array<MListData> | Array<string>) => {
    const dataObj = this.props.data[0];
    this.props.onChange?.({ [dataObj.fieldName]: arr });
  };

  // 分类有多项左侧列
  onLeftRowSelect = (arr: Array<MListData> | Array<string>, selectedData?: MListData[]) => {
    if (arr.length > 0) {
      const activeKey = selectedData && selectedData[0] && selectedData[0].fieldName;
      this.setState({ selectedLeftKeys: arr, isClick: true, leftActiveKey: activeKey });
    }
  };

  // 分类有多项右侧列，触发点击回调事件
  onRightRowSelect = (arr: Array<MListData> | Array<string>) => {
    const { leftActiveKey } = this.state;
    this.setState((pre) => ({
      selectedKeys: {
        ...pre.selectedKeys,
        [pre.leftActiveKey]: arr,
      },
    }));
    this.props.onChange?.({ [leftActiveKey]: arr });
  };

  customRenderItemRight = (rowData: MListData) => {
    const obj = rowData;
    return (
      <>
        <span className="title" key={obj.id}>
          {obj.optionName}
        </span>
        <Icon weId={`${this.props.weId || ''}_e5u8k3`} name="Icon-correct01" />
      </>
    );
  };

  customRenderItemLeft = (rowData: MListData) => {
    const obj = rowData;
    const { leftKeys = [] } = this.props;
    return (
      <>
        <span
          className={`title ${leftKeys.indexOf(obj.id as any) > -1 && 'title-active'}`}
          key={obj.id}
        >
          {obj.optionName}
        </span>
      </>
    );
  };

  renderSingleMenu = () => {
    const { data, selectedKeys } = this.props;
    const dataObj = data[0];
    return (
      <div className={`${groupSearchClsPrefix}-menu-content`}>
        <MList
          weId={`${this.props.weId || ''}_h88zt5`}
          showCheck
          checkboxPosition="right"
          selectedRowKeys={selectedKeys[dataObj.fieldName] || []}
          data={dataObj.children || []}
          customRenderContent={this.customRenderItemRight}
          rowSelect
          onRowSelect={this.onRowSelect}
          stopCheckboxPropagation
          multipleCheck={dataObj.multiSelect || false}
          className="menu-content-left"
        />
      </div>
    );
  };

  renderMultiMenu = () => {
    const { data } = this.props;
    const {
      selectedLeftKeys, selectedKeys, isClick, leftActiveKey,
    } = this.state;
    const { children = [], multiSelect } = this.getRightListDataObj();
    const showRight = children.length > 0 && isClick;
    return (
      <div className={`${groupSearchClsPrefix}-menu-content`}>
        <MList
          weId={`${this.props.weId || ''}_nrfvo1`}
          showCheck
          checkboxPosition="right"
          selectedRowKeys={selectedLeftKeys}
          data={data}
          customRenderContent={this.customRenderItemLeft}
          rowSelect
          onRowSelect={this.onLeftRowSelect}
          stopCheckboxPropagation
          multipleCheck={false}
          style={{ width: showRight ? '40%' : '100%' }}
          className={`menu-content-left ${showRight && 'menu-content-left-right'}`}
        />
        <When weId={`${this.props.weId || ''}_vahlcb`} condition={showRight}>
          <MList
            weId={`${this.props.weId || ''}_fx3sdc`}
            showCheck
            checkboxPosition="right"
            selectedRowKeys={selectedKeys[leftActiveKey] || []}
            data={children}
            customRenderContent={this.customRenderItemRight}
            rowSelect
            onRowSelect={this.onRightRowSelect}
            stopCheckboxPropagation
            multipleCheck={multiSelect || false}
            style={{ width: '60%' }}
            className="menu-content-right"
          />
        </When>
      </div>
    );
  };

  render(): React.ReactNode {
    const { data } = this.props;
    return data.length === 1 ? this.renderSingleMenu() : this.renderMultiMenu();
  }
}
