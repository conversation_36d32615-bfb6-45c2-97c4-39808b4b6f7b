@import (reference) '../../../../../style/index.less';

//列表顶部更多按钮样式
.@{mBoardViewClsPrefix}-group-m-nav-bottom{
  border-bottom: var(--border-solid);
}
.@{mBoardViewClsPrefix}-group-m-nav{
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: var(--m-menu-top-height);
  font-size: var(--font-size-14);
  color: var(--main-fc);
  background: var(--base-white);
  &-top{
    position: relative;
    display: flex;
    flex-direction: row;
    height: var(--m-menu-top-height);
    line-height: var(--m-menu-top-height);
    box-sizing: border-box;
    &-left{
      width: 90px;
      display: inline-block;
      flex: none;
      padding-left: var(--spacing-sm);
      &-content{
        display: flex;
        .@{prefix}-group-m-nav-top-text{
          width: 66px;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          color: var(--regular-fc);
        }
        .@{prefix}-group-text-high-light,
        .@{prefix}-group-icon-high-light{
          color: var(--primary);
          .ui-icon{
            color: var(--primary);
          }
        }
      }
    }
    &-extra {
      flex: 1;
      color: var(--secondary-fc);
      justify-content: flex-end;
      .@{prefix}-group-extra-content{
        display: flex;
        flex: none;
        align-items: center;
        height: 100%;
        justify-content: flex-end;
        .ui-m-searchAdvanced {
          padding: 0 var(--h-spacing-md);
          .ui-m-searchbar .ui-m-searchbar-input {
            flex: 1 1 auto;
            display: flex;
          } 
         }
      }
      
    }
  }
}

.@{mBoardViewClsPrefix}-group-m-nav-dlg{
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    .ui-m-dialog-mask{
      z-index: 997;
    }
  
    .ui-m-dialog-wrap {
      animation-duration: initial;
      z-index: 997;
    }
  
    .ui-m-dialog-content {
        width: 100%;
        border-radius: 0;
      & > div {
        display: flex;
        flex-direction: column;
        padding: 0;
        border-radius: 0;
      }
    }
  
    .ui-m-dialog-footer {
      display: none !important;
    }

    .@{mBoardViewClsPrefix}-group-menu-content{
      display: flex;
      flex-direction: row;
      max-height: 215px;
      overflow: hidden;
      .ui-m-list-item{
        padding: 4px var(--spacing-md)   4px 0px !important;
      }
      .ui-m-list-content{
        .title {
          margin-left: var(--spacing-md) ;
          flex: 1 1;
          overflow: hidden;
          padding-right: 5px;
          font-size: var(--font-size-12);
          text-overflow: ellipsis;
        }
        .title-active{
          color: var(--primary);
        }
        .ui-icon,
        .ui-m-checkbox{
          opacity: 0;
        }
        &:after {
          background-color: transparent !important; 
        }
      }

      //选中效果
      .ui-m-list-item-checked{
        .ui-m-list-content{
          color: var(--primary);
          .ui-icon{
            opacity: 1;
          }
        }
      }
      //左右布局共存
      .menu-content-left-right{
        .ui-m-list-item-checked{
          background: transparent;
          .ui-m-list-content{
            color: var(--primary);
            border-left: 3px solid var(--primary);
            .ui-icon{
              opacity: 0;
            }
          }
        }

      }
    
      .menu-content-left{
        background: var(--m-bg-base);
      }
      .menu-content-right{
        background: var(--m-bg-base);
        .ui-m-list-item{
          background-color: transparent;
        }
      }
    }
  
}