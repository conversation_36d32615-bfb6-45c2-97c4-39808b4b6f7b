/**
 * <AUTHOR>
 * @createTime 2022-03-17
 * @desc   : h5视图的自定义分类搜索组件
 */
import { AnyObj, Icon, MDialog } from '@weapp/ui';
import { classnames, getLabel, find, forEach } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import { mBoardClsPrefix } from '../../../../../constants';
import { EtComponentKey } from '../../../../../constants/EtComponent';
import { GroupSearchField } from '../types';
import './index.less';
import Menus from './Menus';

export const groupSearchClsPrefix = `${mBoardClsPrefix}-group`;

interface TopNavProps extends Attributes {
  extraContent: React.ReactNode;
  data: GroupSearchField[];
  selectedKeys: AnyObj;
  className?: string;
  onChange: (data: any) => void;
  targetDomId: string;
  wrapClassName?: string;
  maskStyle?: {
    left: number | string;
    right: number | string;
  };
}

interface State {
  visible: boolean;
  top: number;
}

@observer
export default class TopNav extends PureComponent<TopNavProps, State> {
  constructor(props: TopNavProps) {
    super(props);
    this.state = {
      visible: false,
      top: 43,
    };
  }

  componentDidMount() {
    const navDom: any = document.getElementsByClassName(`${groupSearchClsPrefix}-m-nav`)[0];
    this.setState({ top: (navDom?.offsetTop || 0) + 43 });
  }

  toggleSiderVisible = () => {
    if (!this.state.visible) {
      const navDom: any = document.getElementsByClassName(
        `${groupSearchClsPrefix}-m-nav-${this.props.targetDomId}`,
      )[0];
      this.setState({ top: (navDom?.getBoundingClientRect()?.top || 0) + 43 });
    }
    this.setState((pre) => ({ visible: !pre.visible }));
  };

  onMenusChange = (data: any) => {
    this.props.onChange?.(data);
  };

  getMultiSelectKeys = () => {
    const { selectedKeys = {} } = this.props;
    const leftKeys: String[] = [];
    forEach(selectedKeys, (value, key) => {
      if (value && value.length > 0) {
        leftKeys.push(key);
      }
    });
    return leftKeys;
  };

  getTopShowText = () => {
    const { data, selectedKeys } = this.props;
    if (data.length === 1) {
      const {
        showName,
        listFilterDefaultSets: { listGroupSets },
        componentKey,
        fieldName,
      } = data[0];
      const selectIds = selectedKeys[fieldName] || '';
      const targetId = componentKey === EtComponentKey.GroupCustom ? 'id' : 'optionId';
      if (selectIds.length === 1) {
        const obj = find(listGroupSets, (l) => l[targetId] === selectIds[0]);
        return obj.optionName;
      }
      return showName;
    }
    return this.getMultiSelectKeys().length > 0
      ? getLabel('221902', '已设置')
      : getLabel('221903', '分类');
  };

  getDatas = () => {
    const { data = [] } = this.props;
    return data.map((d) => ({
      ...d,
      optionName: d.showName,
      children: d.listFilterDefaultSets.listGroupSets.map((o: any) => ({
        ...o,
        id: d.componentKey === EtComponentKey.GroupCustom ? o.id : o.optionId,
      })),
      multiSelect: d.listFilterDefaultSets.multiSelect,
    }));
  };

  render(): React.ReactNode {
    const { visible, top } = this.state;
    const {
      extraContent,
      className,
      selectedKeys,
      targetDomId,
      wrapClassName = '',
      maskStyle = { left: 0, right: 0 },
    } = this.props;
    const ishHight = visible || this.getMultiSelectKeys().length > 0;
    const classStr = classnames(className, {
      [`${groupSearchClsPrefix}-m-nav-bottom`]: visible,
      [`${groupSearchClsPrefix}-m-nav-${targetDomId}`]: true,
    });
    return (
      <>
        <div className={`${groupSearchClsPrefix}-m-nav ${classStr}`}>
          <div className={`${groupSearchClsPrefix}-m-nav-top`}>
            <div className={`${groupSearchClsPrefix}-m-nav-top-left`}>
              <div
                className={`${groupSearchClsPrefix}-m-nav-top-left-content`}
                onClick={this.toggleSiderVisible}
              >
                <span
                  className={classnames(`${groupSearchClsPrefix}-m-nav-top-text`, {
                    [`${groupSearchClsPrefix}-text-high-light`]: ishHight,
                  })}
                >
                  {this.getTopShowText()}
                </span>
                <span
                  className={classnames(`${groupSearchClsPrefix}-m-nav-top-icon`, {
                    [`${groupSearchClsPrefix}-icon-high-light`]: ishHight,
                  })}
                >
                  <Icon
                    weId={`${this.props.weId || ''}_3ht22m`}
                    className={`${groupSearchClsPrefix}-nav-left-icon`}
                    name={visible ? 'Icon-up-arrow01' : 'Icon-Down-arrow01'}
                  />
                </span>
              </div>
            </div>
            <div className={`${groupSearchClsPrefix}-m-nav-top-extra`}>
              <div className={`${groupSearchClsPrefix}-extra-content`}>{extraContent}</div>
            </div>
          </div>
        </div>
        <MDialog
          weId={`${this.props.weId || ''}_d9agzv`}
          visible={visible}
          footer={[]}
          mask
          top={visible ? top : '-100%'}
          maskStyle={{ top, left: maskStyle.left, right: maskStyle.right }}
          maskClosable
          onClose={this.toggleSiderVisible}
          wrapClassName={`${groupSearchClsPrefix}-m-nav-dlg ${wrapClassName}`}
        >
          <Menus
            weId={`${this.props.weId || ''}_yn1c5q`}
            data={this.getDatas()}
            selectedKeys={toJS(selectedKeys)}
            onChange={this.onMenusChange}
            leftKeys={this.getMultiSelectKeys()}
          />
        </MDialog>
      </>
    );
  }
}
