@import (reference) '../../../../style/prefix.less';

.@{mBoardViewClsPrefix} {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // align-items: flex-start;
  // justify-content: flex-start;
  overflow: hidden;
  &-header {
    width: 100%;
    height: 50px;
    flex: 0 0 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 4px;
    background: var(--base-white);
    &-search {
      width: 100%;
      height: 100%;
      flex: 1 1;
      display: flex;
      align-items: center;
      color: var(--secondary-fc);
      justify-content: flex-end;
      .ui-m-searchAdvanced {
        width: 100%;
      }
      .ui-m-searchAdvanced-onlyShowIcon {
        text-align: right;
      }
    }
  }
  &-menu-moreMenuItem {
    position: relative;
    display: flex;
    align-items: center;
    // padding: 8px;
    // min-width: 160px;
    color: var(--main-fc);
    font-size: var(--font-size-14);
    justify-content: space-between;
    flex-wrap: nowrap;
    white-space: nowrap;
    .ebcoms-assets-icon{
      position: relative;
    }
    .leftIcon {
      color: var(--secondary-fc);
      margin-right: 8px;
    }
    &-left {
      line-height: 24px;
    }
    &-right {
      color: var(--secondary-fc);
    }
  }
  &-content {
    width: 100%;
    height: 100%;
    // flex: 1 1 0;
    // padding: var(--v-spacing-md) 0;
    padding: 0 0 var(--v-spacing-md) 0;
    background: #fff;
    .swiper-container,
    .swiper-wrapper {
      height: 100%;
      background: #fff;
    }
    .swiper-slide {
      height: initial;
    }
    .swiper-slide > div {
      max-height: 100vh;
    }
    .swiper-button-prev{
      left: -5px;
    }
    .swiper-button-next{
      right: -5px;
    }
    .swiper-button-prev .ui-m-carousel-arrow-prev, .swiper-button-next .ui-m-carousel-arrow-next{
      color: var(--secondary-fc);
    }
  }
  &-board-wrapper {
    padding-bottom: 50px;
  }
  .weapp-ebdf-boardv-list {
    width: 100%;
  }
  .ui-board {
    width: 100%;
    padding: var(--v-spacing-md) var(--h-spacing-lg);
    .ui-list-body {
      width: 100%;
      .ui-board-list {
        width: 100%;
        margin: 0;
      }
    }
  }
  .@{mBoardViewClsPrefix}-bullet {
    width: 5px;
    height: 5px;
    position: relative;
    background: #bbb;
    background: var(--invalid);
    display: inline-block;
    margin: 0 3px;
    border-radius: 50%;
    background-color: var(--primary);
  }
  .@{mBoardViewClsPrefix}-bullet-active {
    background-color: var(--bubble-color);
  }
  .@{mBoardViewClsPrefix}-board-list-container {
    padding: 0 0 42px;
    height: 100%;
    width: 100%;
  }
  .@{mBoardViewClsPrefix}-board-list-wrapper {
    background: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    height: inherit;
    width: 100%;
    //border: 1px solid var(--border-color);
    .ui-m-list-body {
      overflow: auto;
    }

    .@{mBoardViewClsPrefix}-list {
      height: 0;
      flex: 1 1 0;
      width: 100%;
      &-more{
        width: 100%;
        background: transparent;
        color: #999;
        font-size: var(--font-size-12);
        margin-top: 10px;
        margin-bottom: 10px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        &.load-more-text{
          cursor: pointer;
          &:hover{
            color: var(--primary);
          }
        }
      }
      &-moreLoading{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
      }
    }
    .@{mBoardViewClsPrefix}-list-bottom {
      flex: 0 0 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      background: var(--base-white);
      border: 1px solid rgba(221,224,227,0.5);
      box-shadow: 0px 0px 4px 0px rgba(221,224,227,0.21);
      border-radius: 6px;
      font-size: var(--font-size-12);
      color: #646A73;
    }
  }
  &-board{
    &-spin-list{
      height: 100%;
      .ui-spin-nested-loading,
      .ui-spin-container {
        height: 100%;
      }
    }
  }
}

// 高级搜素panel 在body层
.@{mBoardViewClsPrefix}-searchAd-panel{
  // 隐藏高级搜索无快捷搜索有的layout
  .@{prefix}-hideLayout{
    display: none;
  }
}

.@{mBoardViewClsPrefix}-order {
  width: 100%;
  padding-left: 22px;
  padding-right: var(--v-spacing-lg);
  box-sizing: border-box;
  cursor: pointer;
  color: var(--main-fc);
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  &.selected {
    color: var(--primary);
    .@{mBoardViewClsPrefix}-order-icon {
      color: var(--primary);
    }
  }
  &-name {
    .ellipsis;
  }
  &-icon {
    color: var(--secondary-fc);
  }
  &:hover {
    color: var(--primary);
    .@{mBoardViewClsPrefix}-order-icon {
      color: var(--primary);
    }
  }
}

.@{mBoardViewClsPrefix}-card {
  background: var(--base-white);
  font-size: var(--font-size-12);
  color: var(--placeholder-fc);
  width: 100%;
  .data-temp {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-left: 4px solid #ddd;
    bottom: 0;
  }
  &-header {
    background: var(--base-white);
    // padding: 0 var(--h-spacing-lg) var(--v-spacing-sm);
    padding: 0 var(--h-spacing-lg);
    width: 100%;
    font-size: var(--font-size-14);
    color: var(--main-fc);
    font-weight: bold;
    // margin-bottom: var(--v-spacing-sm);
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-left {
      text-align: left;
      display: flex;
      flex-wrap: wrap;
      width: calc( 100% - 44px);
      &.no-desc{
        display: flex;
        align-items: center;
      }
      &-title{
        width: 100%;
        .flexCt;
        justify-content: flex-start;
        &-name, &-count{
          .ellipsis;
          max-width: 50%;
          display: inline-block;
        }
      }
      &-desc{
        color: var(--secondary-fc);
        font-size: var(--font-size-12);
        font-weight: 400;
        .ellipsis;
        max-width: 100%;
        position: relative;
        top: 4px;
        margin: 0;
      }
    }
  }
  &-main {
    position: relative;
    margin-bottom: var(--h-spacing-md);
    min-height: 50px;
    background: var(--base-white);
    &-noTemplate {
      text-align: center;
      height: 50px;
      width: 100%;
      .flexCt;
    }
    .ebcoms-list {
      background: var(--base-white);
      padding: var(--h-spacing-lg);
      border: 1px solid rgba(221,224,227,0.5);
      box-shadow: 0px 0px 4px 0px rgba(221,224,227,0.21);
      overflow: hidden;
      border-radius: 6px;
      &:hover {
        background: none;
      }
    }
    &-icon-down {
      visibility: hidden;
      position: absolute;
      right: 16px;
      top: 2px;
      z-index: 9;
      color: var(--secondary-fc);
      // transition: all 0.2s ease-in-out;
      // &:hover {
      //  visibility: visible;
      // }
    }
    //&:hover {
    //  .@{mBoardViewClsPrefix}-card-main-icon-down {
    //    visibility: visible;
    //    transition: all .3s ease-in-out;
    //  }
    //}
  }
  &-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    &-right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  &-avatar {
    &-name {
      margin-left: var(--h-spacing-sm);
    }
  }
  &-date {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: auto;
    .ui-icon {
      color: var(--placeholder-fc);
      height: 24px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
    &-val {
      margin-left: var(--h-spacing-sm);
    }
  }
}

.@{mBoardViewClsPrefix}-group-add-area {
  padding: var(--v-spacing-md);
  &-text {
    padding: var(--v-spacing-sm);
    color: var(--primary);
    text-align: left;
    .Icon-Loading{
      position: relative;
      top: -1px;
    }
  }
  &-input {
    margin-bottom: var(--h-spacing-lg);
  }
  &-btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .ui-m-btn {
      width: 40%;
      margin-left: var(--h-spacing-lg);
    }
  }
}

.weapp-ebde-glm-content {
  padding: 0;
}

// 弹框提示 在body层
.@{mBoardViewClsPrefix}-mialog-prompt{
  .ui-m-dialog-content .ui-m-dialog-top .ui-m-dialog-top-title{
    word-break: break-word;
  }
}