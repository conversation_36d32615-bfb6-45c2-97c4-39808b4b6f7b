import { Board, Icon, MenuItemData, Dialog, Spin } from '@weapp/ui';
import { getLabel, isEmpty, cloneDeep } from '@weapp/utils';
import { withRouter } from 'react-router-dom';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { Else, If, Then, When } from 'react-if';
import { ebdBClsPrefix, DEFAULT_LANE_ID } from '../../../../constants';
import { EbdBoardEventName, EbdBPlKeys } from '../../../../types/common';
import { stopPropagation, invoke } from '../../../../utils';
import { isEbFormDataV2 } from '../../../common/DataSet/utils';
import { LayoutCardWithRouter } from '../../../common/field-layout/LayoutCard';
import { PageModeType } from '../../config/cmps/types';
import BoardMenu from './BoardMenu';
import { BoardMainProps } from './types';
import { MarkItemProps } from '../../config/cmps/types';
import { renderCorner, renderVertical } from '../../config/cmps/lineMarkSet';
import excuClick from '../../../../utils/excuClick';
import BoardEmpty from '../../../common/empty';
import BoardList from './comps/boardList';
import { judgeCardCanDrag, transferCards, handleCardDragEvent } from './utils';

const { message } = Dialog;
const diffKey = 'BoardCom';

export interface BoardCardProps extends BoardMainProps {
  item: any;
  index: number;
}
const BoardCard = observer((props: BoardCardProps) => {
  const { store, item, index, compId, pluginCenter, events, config, isDesign } = props;
  const { onCardMenuClick } = store;
  const { cardDraggable, cardMenus = [], extra = {}, ...restCardData } = item;
  const { id, ...showDatas } = restCardData;
  const cornerSettingInfo: MarkItemProps[] = item.cornerSettingInfo ?? [];
  const verticalSettingInfo: MarkItemProps[] = item.verticalSettingInfo ?? [];
  const isDataTemp: boolean = extra?.data_status === '0';

  const onChange = (value: string, menuItem: MenuItemData, e: React.MouseEvent) => {
    onCardMenuClick(value, item, props, e.currentTarget as any);
  };
  const menus = cardMenus.filter((c: any) => c.visible);
  const onRowClick = (e: any) => {
    e && e.stopPropagation();
    e && e.preventDefault();
    if (isDesign) return;
    const eventGroup = toJS(config.eventGroup);
    if (isEmpty(eventGroup)) return;
    const params = {
      dom: e?.currentTarget,
      data: item,
      linkOpts: {
        fields: { ...item },
      },
    };
    events && events.emit(EbdBoardEventName.onCardClickEvt, compId, params);
    props.ebStore.eventAction.create(eventGroup).trigger('CLICKDATA', params);
    // * 后续如果还是有问题 就走下面的excuClick
    // excuClick({
    //   data: item,
    //   dom: e?.currentTarget,
    //   id,
    //   eventAction: props.ebStore.eventAction,
    //   eventGroup: eventGroup || [],
    //   plugin: pluginCenter,
    // });
  };
  const renderContent = () => {
    const renderCard = (_config: any, data: any, props: BoardCardProps) => {
      return <LayoutCardWithRouter weId={`${props.weId || ''}_qaani9`} {...props} data={data} config={_config.card} sort={index} client="PC" compId={compId} dataset={_config?.dataset!} />;
    };
    return (
      <div className={`${ebdBClsPrefix}-card`} key={id} onClick={onRowClick}>
        {/* 竖线 */}
        {renderVertical(verticalSettingInfo)}
        <div className={`${ebdBClsPrefix}-card-main`}>
          <When weId={`${props.weId || ''}_ooibjo`} condition={isDataTemp}>
            <div className="data-temp" title={getLabel('55840', '暂存')} />
          </When>
          <When weId={`${props.weId || ''}_ts87zf`} condition={menus.length}>
            <div onClick={stopPropagation}>
              <BoardMenu
                weId={`${props.weId || ''}_upnqei`}
                selectIcon="Icon-more-o"
                data={menus}
                onChange={onChange}
                className={`${ebdBClsPrefix}-card-main-icon-down`}
                customSelectContent={<Icon weId={`${props.weId || ''}_lc4xlo`} name="Icon-Down-arrow01" />}
                isCard
              />
            </div>
          </When>
          <When weId={`${props.weId || ''}_wmpb9m`} condition={isEmpty(showDatas)}>
            <span className={`${ebdBClsPrefix}-card-main-noTemplate`}>{getLabel('105950', '未设置卡片显示字段')}</span>
          </When>
          <When weId={`${props.weId || ''}_uhhp8u`} condition={!isEmpty(showDatas)}>
            {invoke(pluginCenter, EbdBPlKeys.renderCardBefore, {
              args: [item],
            })}
            {invoke(pluginCenter, EbdBPlKeys.renderCardContent, {
              args: [config, toJS(item), props],
              hook: renderCard,
            })}
            {invoke(pluginCenter, EbdBPlKeys.renderCardAfter, {
              args: [item],
            })}
          </When>
        </div>
        {/* 角标 */}
        {renderCorner(cornerSettingInfo)}
      </div>
    );
  };
  return invoke(pluginCenter, EbdBPlKeys.renderCard, {
    // 默认视图
    hook: renderContent,
    args: [
      {
        item,
        onRowClick,
        onBoardMenuClick: onChange,
        boardMenus: menus,
      },
    ],
  });
});

@observer
class BoardCom extends React.PureComponent<BoardMainProps, any> {
  customCardRender = (item: any, index: number) => <BoardCard weId={`${this.props.weId || ''}_vvihe8`} {...this.props} item={item} index={index} />;
  // 点击加载更多
  renderClickMore = (item: any, pageInfo: any) => {
    const { store, laneGroup } = this.props;
    // 渲染加载更多
    // 点击加载更多模式 无数据、无更多、数量少于分页不显示
    const { hasMore, isLoading, isMoreLoading, current } = pageInfo;
    return (
      <div className="ignore-elements">
        <When weId={`${this.props.weId || ''}_8ul5ev`} condition={!isLoading}>
          <If weId={`${this.props.weId || ''}_rl99dy`} condition={hasMore}>
            <Then weId={`${this.props.weId || ''}_l6apye`}>
              <If weId={`${this.props.weId || ''}_rl99dy`} condition={isMoreLoading}>
                <Then weId={`${this.props.weId || ''}_bvte58`}>
                  <div className={`${ebdBClsPrefix}-board-loadMore`}>
                    <Spin weId={`${this.props.weId || ''}_nuw03e`} size="small" style={{ marginRight: '8px' }} />
                    {getLabel('116168', '加载中...')}
                  </div>
                </Then>
                <Else weId={`${this.props.weId || ''}_u3b41p`}>
                  <div className={`${ebdBClsPrefix}-list-more load-more-text`} onClick={() => store.toLoadMore(item.id, item.laneId)}>
                    <span>{getLabel('76148', '加载更多')}</span>
                    <Icon weId={`${this.props.weId || ''}_yowfvr`} name="Icon-Down-arrow01" />
                  </div>
                </Else>
              </If>
            </Then>
            <Else weId={`${this.props.weId || ''}_0u7e14`}>
              {current > 1 ? (
                <div className={`${ebdBClsPrefix}-list-more`}>
                  <span>{getLabel('82180', '已加载所有数据')}</span>
                </div>
              ) : null}
            </Else>
          </If>
        </When>
      </div>
    );
  };
  getListOptions = () => {
    const { pageMode, isLaneConfig, hasDragCardInGroupRight } = this.props.store;
    const [_pageMode] = invoke(this.props.pluginCenter, EbdBPlKeys.getPageMode) || [pageMode];
    const { singlePageData = [], isMobile, config, groups } = this.props;
    const boardData = isMobile ? [singlePageData] : groups;
    const pageKey = isLaneConfig ? 'laneBoardPageInfo' : 'paginationData';
    const _paginationData = toJS(this.props.store[pageKey]);
    if (isEmpty(boardData) || isEmpty(_paginationData)) return [];
    const ListOptions = boardData.map(i => {
      const idKey = isLaneConfig ? `${i.laneId}_${i.id}` : i.id;
      const { total = 0, pageSize = 0, isMoreLoading, hasMore } = _paginationData[idKey] || {};
      const [_pageSize] = invoke(this.props.pluginCenter, EbdBPlKeys.getPageSize) || [pageSize];
      const getExtraEle = () => {
        if (total <= _pageSize) return null;
        if (_pageMode === PageModeType.More) {
          return this.renderClickMore(i, _paginationData[idKey]);
        } else {
          if (isMoreLoading) {
            return (
              <div className={`${ebdBClsPrefix}-list-moreLoading`}>
                <Spin weId={`${this.props.weId || ''}_59y7v1@${diffKey}`} size="small" />
              </div>
            );
          }
          if (!hasMore) {
            return (
              <div className={`${ebdBClsPrefix}-list-more`}>
                <span>{getLabel('82180', '已加载所有数据')}</span>
              </div>
            );
          }
        }
        return null;
      };
      return {
        id: i.id,
        sortable: hasDragCardInGroupRight, // 以最小维度的权限来判断
        extraEle: getExtraEle(),
      };
    });
    return ListOptions;
  };
  customListRender = (item: any, children: any) => {
    return <BoardList weId={`${this.props.weId || ''}_3jt101`} item={item} children={children} parentProps={this.props} />;
  };
  onSortSatrt = (evt: any) => {
    const { config, ebStore, groups, pluginCenter } = this.props;
    const { eventGroup } = config;
    // 匹配执行事件动作及二开
    handleCardDragEvent(evt, 'beforeDragEvent', groups, ebStore, eventGroup, pluginCenter);
  };
  onSortEnd = (evt: any) => {
    const { groups, laneGroup, config, ebStore, store } = this.props;
    const { eventGroup } = config;
    const { onCardSortEnd, groupMenuData } = store;
    const _menuData = toJS(groupMenuData[laneGroup?.id!]),
      fromListId = evt?.from?.dataset?.id,
      toListId = evt?.to?.dataset?.id;
    const fromInfo = { groupId: fromListId, startIndex: evt?.oldIndex };
    const toInfo = { groupId: toListId, endIndex: evt?.newIndex };
    const data = cloneDeep(toJS(groups));
    // 这种数据没拖拽 直接return
    if (!(data && data.length > 0) || (fromListId === toListId && fromInfo.startIndex === toInfo.endIndex)) return;
    const targetGroup = data.find((i: any) => i.id === toListId);
    const fromGroup = data.find((i: any) => i.id === fromListId);
    // 权限校验 判断下可移动分组 无可移动分组不准移动
    const dragValidInfo = judgeCardCanDrag(data, _menuData, fromGroup, targetGroup);
    const targetName = targetGroup?.name?.nameAlias || targetGroup?.name;
    const fromName = fromGroup?.name?.nameAlias || fromGroup?.name;
    if (!dragValidInfo.hasPerm) {
      message({ type: 'info', content: `${getLabel('280940', '暂无权限拖拽到分组')}：${targetName}` });
      // 不合法，恢复原位
      evt.from.insertBefore(evt.item, evt.from.children[evt.oldIndex]);
      return false;
    }
    if (dragValidInfo.isToFc || dragValidInfo.isFromFc) {
      if (dragValidInfo.isToFc) {
        message({
          type: 'info',
          content: `${getLabel('288061', '无法拖拽到该分组，已封存')}：${targetName}`,
        });
      } else if (dragValidInfo.isFromFc) {
        message({
          type: 'info',
          content: `${getLabel('288160', '当前分组无法拖拽到其他分组，已封存')}：${fromName}`,
        });
      }
      // 不合法，恢复原位
      evt.from.insertBefore(evt.item, evt.from.children[evt.oldIndex]);
      return false;
    }
    const currentGroups = transferCards(fromInfo, toInfo, data, evt?.item);
    onCardSortEnd(currentGroups, evt.item.id, fromInfo, toInfo);
    // 匹配执行事件动作及二开
    handleCardDragEvent(evt, 'afterDragEvent', groups, ebStore, eventGroup);
  };

  render() {
    const { onListSortEnd, onCardClick, groupRefreshKey, loading, isHideNoDataGroup, isHideNoDataGroupLoading, hasDragCardInGroupRight } = this.props.store;
    const { singlePageData = [], isMobile, config, isDesign, pluginCenter, groups, laneGroup } = this.props;
    const boardData = isMobile ? toJS([singlePageData]) : toJS(groups);
    // if (loading) return <div />;
    if (isHideNoDataGroup && isHideNoDataGroupLoading && !isDesign) return <div />;
    if (isEmpty(boardData) && !loading) {
      return <BoardEmpty weId={`${this.props.weId || ''}_0bxqh0`} iconSize={laneGroup && laneGroup?.id !== DEFAULT_LANE_ID ? { width: 60, height: 60 } : { width: 130, height: 130 }} />;
    }
    // 是否能拖拽列表内的卡片（暂时只有eb表单数据源允许）
    // 1201基线新增限制条件分组拖拽
    // const draggable = isEbFormDataSet && !isDesign && !isFilterMode;
    const draggable = hasDragCardInGroupRight && !isDesign;
    const [_draggable] = invoke(pluginCenter, EbdBPlKeys.getCardDraggable, {
      args: [config],
    }) || [draggable];
    const cardSortableOptions = {
      filter: '.ignore-elements',
      // preventOnFilter: true,
      delay: 50,
      // 允许拖拽的项目类名
      draggable: _draggable ? `.ui-board-drag-handle` : `.${ebdBClsPrefix}-card-disabled-drag`,
      onEnd: this.onSortEnd,
      onStart: this.onSortSatrt,
    };
    return (
      <Board
        weId={`${this.props.weId || ''}_3fjzq1`}
        data={boardData}
        key={groupRefreshKey}
        customCardRender={this.customCardRender}
        customListRender={this.customListRender}
        onListSortEnd={onListSortEnd}
        onCardClick={onCardClick}
        // eslint-disable-next-line weapp/jsx-no-raw-object
        CardSortableOptions={cardSortableOptions}
        // eslint-disable-next-line weapp/jsx-no-raw-object
        SortableOptions={{
          handle: `.${ebdBClsPrefix}-drag-handle`,
          delay: 100,
          dragClass: `${ebdBClsPrefix}-container-main-boardDragList`,
        }}
        lockEl="is-lock"
        ListOptions={this.getListOptions()}
      />
    );
  }
}

export default withRouter(BoardCom);
