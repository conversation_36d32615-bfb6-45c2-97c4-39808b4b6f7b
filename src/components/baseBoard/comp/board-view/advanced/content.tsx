import { SearchAdvanced, SearchDatas } from '@weapp/ui';
import React from 'react';
import { ebdBClsPrefix, viewHeaderClsPrefix, EbuilderFormModule } from '../../../../../constants';

interface IProps {
  weId?: string;
  isEbFormDataSet: boolean; // 是否是表单数据源
  onSearch?: (data: SearchDatas, dataSource: string, otherParams?: any) => void;
  oldSearchStore: any;
  newSearchStore: any;
}
// 兼容处理 eb/表单数据源走老的逻辑 其余数据源走我这边新迁移的逻辑 后续应该是统一替换为新迁移的
const BoardAdvancedContent: React.FC<IProps> = ownProps => {
  const { onSearch, isEbFormDataSet, newSearchStore, oldSearchStore } = ownProps;
  const { conditionId, props, searchSetting } = newSearchStore;
  if (isEbFormDataSet) {
    const adQuickParams = oldSearchStore?.adQuickParams || {}
    return (
      <SearchAdvanced
        weId={`${ownProps.weId || ''}_exvuir`}
        onVisibleChange={oldSearchStore?.onVisibleChange}
        className={`${viewHeaderClsPrefix}-head-adsearch`}
        conditionId={conditionId}
        searchAdvancedStore={oldSearchStore?.searchAdvancedStore}
        onSearch={onSearch}
        module={EbuilderFormModule}
        // disableCommonFilter={adQuickParams?.quickSearchInputProps?.suffix === null}
        {...adQuickParams}
        onlyShowIcon={!searchSetting?.hasQuick}
        disableCommonFilter={!searchSetting?.hasAd}
        requestHeaderParams={{ ebBusinessId: props.pageId }}
      />
    );
  }
  return (
    <div>
      <SearchAdvanced
        weId={`${ownProps.weId || ''}_exvuir`}
        className={`${viewHeaderClsPrefix}-head-adsearch`}
        conditionId={conditionId}
        searchAdvancedStore={newSearchStore?.searchAdvancedStore}
        onSearch={onSearch}
        module={newSearchStore?.comServicePath}
        requestHeaderParams={{ ebBusinessId: props.pageId }}
        onlyShowIcon={!searchSetting?.hasQuick}
        disableCommonFilter={!searchSetting?.hasAd}
        onVisibleChange={newSearchStore?.onVisibleChange}
      />
    </div>
  );
};

export default BoardAdvancedContent;
