import { SearchAdvanced, SearchDatas, CorsComponent } from '@weapp/ui';
import React from 'react';
import { toJS } from 'mobx';
import { ebdBClsPrefix, viewHeaderClsPrefix, EbuilderFormModule } from '../../../../../constants';

interface IProps {
  weId?: string;
  isEbFormDataSet: boolean; // 是否是表单数据源
  onSearch?: (data: SearchDatas, dataSource: string, otherParams?: any) => void;
  compId: string;
  dataset: any;
  config: any;
  page: any;
  events: any;
}
const BoardSearchQuick: React.FC<IProps> = ownProps => {
  const { dataset, compId, config, page, events } = ownProps;
  const searchCondition = config?.filter || {};
  return (
    <CorsComponent
      weId={`kfquqy`}
      app="@weapp/ebdcontainercoms"
      compName="SearchConidtions"
      comName="SearchQuick"
      comProps={{
        config: {
          searchConditions: toJS(searchCondition),
          dataset,
        },
        id: compId,
        page,
        pageId: page?.id,
        events: events,
      }}
      searchConfigKey="searchCondition"
      // isPreView={true}
    />
  );
};
export default BoardSearchQuick;
