import { SearchAdvanced, SearchDatas, FormSwitchProps } from '@weapp/ui';
import React from 'react';
import { EbuilderFormModule } from '../../../../../constants';

const { Panel } = SearchAdvanced;
interface IProps {
  weId?: string;
  isEbFormDataSet: boolean; // 是否是表单数据源
  onSearch?: (data: SearchDatas, dataSource: string, otherParams?: any) => void;
  oldSearchStore: any;
  newSearchStore: any;
  customRenderFormSwitch?: (id: string, props: FormSwitchProps) => React.ReactNode;
  otherParams?: any
}
// 兼容处理 eb/表单数据源走老的逻辑 其余数据源走我这边新迁移的逻辑 后续应该是统一替换为新迁移的
const BoardAdvancedPanel: React.FC<IProps> = ownProps => {
  const { oldSearchStore, newSearchStore, onSearch, isEbFormDataSet, customRenderFormSwitch, otherParams } = ownProps;
  const { conditionId, props } = newSearchStore;
  if (isEbFormDataSet) {
    const {searchAdvancedStore, onVisibleChange, onReset, onCancel} = oldSearchStore
    return (
      <Panel
        mode="normal"
        weId={`${props.weId || ''}_tms7vd`}
        onVisibleChange={onVisibleChange}
        defaultVisible={false}
        needRange
        onSearch={onSearch}
        onReset={onReset}
        onCancel={onCancel}
        colSpan={12}
        conditionId={conditionId}
        searchAdvancedStore={searchAdvancedStore}
        module={EbuilderFormModule}
        formProps={{ customRenderFormSwitch }}
        {...otherParams}
        requestHeaderParams={{ ebBusinessId: props?.pageId }}
      />
    );
  }
  return (
    <div>
      <Panel
        mode="normal"
        weId={`${props.weId || ''}_tms7vd`}
        onVisibleChange={newSearchStore?.onVisibleChange}
        defaultVisible={false}
        needRange
        onSearch={newSearchStore.onAdSearch}
        onReset={newSearchStore.onReset}
        onCancel={() => newSearchStore.onVisibleChange(false)}
        colSpan={12}
        conditionId={conditionId}
        searchAdvancedStore={newSearchStore.searchAdvancedStore}
        module={newSearchStore.comServicePath}
        formProps={{ customRenderFormSwitch }}
        requestHeaderParams={{ ebBusinessId: props?.pageId }}
      />
    </div>
  );
};

export default BoardAdvancedPanel;
