import { Loadable } from '@weapp/ui';
import { ComponentType } from 'react';
import { ebdBoardAppName } from '../../../../../constants';

export const BoardAdvancedView = Loadable({
  appName: ebdBoardAppName,
  name: 'BoardAdvancedView',
  loader: () => import(
    /* webpackChunkName: "coms_ebdboard_AdvancedView" */
    './content'
  ),
}) as ComponentType<any>;

export const BoardAdvancedPanelView = Loadable({
  appName: ebdBoardAppName,
  name: 'BoardAdvancedPanelView',
  loader: () => import(
    /* webpackChunkName: "coms_ebdboard_AdvancedPanelView" */
    './panel'
  ),
}) as ComponentType<any>;


export const BoardSearchQuick = Loadable({
  appName: ebdBoardAppName,
  name: 'BoardSearchQuick',
  loader: () => import(
    /* webpackChunkName: "coms_ebdboard_searchQuick" */
    './searchQuick'
  ),
}) as ComponentType<any>;