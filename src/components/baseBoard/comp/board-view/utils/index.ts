import { isEmpty, isEqual } from '@weapp/utils';
import { GroupItem } from '../../../../common/board/types';
import { invoke } from '../../../../../utils';
import { EbdBPlKeys } from '../../../../../types/common';

export const judgeCardCanDrag = (groups: any[], groupMenu: any = [], fromGroup: any, targetGroup: any) => {
  const fromId = fromGroup.id;
  const toId = targetGroup.id;
  // 内部排序默认放行
  if (fromId === toId)
    return {
      hasPerm: true,
      isFc: false,
    };
  const fromGroupMenu = groupMenu[fromId];
  if (!fromGroupMenu)
    return {
      hasPerm: false,
      isToFc: false,
      isFromFc: false,
    };

  const moveDataList = fromGroupMenu.find((item: any) => item.key === 'moveData')?.children || [];
  return {
    hasPerm: moveDataList.some((i: any) => i.key.includes(toId)), // 是否有权限
    isToFc: targetGroup.archive === '1', // 是否目标看板是封存的
    isFromFc: fromGroup.archive === '1', // 是否来源看板是封存的
  };
};
// 定义类型接口
interface Card {
  id: string;
  [key: string]: any;
}

// 重新排序数组工具函数
const reorder = <T>(list: T[], startIndex: number, endIndex: number): T[] => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);
  return result;
};

export const transferCards = (fromInfo: any, toInfo: any, boardData: GroupItem[], item: Card): GroupItem[] => {
  const isSameGroup = fromInfo.groupId === toInfo.groupId;

  // 获取源组和目标组
  const getGroup = (groupId: string): GroupItem => {
    const group = boardData.find(g => g.id === groupId);
    if (!group) {
      throw new Error(`Group with id ${groupId} not found`);
    }
    return {
      ...group,
      cards: Array.isArray(group.cards) ? [...group.cards] : [],
    };
  };

  // 处理同组内拖拽
  if (isSameGroup) {
    const targetGroup = getGroup(toInfo.groupId);
    const newCards = reorder(targetGroup.cards, fromInfo.startIndex!, toInfo.endIndex);

    return boardData.map(group => (group.id === toInfo.groupId ? { ...group, cards: newCards } : { ...group }));
  }

  // 处理跨组拖拽
  const sourceGroup = getGroup(fromInfo.groupId);
  const targetGroup = getGroup(toInfo.groupId);

  // 查找要移动的卡片
  const moveItem = sourceGroup.cards.find(card => card.id === item.id);
  if (!moveItem) {
    throw new Error(`Card with id ${item.id} not found in source group`);
  }

  // 从源组移除卡片
  sourceGroup.cards.splice(fromInfo.startIndex!, 1);
  // 添加到目标组
  targetGroup.cards.splice(toInfo.endIndex, 0, moveItem);

  return boardData.map(group => {
    if (group.id === sourceGroup.id) return sourceGroup;
    if (group.id === targetGroup.id) return targetGroup;
    return group;
  });
};

export const isStatisticsNeedRefresh = (prevProps: any, props: any) => {
  //eBFilterData值不相等，新增统计改变筛选、删除了含有筛选；改变开关；才更新
  const { statisticsItem = [], openValue } = props?.config?.statistics || {};
  const { statisticsItem: _statisticsItem = [], openValue: _openValue } = prevProps?.config?.statistics || {};
  let isStatisticsEmpty =
    Object.keys(prevProps?.config?.statistics || {}).length === 0 || Object.keys(props?.config?.statistics || {}).length === 0;
  let eBFilterDataChange = false;
  let idS = statisticsItem.map((el: any) => el.id) || [];
  let _idS = _statisticsItem.map((el: any) => el.id) || [];

  for (let j = 0; j < _statisticsItem.length; j++) {
    const { eBFilterData: _eBFilterData, id: _id = '' } = _statisticsItem?.[j] || {};
    //删除到最后一个统计，且筛选不为空，不刷新
    if (idS.length === 0 && !isEmpty(_eBFilterData)) {
      eBFilterDataChange = true;
      break;
    }

    for (let i = 0; i < statisticsItem.length; i++) {
      const { eBFilterData = {}, id = '' } = statisticsItem?.[i] || {};
      if (
        (openValue && id === _id && !isEqual(eBFilterData, _eBFilterData)) ||
        (!_idS.includes(id) && !isEmpty(eBFilterData)) ||
        (!idS.includes(_id) && !isEmpty(_eBFilterData))
      ) {
        eBFilterDataChange = true;
        break;
      }
    }
  }
  return (eBFilterDataChange || openValue !== _openValue) && !isStatisticsEmpty;
};

export const handleCardDragEvent = (evt: any, type: string, groups: any[], ebStore: any, eventGroup?: any[][], pluginCenter?: any) => {
  const id = evt?.item?.id;
  const fromListId = evt?.from?.dataset?.id;
  const item = (groups.find((i: any) => i.id === fromListId) || { cards: [] }).cards.find((i: any) => i.id === id);
  if (!item) {
    return;
  }
  if (eventGroup && ebStore) {
    const params = {
      dom: evt?.target,
      data: item,
      linkOpts: {
        fields: { ...item },
      },
    };
    const eventAction = ebStore?.eventAction?.create?.(eventGroup);
    eventAction?.trigger?.(type, params);
  }
  if (pluginCenter) {
    invoke(pluginCenter, EbdBPlKeys[type as keyof typeof EbdBPlKeys], {
      args: [item, evt],
    });
  }
};
