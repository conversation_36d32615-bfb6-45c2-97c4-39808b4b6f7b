/**
 * <AUTHOR>
 * @createTime 2022-02-21
 */
import { Menu, MenuItemData, MenuProps, CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react';
import React from 'react';
import { ebdBClsPrefix } from '../../../../constants';
import './index.less';

export interface GroupMenuProps extends MenuProps {
  data: MenuItemData[];
  onChange?: (value: string, item: any, e: React.MouseEvent) => void;
  selectIcon?: string;
  customRenderItem?: (prop: any) => React.ReactElement;
  isCard?: boolean
}

const BoardMenu: React.ComponentType<GroupMenuProps & React.Attributes> = observer(props => {
  const { data, onChange, isCard } = props;
  const customRenderItem = (itemData: MenuItemData & { iconPath?: string; onlyIcon?: boolean }) => {
    let iconPath = itemData.iconPath ? JSON.parse(itemData.iconPath) : '';
    return (
      <div className={`${ebdBClsPrefix}-list-title-groupMenu`}>
        {!!iconPath && (
          <CorsComponent
            weId={`${props.weId || ''}_mq4b2m`}
            app="@weapp/ebdcoms"
            compName="AssetsItem"
            path=""
            style={{
              borderRadius: 'var(--border-radius-xs)',
            }}
            size="s"
            {...iconPath}
          />
        )}
        {!itemData.onlyIcon && <span style={{ textAlign: iconPath ? 'left' : 'center' }}>{itemData.content}</span>}
      </div>
    );
  };
  return (
    <Menu
      {...props}
      weId={`${props.weId || ''}_rc34ez`}
      data={data}
      type="select"
      selectType="iconOverlay"
      triggerProps={{
        popupPlacement: 'bottomRight',
        popupClassName: `board-groupMenu-select-overlay ${isCard ? 'card' : ''}`,
      }}
      childTriggerProps={{
        popupPlacement: 'rightTop',
        popupClassName: 'board-groupMenu-select-overlay-child',
      }}
      onChange={onChange}
      customRenderItemContent={customRenderItem}
    />
  );
});

export default BoardMenu;
