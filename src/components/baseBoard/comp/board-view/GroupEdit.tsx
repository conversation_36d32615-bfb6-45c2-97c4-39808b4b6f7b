/**
 * <AUTHOR>
 * @createTime 2022-02-20
 */
import {
  Button, Dialog, FormStore, Spin,
} from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import { dlgIconName, KanBanDefaultBgColor } from '../../../../constants';
import BaseFormDom from './form-base';

export interface GroupEditProps extends React.Attributes {
  visible: boolean;
  datas?: any;
  onOk: (params: any) => void;
  onCancel: () => void;
  ebBusinessId: string;
  boardColorStyle?: string;
}

const GroupEdit = observer((props: GroupEditProps) => {
  const defaultColor = KanBanDefaultBgColor
  const {
    visible = false, onCancel, onOk, datas, ebBusinessId, boardColorStyle,
  } = props;
  const [store] = useState(() => new FormStore());
  const [loading, setLoading] = useState(false);
  const [color, setColor] = useState(defaultColor);

  useEffect(() => {
    if (!visible) {
      store.resetForm();
    }
    if (visible && datas.color) {
      onColorChange(datas.color);
    }
    return () => {
      setColor(defaultColor);
    };
  }, [visible, store]);

  const onColorChange = (colorVal: any) => {
    const _color = `${colorVal}`.toLowerCase() === 'transparent' ? defaultColor : `${colorVal}`.toLowerCase();
    setColor(_color);
  };

  const onSave = () => {
    store.validate().then(async (err: any) => {
      if (isEmpty(err.errors)) {
        try {
          setLoading(true);
          const formdata = store.getFormDatas() || {};
          await onOk({ ...formdata, color });
        } finally {
          setLoading(false);
          setColor(defaultColor);
        }
      }
    });
  };

  const buttons = [
    <Button
      weId={`${props.weId || ''}_hle559@${0}`}
      key="save"
      type="primary"
      onClick={onSave}
      disabled={loading}
    >
      {getLabel('40496', '保存')}
    </Button>,
    <Button
      weId={`${props.weId || ''}_sm5lt0@${1}`}
      key="onCancel"
      onClick={onCancel}
      disabled={loading}
    >
      {getLabel('53937', '取消')}
    </Button>,
  ];

  const title = datas?.id ? getLabel('92459', '编辑分组') : getLabel('221899', '新建分组');
  return (
    <Dialog
      weId={`${props.weId || ''}_p118uo`}
      footer={buttons}
      width={500}
      destroyOnClose
      visible={visible}
      title={title}
      mask
      closable
      onClose={onCancel}
      icon={dlgIconName}
    >
      <Spin weId={`${props.weId || ''}_euvsko`} spinning={loading}>
        <BaseFormDom
          weId={`${props.weId || ''}_ujjzuo`}
          ebBusinessId={ebBusinessId}
          datas={datas}
          store={store}
          color={color}
          boardColorStyle={boardColorStyle}
          onColorChange={onColorChange}
        />
      </Spin>
    </Dialog>
  );
});

export default GroupEdit;
