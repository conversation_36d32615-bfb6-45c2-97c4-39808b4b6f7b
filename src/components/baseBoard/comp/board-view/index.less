@import (reference) '../../../../style/prefix.less';

.ebcom-design {
  .@{ebdBClsPrefix}-content {
    overflow: scroll;
    .ui-scroller__bar {
      opacity: 1;
      pointer-events: all;
    }
  }
}
.ebcom-kanban{
  &.ebcom{
    padding: 0 !important;
    &:not(.ebcom-grid)>.content{
      padding: 0;
    }
  }
  // 表单高级视图来的--最大高度限制
  .content{
    .fromEbuilder{
      .@{ebdBClsPrefix}-container-main{
        max-height: calc(100vh - 32px);
      }
      .@{ebdBClsPrefix}-filter-content {
        .weapp-ebdf-filter-search-content{
          padding-bottom: 4px;
        }
      }
    }
  }
  .header {
    border-bottom: 1px solid rgba(242,242,242,1) !important;
    & + .content{
      // 表单高级视图来的--带标题栏的最大高度限制
      .fromEbuilder{
        .@{ebdBClsPrefix}-container-main{
          max-height: calc(100vh - 32px - 40px);
        }
      }
    }
  }
  &-isDoc {
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: transparent;
    z-index: 999;
  }
}
// 解决触控板无法横向滚动
.@{ebdBClsPrefix}-content {
  .ui-scroller__wrap {
    overflow-x: auto !important;
  }
}
.@{ebdBClsPrefix}-left-right-layout-right{
  display: flex;
  flex-direction: column;
}
.@{ebdBClsPrefix}-container {
  width: 100%;
  height: 100%;
  // background: var(--base-white);
  padding-top: 0px;
  // 在容器内
  &.inContainer{
    .weapp-ebdboard-container-main{
      min-height: 500px;
    }
  }
  // 表单高级视图来的
  &.fromEbuilder{
    padding: 15px;
    .@{ebdBClsPrefix}-list-body{
      max-height: calc(100% - 40px);
      &.noTitle{
        max-height: 100%;
      }
      &-hasAdd{
        &.noTitle{
          max-height: calc(100% - 60px);
        }
        max-height: calc(100% - 92px);
      }
    }
  }
  // 在泳道看板中
  &.isLane{
    .@{ebdBClsPrefix}-filter-content{
      padding: 0 16px;
    }
  }
  &-fullLoading, .ui-spin-dot{
    display: flex;
    justify-content: center;
    align-self: center;
  }
  &-main{
    height: 100%;
    width: 100%;
    display: flex;
    &-boardDragList {
      background-color: #fff;
    }
    // // 存在上下默认8px*2的边距 需要去掉
    // .ui-list-scrollview{
    //   height: calc(100% - 16px);
    // }
  }
  .@{ebdBClsPrefix}-wrapper {
    width: 100%;
    height: 100%;
    flex-direction: column;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    .ui-list-scrollview-wrap {
      height: 100%;
    }
  }
  .@{ebdBClsPrefix}-header {
    height: 0;
    flex: 0 0 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: var(--spacing-sm) var(--h-spacing-lg);
    padding: 0 var(--h-spacing-lg);
    width: 100%;
    font-size: var(--font-size-14);
    color: var(--main-fc);
    font-weight: bold;
    border-bottom: 1px solid transparent;
    margin-bottom: 10px;
    &-order {
      margin-left: var(--h-spacing-md);
    }
    &-left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 60%;
      flex: 0 0 60%;
      &-title {
        margin-right: var(--h-spacing-lg);
        width: 100%;
      }
      .ui-menu{
        height: 50px;
        padding-left: 0;
        .ui-menu-tab-top-container{
        height: 100%;
        bottom: 0;
        .ui-menu-list{
          float: left;
          .ui-menu-list-item{
            height: 50px;
            font-weight: normal;
          }
        }
      }
      }
    }
    &-right {
      width: 0;
      flex: 1 1 0;
      text-align: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      min-height: 30px;
      .@{ebdBClsPrefix}-view-header-head-adsearch {
        max-width: 200px;
        margin-left: var(--h-spacing-lg);
      }
    }
  }
  .@{ebdBClsPrefix}-filter-content {
    // margin-top: var(--v-spacing-lg);
    .weapp-ebdf-filter-search-content{
      padding-bottom: 0;
    }
    .@{ebdBClsPrefix}-filter-search .@{ebdBClsPrefix}-filter-search-content {
      padding-bottom: 0;
    }
  }
  .@{ebdBClsPrefix}-content {
    width: 100%;
    overflow: auto;
    flex: 1 1 0;
    .ui-board-list{
      margin: 0 8px 0 0;
    }
    .ui-board-drag-ghost:before{
      background:#F4F5DA !important;
    }
    & > .ui-list-scrollview-wrap > .ui-scroller > .ui-scroller__wrap > .ui-scroller__view {
      height: 100%;
    }
    .ui-board {
      padding: 0;
    }

    // 高级搜素panel
    // 隐藏高级搜索无快捷搜索有的layout
    .@{ebdBClsPrefix}-hideLayout{
      display: none;
    }
    .weapp-ebdf-personSelect{
      align-items: flex-start;
    }
    .ui-searchAdvanced-condition-container{
      margin-bottom: 12px;
    }

    .ui-searchAdvanced-condition{
      // 日期层级平铺
      .ui-date-menu{
        flex-wrap: wrap;
        &>.ui-date-picker{
          margin-bottom: 5px;
          width: auto !important;
          max-width: 100%;
          flex: 1;
        }
        &>.ui-scroller{
          &>.ui-scroller__wrap{
            margin-bottom: 0 !important;
          }
          .ui-date-menu-group-content{
            margin: 0;
          }
        }
      }
    }
  }
}
//ie下容器内看板无法撑开 设置默认最小高度
  .weapp-ie11{
    .ebcom-gridcontainer{
      .@{ebdBClsPrefix}-container{
        min-height: 600px;
      }
    }
  }

.@{ebdBClsPrefix}-list-empty {
  height: 100%;
  // background: var(--bg-base);
  min-height: 150px;
  max-height: unset !important;
  flex: 1 !important;
  .ui-scroller__wrap{
    overflow: initial !important;
  }
  .ui-spin {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: -50px;
  }
  &-placeholder {
    height: 100%;
    .ui-board-list-cards
    > .ui-list-scrollview-wrap
    > .ui-scroller
    > .ui-scroller__wrap
    > .ui-scroller__view {
      height: 100%;
      .ui-list-body {
        background: transparent;
      }
    }
  }
  &-icon {
    position: absolute;
    // top: 100px; //暂无数据居中处理
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    bottom: 40px;
    overflow: hidden;
    .flexCt;
    flex-direction: column;
  }
}

.@{ebdBClsPrefix}-order {
  width: 100%;
  height: calc(var(--hd) * 30);
  line-height: calc(var(--hd) * 30);
  padding-left: 22px;
  padding-right: var(--v-spacing-lg);
  box-sizing: border-box;
  cursor: pointer;
  color: var(--main-fc);
  position: relative;
  user-select: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  &.selected {
    color: var(--primary);
    .@{ebdBClsPrefix}-order-icon {
      color: var(--primary);
    }
  }
  &-name {
    margin-right: var(--h-spacing-sm);
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &-icon {
    color: var(--secondary-fc);
  }
  &:hover {
    color: var(--primary);
    .@{ebdBClsPrefix}-order-icon {
      color: var(--primary);
    }
  }
}

.@{ebdBClsPrefix}-list {
  // border: 1px solid var(--border-color);
  width: 280px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  border-radius: var(--border-radius-xs);
  cursor: auto;
  .ui-list-scrollview{
    overflow: inherit;
  }
  .ignore-elements {
    cursor: default;
  }
  &.has-custom-bg{
    padding: 0 4px;
  }
  // 新建分组
  &-add {
    &.outer{
      .blur-input-content{
        width: 100%;
      }
    }
    .blur-input-content{
      font-size: var(--font-size-14);
      color: #646A73;
      font-weight: 400;
      .blur-input-wrapper{
        cursor: pointer !important;
        display: flex;
        width: 100%;
      }
      &-addIcon{
        margin-right: 4px;
        .ui-icon{
          position: relative;
          top: -2px;
        }
      }
      &-text{
        width: 100%;
        height: 30px;
        line-height: 30px;
        &-hide{
          opacity: 0;
          height: 0;
        }
        &-show{
          opacity: 1;
          transition: all 0.6s ease;
        }
      }
    }
  }
  &-title {
    width: 100%;
    height: 40px;
    flex: 0 0 40px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: var(--font-size-14);
    flex-wrap: nowrap;
    transition: all 0.3s ease;
    z-index: 2;
    border-bottom: 1px solid transparent;
    padding: 0 10px;
    border-radius: 6px;
    margin-bottom: 6px;
    &-shadow{
      // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-bottom: 1px solid rgba(221,224,227,0.5);
    }
    &-widthDesc{
      padding: 10px;
      height: 50px;
      flex: 0 0 50px;
    }
    &-left{
      width: calc(100% - 40px);
      .blur-input-content{
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        // width: 100%;
        width: calc(100% - 34px);//预留menu的宽度
        &-text > div{
          .ellipsis;
        }
        .blur-input-wrapper-focus {
          width: 0;
          flex: 1 1 0;
        }
        .blur-input-wrapper {
          // max-width: 50%;
          cursor: pointer;
          display: block;
          // flex: 1;
          // width: 0;
          .ellipsis;
          .ui-input.is-readonly {
            color: var(--main-fc);
            font-weight: bold;
            font-size: var(--font-size-14);
          }
        }
        .help-title{
          max-width: 50%;
          .ellipsis;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          span{
            .ellipsis;
          }
          .blur-input-wrapper {
            max-width: 50%;
            cursor: pointer;
            display: block;
            // flex: 1;
            // width: 0;
            .ellipsis;
            .ui-input.is-readonly {
              color: var(--main-fc);
              font-weight: bold;
              font-size: var(--font-size-14);
            }
          }
          .help-title{
            max-width: 50%;
            .ellipsis;
            display: flex;
            align-items: center;
            span{
              cursor: pointer;
            }
          }
        }
      }
      &-desc{
        color: var(--secondary-fc);
        font-size: var(--font-size-12);
        font-weight: 400;
        .ellipsis;
        max-width: 100%;
        position: relative;
        top: 6px;
        margin: 0;
      }
    }
    .ui-icon {
      color: var(--regular-fc);
    }
    &-groupMenu{
      display: flex;
      align-items: center;
      .ebcoms-assets-icon{
        position: relative;
        top: 2px;
      }
    }
    &.@{ebdBClsPrefix}-drag-handle{
      cursor: pointer;
    }
  }
  &.is-lock .@{ebdBClsPrefix}-list-title {
    .blur-input-wrapper {
      cursor: auto;
    }
  }

  &-body-noMore {
    .ui-list-scrollview-content::after {
      content: getLabel('105956', '已加载全部数据');
      display: inline-block;
      text-align: center;
      width: 100%;
      padding: 8px 0;
      // background: var(--bg-base);
      color: var(--placeholder-fc);
      word-break: break-word;
    }
  }
  &-body {
    width: 100%;
    max-height: calc(100% - 50px);
    position: relative;
    &.noTitle{
      max-height: 100%;
    }
    &-hasAdd{
      &.noTitle{
        max-height: calc(100% - 60px);
      }
      max-height: calc(100% - 106px);
    }
    &-child{
      height: 100%;
      overflow-y: scroll;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, 和 Opera */
      }
    }
    .ui-list{
      display: block;
    }
    .ui-list-body {
      background: transparent;
      min-width: 100%;
      overflow: inherit;
      // 默认换行
      .ebcoms-list-flex.ebcoms-list-grid-row-main{
        flex-wrap: wrap;
      }
      .ebcoms-list-ellipsis {
        overflow: inherit;
        & > span{
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
      .ui-board-drag-handle{
        margin-bottom: 6px;
      }
    }
    .ui-scroller__bar.is-vertical{
      display: none;
    }
    .ui-list-scrollview-content {
      height: 100%;
    }
    .ebcoms-list:hover {
      background: var(--base-white);
    }
  }
  &-bottom {
    background: var(--base-white);
    height: 40px;
    flex: 0 0 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid rgba(221,224,227,0.5);
    box-shadow: 0px 0px 4px 0px rgba(221,224,227,0.21);
    border-radius: 6px;
    font-size: var(--font-size-12);
    color: #646A73;
    width: 100%;
    transition: all .3s;
    &:hover{
      color: var(--primary);
    }
  }
  .ui-board-list-cards {
    width: 100%;
    padding: 0;
    height: auto;
    .ui-list-scrollview-wrap{
      width: 100%;
    }
    .ui-list-sortable-chosen {
      cursor: move;
    }
  }

  .@{ebdBClsPrefix}-card {
    background: var(--base-white);
    font-size: var(--font-size-12);
    color: var(--placeholder-fc);
    margin: 0;
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    border: 1px solid rgba(221,224,227,0.5);
    transition: border-color 0.3s ease-in-out;
    box-shadow: 0px 0px 4px 0px rgba(221,224,227,0.21);
    &:hover{
      border-color: var(--primary);
    }
    .data-temp {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-left: 4px solid #ddd;
      bottom: 0;
    }
    &-header {
      color: var(--main-fc);
    }
    &-main {
      position: relative;
      // background: var(--base-white);
      display: flex;
      margin: 0;
      padding: 0;
      &-noTemplate {
        text-align: center;
        height: 50px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .ebcoms-list {
        // background: var(--base-white);
        padding: 20px var(--h-spacing-lg);
        overflow: hidden;
        &:not(:last-child) {
          border-bottom: none;
        }
        //ebdcoms卡片，人员头像显示大小需要动态调整下，他们暂不支持头像-名称的格式，需要利用显示转换调整
        .use-trans-style .ui-avatar .ui-avatar-sm {
          width: 100%;
          height: 100%;
        }
        &-flex{
          .ebcoms-list-grid-row{
            &:first-child{
              .ebcoms-list-grid-row-main{
                // 和产品沟通 第一行 第一个字段默认14px
                .ebcoms-list-render-cell{
                  // &:first-child{
                  //   font-size: 14px;
                  //   margin-bottom: 4px;
                  // }
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
      &-icon-down{
        visibility: hidden;
        position: absolute;
        z-index: 9;
        color: var(--secondary-fc);
        top: 8px;
        right: 16px;
        width: 20px;
        height: 20px;
        background: #F3F4F6;
        border-radius: 3px;
        &:hover {
          visibility: visible;
        }
        .ui-menu-select-cover-children{
          position: absolute;
          top: 10px;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      &:hover {
        .@{ebdBClsPrefix}-card-main-icon-down {
          visibility: visible;
        }
      }
    }
    &-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      &-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
    &-avatar {
      display: flex;
      align-items: center;
      &-name {
        margin-left: var(--h-spacing-sm);
        line-height: 1;
      }
    }
    &-date {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      cursor: auto;
      .ui-icon {
        color: var(--placeholder-fc);
        display: inline-flex;
        align-items: flex-start;
        justify-content: center;
      }
      &-val {
        margin-left: var(--h-spacing-sm);
      }
    }
  }
  &-more{
    width: 100%;
    background: transparent;
    color: #999;
    font-size: var(--font-size-12);
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    &.load-more-text{
      cursor: pointer;
      &:hover{
        color: var(--primary);
      }
    }
  }
  &-moreLoading{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
  }
}
.@{ebdBClsPrefix}-order {
  width: 100%;
  height: calc(var(--hd) * 30);
  line-height: calc(var(--hd) * 30);
  padding-left: 22px;
  padding-right: var(--v-spacing-lg);
  box-sizing: border-box;
  cursor: pointer;
  color: var(--regular-fc);
  position: relative;
  font-weight: normal;
  &.selected {
    color: var(--primary);
    .@{ebdBClsPrefix}-order-icon {
      color: var(--primary);
    }
  }
  &-name {
    margin-right: var(--h-spacing-sm);
    .ui-icon {
      position: absolute;
      top: 8px;
      left: 6px;
      bottom: 0;
    }
  }
  &-icon {
    color: var(--secondary-fc);
  }
  &:hover {
    color: var(--primary);
    .@{ebdBClsPrefix}-order-icon {
      color: var(--primary);
    }
  }
}

.board-groupMenu-select-overlay {
  .ui-menu-list {
    color: var(--main-fc);
    font-weight: normal;
    font-size: var(--font-size-12);
  }
  .ui-menu-trigger {
    max-width: 150px;
  }
  &.card{
    .ui-icon-wrapper{
      width: 20px;
      height: 20px;
      background: #F3F4F6;
      border-radius: 3px;
      position: relative;
      .Icon-Down-arrow01{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .ui-menu-select-cover-children {
    border-color: transparent;
    background: transparent;
  }
  .ui-menu-select-cover-container-active .ui-menu-select-iconOverlay {
    background: transparent;
    .ui-icon {
      color: var(--primary);
    }
  }
  .ui-menu-select-iconOverlay {
    svg.ui-icon-svg > svg {
      display: none;
    }
  }
}
.@{ebdBClsPrefix}-custom-group-name{
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  .weapp-ebdf-locale-wrapper {
    width: 90%;
  }
  .@{ebdBClsPrefix}-custom-colorpicker{
    .ui-colorpicker{
      max-width: 50px;
      min-width: 0;
    }
    &-button{
      width: 22px;
      height: 22px;
      border: var(--border-solid);
      margin-left: 8px;
      border-radius: 50%;
      cursor: pointer;
    }
  }
}
.@{ebdBClsPrefix}-board{
  &-spin-list{
    height: 100%;
    .ui-spin-nested-loading,
    .ui-spin-container {
      height: 100%;
    }
  }
  &-loadMore{
    font-size: var(--font-size-12);
    .flexCt;
  }
}