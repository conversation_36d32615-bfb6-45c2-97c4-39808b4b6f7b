import { useState } from 'react';
import { AnyObj } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import { When } from 'react-if';
import { ebdBClsPrefix, DEFAULT_LANE_ID } from '../../../../../constants';
import { formatMenuConfig, getBoardBg } from '../../../../common/board/utils';
import { invoke } from '../../../../../utils';
import { EbdBPlKeys } from '../../../../../types/common';
import BlurInput from '../BlurInput';
import BoardMenu from '../BoardMenu';
import { BoardListProps } from './boardList';
import '../index.less';

export interface BoardTitleCustomProps {
  item: AnyObj;
  type: string | number;
  backgroundColor: string;
  fontColor: string;
}
export interface BoardTitleProps extends BoardListProps {
  customConfig?: BoardTitleCustomProps;
}
const GroupTitle = observer((props: BoardTitleProps) => {
  const [isEditing, setIsEditing] = useState(false);

  const { parentProps, item, showTopShadow, customConfig = {} } = props;
  const { page, store, isMobile, pluginCenter, laneGroup } = parentProps;
  const { cards = [], desc = '', ...restData } = item;
  const { getRealTimeGroupMenu, hasDragGroupRight, activeGroup, otrGroupInfo, isCustomMode, onListMenuChange, isLaneConfig, laneGroups } = store;
  const pageId = page?.id;
  const laneGroupId = laneGroup?.id || DEFAULT_LANE_ID;
  let { name: str, id, type, color, groupCount = [] } = restData;
  // 先判定是否开始了背景色自定义
  const { backgroundColor, borderColor, fontColor } = getBoardBg(activeGroup.enableColor!, color);

  const ownTitleParams = { item, type, backgroundColor, fontColor };
  const mergeParams = {
    ...ownTitleParams,
    ...customConfig,
  };
  const titleCls = classnames({
    [`${ebdBClsPrefix}-list-title`]: true,
    [`${ebdBClsPrefix}-list-title-shadow`]: showTopShadow,
    [`${ebdBClsPrefix}-list-title-widthDesc`]: desc || desc === 0,
    [`${ebdBClsPrefix}-drag-handle`]: !isMobile && !isEditing && hasDragGroupRight,
  });
  const onBlurInputFocusChange = (isFocus: boolean) => {
    setIsEditing(isFocus);
  };
  const handleMenuChange = (value: any, itemData: any, e: React.MouseEvent) => {
    const otherParams = { itemData, e, props: props.parentProps };
    onListMenuChange(value, item, otherParams);
  };
  const editGroup = (_name: any) => {
    if (_name === str) return;
    store.updateGroup(laneGroupId, { ...restData, name: _name });
  };

  // 目前没有多泳道概念 用默认泳道
  const menus = formatMenuConfig(getRealTimeGroupMenu(DEFAULT_LANE_ID, id));
  const _desc = mergeParams.item?.desc;
  const renderGroupMenus = (_menus: any[]) => {
    return (
      <When weId={`${props.weId || ''}_p1lr3w`} condition={_menus.length}>
        <BoardMenu weId={`${props.weId || ''}_29xjh0`} selectIcon="Icon-more-o" data={_menus} onChange={handleMenuChange} bindTriggerContainer className="board-group-menu" />
      </When>
    );
  };
  // 泳道分组默认只显示一个即可
  if (isLaneConfig) {
    const isFirstLane = laneGroups[0]?.id === laneGroupId;
    if (!isFirstLane) return null;
  }
  return (
    <div
      className={titleCls}
      style={{
        background: mergeParams.backgroundColor,
        color: mergeParams.fontColor,
      }}
    >
      <div className={`${ebdBClsPrefix}-list-title-left`}>
        <BlurInput
          weId={`${props.weId || ''}_ehmf1e`}
          value={`${type}` === '0' ? otrGroupInfo.customWfz || str : str}
          onChange={editGroup}
          readOnly={`${type}` === '0' || !isCustomMode}
          onFocusChange={onBlurInputFocusChange}
          ebBusinessId={pageId}
          groupCount={groupCount}
        />
        <When weId={`${props.weId || ''}_4qth5m`} condition={(_desc || _desc === 0) && _desc !== 'undefined'}>
          <div className={`${ebdBClsPrefix}-list-title-left-desc`} dangerouslySetInnerHTML={{ __html: _desc }} title={_desc} />
        </When>
      </div>
      {invoke(pluginCenter, EbdBPlKeys.renderGroupMenus, { hook: renderGroupMenus, args: [menus, mergeParams.item] })}
    </div>
  );
});

export const renderGroupTitle = (props: BoardListProps, customConfig?: BoardTitleCustomProps) => {
  return <GroupTitle weId={`${props.weId || ''}_dh4wgi`} {...props} customConfig={customConfig} />;
};

export default GroupTitle;
