import { useState, useEffect, useRef } from 'react';
import { AnyObj, Spin, Icon } from '@weapp/ui';
import { classnames, getLabel, debounce } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import { Else, If, Then, When } from 'react-if';
import { ebdBClsPrefix, KanBanDefaultBgColor, DEFAULT_LANE_ID } from '../../../../../constants';
import { formatMenuConfig, getBoardBg, setCacheData } from '../../../../common/board/utils';
import { invoke, hasRegisterFuncInPlugin } from '../../../../../utils';
import { EbdBoardEventName, EbdBPlKeys } from '../../../../../types/common';
import BlurInput from '../BlurInput';
import <PERSON><PERSON><PERSON>oader from '../../lazy-loader/pc';
import { PageModeType } from '../../../config/cmps/types';
import BoardEmpty from '../../../../common/empty';
import { BoardMainProps } from '../types';
import { renderGroupTitle } from './groupTitle';
import '../index.less';

export const throttled = (fn: Function, wait: number) => {
  let previous = 0;
  return (...args: any) => {
    let now = +new Date();
    if (now - previous > wait) {
      previous = now;
      fn.apply(this, args);
    }
  };
};
export interface BoardListProps extends React.Attributes {
  item: AnyObj;
  children: React.ReactNode;
  parentProps: BoardMainProps;
  showTopShadow?: boolean;
}

const BoardList: React.ComponentType<BoardListProps> = observer(props => {
  const { parentProps, item, children } = props;
  const { page, store, compId, events, pluginCenter, laneGroup } = parentProps;
  const { hasDragCardToOtherRight } = store;
  const [customRenderAddCmp, setCustomRenderAddCmp] = useState(null as any);
  const [showTopShadow, setShowTopShadow] = useState(false);
  const [hasPluginTitle, setPluginTitle] = useState(true);
  const [hasPluginAddBtn, setPluginAddBtn] = useState(true);
  const [addFormGroupPending, setAddFormGroupPending] = useState(false);

  const listRef = useRef() as any;
  const pageId = page?.id;
  const laneGroupId = laneGroup?.id || DEFAULT_LANE_ID;

  useEffect(() => {
    // 未来弃用-该用按钮配置
    if (!events || !events.on) return;
    events && events.on(EbdBoardEventName.onCardAddEvt, compId, handleCustomRenderAddCmp);
    events && events.on(EbdBoardEventName.onCardDataUpdate, compId, handleCustomClearAddCmp);
    return () => {
      events && events.off(EbdBoardEventName.onCardAddEvt, compId, handleCustomRenderAddCmp);
      events && events.off(EbdBoardEventName.onCardDataUpdate, compId, handleCustomClearAddCmp);
    };
  }, []);
  // 初始化-判断是否存在标题和新建按钮 默认是都存在的 可能被插件包复现
  useEffect(() => {
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.renderGroupTitle)) {
      const _hasTitle = invoke(pluginCenter, EbdBPlKeys.renderGroupTitle, { hook: () => {}, args: [] });
      setPluginTitle(!!_hasTitle);
    }
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.renderCardAddBtn)) {
      const addPluginBtn = invoke(pluginCenter, EbdBPlKeys.renderCardAddBtn, { hook: () => {}, args: [] });
      setPluginAddBtn(!!addPluginBtn);
    }
  }, [pluginCenter]);

  const {
    paginationData,
    laneBoardPageInfo,
    onAddNewData,
    hasAddCardRight,
    activeGroup,
    pageMode,
    isEbuilderField,
    createGroupDataByEbuilder,
    onViewportLoaded,
    loadedBoardIds,
    loadingTimestamp,
    setBoardLoaded,
    showLoadingStatus,
    getCurrentGroup,
  } = store;
  const { cards = [], desc = '', ...restData } = item;
  let { name: str, id, type, color } = restData;
  // 先判定是否开始了背景色自定义
  const { backgroundColor, borderColor, fontColor } = getBoardBg(activeGroup.enableColor!, color);
  const handleCustomRenderAddCmp = (dom: any) => {
    setCustomRenderAddCmp(dom);
  };
  const handleCustomClearAddCmp = (payload: any) => {
    setCustomRenderAddCmp(null);
  };
  const addGroup = (_name: any) => {
    const payload = {
      color: KanBanDefaultBgColor,
      name: _name,
    };
    store.setState({ groupEditingData: {} });
    setCacheData(null);
    store.addOrEditGroup(payload, laneGroupId);
  };
  const onBoardEntryView = (item: any) => {
    const { getData } = store;
    const fetchData = () => {
      if (store.isDesign) {
        store?.designerStore?.previewDataControl({ groupId: item.id, laneId: item.laneId });
      } else {
        getData({ groupId: item.id, laneId: item.laneId });
      }
    };
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.getBoardData)) {
      return invoke(pluginCenter, EbdBPlKeys.getBoardData, {
        args: [
          {
            item: item,
            fetchData,
            customRender: (dom: any) => setCustomRenderAddCmp(dom),
            extraParams: { laneGroupId },
          },
        ],
      });
    }
    if (!item.laneId) return;
    fetchData();
  };

  const addData = () => {
    // 走插件包机制
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBoardEventName.onCardAddEvt)) {
      invoke(pluginCenter, EbdBoardEventName.onCardAddEvt, {
        args: [
          {
            data: restData,
            customRender: (dom: any) => {
              setCustomRenderAddCmp(dom);
            },
          },
        ],
      });
      return;
    }
    return onAddNewData(item.laneId, restData);
  };
  const setPageMode = (item: any) => {
    // 判断如果分页模式是加载更多 支持下拉刷新
    const { pageMode, isLaneConfig } = store;
    const [_pageMode] = invoke(pluginCenter, EbdBPlKeys.getPageMode) || [pageMode];
    // 滚动加载更多模式
    if (_pageMode === PageModeType.Scroll) {
      const pageKey = isLaneConfig ? 'laneBoardPageInfo' : 'paginationData';
      const _paginationData = toJS(store[pageKey]);
      const idKey = isLaneConfig ? `${item.laneId}_${item.id}` : item.id;
      const { hasMore } = _paginationData[idKey] || {};
      if (hasMore) {
        listenToScroll(item.id);
      }
    }
  };
  const loadMore = (e: React.SyntheticEvent, scrollToBottom: boolean, groupId: string, oldScrollHeight?: number) => {
    if (scrollToBottom) {
      store.toLoadMore(groupId, laneGroup?.id, oldScrollHeight);
    }
  };
  const debounceLoadMore = debounce(loadMore, 500);
  const listenToScroll = (groupId: string) => {
    const scrollBox: any = document.querySelector(`#${compId}_${laneGroup?.id} .${ebdBClsPrefix}-list-body#list_${groupId} .weapp-ebdboard-list-body-child`);
    if (scrollBox) {
      const onScroll = (e: React.SyntheticEvent) => {
        const contentH = scrollBox.clientHeight; // 获取可见区域高度
        const { scrollTop } = scrollBox; // 获取被卷去的高度
        const { scrollHeight } = scrollBox; // 获取全文高度
        // 判断 可见区域高度 + 被卷去的高度 >=全文高度时，加载下一页内容
        debounceLoadMore(e, contentH + scrollTop >= scrollHeight - 80, groupId, scrollHeight);
      };
      scrollBox.onscroll = onScroll;
    }
  };
  // 二开口子：自定义渲染新建数据区域
  const customRenderAddNode = () => {
    if (!customRenderAddCmp) return null;
    if (customRenderAddCmp.type) {
      return customRenderAddCmp;
    }
    return <div dangerouslySetInnerHTML={{ __html: customRenderAddCmp }} />;
  };
  const setTopShadowThrottled = throttled((show: boolean) => {
    setShowTopShadow(show);
  }, 100);

  const setTopShadowDebounce = debounce((show: boolean) => {
    setShowTopShadow(show);
  }, 100);
  const _pageInfo = (laneGroupId ? laneBoardPageInfo[`${laneGroupId}_${id}`] : paginationData[id]) || {};

  const { total = 0, pageSize, hasMore, isLoading = false } = _pageInfo;
  const showNoMoreText = total >= pageSize && hasMore === false;
  setPageMode(item);
  const isAddGroup = item.id === 'fake-add';
  const renderAddBtn = () => {
    const addBtnName = `+ ${getLabel('105951', '新增数据')}`;
    const clickFunc = addData;
    const addBtn = (btnName: string, cFunc: React.MouseEventHandler<HTMLDivElement>) => (
      <div className={`${ebdBClsPrefix}-list-bottom`} onClick={cFunc}>
        <span>{btnName}</span>
      </div>
    );
    // 事井然搭建环境已经使用-保持现状
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.renderCardAddBtn)) {
      return (
        <>
          {invoke(pluginCenter, EbdBPlKeys.renderCardAddBtn, {
            hook: addBtn,
            args: [item, addBtnName, clickFunc],
          })}
        </>
      );
    }
    return addBtn(addBtnName, clickFunc);
  };
  const handleCreateGroupDataByEbuilder = async () => {
    if (addFormGroupPending) return;
    try {
      setAddFormGroupPending(true);
      await createGroupDataByEbuilder('add');
    } catch (error) {
    } finally {
      setAddFormGroupPending(false);
    }
  };
  const groupTitleParams = { item, type, backgroundColor, fontColor };
  const renderTitle = () => {
    const currentLanegroups = toJS(getCurrentGroup(laneGroupId));
    const renderGroupTitleCmp = (groupTitleParams: any) => renderGroupTitle({...props, showTopShadow}, groupTitleParams);
    return invoke(pluginCenter, EbdBPlKeys.renderGroupTitle, { hook: renderGroupTitleCmp, args: [groupTitleParams, currentLanegroups] });
  };
  // 新建分组
  if (isAddGroup) {
    // * 字段分组按关联-ebuilder分组 默认唤起新建表单
    return (
      <div className={`${ebdBClsPrefix}-list is-lock`} key={id} style={{ borderColor }}>
        <div
          className={`${ebdBClsPrefix}-list-title ${ebdBClsPrefix}-list-add outer`}
          style={{
            background: backgroundColor,
          }}
        >
          {isEbuilderField ? (
            <div className={'blur-input-content'}>
              <span className="blur-input-wrapper">
                <span className={'blur-input-content-addIcon'}>{addFormGroupPending ? <Icon weId={`${props.weId || ''}_71h93o`} name="Icon-Loading" spin moduleColor="blue" /> : '+'}</span>
                {/* 唤起表单新建 */}
                <span onClick={handleCreateGroupDataByEbuilder}>{str}</span>
              </span>
            </div>
          ) : (
            <BlurInput weId={`${props.weId || ''}_ehmf1e`} value={''} addTitle={str} onChange={addGroup} ebBusinessId={pageId} isAdd />
          )}
        </div>
      </div>
    );
  }
  const [getCardAddVisible] = invoke(pluginCenter, EbdBPlKeys.getCardAddVisible, { args: [item] }) || [false];
  const [getShowEmptyVisible] = invoke(pluginCenter, EbdBPlKeys.getShowEmptyVisible, { args: [item] }) || [false];
  // 新建数据显隐
  const newHasAddCardRight = hasAddCardRight || getCardAddVisible || customRenderAddCmp;

  return (
    <LazyLoader
      weId={`${props.weId || ''}_af2apf`}
      compId={compId}
      id={laneGroupId}
      onVisibilityChange={onBoardEntryView}
      item={item}
      loadedBoardIds={loadedBoardIds}
      onViewportLoaded={onViewportLoaded}
      loadingTimestamp={loadingTimestamp}
      setBoardLoaded={setBoardLoaded}
    >
      <Spin weId={`${props.weId || ''}_s6uly3`} spinning={!!(isLoading && showLoadingStatus)} style={{ height: '100%' }} wrapperClassName={`${ebdBClsPrefix}-board-spin-list`}>
        <div
          className={classnames({
            [`${ebdBClsPrefix}-list`]: true,
            'is-lock': !hasDragCardToOtherRight,
            'has-custom-bg': backgroundColor,
          })}
          key={id}
          style={{ borderColor, background: backgroundColor }}
        >
          {renderTitle()}
          <If weId={`${props.weId || ''}_s31jmf`} condition={cards.length}>
            <Then weId={`${props.weId || ''}_iwpz56`}>
              <div
                className={classnames({
                  [`${ebdBClsPrefix}-list-body`]: true,
                  noTitle: !hasPluginTitle,
                  [`${ebdBClsPrefix}-list-body-hasAdd`]: newHasAddCardRight && hasPluginAddBtn,
                  [`${ebdBClsPrefix}-list-body-noMore`]: showNoMoreText,
                  [`${ebdBClsPrefix}-list-body-padding`]: hasMore && pageMode === PageModeType.More && !isLoading,
                })}
                id={`list_${id}`}
                style={{ background: backgroundColor }}
              >
                <div
                  className={`${ebdBClsPrefix}-list-body-child`}
                  ref={listRef}
                  onScroll={e => {
                    setTopShadowThrottled(listRef?.current?.scrollTop !== 0);
                    setTopShadowDebounce(listRef?.current?.scrollTop !== 0);
                  }}
                >
                  {children}
                </div>
              </div>
            </Then>
          </If>
          {/* 新版本空数据处理逻辑  改版后如果有新建数据权限，则显示新增按钮，否则显示空数据图标 */}
          <When weId={`${props.weId || ''}_qg4sj1`} condition={!cards.length}>
            <If weId={`${props.weId || ''}_6zos58`} condition={newHasAddCardRight && !getShowEmptyVisible}>
              <Then weId={`${props.weId || ''}_zx1c59`}>
                <div className={`${ebdBClsPrefix}-list-body`}>{children}</div>
              </Then>
              <Else weId={`${props.weId || ''}_euhaaq`}>
                <div className={`${ebdBClsPrefix}-list-body ${ebdBClsPrefix}-list-empty`} style={{ background: backgroundColor, color: fontColor }}>
                  <div className={`${ebdBClsPrefix}-list-empty-placeholder`}>{children}</div>
                  <BoardEmpty
                    weId={`${props.weId || ''}_tktvjn`}
                    extraProps={{
                      style: { color: fontColor },
                    }}
                    onlyText
                  />
                </div>
              </Else>
            </If>
          </When>
          <When weId={`${props.weId || ''}_8ul5ev`} condition={newHasAddCardRight}>
            {renderAddBtn()}
          </When>
          {customRenderAddNode()}
        </div>
      </Spin>
    </LazyLoader>
  );
});

export default BoardList;
