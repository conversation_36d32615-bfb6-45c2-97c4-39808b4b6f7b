import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>r, FormSwitchProps, Icon, SearchAdvanced, Spin, TypesBrowser, CorsComponent } from '@weapp/ui';
import { classnames, getLabel, eventEmitter, isEqual, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { withRouter } from 'react-router-dom';
import { inject, observer } from 'mobx-react';
import React, { ComponentProps } from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix, EbuilderFormModule, ModuleType, viewHeaderClsPrefix } from '../../../../constants';
import { browserTypes, EtComponentKey, typeBrowserProps } from '../../../../constants/EtComponent';
import { CommonSearchType, EbdBoardEventName, EbdBPlKeys } from '../../../../types/common';
import { CustomEventName } from '../../../../utils/event-emitter/types';
import { OrderType } from './types';
import { isNeedRefreshByConfig, invoke, hasRegisterFuncInPlugin, isValidGroupInfo } from '../../../../utils';
import DateSelect from '../../../common/date-select';
import LeftRightLayout from '../../../common/left-right-layout';
import PersonSelect from '../../../common/person-select';
import SearchSelecter from '../../../common/search-selecter';
import GroupMenu from './BoardMenu';
import { BoardMainProps } from './types';
import { BoardAdvancedView, BoardSearchQuick } from './advanced';
import { isStatisticsNeedRefresh } from './utils';
import GroupEdit from './GroupEdit';
import BoardCore from './core';

import './index.less';

const { Panel } = SearchAdvanced;
const { MultiCascader } = Cascader as any;

@inject('store')
@observer
export class BoardMain extends React.PureComponent<BoardMainProps, any> {
  filterWrapRef: any;
  state = {
    mainRefreshKey: new Date().getTime(), // 整体看板入口刷新key;
  };
  setWrapperRef = (ref: any) => {
    this.filterWrapRef = ref;
    this.props.onRef?.(ref);
  };
  componentDidMount() {
    const { store, compId, config, events = {}, pluginCenter } = this.props;
    // 触发加载完成事件
    events?.emit('compLoad');
    invoke(pluginCenter, EbdBoardEventName.onDidMount);
    events.emit && events.emit(EbdBoardEventName.onDidMount, compId);
    // 存储路由参数
    store.setRouteProps(this.props);
    // 绑定事件
    eventEmitter.on('weappEbdfpage', CustomEventName.cardAdd, this.onCardAdd, compId);
    eventEmitter.on('weappEbdfpage', CustomEventName.cardEdit, this.onCardReload, compId);
    eventEmitter.on('weappEbdfpage', CustomEventName.flowAdd, this.onFlowAdd, compId);
    eventEmitter.on('weappEbdfpage', CustomEventName.cardClose, this.onCardClose, compId);
    events.on(EbdBoardEventName.scrollToLeft, compId, this.scrollToLeft);
    // 触发默认事件
    this.onEventEmit(config?.eventGroup);
    // 初始化
    this.initBoard();
    // 处理插件包更新
    this.handleMount();
  }

  componentWillUnmount() {
    this.removeEventLinstens();
  }
  componentWillReceiveProps(nextProps: Readonly<BoardMainProps>) {
    // 插件包新注入
    if (!isEqual(nextProps.pluginCenter, this.props.pluginCenter) && !isEmpty(nextProps.pluginCenter)) {
      this.initBoard(nextProps);
      this.handleMount(nextProps);
      return;
    }
  }

  componentDidUpdate(prevProps: BoardMainProps) {
    // 注册了插件包事件
    if (hasRegisterFuncInPlugin(this.props.pluginCenter, EbdBPlKeys.onCustomUpdate)) {
      invoke(this.props.pluginCenter, EbdBPlKeys.onCustomUpdate);
      return;
    }
    const {
      compId: _compId,
      pageId: _pageId,
      config: _config,
      isMobile: _isMobile,
      comServicePath: _comServicePath,
      urlParams: _urlParams,
      // location: _location,
    } = prevProps;
    const {
      compId,
      pageId,
      config,
      isMobile,
      // location,
      comServicePath,
      store,
    } = this.props;
    if (
      !isEqual(compId, _compId) ||
      !isEqual(pageId, _pageId) ||
      !isEqual(isMobile, _isMobile) ||
      !isEqual(comServicePath, _comServicePath) ||
      // || (!isEqual(urlParams, _urlParams) && location.pathname === _location.pathname)
      isNeedRefreshByConfig(_config, config) ||
      isStatisticsNeedRefresh(prevProps, this.props)
    ) {
      this.initBoard();
    } else {
      // 切换数据源 配置更新 数据还原
      if (_config?.dataset?.id && config?.dataset?.id && (_config.dataset.id !== config.dataset.id || _config.dataset.type !== config.dataset.type)) {
        store.reset();
      }
    }
  }
  removeEventLinstens() {
    const { compId, pluginCenter, events } = this.props;
    eventEmitter.off('weappEbdfpage', CustomEventName.cardAdd, this.onCardAdd, compId);
    eventEmitter.off('weappEbdfpage', CustomEventName.cardEdit, this.onCardReload, compId);
    eventEmitter.off('weappEbdfpage', CustomEventName.flowAdd, this.onFlowAdd, compId);
    invoke(pluginCenter, EbdBoardEventName.onDestroy);
    events && events.emit && events.emit(EbdBoardEventName.onDestroy, compId);
    events.off(EbdBoardEventName.scrollToLeft, compId, this.scrollToLeft);
    events.off(EbdBoardEventName.onBoardReload, compId, this.reloadKanban);
    events.off('filter', compId);
  }

  initBoard = (props = this.props) => {
    this.initConfig(props);
    const { store, pluginCenter, isDoc, refPluginPackage, config } = props;
    // 插件包等待pluginCenter注入
    if (!isEmpty(refPluginPackage) && !pluginCenter) return;
    if (!isValidGroupInfo(config?.group!)) {
      return;
    }
    if (hasRegisterFuncInPlugin(pluginCenter, EbdBPlKeys.onCustomDidMount)) {
      invoke(pluginCenter, EbdBPlKeys.onCustomDidMount, { args: [props] });
      return;
    }
    if (!isDoc) {
      store.initData();
    }
    if (!props.isDesign) {
      try {
        const [showLoadingStatus] = invoke(pluginCenter, EbdBPlKeys.getShowLoading) || [false];
        store.setState({ showLoadingStatus });
      } catch (error) {}
    }
  };

  initConfig = (props = this.props) => {
    const { store, compId, pageId, config, isMobile, isDesign, urlParams, comServicePath, events, isDoc, pluginCenter, ebStore } = props;
    store.initConfig(
      {
        compId,
        pageId,
        config,
        isMobile,
        isDesign,
        comServicePath,
        events,
        isDoc,
        ebStore,
        pluginCenter,
      },
      urlParams
    );
  };
  reloadKanban = (uParams?: any) => {
    const { store, pluginCenter } = this.props;
    const reloadFunc = () => {
      store.setSearchParams(uParams);
      store.initData();
    };
    invoke(pluginCenter, EbdBPlKeys.onCustomReload, { hook: reloadFunc, args: [uParams] });
  };
  handleMount = (props = this.props) => {
    invoke(props.pluginCenter, EbdBPlKeys.onBeforeMount, {
      args: [props.store, props],
    });
    // 兼容新拉出插件包场景
    props.onBeforeMount?.(props.store);
    // 处理筛选事件
    if (!props.events) {
      return;
    }
    // 先解绑
    props.events.off('filter', props.compId);
    props.events.off(EbdBoardEventName.onBoardReload, props.compId, this.reloadKanban);
    props.events.off(EbdBoardEventName.onReload, props.compId, this.onReload);
    if (hasRegisterFuncInPlugin(props.pluginCenter, EbdBPlKeys.onFilter)) {
      invoke(props.pluginCenter, EbdBPlKeys.onFilter, { args: [props] });
    } else {
      // 监听筛选后刷新事件(刷新组件动作)
      props.events.on('filter', props.compId, (val: any[], filterId?: string) => {
        props.store.setFilter(val, filterId);
        props.store.initData();
      });
    }
    // 监听刷新事件
    props.events.on(EbdBoardEventName.onBoardReload, props.compId, this.reloadKanban);
    // 更新store
    props.events.on(EbdBoardEventName.onReload, props.compId, this.onReload);
  };

  onEventEmit = (eventGroup?: any[]) => {
    const { ebStore } = this.props;
    if (isEmpty(eventGroup) || !ebStore) return;
    // 只处理加载完成事件
    const validEvent = eventGroup?.filter(i => i.type === 'DIDMOUNT');
    if (!isEmpty(validEvent)) ebStore.eventAction.create(eventGroup).trigger('CLICKDATA', {});
  };
  onReload = () => {
    this.setState({ mainRefreshKey: new Date().getTime() });
  };

  onCardAdd = (data: any) => {
    const { store, pluginCenter, events, compId } = this.props;
    // 存在多个看板场景重复触发 需要判断当前compId
    if (data.compId !== compId) {
      return;
    }
    const isEbuilderGroupAdd = sessionStorage.getItem('isEbuilderGroupAdd');
    if (isEbuilderGroupAdd === '1') {
      // 关联ebuilder创建的不需要再走/addGroup，表单那边已经添加数据，直接前端reload
      store.resetViewportLoading();
      return;
    }
    store.addCard(data.dataid);
    eventEmitter.emit('weappEbdfpage', CustomEventName.cardClose);
    const cutPayload = { type: 'add', data };
    invoke(pluginCenter, EbdBoardEventName.onCardDataUpdate, { args: cutPayload });
    events && events.emit && events.emit(EbdBoardEventName.onCardDataUpdate, compId, cutPayload);
  };

  onFlowAdd = (data: any) => {
    const { store, compId } = this.props;
    // 存在多个看板场景重复触发 需要判断当前compId
    if (data.compId !== compId) {
      return;
    }
    store.addCard(data.requestId, true);
  };

  onCardReload = (params: any) => {
    const { store, compId } = this.props;
    const { groups } = store;
    // 找到groups下card里id等于params.id的group
    // 存在多个看板场景重复触发 需要判断当前compId
    if (params.compId !== compId) {
      return;
    }
    const refreshGroups = groups.filter(group => (group.cards || []).find(card => card.id === params.id)).map(group => group.id);
    if (refreshGroups.length) {
      store.resetSpecificBoards(refreshGroups);
    } else {
      store.resetViewportLoading();
    }
  };
  onCardClose = () => {
    // 去掉标识
    const isEbuilderGroupAdd = sessionStorage.getItem('isEbuilderGroupAdd');
    if (isEbuilderGroupAdd === '1') {
      sessionStorage.removeItem('isEbuilderGroupAdd');
    }
  };
  scrollToLeft = () => {
    const { compId } = this.props;
    const target = document.querySelector(`#${compId} .ui-scroller__wrap`);
    if (target) {
      target.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
    }
  };

  // 根据权限获取高级搜索常用筛选
  getPanelParams = () => {
    // @ts-ignore
    const { appManager, getAdPanelParams } = this.props.store;
    return getAdPanelParams(appManager);
  };

  customRenderOrderItem = ({ props }: any) => {
    const { data } = props;
    const { store } = this.props;

    const handleClick = () => {
      store.onOrderClick(data.id);
    };

    const selected: boolean = data?.selected;
    return (
      <div key={`key_${data.id}`} className={classnames({ [`${ebdBClsPrefix}-order`]: true, selected })} onClick={handleClick}>
        <span className={`${ebdBClsPrefix}-order-name`}>
          {selected && <Icon weId={`${this.props.weId || ''}_d4mbzb`} name="Icon-correct01" size="xs" />}
          {data.fieldName}
        </span>
        <When weId={`${this.props.weId || ''}_ttt4as`} condition={data.id}>
          <span className={`${ebdBClsPrefix}-order-icon`}>
            <When weId={`${this.props.weId || ''}_nlfz3v`} condition={data.orderType === OrderType.DESC}>
              <Icon weId={`${this.props.weId || ''}_23sl32`} size="md" name="Icon-sort" />
            </When>
            <When weId={`${this.props.weId || ''}_kji3mc`} condition={data.orderType === OrderType.ASC}>
              <Icon weId={`${this.props.weId || ''}_8p1abu`} size="md" name="Icon-sort" style={{ transform: 'rotate(180deg)', transformOrigin: 'center' }} />
            </When>
          </span>
        </When>
      </div>
    );
  };
  /**
   * 高级搜索面板自定义组件
   * @param key
   * @param props
   */
  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    const { otherParams = {}, id = '', value, onChange, options } = props.props;
    if (key.indexOf(EtComponentKey.GroupCustom) > -1) {
      return <SearchSelecter weId={`${this.props.weId || ''}_zfv026`} {...props.props} />;
    }
    if (otherParams?.componentKey === EtComponentKey.ComboSelect) {
      const { fieldConfig } = otherParams;
      return <CorsComponent weId={`${this.props.weId || ''}_d5vfag`} app="@weapp/ebdfpage" compName="pcComps.ComboSelect" fieldId={id} fieldConfig={fieldConfig} value={value} onChange={onChange} />;
    }
    if (otherParams?.componentKey === EtComponentKey.DateComponent) {
      const { dateVal, type } = otherParams;
      return <DateSelect weId={`${this.props.weId || ''}_0c681z`} datas={dateVal} value={value} isOptionTile={false} onChange={onChange} type={type} />;
    }

    if (otherParams?.componentKey === EtComponentKey.Employee) {
      const { dateVal } = otherParams;
      return <PersonSelect weId={`${this.props.weId || ''}_cb7cf2`} datas={dateVal} value={value} isOptionTile={false} onChange={onChange} />;
    }
    // 人员范围选择浏览框
    if (otherParams?.componentKey === EtComponentKey.EmployeeScope) {
      const onEmployeeScopeChange = (val: any) => {
        onChange(val);
      };
      return (
        <TypesBrowser
          weId={`${props.weId || ''}_ebz3w9`}
          module="hrm"
          type="hrmcombination"
          value={toJS(value)}
          options={browserTypes()}
          browsers={typeBrowserProps}
          onChange={onEmployeeScopeChange}
        />
      );
    }

    // 人员组织多选浏览框
    if (otherParams?.componentKey === EtComponentKey.EmployeeOrganization) {
      const onEmployeeOrganizationChange = (val: any) => {
        onChange(val);
      };
      return (
        <Browser
          weId={`${props.weId || ''}_zddg5r`}
          type="combinationResource"
          module="hrm"
          multiple
          searchAtTab
          isMultType
          value={toJS(value)}
          hasAdvanceSearch
          disabledTabCache
          onChange={onEmployeeOrganizationChange}
        />
      );
    }

    if (otherParams?.componentKey === EtComponentKey.Cascader) {
      return <MultiCascader weId={`${this.props.weId || ''}_n9d59f`} options={options} value={toJS(value) || []} mergeChild={false} onChange={onChange} placeholder={getLabel('40502', '请选择')} />;
    }
    return <span>The current custom type is supported. Please contact the developer</span>;
  };
  renderMain = () => {
    const { mainRefreshKey } = this.state;
    const { store, compId, pageId, config, pluginCenter, isDesign, page, events } = this.props;
    const { onSearch, commonSearch, getFilterSearchInstance, filterSearch, conditionId, baseSearch, orders, appManager, groupTopSearchDatas, filterMount, newSearchStore, loading, showLoadingStatus } =
      store;
    const { adQuickParams, onVisibleChange, onReset, onCancel, searchAdvancedStore, filterData } = baseSearch;
    const isGroup = groupTopSearchDatas.length > 0;
    // 设计器内暂不显示高级搜索
    const showHeader = (isGroup || !!adQuickParams || orders.length > 0 || newSearchStore?.searchSetting?.hasAd) && !isDesign;
    const searchRelationEnable = config?.filter?.searchRelationEnable;
    return (
      <div className={`${ebdBClsPrefix}-container-main`} key={mainRefreshKey}>
        {invoke(pluginCenter, EbdBoardEventName.renderContentBefore)}
        <div className={`${ebdBClsPrefix}-wrapper`}>
          <When weId={`${this.props.weId || ''}_1yzniu`} condition={showHeader}>
            <div className={`${ebdBClsPrefix}-header`}>
              <div className={`${ebdBClsPrefix}-header-left`}>
                {/* 分类搜索顶部 */}
                {/* <When weId={`${this.props.weId || ''}_ad5v75`} condition={groupTopSearchDatas.length > 0}> */}
                {/* 0401基线-暂时屏蔽分类筛选 后续需求来做 */}
                <When weId={`${this.props.weId || ''}_ad5v75`} condition={false}>
                  <CorsComponent
                    weId={`${this.props.weId || ''}_i62lne`}
                    app="@weapp/ebdfpage"
                    compName="pcComps.GroupSearchTopCom"
                    searchParam={{
                      ...toJS(store.cacheSearchParams),
                      iewType: ModuleType.Board,
                      compId,
                      pageId,
                    }}
                    groupDatas={groupTopSearchDatas}
                    groupChange={store.updateGroupSearchParams}
                    groupSelectedData={store.groupSearchParams}
                  />
                </When>
              </div>
              <div className={`${ebdBClsPrefix}-header-right`}>
                {/* 统一走新的store里取当前是否有高级搜索配置 */}
                <When weId={`${this.props.weId || ''}_ae2bda`} condition={newSearchStore?.searchSetting?.hasAd || newSearchStore?.searchSetting?.hasQuick}>
                  {/* <SearchAdvanced
                weId={`${this.props.weId || ''}_exvuir`}
                onVisibleChange={onVisibleChange}
                className={`${viewHeaderClsPrefix}-head-adsearch`}
                conditionId={conditionId}
                searchAdvancedStore={searchAdvancedStore}
                onSearch={onSearch}
                module={EbuilderFormModule}
                disableCommonFilter={adQuickParams?.quickSearchInputProps?.suffix === null}
                {...adQuickParams}
                requestHeaderParams={{ ebBusinessId: pageId }}
              /> */}
                  <BoardAdvancedView
                    weId={`${this.props.weId || ''}_q9f8ty`}
                    className={`${viewHeaderClsPrefix}-head-adsearch`}
                    onSearch={onSearch}
                    oldSearchStore={baseSearch}
                    newSearchStore={newSearchStore}
                    // todo 先都为true 统一走eb表单的逻辑
                    // isEbFormDataSet={isEbFormDataSet}
                    isEbFormDataSet={true}
                  />
                  {/* <BoardSearchQuick
                    weId={`${this.props.weId || ''}_439561`}
                    dataset={config?.dataset}
                    compId={compId}
                    config={config}
                    page={page}
                    events={events}
                  /> */}
                </When>
                <When weId={`${this.props.weId || ''}_71i78r`} condition={orders.length > 0}>
                  <div className={`${ebdBClsPrefix}-header-order`}>
                    <GroupMenu
                      weId={`${this.props.weId || ''}_29xjh0`}
                      selectIcon="Icon-Post-task"
                      data={orders}
                      action="click"
                      customRenderItem={this.customRenderOrderItem}
                      triggerProps={{
                        popupPlacement: 'bottomRight',
                        popupClassName: 'board-order-select-overlay',
                      }}
                    />
                  </div>
                </When>
              </div>
            </div>
          </When>
          <When weId={`${this.props.weId || ''}_ae2bda`} condition={commonSearch === CommonSearchType.TopSearch}>
            <div className={`${ebdBClsPrefix}-filter-content`}>
              <CorsComponent
                weId={`${this.props.weId || ''}_t9jkdm`}
                app="@weapp/ebdfpage"
                compName="FilterSearchCom"
                wrapperRef={this.filterWrapRef}
                onSearch={filterSearch}
                // store={filterSearchStore}
                conditionId={conditionId} // 这个先隐藏下，保存常用筛选日历和占用视图没做
                getInstance={getFilterSearchInstance}
                saveAsPublic={appManager}
                outerInfo={{ filterData }}
                callBack={filterMount}
                needCondition
                needOtherCustom
                searchRelationEnable={searchRelationEnable}
              />
            </div>
          </When>
          <div className={`${ebdBClsPrefix}-content`}>
            <Panel
              mode="normal"
              weId={`${this.props.weId || ''}_tms7vd`}
              onVisibleChange={onVisibleChange}
              defaultVisible={false}
              needRange
              onSearch={onSearch}
              onReset={onReset}
              onCancel={onCancel}
              colSpan={12}
              conditionId={conditionId}
              searchAdvancedStore={searchAdvancedStore}
              module={EbuilderFormModule}
              formProps={{ customRenderFormSwitch: this.customRenderFormSwitch }}
              {...this.getPanelParams()}
              requestHeaderParams={{ ebBusinessId: pageId }}
            />
            {/* <BoardAdvancedPanelView
              onSearch={onSearch}
              oldSearchStore={baseSearch}
              newSearchStore={newSearchStore}
              // todo 先都为true 统一走eb表单的逻辑
              // isEbFormDataSet={isEbFormDataSet}
              isEbFormDataSet={true}
              customRenderFormSwitch={this.customRenderFormSwitch}
            /> */}
            <BoardCore weId={`${this.props.weId || ''}_fzn2cj`} {...this.props} />
          </div>
        </div>
        {invoke(pluginCenter, EbdBoardEventName.renderContentAfter)}
      </div>
    );
  };
  render() {
    const { store, compId, pageId, pluginCenter, config } = this.props;
    const { fromEbuilder } = config;
    // @ts-ignore
    const { groupEditVisible, groupEditingData, loading, leftMenuTreeDatas, hasGroupleft, boardColorStyle } = store;
    const editGroupData = { name: groupEditingData.name, color: groupEditingData?.color, id: groupEditingData?.id };
    // 是否在容器内
    const isInContainer = !!config?.flow?.parent;
    const isDevelopment = process.env.NODE_ENV === 'development';
    return (
      <div className={`${ebdBClsPrefix}-container ${isInContainer ? 'inContainer' : ''} ${fromEbuilder ? 'fromEbuilder' : ''}`} ref={this.setWrapperRef}>
        <LeftRightLayout
          weId={`${this.props.weId || ''}_j8k66r`}
          hasLeftGroup={hasGroupleft}
          leftMenuDatas={leftMenuTreeDatas}
          searchParams={{
            ...store.cacheSearchParams,
            viewType: ModuleType.Board,
            compId,
            pageId,
          }}
          dfSelectKeys={store.groupSearchParams}
          leftOnChange={store.updateGroupSearchParams}
        >
          {invoke(pluginCenter, EbdBPlKeys.renderContentTop)}
          {invoke(this.props.pluginCenter, EbdBPlKeys.renderMain, { hook: this.renderMain, args: [this.props] })}
          {/* 非eb关联打开 */}
          <GroupEdit
            weId={`${this.props.weId || ''}_9ei55c`}
            visible={groupEditVisible}
            onOk={store.addOrEditGroup}
            onCancel={store.closeGroupEdit}
            datas={editGroupData}
            boardColorStyle={boardColorStyle}
            ebBusinessId={pageId}
          />
          {invoke(pluginCenter, EbdBoardEventName.renderContentBottom)}
          {isDevelopment && <CorsComponent weId={`${this.props.weId || ''}_dv3rwh`} app="@weapp/ebdpage" compName="RoutePageView" type="EB_FORM_VIEW" />}
        </LeftRightLayout>
      </div>
    );
  }
}

export default withRouter(BoardMain) as unknown as ComponentProps<any>;
