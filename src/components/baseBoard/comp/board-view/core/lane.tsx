// 渲染泳道二维看板
import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { Spin, Icon, Pagination, Scroller } from '@weapp/ui';
import { getLabel, debounce, isEqual, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { withRouter } from 'react-router-dom';
import { ebdBClsPrefix } from '../../../../../constants';
import BoardCom from '../BoardCom';
import { BoardMainProps } from '../types';
import { GroupItem } from '../../../../common/board/types';
import { LayoutCardWithRouter } from '../../../../common/field-layout/LayoutCard';
import { invoke, safeSetInterval } from '../../../../../utils';
import { Ebd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EbdBoardEventName } from '../../../../../types/common';
import { LaneKanbanAppName } from '../../../../plugin-packages/constants';
import BoardEmpty from '../../../../common/empty';
import { PageModeType } from '../../../config/cmps/types';
import { setOnScrollStatus, setScrollStatus } from './utils';
import { LaneItemData, LaneStatType } from '../../../config/cmps/yd/types';
import './index.less';

interface YDProps extends BoardMainProps {
  [x: string]: any;
  loading: boolean;
  layout: 'both' | 'inline'; // 泳道/进展看板 整体滚动还是内部滚动
}
const cls = `${ebdBClsPrefix}-laneGroup`;
const diffKey = 'LaneBoard';
const LaneBoard = observer((props: YDProps) => {
  const { loading, compId, config, pluginCenter, store, layout } = props;
  const { laneGroups, laneStats, laneGroupsLoading, lanePage } = store;
  const { dataset, card } = config;

  const laneTitleConfig = useMemo(
    () => ({
      dataset,
      field: card,
    }),
    [dataset, card]
  );

  const [newLaneTitleConfig] = invoke(pluginCenter, EbdBPlKeys.getLaneTitleConfig) || [laneTitleConfig];

  const [state, setState] = useState({
    collapsedItems: [] as string[],
    refreshKey: Date.now(),
    leftRefreshKey: Date.now(),
    boardHeaderRefreshKey: Date.now(),
    isSetPd: false,
  });

  const handleCollapseToggle = useCallback((itemId: string) => {
    setState(prev => {
      const isCollapsed = prev.collapsedItems.includes(itemId);
      return {
        ...prev,
        collapsedItems: isCollapsed ? prev.collapsedItems.filter(id => id !== itemId) : [...prev.collapsedItems, itemId],
      };
    });
  }, []);

  const removeDefaultPd = () => {
    const getBoardCt = () => {
      return document.querySelector(`#${props.compId} > .content`);
    };
    safeSetInterval(
      () => {
        return getBoardCt();
      },
      3000,
      10,
      () => {
        getBoardCt()?.setAttribute('style', 'padding: 0');
        setState(prev => ({ ...prev, isSetPd: true }));
      },
      () => {
        setState(prev => ({ ...prev, isSetPd: true }));
      }
    );
  };

  useEffect(() => {
    if (laneLayout === 'both') {
      removeDefaultPd();
    } else {
      setState(prev => ({ ...prev, isSetPd: true }));
    }
  }, []);

  // 整体滚动泳道滚动监听
  const handleBothScroll = (event: any) => {
    const scrollLeftValue = event.target.scrollLeft;
    setScrollStatus(scrollLeftValue, event.target.parentElement);
  };
  // * 进展看板内容横向滚动监听
  const handleProgressScroll = (event: any) => {
    const scrollLeftValue = event.target.scrollLeft;
    const dataKey = event.target.closest('[data-key]')?.getAttribute('data-key');
    if (!dataKey) return;
    const targetContent = document.querySelector(`.${ydInline}-content-item-top-right-header[data-key=${dataKey}]`);
    if (targetContent) {
      targetContent.scrollLeft = scrollLeftValue;
      setOnScrollStatus(scrollLeftValue, event, true);
    }
  };
  // * 进展看板标题横向滚动监听
  const handleProgressTitleScroll = (event: any) => {
    const scrollLeftValue = event.target.scrollLeft;
    const dataKey = event.target.closest('[data-key]')?.getAttribute('data-key');
    if (!dataKey) return;
    const targetContent = document.querySelector(`.${ydCs}-commonSingleBoard-item[data-key=${dataKey}] .ui-scroller__wrap`);
    if (targetContent) {
      targetContent.scrollLeft = scrollLeftValue;
      setOnScrollStatus(scrollLeftValue, event);
    }
  };

  // 看板横向滚动
  const titleRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // 整体滚动
  const bothRightRef = useRef<HTMLDivElement>(null);
  // 泳道滚动分页相关
  const bottomSentinelRef = useRef<HTMLDivElement>(null);
  const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
  const isInitialIntersection = useRef(true);
  const scrollLoadMoreTimeRef = useRef(new Date().getTime());

  const ydCs = `${cls}-yd`;
  const ydB = `${ydCs}-both`;
  const ydInline = `${ydCs}-inline`;
  const isJinZhanBoard = config?.type === LaneKanbanAppName;

  const laneLayout = useMemo(() => {
    return isJinZhanBoard ? 'inline' : 'both'; // 进展看板/泳道 内部滚动还是整体滚动
  }, [laneGroups, laneStats, lanePage]);
  // 重要-监听泳道滚动到底部
  useEffect(() => {
    if (laneLayout === 'both' || !containerRef.current || lanePage.moreLoading) return;

    let isTriggered = false;

    const options = {
      root: containerRef.current,
      rootMargin: '10px', // 提前10px触发
      threshold: 0,
    };

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const entry = entries[0];

      // 首次加载时跳过触发
      if (isInitialIntersection.current) {
        isInitialIntersection.current = false;
        return;
      }
      const currentTime = new Date().getTime();
      const timeDifference = currentTime - scrollLoadMoreTimeRef.current;
      if (timeDifference < 600) {
        return;
      }
      scrollLoadMoreTimeRef.current = currentTime;

      if (entry.isIntersecting && !isTriggered) {
        isTriggered = true;
        let pageNo = toJS(lanePage.pageNo);
        onLanePageChange(++pageNo);
        // 2秒后重置触发状态
        setTimeout(() => {
          isTriggered = false;
        }, 2000);
      }
    };

    // 创建观察器
    intersectionObserverRef.current = new IntersectionObserver(handleIntersection, options);

    // 观察底部元素
    if (bottomSentinelRef.current) {
      intersectionObserverRef.current.observe(bottomSentinelRef.current);
    }

    return () => {
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, [isJinZhanBoard, props.events, props.compId, lanePage.moreLoading]);

  // 内部滚动监听
  useEffect(() => {
    if (!contentRef.current || laneLayout === 'both') return;
    let contents: NodeListOf<Element>;
    let titles: NodeListOf<Element>;
    // 内部滚动的
    if (laneLayout === 'inline') {
      const scrollContent = () => {
        return contentRef.current?.querySelectorAll(`.${ydCs}-commonSingleBoard-item  > .ui-list-scrollview-wrap > .ui-scroller > .ui-scroller__wrap`)!;
        // if (laneGroups.length > 1) {
        //   return contentRef.current?.querySelectorAll(`.${ydCs}-commonSingleBoard-item  > .ui-list-scrollview-wrap > .ui-scroller > .ui-scroller__wrap`)!;
        // }
        // return contentRef.current?.querySelectorAll(`.${ydCs}-commonSingleBoard-item  > .ui-list-scrollview-wrap > .ui-scroller > .ui-scroller__wrap > .ui-scroller__view`)!;
      };
      const scrollTitle = () => {
        return contentRef.current?.querySelectorAll(`.${ydInline}-content-item-top-right-header`)!;
      };
      // 存在渲染延时 安全查询
      safeSetInterval(
        () => {
          contents = scrollContent();
          titles = scrollTitle();
          if (contents) {
            return contents.length && titles.length;
          }
          return false;
        },
        3000,
        10,
        () => {
          contents = scrollContent();
          titles = scrollTitle();
          contents &&
            contents.forEach(ref => {
              if (ref) {
                ref.addEventListener('scroll', handleProgressScroll);
              }
            });
          titles &&
            titles.forEach(ref => {
              if (ref) {
                ref.addEventListener('scroll', handleProgressTitleScroll);
              }
            });
        }
      );
    }

    return () => {
      if (laneLayout === 'inline') {
        if (contents) {
          contents.forEach(ref => {
            if (ref) {
              ref.removeEventListener('scroll', handleProgressScroll);
            }
          });
        }
        if (titles) {
          titles.forEach(ref => {
            if (ref) {
              ref.removeEventListener('scroll', handleProgressTitleScroll);
            }
          });
        }
      }
    };
  }, [state.refreshKey, laneLayout, laneGroups, isJinZhanBoard]);

  // 整体滚动监听
  useEffect(() => {
    if (!bothRightRef.current) return;
    safeSetInterval(
      () => {
        return !!bothRightRef.current;
      },
      3000,
      10,
      () => {
        bothRightRef.current?.addEventListener('scroll', handleBothScroll);
      }
    );
    return () => {
      if (laneLayout === 'both' && bothRightRef.current) {
        bothRightRef.current?.removeEventListener('scroll', handleBothScroll);
      }
    };
  }, [state.refreshKey, laneLayout, laneGroups, bothRightRef.current]);

  const isCollapsedAll = useMemo(() => {
    const allGroupsIs = laneGroups.map((item: any) => item.id);
    return isEqual(allGroupsIs, state.collapsedItems);
  }, [state.collapsedItems, laneGroups]);

  useEffect(() => {
    props.events.on(EbdBoardEventName.onLaneRightBoardReload, props.compId, reloadLaneBoard);
    props.events.on(EbdBoardEventName.onLaneBoardCollapsed, props.compId, onLaneBoardCollapsed);
    return () => {
      props.events.off(EbdBoardEventName.onLaneRightBoardReload, props.compId, reloadLaneBoard);
      props.events.off(EbdBoardEventName.onLaneBoardCollapsed, props.compId, onLaneBoardCollapsed);
    };
  }, [props.events, props.compId]);

  const isSingleLaneBoard = useMemo(() => {
    return (laneGroups || []).length === 1;
  }, [laneGroups]);

  const reloadLaneBoard = useCallback((key?: string) => {
    switch (key) {
      case 'boardHeader':
        setState(prev => ({ ...prev, boardHeaderRefreshKey: Date.now() }));
        break;
      case 'left':
        setState(prev => ({ ...prev, leftRefreshKey: Date.now() }));
        break;
      default:
        setState(prev => ({ ...prev, refreshKey: Date.now() }));
        break;
    }
  }, []);
  const onLaneBoardCollapsed = (level?: string, newLaneGroups?: LaneItemData[]) => {
    const allGroupsIs = (newLaneGroups || laneGroups).map((item: any) => item.id);
    if (level === 'collapsed') {
      setState(prev => ({ ...prev, collapsedItems: allGroupsIs }));
    } else if (level === 'expand') {
      setState(prev => ({ ...prev, collapsedItems: [] }));
    } else {
      // todo 展开收起指定泳道
    }
    props.events.emit(EbdBoardEventName.onChangeLaneBoardCollapsed, props.compId, level !== 'collapsed');
  };

  const mainGroups = useMemo(() => {
    return laneGroups[0]?.groups || [];
  }, [laneGroups]);

  const loadlongContent = useCallback(
    (isRightBoard?: boolean) => {
      return (
        <div className={`${cls}-loading ${isRightBoard ? 'pdr' : ''}`}>
          <div className={`${cls}-loading-ct`}>
            <Spin weId={`${props.weId || ''}_t95961`} size="small" />
          </div>
        </div>
      );
    },
    [props.weId]
  );
  const onLanePageChange = (pageNo: number, pageNum?: number) => {
    props.events.emit(EbdBoardEventName.onLaneBoardPageChange, props.compId, { type: lanePage.pageType || PageModeType.More, pageNo });
    store.setLanePageState({ pageNo, pageNum: `${pageNum}` });
  };
  const laneContentPagination = useCallback(() => {
    if (!isJinZhanBoard || lanePage.pageType === PageModeType.None || !laneGroups.length) return null;
    const commonNoMore = (extra?: JSX.Element) => {
      return (
        <>
          {lanePage.moreLoading ? (
            <div className={`${ydInline}-loadMore`}>
              <Spin weId={`${props.weId || ''}_loading`} size="small" style={{ marginRight: '8px' }} />
              {getLabel('116168', '加载中...')}
            </div>
          ) : !lanePage.hasMore ? (
            <div className={`${ydInline}-loadMore`}>{getLabel('191726', '没有更多了')}</div>
          ) : (
            extra
          )}
        </>
      );
    };
    // 这里是默认
    if (lanePage.pageType === PageModeType.More || !lanePage.pageType) {
      let pageNo = toJS(lanePage.pageNo);
      const extraEle = (
        <div className={`${ydInline}-loadMore-clickMore`} onClick={() => onLanePageChange(++pageNo)}>
          <span>{getLabel('76148', '加载更多')}</span>
          <Icon weId={`yowfvr`} name="Icon-Down-arrow01" />
        </div>
      );
      return <div className={`${ydInline}-loadMore`}>{commonNoMore(extraEle)}</div>;
    }
    if (lanePage.pageType === PageModeType.page) {
      return (
        <div className={`${ydInline}-loadMore pageNumMore`}>
          <Pagination
            weId={`fmflgm`}
            value={lanePage.pageNo}
            total={lanePage.total}
            pageSize={+lanePage.pageNum}
            showSizeChanger={!lanePage.pageSize.customPage}
            onChange={onLanePageChange}
            onSizeChange={onLanePageChange}
            pageSizeOptions={[5, 10, 20, 50]}
          />
        </div>
      );
    }
    if (lanePage.pageType === PageModeType.Scroll) {
      return (
        <>
          {/* 单个泳道下预留高度给滚动 */}
          {isSingleLaneBoard ? <div style={{ height: '20px' }}></div> : null}
          <div
            ref={bottomSentinelRef}
            style={{
              height: '1px',
              width: '100%',
              opacity: 0,
              pointerEvents: 'none',
            }}
          />
          {commonNoMore()}
        </>
      );
    }
  }, [isJinZhanBoard, lanePage, laneGroups, laneGroupsLoading, state.refreshKey]);

  const boardContent = useCallback(
    (item, index) => {
      const itemCls = `${ydCs}-commonSingleBoard-item`;
      const { groups = [] } = item;
      const isCollapsed = state.collapsedItems.includes(item.id);
      return (
        <div
          id={`${compId}_${item.id}`}
          data-key={`${compId}-${item.id}-${index}`}
          className={`${itemCls} ${isJinZhanBoard ? 'showTitle' : ''} ${isCollapsed ? 'collapsed' : 'expanded'} ${item.id}`}
          key={index}
        >
          {isEmpty(groups) ? (
            <div className={`${itemCls}-empty`}>
              <BoardEmpty weId={`${props.weId || ''}_l0d28f@${diffKey}`} />
            </div>
          ) : (
            <>
              {laneLayout === 'inline' && <div className="shadow-container"></div>}
              <BoardCom weId={`${props.weId || ''}_70ngjr`} {...props} laneGroup={item} groups={groups} />
            </>
          )}
        </div>
      );
    },
    [laneGroups, state.collapsedItems, isJinZhanBoard, props]
  );
  const renderLaneStat = useCallback((statItem: LaneStatType) => {
    const mainCls = `${ydInline}-content-item`;
    return (
      <>
        {(statItem?.stats || []).map((item, index) => {
          return (
            <div className={`${mainCls}-bottom-left-list-item`} key={index}>
              <div className={`${mainCls}-bottom-left-list-item-title`}>
                <div className={`${mainCls}-bottom-left-list-item-title-name`}>{item.inputValue?.valueLocale ? item.inputValue?.valueLocale : item.inputValue}</div>：<div>{item.count}</div>
              </div>
            </div>
          );
        })}
      </>
    );
  }, []);
  const contentHeight = useMemo(() => {
    const content = document.querySelector(`#${props.compId} .${cls}-yd`);
    if (!content) return 0;
    return content.clientHeight - 40;
  }, [props.compId, laneGroups]);

  // 看板整体滚动
  const laneContentBoth = useCallback(() => {
    console.log('*👏👏👏***laneStats****', toJS(laneStats));
    return (
      <Scroller direction="v" weId={`${props.weId || ''}_rzmsu1`}>
        <div className={ydB}>
          <div className={`${ydB}-left`}>
            <div className={`${ydB}-left-title`} onClick={() => onLaneBoardCollapsed(isCollapsedAll ? 'expand' : 'collapsed')}>
              <Icon weId={`${props.weId || ''}_ag6jqw`} name={isCollapsedAll ? 'Icon-up-arrow01' : 'Icon-Down-arrow01'} />
              {getLabel('291674', '泳道')}
            </div>
            {laneGroups.map(item => {
              const mainCls = `${ydB}-left-item`;
              const isCollapsed = state.collapsedItems.includes(item.id);
              return (
                <div className={`${mainCls} ${isCollapsed ? 'collapsed' : 'expanded'}`} key={item.id}>
                  <div className={`${mainCls}-title`}>
                    <span onClick={() => handleCollapseToggle(item.id)} className={`${mainCls}-title-icon`}>
                      <Icon weId={`${props.weId || ''}_ag6jqw`} name={state.collapsedItems.includes(item.id) ? 'Icon-up-arrow01' : 'Icon-Down-arrow01'} />
                    </span>
                    {item.name}
                  </div>
                  <div className={`${mainCls}-stat`}>
                    {/* {invoke(pluginCenter, EbdBPlKeys.renderLaneBoardStat, { hook: renderLaneStat, args: [statItem, item] })} */}
                  </div>
                </div>
              );
            })}
          </div>
          <div className={`${ydB}-right`}>
            <div className="shadow-container"></div>
            <div className={`${ydB}-right-wrap`} ref={bothRightRef}>
              <div className={`${ydB}-right-top`} ref={titleRef} key={state.refreshKey}>
                <div className={`${ydB}-right-top-title`}>
                  {mainGroups.map((i, idx) => {
                    return (
                      <div className={`${ydB}-right-top-title-item`} key={idx}>
                        {i.name}
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className={`${ydB}-right-main`}>
                {laneGroups.map((item, index) => {
                  return (
                    <div className={`${ydCs}-commonSingleBoard ${ydB}-right-item`} key={item.id}>
                      {laneGroupsLoading ? loadlongContent(true) : boardContent(item, index)}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </Scroller>
    );
  }, [laneGroups, laneStats, state.collapsedItems, state.refreshKey, laneGroupsLoading]);

  // 看板内部滚动
  const laneContentInline = useCallback(() => {
    const [leftLaneTitle, rightLaneTitle] = invoke(pluginCenter, EbdBPlKeys.getCustomLaneTitle) || [getLabel('291674', '泳道'), getLabel('291673', '阶段信息')];
    return (
      <>
        <div className={`${ydInline}-header`}>
          <div className={`${ydInline}-header-left`}>
            <div className={`${ydInline}-header-left-title`} onClick={() => onLaneBoardCollapsed(isCollapsedAll ? 'expand' : 'collapsed')}>
              <Icon weId={`${props.weId || ''}_ag6jqw`} name={isCollapsedAll ? 'Icon-up-arrow01' : 'Icon-Down-arrow01'} />
              {leftLaneTitle}
            </div>
          </div>
          <div className={`${ydInline}-header-right`} ref={titleRef} key={state.refreshKey}>
            <div className={`${ydInline}-header-right-title`}>{rightLaneTitle}</div>
          </div>
        </div>
        <div className={`${ydInline}-content`} ref={containerRef}>
          <Scroller height={contentHeight} weId={`${props.weId || ''}_rzmsu1`}>
            <div style={{ height: laneGroups.length <= 1 ? '100%' : `auto` }} ref={contentRef}>
              {laneGroups.map((item, index) => {
                const mainCls = `${ydInline}-content-item`;
                const _cls = `${mainCls}-top-right-header`;
                const statItem = laneStats.find(i => i.laneId === item.id);
                const { groups = [] } = item;
                const isCollapsed = state.collapsedItems.includes(item.id);
                return (
                  <div className={mainCls} key={item.id}>
                    <div className={`${mainCls}-top`}>
                      <div className={`${mainCls}-top-left`} key={state.leftRefreshKey}>
                        <div className={`${mainCls}-top-left-title`}>
                          <span onClick={() => handleCollapseToggle(item.id)} className={`${mainCls}-top-left-title-icon`}>
                            <Icon weId={`${props.weId || ''}_ag6jqw`} name={state.collapsedItems.includes(item.id) ? 'Icon-up-arrow01' : 'Icon-Down-arrow01'} />
                          </span>
                          {isJinZhanBoard ? (
                            <LayoutCardWithRouter
                              weId={`${props.weId || ''}_qaani9`}
                              {...props}
                              data={item}
                              config={{ ...newLaneTitleConfig.field, dataset: newLaneTitleConfig.dataset }}
                              sort={index}
                              client="PC"
                              compId={compId}
                              dataset={newLaneTitleConfig.dataset!}
                            />
                          ) : (
                            item.name
                          )}
                        </div>
                      </div>
                      <div className={`${mainCls}-top-right`}>
                        <div className="shadow-container"></div>
                        <div className={`${_cls} ${isCollapsed ? 'collapsed' : 'expanded'}`} data-key={`${compId}-${item.id}-${index}`} key={state.boardHeaderRefreshKey}>
                          <div className={`${_cls}-title`}>
                            {groups.map((i, idx) => {
                              const renderItem = (groupItem: GroupItem) => {
                                return <div>{groupItem.name}</div>;
                              };
                              return (
                                <div className={`${_cls}-title-item`} key={idx}>
                                  {invoke(pluginCenter, EbdBPlKeys.renderLaneBoardTitle, { hook: renderItem, args: [i, idx, groups, isCollapsed] })}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className={`${mainCls}-bottom`}>
                      <div className={`${mainCls}-bottom-left`}>
                        <div className={`${mainCls}-bottom-left-list ${isCollapsed ? 'collapsed' : 'expanded'}`}>
                          {invoke(pluginCenter, EbdBPlKeys.renderLaneBoardStat, { hook: renderLaneStat, args: [statItem, item] })}
                        </div>
                      </div>
                      <div className={`${ydCs}-commonSingleBoard ${laneGroupsLoading ? 'laneGroupsLoading' : ''}`} key={state.refreshKey}>
                        {laneGroupsLoading ? loadlongContent(true) : boardContent(item, index)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            {/* 底部加载区域 */}
            {laneContentPagination()}
          </Scroller>
        </div>
      </>
    );
  }, [laneGroups, laneStats, state.collapsedItems, state.refreshKey, laneGroupsLoading]);

  const laneEmpty = useCallback(() => {
    return (
      <div className={`${cls}-empty`}>
        <BoardEmpty weId={`${props.weId || ''}_j7qosd`} iconSize={{ width: 130, height: 130 }} />
      </div>
    );
  }, [props.weId]);

  const hasLaneData = useMemo(() => {
    return !loading && !isEmpty(laneGroups);
  }, [loading, laneGroups]);

  if (!state.isSetPd) return null;

  return (
    <div className={`${ydCs} ${laneLayout === 'inline' ? 'insideScrollX' : ''} ${isSingleLaneBoard ? 'singleLaneBoard' : ''}`} style={{ background: isJinZhanBoard ? '#F7F9FC' : '#fff' }}>
      {loading ? loadlongContent() : hasLaneData ? (laneLayout === 'both' ? laneContentBoth() : laneContentInline()) : laneEmpty()}
    </div>
  );
});

export default withRouter(LaneBoard);
