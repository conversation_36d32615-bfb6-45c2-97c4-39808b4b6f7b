@import (reference) '../../../../../style/prefix.less';

.@{ebdBClsPrefix}-core {
  height: 100%;

  &>div {
    height: 100%;
  }

  .board-default {
    .ui-scroller__view {
      height: 100%;
    }
  }
}

.@{ebdBClsPrefix}-laneGroup {
  height: 100%;
  @leftCtWidth: 240px;
  @headerHeight: 40px;
  @collapsedHeight: 60px;
  @laneBoardDefaultHeight: 390px;
  @boardWidth: 288px;
  @boardContentPdTop: 10px;
  @pd: 18px;
  @iconMgR: 8px;

  .scrollBar {
    .ui-scroller__bar.is-horizontal:hover {
      height: 8px;
    }

    scrollbar-width: thin; // 为 Firefox 设置细滚动条

    &::-webkit-scrollbar {
      width: 8px; // 为 Webkit 浏览器设置滚动条宽度
      height: 8px; // 为横向滚动条设置高度
      cursor: pointer;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }
  }

  .titleRightCls {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    &-title {
      min-height: @headerHeight;
      line-height: @headerHeight;
      display: flex;
      min-width: 100%;

      &-item {
        width: @boardWidth;
        flex-shrink: 0;
        position: relative;
        padding-right: 6px;
        .flexCt;
        justify-content: flex-start;
      }
    }
  }

  .shadowContainer {
    position: absolute;
    content: '';
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .commonItemTitleCls {
    display: flex;
    background-color: var(--base-white);
    line-height: @headerHeight;
    min-height: @headerHeight;
    padding: 2px @pd;

    &-icon {
      color: #646a73;
      height: 20px;
      width: 20px;
      line-height: 20px;
      border-radius: 50%;
      cursor: pointer;
      border: 1px solid #dde0e3;
      flex-shrink: 0;
      transition: all 0.3s;
      display: inline-flex;
      align-self: center;
      align-items: center;
      justify-content: center;

      &:hover {
        color: var(--primary);
        border-color: var(--primary);
      }
    }

    .ebcoms-list {
      padding: 4px 0 4px 10px;
      position: relative;
      min-width: initial;
      overflow: hidden;
      .flexCt;

      .ui-input,
      .ui-input-wrap,
      .ui-textarea {
        min-height: unset;
        padding: 0;
      }

      .ebcoms-list-flex.ebcoms-list-grid-row-main {
        overflow: auto;
      }

      &:hover {
        background: #fff;
      }
    }
  }

  .titleCls {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    height: 100%;

    &::-webkit-scrollbar {
      display: none;
    }

    &-title {
      display: flex;
      min-width: 100%;

      &-item {
        width: @boardWidth;
        flex-shrink: 0;
        position: relative;
        padding-right: 6px;
        .flexCt;
        justify-content: flex-start;
      }
    }
  }

  .scrollBorder {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 1px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
      background-color: var(--border-color);
    }
  }

  &-loading {
    height: 100%;
    width: 100%;
    .flexCt;

    &-ct {
      height: 100%;

      .ui-spin {
        .flexCt;
      }
    }

    &.pdr {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      padding-top: 0;
    }
  }

  &-yd {
    height: 100%;
    color: #1f2329;
    font-size: var(--font-size-12);
    overflow: hidden;
    display: flex;
    flex-direction: column;

    // 动态显示边框
    &.showBorder {

      .@{ebdBClsPrefix}-laneGroup-yd-header-left::after,
      .@{ebdBClsPrefix}-laneGroup-yd-content-left::after {
        opacity: 1;
      }
    }

    // 单个看板内部滚动
    &.insideScrollX {
      .@{ebdBClsPrefix}-laneGroup-yd-content-item-bottom {
        &-right {
          overflow-x: hidden;

          &>div {
            display: block;
            // .scrollBar;

            .ui-scroller__wrap {
              // .scrollBar;
              // margin-right: -11px !important;
            }
          }

          &-item {
            overflow-y: initial;
          }
        }
      }
    }

    // 默认撑开
    &.singleLaneBoard {
      .@{ebdBClsPrefix}-laneGroup-yd-content {
        height: 100%;

        &-item {
          height: 100%;
          display: flex;
          flex-direction: column;

          &-bottom {
            flex: 1;

            &-left-list.expanded {
              height: 100%;
            }

            &-right-item {
              height: 100% !important;

              &>.ui-list-scrollview-wrap>.ui-scroller>.ui-scroller__wrap>.ui-scroller__view {
                overflow-y: hidden;
              }
            }
          }
        }
      }
    }

    &-commonSingleBoard {
      flex: 1;
      overflow: auto;
      position: relative;

      .shadow-container {
        .shadowContainer;
      }

      .@{ebdBClsPrefix}-laneGroup-yd-commonSingleBoard-item[data-can-scroll='true'] .shadow-container {
        opacity: 1;
        z-index: 2;
      }

      &-item {
        height: @laneBoardDefaultHeight;
        overflow-y: hidden;
        position: relative;
        transition: height 0.3s ease-out;
        padding-top: 6px;

        &-empty {
          .ui-empty {

            &-image,
            &-description {
              justify-content: center;
            }
          }
        }

        &-groups {
          opacity: 0;
          transition: opacity 0.3s ease-out;
          position: absolute;
          left: 0;
          top: @boardContentPdTop;
          height: @headerHeight;
          line-height: @headerHeight;
          display: flex;
          width: 100%;
          .titleRightCls;
        }

        &.collapsed {
          height: 0;
          padding: 0 !important;

          .@{ebdBClsPrefix}-board-empty {
            opacity: 0;
            transition: opacity 0.3s ease-out;
          }

          .ui-list-scrollview-wrap {
            opacity: 0;
          }

          .@{ebdBClsPrefix}-laneGroup-yd-content-right-item-groups {
            opacity: 1;
          }
        }

        &-collapsedTitleWrap {
          .titleRightCls;
        }

        .ui-list-scrollview-wrap {
          opacity: 1;
          transition: opacity 0.3s ease-out;
        }

        .ui-scroller__wrap>.ui-scroller__view {
          height: 100%;
        }

        .@{ebdBClsPrefix}-list-body-hasAdd {
          max-height: calc(100% - 40px) !important;
        }

        .ui-board.ui-list {
          overflow: hidden !important;
        }

        &>.ui-list-scrollview-wrap>.ui-scroller>.ui-scroller__wrap {
          &>.ui-scroller__view {
            max-height: calc(100% - 10px);
          }
        }
      }

      // 原始看板复写
      .@{ebdBClsPrefix}-board-empty {
        .ui-empty {
          bottom: initial;
        }
      }

      .@{ebdBClsPrefix}-list-body {
        max-height: calc(100% - 40px);
      }
    }

    &-inline {
      &-header {
        display: flex;
        position: sticky;
        top: 0;
        z-index: 10;

        &-left {
          width: @leftCtWidth;
          padding-left: @pd;
          flex-shrink: 0;
          .scrollBorder;

          &-title {
            min-height: @headerHeight;
            line-height: @headerHeight;
            cursor: pointer;

            .ui-icon {
              margin-right: @iconMgR;
              position: relative;
              top: -1px;
            }
          }
        }

        &-right {
          .titleRightCls;
        }
      }

      &-content {
        height: 100%;

        .@{ebdBClsPrefix}-list-empty,
        .@{ebdBClsPrefix}-list-body {
          background-color: #f7f9fc !important;
        }

        &-item {
          &-top {
            display: flex;

            &-left {
              width: @leftCtWidth;

              &-title {
                .commonItemTitleCls;
              }

              &-list {
                height: @laneBoardDefaultHeight;
                padding-top: 10px;
                transition: height 0.3s ease-out;

                &-item {
                  color: #646a73;
                  font-size: var(--font-size-12);
                  margin-bottom: 8px;
                  padding-left: calc(12px + @iconMgR);
                }

                &.collapsed {
                  height: 0;
                  overflow: hidden;
                  padding: 0;
                }
              }
            }

            &-right {
              flex: 1;
              overflow: auto;
              position: relative;

              .@{ebdBClsPrefix}-laneGroup-yd-content-item-top-right[data-can-scroll="true"] .shadow-container {
                opacity: 1;
                z-index: 2;
              }


              .shadow-container {
                .shadowContainer;
              }

              &-header {
                .titleCls;
                background-color: var(--base-white);

                &-title {
                  height: 100%;
                }
              }
            }

            .@{ebdBClsPrefix}-laneGroup-yd-inline-content-item-top-right[data-can-scroll='true']>.shadow-container {
              opacity: 1;
              z-index: 2;
            }
          }

          &-bottom {
            display: flex;
            transition: height 0.3s ease-out;
            position: relative;
            overflow: hidden;

            &-left {
              width: @leftCtWidth;
              position: relative;

              &-list {
                height: @laneBoardDefaultHeight;
                padding-top: 10px;

                &-item {
                  color: #646a73;
                  font-size: var(--font-size-12);
                  margin-bottom: 8px;
                  padding-left: calc(12px + @iconMgR);

                  &-title {
                    display: flex;

                    &-name {
                      max-width: 70%;
                      .ellipsis;
                    }
                  }
                }

                &.collapsed {
                  height: 0;
                  overflow: hidden;
                  padding: 0;
                }
              }
            }
          }
        }

        &>.ui-scroller>.ui-scroller__wrap>.ui-scroller__view {
          height: 100%;
        }
      }

      &-loadMore {
        width: 100%;
        height: 30px;
        background: #f7f9fc;
        font-size: 12px;
        color: #999;
        .flexCt;

        .ui-spin {
          .flexCt;
        }

        &.pageNumMore {
          justify-content: flex-end;
          padding-right: 16px;
        }

        &-clickMore {
          transition: all 0.3s;
          cursor: pointer;

          &:hover {
            color: var(--primary);
          }
        }
      }
    }

    &-both {
      display: flex;
      transition: height 0.3s ease-out;
      position: relative;
      overflow: hidden;
      // background-color: #f7f9fc;

      &-left {
        width: @leftCtWidth;
        position: relative;
        flex-shrink: 0;

        &-title {
          min-height: @headerHeight;
          line-height: @headerHeight;
          cursor: pointer;
          padding: 0 16px;

          &-icon {
            margin-right: 4px;
          }

          .ui-icon {
            margin-right: @iconMgR;
            position: relative;
            top: -1px;
          }
        }

        &-item {
          max-height: @laneBoardDefaultHeight;

          &.collapsed {
            .@{ebdBClsPrefix}-laneGroup-yd-both-left-item-stat {
              height: 0;
            }
          }

          &-title {
            .commonItemTitleCls;
            padding: 0;
            cursor: pointer;
            padding: 0 16px;

            &-icon {
              margin-right: 8px;
            }
          }

          &-stat {
            height: calc(@laneBoardDefaultHeight - 40px);
            transition: height 0.3s ease-out;
          }
        }

      }

      &-right {
        overflow: hidden;
        position: relative;

        &-wrap {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          overflow-x: scroll;
          padding-left: 8px;
        }

        &[data-can-scroll="true"]>.shadow-container {
          opacity: 1;
          z-index: 2;
        }

        .shadow-container {
          .shadowContainer;
        }

        &-top {
          .titleRightCls;
          flex: initial;
        }

        &-main {
          flex: 1;
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
        }

        .@{ebdBClsPrefix}-laneGroup-yd-commonSingleBoard {
          display: inline-flex;

          &-item {
            display: inline-flex;
            padding-top: 0;

            .ui-board {
              display: flex;
            }
          }

        }
      }

    }
  }
}