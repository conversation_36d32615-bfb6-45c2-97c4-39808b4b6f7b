export const setScrollStatus = (scrollLeftValue: number, ele: HTMLDivElement) => {
  if (scrollLeftValue > 0) {
    ele.setAttribute('data-can-scroll', 'true');
  } else {
    ele.removeAttribute('data-can-scroll');
  }
};
export const setOnScrollStatus = (scrollLeftValue: number, event: any, isContent?: boolean) => {
  const ele = isContent ? event.target.parentElement.parentElement.parentElement : event.target.parentElement;
  setScrollStatus(scrollLeftValue, ele);
};
