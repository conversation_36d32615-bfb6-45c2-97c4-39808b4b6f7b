export const setScrollStatus = (scrollLeftValue: number, ele: HTMLDivElement) => {
  if (scrollLeftValue > 0) {
    ele.setAttribute('data-can-scroll', 'true');
  } else {
    ele.removeAttribute('data-can-scroll');
  }
};
export const setOnScrollStatus = (scrollLeftValue: number, event: any, isContent?: boolean) => {
  const ele = isContent ? event.target.parentElement.parentElement.parentElement : event.target.parentElement;
  setScrollStatus(scrollLeftValue, ele);
};


export const dealGroupCountData = (groupCount: any[] = []) => {
  let name = '',
    count = '',
    showgroup = true;
  if (groupCount.length === 0) return { name, count, showgroup: false };
  groupCount.map((el: any, ind: number) => {
    if (el?.name?.valueLocale === '') {
      el.name.valueLocale = ' ' + '*' + ' ';
    }

    if (ind === groupCount.length - 1) {
      count = count + el.count;
      name = name + el?.name?.valueLocale;
    } else {
      count = count + el.count + '/';
      name = name + el?.name?.valueLocale + '/';
    }
  });
  return { name, count, showgroup };
};