// 渲染pc/移动端看板内容 以支持标准和泳道二维看板
import { Then, If, Else } from 'react-if';
import { Spin } from '@weapp/ui';
import { withRouter } from 'react-router-dom';
import { observer } from 'mobx-react';
import { ebdBClsPrefix, DEFAULT_LANE_ID, DEFAULT_LANE_ITEM } from '../../../../../constants';
import BoardCom from '../../board-view/BoardCom';
import { BoardMainProps } from '../../board-view/types';
import LaneBoard from './lane';
import { EbdBPlKeys } from '../../../../../types/common';
import { invoke } from '../../../../../utils';
import './index.less';

const cls = `${ebdBClsPrefix}-core`;
interface Props extends BoardMainProps {
  [x: string]: any;
  // groups: GroupItem[];
  loading?: boolean;
  showLoadingStatus?: boolean;
}
const RenderSingleBoard = (props: Props) => {
  const { loading, showLoadingStatus, compId } = props;
  return (
    <>
      <If weId={`${props.weId || ''}_3wkou8`} condition={!!(loading && showLoadingStatus)}>
        <Then weId={`${props.weId || ''}_w25zdu`}>
          <Spin weId={`${props.weId || ''}_rtwpke`} spinning={loading} className={`${ebdBClsPrefix}-container-fullLoading`} />
        </Then>
        <Else weId={`${props.weId || ''}_jticov`}>
          <div id={`${compId}_${DEFAULT_LANE_ID}`} className={'board-default'}>
            <BoardCom weId={`${props.weId || ''}_bzabug`} {...props} laneGroup={DEFAULT_LANE_ITEM} />
          </div>
        </Else>
      </If>
    </>
  );
};

const BoardCore = (props: Props) => {
  const { store } = props;
  const { isLaneConfig, loading, laneGroups } = store;
  // 进展看板
  const [hasLaneBoard] = invoke(props.pluginCenter, EbdBPlKeys.getIsLaneBoard) || [false];
  const [_laneLoading] = invoke(props.pluginCenter, EbdBPlKeys.getIsLaneLoading) || [loading];
  return (
    <div className={cls}>
      {hasLaneBoard || isLaneConfig ? (
        <div style={{ paddingBottom: 8 }}>
          <LaneBoard weId={`${props.weId || ''}_119ooe`} {...props} loading={_laneLoading} />
        </div>
      ) : (
        <RenderSingleBoard weId={`${props.weId || ''}_xoxj59`} {...props} groups={laneGroups[0]?.groups || []} />
      )}
    </div>
  );
};

export default withRouter(observer(BoardCore));
