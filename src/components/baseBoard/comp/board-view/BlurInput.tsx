import React, { useEffect, useMemo, useState, useRef } from 'react';
import { InputProps, Popover } from '@weapp/ui';
import { getLabel, trim } from '@weapp/utils';
import { observer } from 'mobx-react';
import LocaleInput from '../../../common/locale/LocaleInput';
import { LocaleExValueDataType } from '../../../common/locale/types';
import { getLocaleValueString } from '../../../common/locale/utils';

export interface BlurInputProps extends React.Attributes, Omit<InputProps, 'onChange' | 'value'> {
  onChange: (data: LocaleExValueDataType) => void;
  title?: string;
  readOnly?: boolean;
  onFocusChange?: (isFocue: boolean) => void;
  ebBusinessId: string;
  value: LocaleExValueDataType;
  groupCount?: any; //分组统计配置
  isAdd?: Boolean; // 是否为新建分组
  addTitle?: string; // 新建标题
}

const BlurInput: React.ComponentType<BlurInputProps> = observer(props => {
  const inputRef = useRef() as any;
  const timerRef = useRef() as any;
  const isIconFocusRef = useRef() as any;
  const { isAdd, value = '', title: placeholder = getLabel('105955', '输入分组名称'), onFocusChange, ebBusinessId, groupCount = [], addTitle = '' } = props;

  const [val, setVal] = useState<LocaleExValueDataType>('');
  const [isFocus, setIsFocus] = useState<boolean>(false);

  const onChange = (v: LocaleExValueDataType) => {
    setVal(v);
  };

  const onBlur = (data?: LocaleExValueDataType) => {
    setTimeout(() => {
      if (isIconFocusRef.current) {
        return;
      }
      if (!data) {
        setVal(value);
      } else if (typeof data === 'string' && !trim(data)) {
        setVal(value);
      } else {
        props.onChange(data);
      }
      onClose();
    }, 300);
  };
  const onClose = () => {
    setIsFocus(false);
    isIconFocusRef.current = false;
    onFocusChange?.(false);
    isAdd && setVal('');
  };
  useEffect(() => {
    return () => {
      clearTimeout(timerRef.current);
    };
  }, []);
  useEffect(() => {
    setVal(value);
  }, [value]);

  const onFocus = () => {
    setIsFocus(true);
    onFocusChange?.(true);
    timerRef.current = setTimeout(() => {
      inputRef.current && inputRef.current.handleFocus();
    }, 300);
  };
  const isReadOnly = props?.readOnly || !isFocus;
  const titleStr = getLocaleValueString(val);

  const getGroupData = useMemo(() => {
    let name = '',
      count = '',
      showgroup = true;
    if (groupCount.length === 0) return { name, count, showgroup: false };
    groupCount.map((el: any, ind: number) => {
      if (el?.name?.valueLocale === '') {
        el.name.valueLocale = ' ' + '*' + ' ';
      }

      if (ind === groupCount.length - 1) {
        count = count + el.count;
        name = name + el?.name?.valueLocale;
      } else {
        count = count + el.count + '/';
        name = name + el?.name?.valueLocale + '/';
      }
    });
    return { name, count, showgroup };
  }, [groupCount]);

  const popup = useMemo(() => {
    return <div style={{ lineBreak: 'anywhere' }}>{getGroupData.name}</div>;
  }, [getGroupData]);
  return (
    <div className={'blur-input-content'}>
      <div onDoubleClick={onFocus} onClick={() => (isAdd ? onFocus() : '')} className={isReadOnly ? 'blur-input-wrapper' : 'blur-input-wrapper-focus'} title={titleStr}>
        <div className="blur-input-content-text">
          {isAdd ? (
            <div className={isFocus ? 'blur-input-content-text-hide' : 'blur-input-content-text-show'}>
              {!isFocus ? <span className={'blur-input-content-addIcon'}>+</span> : ''}
              {addTitle}
            </div>
          ) : (
            ''
          )}
          <div className={isAdd ? (isFocus ? 'blur-input-content-text-show' : 'blur-input-content-text-hide') : ''}>
            <LocaleInput
              weId={`${props.weId || ''}_5lv2i0`}
              ebBusinessId={ebBusinessId}
              placeholder={placeholder}
              value={val}
              readOnly={isReadOnly}
              onBlur={onBlur}
              onChange={onChange}
              onSave={onBlur}
              onDialogClose={onClose}
              required
              customRenderSuffix={(_, ele) => {
                const customClick = (e: any) => {
                  isIconFocusRef.current = true;
                }
                return <div {..._} onClick={customClick}>{ele}</div>
              }}
            />
          </div>
        </div>
      </div>
      {getGroupData?.showgroup && (
        <div className={'help-title'}>
          <Popover weId={`${props.weId || ''}_wxamud`} popup={popup} placement={'top'} popoverType="tooltip">
            <span>{`（${getGroupData?.count}）`}</span>
          </Popover>
        </div>
      )}
    </div>
  );
});

export default BlurInput;
