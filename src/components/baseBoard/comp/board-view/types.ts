import { Attributes } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { BoardViewStore } from '../../../common/board/store';
import { BaseParamsProps, GroupItem } from '../../../common/board/types';
import { AnyObj } from '../../../../types/common';
import { LaneItemData } from '../../config/cmps/yd/types';

export interface BoardViewProps extends RouteComponentProps, BaseParamsProps, Attributes {
  ebStore: any;
  /** 组件mount前的回调 */
  onBeforeMount?: (store: any) => void;
}

export interface BoardMainProps extends BaseParamsProps, BoardViewProps {
  store: BoardViewStore;
  contentHeight?: number;
  urlParams: any;
  singlePageData?: any;
  events: AnyObj;
  /** 组件mount前的回调 */
  onBeforeMount?: (store: any) => void;
  groups: GroupItem[];
  laneGroup: LaneItemData;
}
export enum OrderType {
  DESC = 'desc', // 降序
  ASC = 'asc', // 升序
}
