import {
  AnyObj,
  ColorPicker,
  Form,
  FormDatas,
  FormItemProps,
  FormLayoutType,
  FormStore,
  FormSwitchProps,
} from '@weapp/ui';
import { classnames } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { Attributes, useEffect, useReducer } from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../../../constants';
import { getIsWhiteColors } from '../../../../common/board/utils';
import LocaleInput from '../../../../common/locale/LocaleInput';
import { ColorStyleType } from '../../../engine/func-groups/constants';
import { FieldKeyType, formCreatItems, getFormCreateLayout } from './FormConfig';

interface BaseFormDomProps extends Attributes {
  store: FormStore;
  datas?: FormDatas;
  layout?: Array<FormLayoutType>;
  items?: FormItemProps;
  hideField?: FieldKeyType[];
  ebBusinessId: string;
  formId?: string;
  color?: any;
  boardColorStyle?: string;
  onColorChange?: (color: any) => void;
}

function init(storeParams: AnyObj) {
  storeParams.store.initForm({
    data: {},
    items: storeParams.items,
    layout: storeParams.layout,
    groups: [],
  });
  return storeParams;
}

const BaseFormDom = observer((props: BaseFormDomProps) => {
  const {
    datas = {},
    store: pStore,
    items = formCreatItems,
    layout = getFormCreateLayout(), // props.hideField
    ebBusinessId,
    color,
    boardColorStyle,
  } = props;
  const [{ store }, initStore] = useReducer(init, { store: pStore, items, layout });

  useEffect(() => {
    initStore();
  }, []);

  useEffect(() => {
    store.updateDatas(datas);
  }, [datas, store]);

  const customRenderFormSwitch = (key: string, formProps: FormSwitchProps) => {
    const { value = '', onChange, ...restProps } = formProps.props;
    if (key === FieldKeyType.Name) {
      return (
        <div
          className={classnames({
            [`${ebdBClsPrefix}-custom-group-name`]:
              boardColorStyle === ColorStyleType.HasStyle,
          })}
        >
          <LocaleInput
            weId={`${props.weId || ''}_foyrai`}
            {...restProps}
            value={value}
            onChange={onChange}
            ebBusinessId={ebBusinessId}
          />
          <When
            weId={`${props.weId || ''}_nt0x4n`}
            condition={boardColorStyle === ColorStyleType.HasStyle}
          >
            <div className={`${ebdBClsPrefix}-custom-colorpicker`}>
              <ColorPicker
                weId={`${props.weId || ''}_7d5qm4`}
                onChange={props.onColorChange}
                value={color}
                enableAlpha={false}
              >
                <div
                  className={`${ebdBClsPrefix}-custom-colorpicker-button`}
                  style={{
                    background: color,
                    borderColor: `${getIsWhiteColors(color) ? 'var(--border-color)' : color}`,
                  }}
                />
              </ColorPicker>
            </div>
          </When>
        </div>
      );
    }
  };

  return (
    <Form
      weId={`${props.weId || ''}_014jtf`}
      store={store}
      isMobile={false}
      customRenderFormSwitch={customRenderFormSwitch}
    />
  );
});

export default BaseFormDom;
