import { FormItemProps } from '@weapp/ui';
import { getLabel } from '@weapp/utils';

export enum FieldKeyType {
  Name = 'name'
}

export const formCreatItems: FormItemProps = {
  [FieldKeyType.Name]: {
    itemType: 'CUSTOM',
    required: true,
    errorType: 'popover',
  },
};

export const getFormCreateLayout = () => [
  [
    {
      id: FieldKeyType.Name,
      label: getLabel('105954', '分组名称'),
      items: [FieldKeyType.Name],
      labelSpan: 7,
      hide: false,
      className: 'formCreateLayout-cls',
    },
  ],
];
