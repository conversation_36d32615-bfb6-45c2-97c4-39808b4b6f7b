import { observer, Provider } from 'mobx-react';
import React, { PureComponent, Suspense } from 'react';
import { withRouter } from 'react-router-dom';
import {getUrlParams} from '../../../../utils';
import { BoardViewStore } from '../../../common/board/store';
import withEbParams from '../../../common/withEbParams';
import Main from './Main';
import { BoardViewProps } from './types';
import './index.less';

@withEbParams
@observer
class BoardView extends PureComponent<BoardViewProps & React.Attributes> {
  render() {
    const {
      compId, pageId, config, ebParams = {}, onRef, isDoc, ebStore, pluginCenter, onBeforeMount, refPluginPackage
    } = this.props;
    const urlParams: any = { ...getUrlParams(), ...ebParams };
    return (
      <Provider weId={`${this.props.weId || ''}_38axof`} store={new BoardViewStore()}>
        <Suspense weId={`${this.props.weId || ''}_4kyviz`} fallback={<div />}>
          {/* 文档下屏蔽点击事件 */}
          {isDoc ? <div className='ebcom-kanban-isDoc' /> : ''}
          <Main
            weId={`${this.props.weId || ''}_1qf2vu`}
            {...this.props}
            onRef={onRef}
            isDoc={isDoc}
            compId={compId}
            pageId={pageId}
            config={config}
            urlParams={urlParams}
            ebStore={ebStore}
            pluginCenter={pluginCenter}
            onBeforeMount={onBeforeMount}
            refPluginPackage={refPluginPackage}
          />
        </Suspense>
      </Provider>
    );
  }
}
export default withRouter(BoardView);