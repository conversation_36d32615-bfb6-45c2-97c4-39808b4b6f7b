/**
 * <AUTHOR>
 * @createTime 2022 -02 -09
 */

import { action, observable, runInAction } from 'mobx';
import { ajax } from '../../../utils/ajax';
import EngineBaseStore from '../../../store/EngineBaseStore';
import { IdParams } from './types';

export class BoardEngineStore extends EngineBaseStore {
  @observable boardId: string = '';

  @observable idParams: IdParams | {} = {};

  @action
  init = async (params: any) => {
    this.selectedFormId = params?.objId || '';
    this.appId = params?.appId || '';
    this.boardId = params?.id || '';
    this.currentCardData = params;
    await this.getBackBoardInfo(this.boardId);
  };

  @action('请求统计')
  getBackBoardInfo = async (boardId: string) => {
    const datas: IdParams = await ajax({
      method: 'GET',
      url: `/api/bs/ebuilder/form/kanban/getInfo?apid=${this.appId}`,
      params: {
        kanbanId: boardId,
      },
      ebBusinessId: this.appId,
    });
    runInAction(() => {
      const {
        objId, pageId, viewId, compId,
      } = datas;
      this.idParams = {
        objId,
        pageId,
        viewId,
        appId: this.appId,
        compId,
      };
    });
  };
}

export default new BoardEngineStore();
