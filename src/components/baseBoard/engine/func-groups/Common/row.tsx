import { Layout } from '@weapp/ui';
import { ebdBClsPrefix } from '../../../../../constants';
import { TitleText } from './';
import './index.less';

const { Row, Col } = Layout;
export interface IRowItemProps {
  weId?: string;
  title?: string | React.ReactNode;
  helpInfo?: string[];
  content: string | React.ReactNode;
  extraContent?: string | React.ReactNode;
  extraRowCls?: string;
  titleRowSpan?: number;
  contentRowSpan?: number;
}
const cls = `${ebdBClsPrefix}-board-group-common`;
const rowItem = (props: IRowItemProps) => {
  return (
    <Row weId={`${props.weId || ''}_zyga7c`} className={`${cls}-row ${props.extraRowCls || ''}`}>
      <Col weId={`${props.weId || ''}_1qb021`} span={props.titleRowSpan || 4}>
        <TitleText weId={`${props.weId || ''}_5oqqb5`} title={props.title} helpInfo={props.helpInfo?.filter(Boolean)} />
      </Col>
      <Col weId={`${props.weId || ''}_9s7ue0`} span={props.contentRowSpan || 8}>
        {props.content}
        {props.extraContent}
      </Col>
    </Row>
  );
};

export default rowItem;
