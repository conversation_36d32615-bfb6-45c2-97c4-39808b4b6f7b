import { ComponentType } from 'react';
import Loadable from '../../../../react-loadable';
import { IRowItemProps } from './row';
import { ITitleTextProps } from './titleText';



const TitleText = Loadable({
  name: 'TitleText',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_group_TitleText" */
      './titleText'
    ),
}) as ComponentType<ITitleTextProps>;
const GroupRowItem = Loadable({
  name: 'RowItem',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_group_RowItem" */
      './row'
    ),
}) as ComponentType<IRowItemProps>;

export { GroupRowItem, TitleText };
