import { Help, PopupPlacement } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../../../constants';
import './index.less';

export interface ITitleTextProps {
  weId?: string;
  title: string | React.ReactNode;
  helpInfo?: string[];
  placement?: PopupPlacement;
}
const cls = `${ebdBClsPrefix}-board-group-common`
const TitleText = (props: ITitleTextProps) => {
  const { weId, title, helpInfo = [], placement = 'bottomLeft' } = props;
  const helpCmp = helpInfo.map(i => {
    return <p key={i}>{i}</p>;
  });
  return (
    <div className={`${cls}-titleText`}>
      <span>{title}</span>
      {isEmpty(helpInfo) ? (
        ''
      ) : (
        <Help
          weId={`${weId || ''}_kr05d2`}
          title={<div>{helpCmp}</div>}
          popoverProps={{ placement, triggerProps: { popupClassName: `${cls}-titleText-help` } }}
        />
      )}
    </div>
  );
};

export default TitleText;
