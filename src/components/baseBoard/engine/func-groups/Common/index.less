@import (reference) '../../../../../style/prefix.less';


.@{ebdBClsPrefix}-board-group-common {
  &-wrap{
    background-color: #fff;
  }
  &-row{
    .flexCt;
    justify-content: flex-start;
    min-height: 36px;
    width: 100%;
  }
  &-titleText {
    color: var(--regular-fc);
    font-size: var(--font-size-12);
    .ui-help{
      position: relative;
      top: -1px;
      left: 4px;
    }
    &-help{
      p{
        margin: 0 0 4px 0;
        padding: 0;
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
  }
  // 分组标题设置
  &-groupTitle{
    display: flex;
    flex-wrap: wrap;
    &-tag{
      margin: 0 6px 6px 0;
      cursor: pointer;
      .ui-tag:hover{
        background: var(--tag-primary-bg-color);
        color: var(--tag-primary-font-color);
        border-color: var(--tag-primary-border-color);
      }
    }
  }
  &-custom-colorpicker{
    position: relative;
    top: -2px;
    .ui-colorpicker{
      max-width: 50px;
      min-width: 0;
    }
    &-button{
      width: 22px;
      height: 22px;
      border: var(--border-solid);
      margin-left: 8px;
      border-radius: 50%;
      cursor: pointer;
    }
  }
}