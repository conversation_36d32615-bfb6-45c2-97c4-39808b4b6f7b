@import (reference) '../../../../style/prefix.less';

.@{ebdBClsPrefix}-func-groups {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 32px;
  overflow: hidden;
  & > .ui-btn {
    margin-right: var(--h-spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.@{ebdBClsPrefix}-board-group {
  .ui-dialog-body-container, .ui-dialog-footer{
    background-color: #fff;
  }
  .ui-dialog-content.ui-dialog-content-middle, .ui-dialog-body-container{
    min-height: initial !important;
  }
}
.@{ebdBClsPrefix}-view-group {
  color: var(--regular-fc);
  display: flex;
  flex-direction: column;

  &-header {
    width: 100%;
    flex: 0 1 auto;
    .view-group-header-row {
      font-size: var(--font-size-12);
      height: var(--line-height-base);
      display: flex;
      vertical-align: middle;
      .view-group-header-col-label {
        line-height: var(--line-height-base);
      }
      .view-group-header-col-content {
        display: flex;
        vertical-align: middle;
        align-items: center;
        .ui-radio-group {
          .ui-radio-wrapper {
            min-width: 150px;
          }
        }
        .ui-select {
          min-width: 200px;
          margin-right: 8px;
        }
      }
    }
  }

  &-main {
    width: 100%;
    font-size: var(--font-size-12);
    &-header {
      text-align: right;
      background: var(--base-white);
      border: 1px solid var(--border-color);
      border-bottom: none;
      padding: 8px 16px 0px 16px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      gap: 8px;
      .@{ebdBClsPrefix}-colorpicker-selector{
        display: flex;
        align-items: center;
        gap: 5px;
        &-box{
          border: var(--border-solid);
          border-radius: var(--border-radius-xs);
          width: 40px;
          height: 24px;
          background: var(--base-white);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        &-box-active{
          border-color: var(--primary);
          .@{ebdBClsPrefix}-colorpicker-selector-icon{
            &::after{
              border-color: var(--primary);
            }
            .ui-icon{
              color: var(--primary);
            }
          }
        }
        &-icon{
          span{
            display: flex;
            align-items: center;
            justify-content: center;
          }
          &-active{
            .ui-icon-svg{
              color: var(--primary);
            }
          }
        }
      }
    }
    &-card {
      overflow-y: auto;
      background: var(--base-white);
      padding: var(--v-spacing-md) var(--h-spacing-lg);
      border: 1px solid var(--border-color);
      border-top: none;
      height: 100%;
      max-height: 420px;
      .@{ebdBClsPrefix}-drag-list .@{prefix}-drag-list-item:hover {
        background: var(--base-white);
      }
      .list-item-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-left: 30px;
        .@{ebdBClsPrefix}-locale-input {
          display: flex;
          align-items: center;
        }
        .@{ebdBClsPrefix}-custom-colorpicker{
          margin-left: 8px;
          .ui-colorpicker{
            min-width: auto;
            max-width: auto;
            &:hover{
              background: transparent;
            }
          }
          &-button{
            width: 22px;
            height: 22px;
            border: var(--border-solid);
            border-radius: 50%;
            cursor: pointer;
          }
        }
        .@{ebdBClsPrefix}-custom-desc-set{
          margin: 0 6px 0 10px;
          .ui-icon{
            cursor: pointer;
          }
        }
      }
      .weapp-ebdf-drag-list .weapp-ebdf-drag-list-item .list-item-content > div {
        height: auto;
      }
    }
    &-field {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .ui-icon{
        cursor: pointer;
      }
      &-select {
        width: 200px;
      }
      &-imgTip{
        margin-left: 4px;
        display: inline-block;
        transition: all 0.3s ease;
        &-popover{
          overflow: hidden;
          border-radius: 6px;
          .ui-popover-content{
            padding: 0;
          }
          &.fade-container {
            animation-duration: 0.5s;
            animation-fill-mode: both;
          }
          &.fade-in {
            animation-name: fadeIn;
          }
          @keyframes fadeIn {
            from {
              opacity: 0;
            }
            to {
              opacity: 1;
            }
          }
        }
        &-content{
          padding: 12px 12px 6px;
          &-title{
            font-size: var(--font-size-12);
            color: var(--main-fc);
            margin: 0 0 6px 10px;
            position: relative;
            &::before{
              position: absolute;
              content: '';
              background-color: var(--primary);
              width: 2px;
              height: 12px;
              left: -6px;
              top: 44%;
              transform: translateY(-50%);
            }
          }
          &.3{
            img{
              width: 300px;
            }
          }
          &.5{
            img{
              width: 440px;
            }
          }
          img{
            height: 140px;
          }
        }
        .Icon-investigation02{
          &:hover{
            color: var(--primary);
          }
        }
      }
      .ui-browser{
        position: relative;
        left: -8px;
        top: -2px;
      }
    }
    //关联自定义分组
    &-layout{
      background: var(--base-white);
        border: 1px solid var(--border-color);
        .@{ebdBClsPrefix}-view-group-main-header{
          border: none;
        }
        .@{ebdBClsPrefix}-view-group-main-card{
          border: none;
        }
        .layout-left{
          border-right: 1px solid var(--border-color);
          height: 100%;
          overflow-x: hidden;
          overflow-y: auto;
          .ui-spin{
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .layout-left-content-list{
            .list-item-active{
              color: var(--primary);
            }
          }
          .ui-list{
            .ui-list-content{
              cursor: pointer;
            }
          }
        }
        .layout-content{
          height: 100%;
        }
    }
  }
  &-titleText {
    .ui-help{
      position: relative;
      top: -1px;
      left: 4px;
    }
    &-help{
      p{
        margin: 0 0 4px 0;
        padding: 0;
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
  }
}

.ui-btn-default.engine-btn-select {
  color: var(--primary) !important;
  background-color: var(--bubble-color) !important;
  border-color: var(--primary) !important;
  svg {
    color: var(--primary);
  }
}

.ui-btn-link.engine-btn-select {
  color: var(--primary) !important;
  svg {
    color: var(--primary);
  }
}

.@{ebdBClsPrefix}-func-groups-statistics{
  .groupstatistics-top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    background: var(--base-white);
    font-weight: 400;
    font-style: normal;
    font-size: var(--font-size-12);
    padding: 0 8px;
    margin-bottom: 16px;
    color: #666666;
  }
  .statistics-table{
    .edit-color{
      color: var(--btn-link-fc);
      cursor: pointer;
    }
    .edit-splice{
      padding: 0 4px;
    }
  }

  &-setting{
    .setting-top{
      .setting-top-content{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
        font-style: normal;
        font-size: var(--font-size-12);
        height: 40px;
        background: var(--base-white);
        border: 1px solid var(--border-color);
        padding: 0 16px;
        color: #666666;
        .setting-top-lebale{
          width: 50px;
          position: relative;
        }
        .setting-top-lebale::after{
          content: '*';
          color: var(--form-item-required);
          vertical-align: middle;
          position: absolute;
          font-size: 20px;
          padding-left: 24px;
          position: absolute;
          right: -20px;
        }
      }
      .setting-name{
        border-bottom: 0;
        .ui-locale{
          .ui-locale-input{
            margin: 0 !important;
            width: 350px;
            border: 0;
          }
        }
      }
      .setting-filter{
        .ebcomponents-config-filter{
          width: 350px;
          .ebcomponents-config-filter-inp{
            border: 0;
          }
        }
      }
    }
  }
}