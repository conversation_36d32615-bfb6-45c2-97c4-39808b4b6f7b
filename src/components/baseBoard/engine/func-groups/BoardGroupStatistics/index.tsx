import { Button, Table, Dialog, Icon, Switch } from '@weapp/ui';
import { getLabel, cloneDeep } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useEffect, useState, useMemo } from 'react';
import { ebdBClsPrefix } from '../../../../../constants';
import store from '../store';
import { BoardGroupStatisticsProps, BoardGroupStatisticsMainProps } from '../types';
import '../index.less';
import StatisticsSetting from './statisticsSetting';
import { CorsComponent } from '@weapp/ui';
import { specialFieldsFilter } from '../../../../../utils';
import { ConfigItem } from '../types';
import ebdcoms from '../../../../../utils/ebdcoms';
const { confirm } = Dialog;
const { UUID } = ebdcoms.get();

const diffKey = 'BoardGroupStatistics'
const Main: React.ComponentType<BoardGroupStatisticsMainProps> = observer(props => {
  const { visible, onClose, idParams, dataset, statistics } = props;
  const [statisticsItemDetail, setstatisticsItemDetail] = useState({});
  const [isAdd, setIsAdd] = useState(false);
  const [settingVisible, setsettingVisible] = useState(false);
  let { openValue = false, statisticsItem = [] } = statistics!;

  useEffect(() => {
    if (visible) {
      store.getStatistics();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const handleOk = async () => {
    const { statistics } = props;
    await store.updateViewDesignerByKey(ConfigItem.Statistics, statistics);
    await store.previewBackList(true);
    onClose();
  };

  const confirmDel = (data: any) => {
    confirm({
      content: getLabel('250968', '确定删除吗'),
      onOk: () => {
        delStatisticsItem(data);
      },
    });
  };

  const delStatisticsItem = (data: any) => {
    const _statisticsItem = JSON.parse(JSON.stringify(statisticsItem));
    statisticsItem.map((el: any, ind: number) => {
      if (el.id === data.id) {
        _statisticsItem.splice(ind, 1);
      }
    });
    store.setStatistics('statisticsItem', _statisticsItem);
  };

  const addStatisticsItem = () => {
    const defaultStep = {
      id: UUID(),
      inputValue: {
        valueLocale: '',
      },
      eBFilterData: {},
    };
    setstatisticsItemDetail(defaultStep);
    setIsAdd(true);
    openSetting();
  };

  const editStatisticsItem = (data: any) => {
    setstatisticsItemDetail(toJS(data));
    setIsAdd(false);
    openSetting();
  };

  const getAuthColumns = useMemo(() => {
    return [
      {
        dataIndex: 'inputValue',
        title: getLabel('53866', '名称'),
        width: '20%',
        bodyRender: (data: any) => {
          return <span>{data?.inputValue?.valueLocale}</span>;
        },
      },
      {
        dataIndex: 'eBFilterData',
        title: getLabel('53953', '筛选条件'),
        width: '30%',
        bodyRender: (data: any) => {
          return (
            <ConditionSet
              weId={`${props.weId || ''}_aylwye@${diffKey}`}
              dataset={dataset}
              data={data}
              store={store}
              statisticsItem={statisticsItem}
            />
          );
        },
      },
      {
        dataIndex: 'caozuo',
        title: getLabel('54342', '操作'),
        width: '30%',
        bodyRender: (data: any) => {
          return (
            <div>
              <span
                className="edit-color"
                onClick={() => {
                  editStatisticsItem(data);
                }}
              >
                {getLabel('54009', '编辑')}
              </span>
              <span className="edit-splice">{'|'}</span>
              <span
                className="edit-color"
                onClick={() => {
                  confirmDel(data);
                }}
              >
                {getLabel('53951', '删除')}
              </span>
            </div>
          );
        },
      },
    ];
  }, [dataset, statistics]);

  const handleChange = (val: any) => {
    store.setStatistics('openValue', val);
  };

  const openSetting = () => {
    setsettingVisible(true);
  };

  const closeSetting = () => {
    setsettingVisible(false);
  };
  return (
    <Dialog
      weId={`${props.weId || ''}_vuynwj`}
      visible={visible}
      title={getLabel('245988', '分组统计')}
      closable
      icon="Icon-e-builder-o"
      destroyOnClose
      onClose={onClose}
      width={720}
      height={500}
      wrapClassName={`${ebdBClsPrefix}-func-groups-statistics`}
      footer={[
        <Button weId={`${props.weId || ''}_werfs45@ok`} key="ok" type="primary" onClick={handleOk}>
          {getLabel('221901', '确定')}
        </Button>,
        <Button weId={`${props.weId || ''}_k0df4t@cancel`} key="cancel" onClick={onClose}>
          {getLabel('53937', '取消')}
        </Button>,
      ]}
      buttons={[
        <Button weId={`${props.weId || ''}_6b2yjt@${diffKey}`} key="a" type="primary" onClick={addStatisticsItem}>
          {getLabel('54007', '新建')}
        </Button>,
      ]}
    >
      <div className={`groupstatistics-top`}>
        <div className={`groupstatistics-top-lable`}>{getLabel('245989', '开启统计')}</div>
        <Switch weId={`${props.weId || ''}_khpv3f`} size="sm" onChange={handleChange} value={openValue} />
      </div>
      {
        // openValue &&
        <Table
          weId={`${props.weId || ''}_6gm7ja`}
          rowKey="key"
          className={'statistics-table'}
          columns={getAuthColumns}
          data={statisticsItem}
          scroll={{ y: 260 }}
          scrollBarInBody={true}
          isShowIndex={true}
        />
      }
      {settingVisible && (
        <StatisticsSetting
          weId={`${props.weId || ''}_2ioavl`}
          settingVisible={settingVisible}
          onClose={closeSetting}
          dataset={dataset}
          idParams={idParams}
          statisticsItemDetail={statisticsItemDetail}
          statistics={statistics}
          store={store}
          isAdd={isAdd}
        />
      )}
    </Dialog>
  );
});

const ConditionSet: React.ComponentType<any> = observer(props => {
  const { data, dataset, statisticsItem, store } = props;

  const onFilterChange = (eBFilterData: any) => {
    let _statisticsItem: any = cloneDeep(statisticsItem);
    _statisticsItem.map((el: any) => {
      if (el.id === data?.id) {
        el.eBFilterData = eBFilterData;
      }
    });
    store.setStatistics('statisticsItem', _statisticsItem);
  };

  return (
    <CorsComponent
      weId={`${props.weId || ''}_enojsk`}
      app="@weapp/components"
      compName="ConditionSet"
      dataSet={dataset}
      value={data?.eBFilterData}
      onChange={onFilterChange}
      fieldFilter={specialFieldsFilter}
      placeholder={getLabel('87541', '点击设置过滤条件')}
      title={getLabel('99497', '日历固定查询条件')}
    />
  );
});

export const BoardGroupStatistics: React.ComponentType<BoardGroupStatisticsProps> = observer(props => {
  const [show, setShow] = useState<boolean>(false);
  const { buttonType = 'default', title, hasIcon = true, btnClassName, idParams, dataset, statistics } = props;

  const triggerShow = () => {
    setShow(!show);
  };

  return (
    <>
      <Button weId={`${props.weId || ''}_cixdx9`} type={buttonType} className={btnClassName} onClick={triggerShow}>
        {hasIcon && <Icon weId={`${props.weId || ''}_j0w8m2`} name="Icon-folder" />}
        <span>{title || getLabel('245988', '分组统计')}</span>
      </Button>
      <Main
        weId={`${props.weId || ''}_ujoln6`}
        idParams={idParams}
        visible={show}
        onClose={triggerShow}
        onOk={triggerShow}
        dataset={dataset}
        statistics={statistics}
      />
    </>
  );
});
export default BoardGroupStatistics;
