import { Button, Dialog } from '@weapp/ui';
import { getLabel, cloneDeep, isEmpty } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useState } from 'react';
import { ebdBClsPrefix } from '../../../../../constants';
import { StatisticsSettingProps } from '../types';
import EBFilter from '../../../../../components/common/Filter';
import { specialFieldsFilter } from '../../../../../utils';
import ebdcoms from '../../../../../utils/ebdcoms';

const { LocaleEx } = ebdcoms.get();

const StatisticsSetting: React.ComponentType<StatisticsSettingProps> = observer(props => {
  const { settingVisible, onClose, idParams, dataset, statistics, statisticsItemDetail = {}, store, isAdd = false } = props;
  const { pageId } = idParams;
  const [inputValue, setInputValue] = useState(statisticsItemDetail?.inputValue);
  const [eBFilterData, setEBFilterData] = useState(statisticsItemDetail?.eBFilterData);
  let { statisticsItem = [] } = statistics!;

  const handleOk = async () => {
    if (isEmpty(eBFilterData) || isEmpty(inputValue?.valueLocale)) {
      Dialog.message({ type: 'info', content: getLabel('247356', '请填入必填项') });
      return;
    }

    if (isAdd) {
      pushStatisticsItem();
    } else {
      onDatasChange();
    }
    onClose();
  };

  const onValueChange = (inputValue: string) => {
    let _inputValue = {
      valueLocale: inputValue,
    };
    setInputValue(_inputValue);
  };

  const onChangeEBFilter = (val: any) => {
    setEBFilterData(val);
  };

  const onDatasChange = () => {
    let _statisticsItem: any = cloneDeep(statisticsItem);
    let _id = statisticsItemDetail?.id;
    _statisticsItem.map((el: any) => {
      if (el.id === _id) {
        el.inputValue = inputValue;
        el.eBFilterData = eBFilterData;
      }
    });
    store.setStatistics('statisticsItem', _statisticsItem);
  };

  const pushStatisticsItem = () => {
    statisticsItemDetail.inputValue = inputValue;
    statisticsItemDetail.eBFilterData = eBFilterData;
    statisticsItem.push(statisticsItemDetail);
    store.setStatistics('statisticsItem', statisticsItem);
  };

  return (
    <Dialog
      weId={`${props.weId || ''}_vuynwj`}
      visible={settingVisible}
      title={getLabel('54205', '设置')}
      closable
      icon="Icon-e-builder-o"
      destroyOnClose
      onClose={onClose}
      width={500}
      height={300}
      wrapClassName={`${ebdBClsPrefix}-func-groups-statistics-setting`}
      footer={[
        <Button weId={`${props.weId || ''}_werfs45@ok`} key="ok" type="primary" onClick={handleOk}>
          {getLabel('221901', '确定')}
        </Button>,
        <Button weId={`${props.weId || ''}_k0df4t@cancel`} key="cancel" onClick={onClose}>
          {getLabel('53937', '取消')}
        </Button>,
      ]}
    >
      <div className={`setting-top`}>
        <div className={`setting-top-content setting-name`}>
          <div className={`setting-top-lebale`}>
            <span>{getLabel('53866', '名称')}</span>
          </div>
          <LocaleEx weId={`${props.weId || ''}_5flgxc`} value={inputValue?.valueLocale} targetId={pageId} onChange={onValueChange as any} />
        </div>
        <div className={`setting-top-content setting-filter`}>
          <div className={`setting-top-lebale`}>
            <span>{getLabel('53953', '筛选条件')}</span>
          </div>
          <EBFilter
            weId={`${props.weId || ''}_8xt0d0`}
            value={eBFilterData}
            dataset={dataset!}
            onChange={onChangeEBFilter}
            title={getLabel('105959', '看板视图固定查询条件')}
            filter={specialFieldsFilter}
          />
        </div>
      </div>
    </Dialog>
  );
});

export default StatisticsSetting;
