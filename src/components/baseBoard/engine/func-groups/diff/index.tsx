import { useEffect, useMemo } from 'react';
import { Menu, Layout, Select, Spin, Empty, Icon, List, ListData, FormSwitch, Help } from '@weapp/ui';
import { getLabel, classnames } from '@weapp/utils';
import { Else, If, Then } from 'react-if';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { ebdBClsPrefix } from '../../../../../constants';
import GroupOptionCom from '../GroupOptionCom';
import { GroupSetComProps } from '../GroupSetCom';
import { SelectType, EtComponentKey } from '../../../../../constants/EtComponent';
import { TitleText } from '../Common';
import { RelationGroupsType } from './constants';
import './index.less';

const { Row, Col } = Layout;
const cls = `${ebdBClsPrefix}-view-group-main-diffGroup`;
interface DiffGroupProps extends GroupSetComProps {
  [x: string]: any;
}

export const getRelationMenuData = (isEbuilder: boolean) => {
  const normalGroups = {
    id: RelationGroupsType.Normal,
    content: getLabel('151785', '默认分组'),
  };
  const customGroups = {
    id: RelationGroupsType.Custom,
    content: getLabel('105967', '自定义分组'),
  };
  return isEbuilder ? [normalGroups, customGroups] : [normalGroups];
};

const DiffGroup = observer((props: Omit<DiffGroupProps, 'sourceType'>) => {
  const { store, appId, diffStore, datasetMap, group } = props;
  const { boardColorStyle, setBoardColorStyle } = store;
  const {
    relationSelected,
    releationCustomSelected,
    diffListLoading,
    releationCustomListDatas,
    releationCustomListPageInfo,
    relationCustomTargetField,
    relationCustomTargetDataOption,
    groupFieldOpts,
    groupField,
    relationNormalGroups,
    relationFieldOpts,
    onGroupFieldChange,
    onGroupListEndReached,
    onRelationSelecteChange,
    onRLRowClick,
    onRootFieldChange,
    addRelationCustomGroup,
    changeRelationCustomOption,
    rootField,
  } = diffStore;

  const isEbuilderRootField = useMemo(() => {
    const item = relationFieldOpts.find((i: any) => i.id === rootField);
    const compType = item?.compType || item?.type;
    return compType === EtComponentKey.Ebuilder || compType === EtComponentKey.RelateBrowser;
  }, [rootField, relationFieldOpts]);

  const customRenderItem = (item: ListData) => {
    return (
      <div
        key={item.id}
        title={item.content}
        className={classnames('list-item', {
          'list-item-active': releationCustomSelected === item.id,
        })}
      >
        <span style={{ marginLeft: '10px' }}>{item.content || item.name}</span>
      </div>
    );
  };
  console.log('****toJS(relationNormalGroups)****', toJS(relationNormalGroups));

  return (
    <div className={cls}>
      {/* 按关联数据差异化分组-自定义分组 */}
      <Row weId={`${props.weId || ''}_35q847`} className="view-group-header-row">
        <Col weId={`${props.weId || ''}_1sbwog`} span={4} className="view-group-header-col-label">
          <TitleText weId={`${props.weId || ''}_m57ajq`} title={getLabel('186580', '关联字段')} />
        </Col>
        <Col weId={`${props.weId || ''}_sivl6f`} className="view-group-header-col-content">
          <FormSwitch
            weId={`${props.weId || ''}_wih799`}
            id="relationFieldselect"
            isMobile={false}
            itemType="SELECT"
            data={toJS(relationFieldOpts)}
            value={rootField}
            onChange={onRootFieldChange}
          />
          <Help
            weId={`${props.weId || ''}_k98855`}
            title={getLabel('222256', '用于接收rootid参数传递过来的值（场景如:任务列表配置成看板挂在项目卡片页面关联中，则此处选择任务表中的关联项目字段。）')}
          />
        </Col>
      </Row>
      <If weId={`${props.weId || ''}_hjeca9`} condition={rootField}>
        <Then weId={`${props.weId || ''}_8kvhul`}>
          <Menu
            weId={`${props.weId || ''}_fa3s6f`}
            defaultValue={RelationGroupsType.Normal}
            data={getRelationMenuData(isEbuilderRootField)}
            value={relationSelected}
            onChange={onRelationSelecteChange}
            extraContent={
              relationSelected === RelationGroupsType.Custom ? (
                <Select weId={`${props.weId || ''}_gflilh`} data={toJS(groupFieldOpts)} value={groupField} onChange={onGroupFieldChange} />
              ) : (
                <></>
              )
            }
            className={`${ebdBClsPrefix}-view-group-main-releationField`}
          />
          <Menu.MenuContent weId={`${props.weId || ''}_ckj43y`} key={RelationGroupsType.Normal} dataId={RelationGroupsType.Normal} value={relationSelected}>
            <GroupOptionCom
              weId={`${props.weId || ''}_6ii6h0`}
              appId={appId}
              // 暂时不用颜色
              disabledColor
              boardColorStyle={boardColorStyle}
              changeBoardColorStyle={setBoardColorStyle}
              customShowOption={toJS(relationNormalGroups)}
              addCustomGroup={() => addRelationCustomGroup('relationNormalGroups')}
              changeCustomOption={changeRelationCustomOption('relationNormalGroups')}
            />
          </Menu.MenuContent>
          <Menu.MenuContent weId={`${props.weId || ''}_ro39r6`} key={RelationGroupsType.Custom} dataId={RelationGroupsType.Custom} value={relationSelected}>
            <Layout weId={`${props.weId || ''}_ak64d0`} style={{ height: 400 }} className={`${ebdBClsPrefix}-view-group-main-layout`}>
              <Layout.Box weId={`${props.weId || ''}_o88p4k`} className="layout-left" type="side" width={150}>
                <If weId={`${props.weId || ''}_xn8rxx`} condition={diffListLoading && groupField}>
                  <Then weId={`${props.weId || ''}_3u73sh`}>
                    <Spin weId={`${props.weId || ''}_cnuxzy`} spinning />
                  </Then>
                  <Else weId={`${props.weId || ''}_pn202i`}>
                    <If weId={`${props.weId || ''}_xn8rxx`} condition={releationCustomListDatas.length === 0 || !groupField}>
                      <Then weId={`${props.weId || ''}_0n0d8p`}>
                        <Empty
                          weId={`${props.weId || ''}_sd1txc`}
                          description={groupField ? getLabel('54023', '暂无数据') : getLabel('186581', '请先选择自定义分组字段')}
                          image={<Icon weId={`${props.weId || ''}_fn34em`} style={{ width: 100, height: 100 }} name="Icon-empty-file" />}
                          imageStyle={{ height: 100 }}
                          style={{ padding: '50px 0px' }}
                        />
                      </Then>
                      <Else weId={`${props.weId || ''}_pn202i`}>
                        {/* 按关联数据差异化分组-关联字段-自定义分组 */}
                        <List
                          weId={`${props.weId || ''}_z17ykb`}
                          direction="column"
                          data={toJS(releationCustomListDatas)}
                          customRenderContent={customRenderItem}
                          isStopMouseDown
                          className="layout-left-content-list"
                          onRowClick={onRLRowClick}
                          {...(SelectType.includes(relationCustomTargetField?.compType)
                            ? {}
                            : {
                                hasMore: releationCustomListPageInfo.hasMore,
                                paginationProps: {
                                  value: releationCustomListPageInfo.current,
                                  pageSize: 20,
                                  paginationType: 'more',
                                  onChange: onGroupListEndReached,
                                },
                                loading: releationCustomListPageInfo.loading,
                              })}
                        />
                      </Else>
                    </If>
                  </Else>
                </If>
              </Layout.Box>
              <Layout.Box weId={`${props.weId || ''}_nkqmvl`} className="layout-content" type="content">
                <If weId={`${props.weId || ''}_c36bl0`} condition={releationCustomSelected && !diffListLoading}>
                  <Then weId={`${props.weId || ''}_c0z4m8`}>
                    <GroupOptionCom
                      weId={`${props.weId || ''}_6ii6h0`}
                      appId={appId}
                      // 暂时不用颜色
                      disabledColor
                      boardColorStyle={boardColorStyle}
                      changeBoardColorStyle={setBoardColorStyle}
                      customShowOption={toJS(relationCustomTargetDataOption)}
                      addCustomGroup={() => addRelationCustomGroup('relationCustomTargetDataGroups')}
                      changeCustomOption={changeRelationCustomOption('relationCustomTargetDataGroups')}
                    />
                  </Then>
                  <Else weId={`${props.weId || ''}_kj953p`}>
                    <Empty
                      weId={`${props.weId || ''}_9xutl7`}
                      description={getLabel('186582', '请先选择自定义分组字段数据项')}
                      image={<Icon weId={`${props.weId || ''}_fn34em`} style={{ width: 100, height: 100 }} name="Icon-empty-file" />}
                      imageStyle={{ height: 100 }}
                      style={{ padding: '50px 0px' }}
                    />
                  </Else>
                </If>
              </Layout.Box>
            </Layout>
          </Menu.MenuContent>
        </Then>
      </If>
    </div>
  );
});

export default DiffGroup;
