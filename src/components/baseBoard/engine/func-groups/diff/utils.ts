import { trim, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { RelationGroupsType } from './constants';
import DiffStore from './store';

/**
 * 构建差异化分组options
 * @param diffStore
 * @param isDataOptions 是否要生成dataOptions 如果是差异化分组默认有 非差异化默认自行去掉
 * @returns
 */
export const buildDiffGroupOptions = (diffStore: DiffStore, isDataOptions: boolean) => {
  console.log('****diffStore****', toJS(diffStore));
  // * 差异化分组-默认分组
  const normalDiffOptions = {
    groupType: RelationGroupsType.Normal,
    // enableColor: targetActive === RelationGroupsType.Normal ? this.boardColorStyle === ColorStyleType.HasStyle : originEnable,
    // enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
    values: diffStore.relationNormalGroups
      .filter((opt: any) => trim(opt.name))
      .map((opt: any) => {
        if (opt.id && opt.id.startsWith('temp_')) {
          return {
            name: opt.name,
            type: opt.type,
            color: opt?.color || '',
          };
        }
        return opt;
      }),
  };
  // *差异化分组下-自定义分组
  // 历史数据或者非明确场景存在rootDataId重复的问题 所以需要过滤一次保证唯一性
  const relationCustomGroupsArray = diffStore.relationCustomGroupsArray.map(arr => {
    let { optionValue, ...restProps } = arr;
    // * fieldId 为关联ebuilder表单字段中关联ebuilder所指向的id
    // * dataOptions 需要传rootData 值都为左侧选中项的值
    let values = toJS(arr.values)
      .filter((opt: any) => trim(opt.name))
      .map((opt: any) => {
        // * temp_开头或者是构建dataOptions 不显示id
        if (opt.id && opt.id.startsWith('temp_')) {
          return {
            name: opt.name,
            type: opt.type,
            color: opt?.color,
          };
        }
        return opt;
      });
    if (isDataOptions) {
      restProps = { ...restProps };
      // * 找不到未分组 需要补全
      if (!values.find((i: any) => +i.type === 0)) {
        values.push({
          type: 0,
          name: getLabel('105298', '未分组'),
        });
      }
    } else {
      restProps = { ...restProps, fieldId: diffStore.groupField };
    }
    return {
      ...restProps,
      // enableColor: targetActive === RelationGroupsType.Custom ? this.boardColorStyle === ColorStyleType.HasStyle : originEnable,
      // enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
      values: toJS(values),
    };
  });
  console.log('****normalDiffOptions****', normalDiffOptions);
  console.log('****relationCustomGroupsArray****', relationCustomGroupsArray);

  return [
    normalDiffOptions,
    ...relationCustomGroupsArray
  ];
};
