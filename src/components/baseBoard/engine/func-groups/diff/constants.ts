import { getLabel } from '@weapp/utils';
import { KanBanDefaultBgColor } from '../../../../../constants';

// 按关联数据差异化分组 类型
export enum RelationGroupsType {
  Normal = '100',
  Custom = '101',
}
export const RelationMenuData = () => [
  {
    id: RelationGroupsType.Normal,
    content: getLabel('151785', '默认分组'),
  },
  {
    id: RelationGroupsType.Custom,
    content: getLabel('105967', '自定义分组'),
  },
];

/**
 * 初始化默认分组
 */
export const DfRelationGroupData = () => [
  {
    type: '1',
    id: `temp_${Date.now()}`,
    name: get<PERSON>abel('117530', '未开始'),
    color: KanBanDefaultBgColor,
  },
  {
    type: '1',
    id: `temp_${Date.now() + 1}`,
    name: get<PERSON>abel('54753', '进行中'),
    color: KanBanDefaultBgColor,
  },
  {
    type: '1',
    id: `temp_${Date.now() + 2}`,
    name: get<PERSON><PERSON><PERSON>('54752', '已完成'),
    color: KanBanDefaultBgColor,
  },
];
