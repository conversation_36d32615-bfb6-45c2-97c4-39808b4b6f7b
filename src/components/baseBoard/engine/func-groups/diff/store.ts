import { AnyObj, ListData } from '@weapp/ui';
import { isEmpty, forIn, forEach, cloneDeep } from '@weapp/utils';
import { toJS, observable, computed, runInAction, action } from 'mobx';
import { RelationGroupsType } from './constants';
import { CustomGroupItem, GroupDataOptionItem, GroupDataProps } from '../types';
import { getRelationBrowserFields } from '../utils';
import { DfRelationGroupData, GroupsTypeType } from '../constants';
import { DataSetItem } from '../../../../common/DataSet/types';
import { KanBanDefaultBgColor } from '../../../../../constants';
import { EtComponentKey, SelectType } from '../../../../../constants/EtComponent';
import { GroupType } from '../../../../../constants/common';
import { getEbBrowserData, getEbRelationBrowserData } from '../../../../../api/common';
import { isEbRelationBrowser } from '../../../../../utils';

// 差异化分组store
class DiffStore {
  @observable datasetMap = [] as any[];
  @observable groups = {} as any;
  // 差异化分组自定义分组关联字段数据loading
  @observable diffListLoading = true;

  // 关联字段差异化分组-关联字段
  @observable relationFieldOpts: any[] = [];

  @observable rootField: string = '';

  @observable rootFieldObjId: string = '';

  // 关联目标字段自定义分组
  @observable groupField: string = '';

  @observable groupFieldOpts: any[] = [];

  @observable relationSelected: RelationGroupsType = RelationGroupsType.Normal;

  @observable relationNormalGroups: CustomGroupItem[] = []; // 默认分组

  /**
   * 自定义分组存储的所有数据项配置（分组）
   */
  @observable relationCustomGroupsArray: any[] = [];

  @observable releationCustomListDatas: any[] = []; // 自定义分组左侧选项列表

  @observable releationCustomSelected: string = ''; // 自定义分组左侧选项默认选择值

  @observable relationCustomTargetField: any = {};

  @observable releationCustomListPageInfo: { current: number; hasMore: boolean; loading: boolean } = {
    current: 1,
    hasMore: true,
    loading: false,
  };
  // 差异化分组自定义分组
  @computed
  get isCustomRelationGroupsType() {
    return this.relationSelected === RelationGroupsType.Custom;
  }
  @computed
  get relationCustomTargetDataOption() {
    const targetCustomOpt = this.relationCustomGroupsArray.filter(a => a.rootDataId === this.releationCustomSelected) || [];
    const options =
      targetCustomOpt[0]?.values
        ?.filter((cpt: any) => `${cpt.type}` !== '0')
        .map((i: any, index: number) => {
          // 已经生成的id不能被覆盖
          return { ...i, id: i.id || `temp_${Date.now() + index}` };
        }) || [];
    return options?.length > 0 ? options : DfRelationGroupData();
  }
  constructor(data?: any, datasetMap?: any[]) {
    if (!isEmpty(datasetMap)) {
      this.initDatasetMap(datasetMap!);
    }
    if (!isEmpty(data)) {
      this.initDiff(data);
    }
  }
  @action resetDiff = () => {
    this.groupField = '';
    this.releationCustomSelected = '';
    this.groupFieldOpts = [];
    this.relationSelected = RelationGroupsType.Normal;
  };

  @action setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };
  @action initDatasetMap = async (datasetMap: any[]) => {
    if (!isEmpty(datasetMap)) {
      this.datasetMap = datasetMap;
      this.initFields();
    }
  };
  @action initDiff = async (groups: any) => {
    const { options = [], groupField = '', rootField = '' } = groups;
    this.groups = groups;
    // 回显
    this.rootField = rootField;
    this.groupField = groupField;
    this.relationNormalGroups = this.getRelationNormalGroups(options);
    this.initDiffCustom(groups);
    /**
     * 如果开启关联字段差异化分组，同时rootField 有值需要获取关联字段表的选项
     */
    if (this.rootField) {
      this.onRootFieldChange(this.rootField, true);
    }
    if (!groupField) {
      this.releationCustomListDatas = [];
      this.groupFieldOpts = [];
    }
  };
  @action('初始化字段')
  initFields = async () => {
    if (this.datasetMap.length > 0) {
      // 关联字段
      this.relationFieldOpts = this.datasetMap
        .filter(
          (data: any) =>
            (`${data?.multiSelect}` === 'false' || !data?.multiSelect) &&
            !['8', '9', '10', '11', '12', '13', '14', '15', '16'].includes(data?.id) &&
            (isEbRelationBrowser(data) || data.compType === EtComponentKey.Ebuilder || SelectType.includes(data.compType) || SelectType.includes(data.type))
        )
        .map((data: any) => ({ ...data, id: data.id, content: data.text }));
    }
  };
  @action('初始化差异化分组自定义分组')
  initDiffCustom = (data: GroupDataProps) => {
    const { active = GroupType.custom, dataOptions = [], dataGroup = GroupsTypeType.normalCustom } = data;
    // 抽取关联字段差异下所有的自定义分组值，便于管理
    this.relationCustomGroupsArray = dataOptions.filter((opt: GroupDataOptionItem) => `${opt.groupType}` === RelationGroupsType.Custom);
    // 【按关联数据差异化分组】关联字段差异下默认值menu
    this.relationSelected = `${active}` as RelationGroupsType;
    // this.relationSelected = (
    //   `${dataGroup}` === GroupsTypeType.releationField ? RelationGroupsType.Custom : RelationGroupsType.Normal
    // ) as RelationGroupsType;
    // this.fillDiffCustom();
  };
  @action('初始化差异化分组自定义分组')
  fillDiffCustom = () => {
    // 没有差异化分组自定义数据选中id不往下走
    if (!this.releationCustomSelected) {
      return;
    }
    // 差异化分组自定义分组没有当前分组的场景需要补全
    const diffCustom = this.relationCustomGroupsArray.find((item: any) => item.rootDataId === this.releationCustomSelected);
    if (!diffCustom) {
      this.updateRelationCustomTargetDataGroups(DfRelationGroupData());
    }
  };

  @action('处理关联字段差异化分组默认分组值')
  getRelationNormalGroups = (diffOptions: any[] = []) => {
    const targetRelationNormalGroups = diffOptions.find((opt: GroupDataOptionItem) => `${opt.groupType}` === RelationGroupsType.Normal);
    const _relationNormalGroups = (targetRelationNormalGroups?.values?.filter((cpt: any) => `${cpt.type}` !== '0') || []).map((i: any) => ({
      ...i,
      color: i?.color || i?.type === 0 ? i.color : KanBanDefaultBgColor,
    }));
    return _relationNormalGroups?.length && _relationNormalGroups.every((i: any) => i.id) ? _relationNormalGroups : DfRelationGroupData();
  };
  @action
  onRootFieldChange = (value: any, isInit: boolean = false) => {
    if (value !== this.rootField) {
      runInAction(() => {
        // 先重置
        this.groupField = '';
        this.releationCustomSelected = '';
        this.releationCustomListDatas = [];
        this.relationSelected = RelationGroupsType.Normal;
      });
    }
    this.rootField = value;
    const _rootFieldObjId = this.getRsFieldFormId(value);
    this.rootFieldObjId = _rootFieldObjId;
    const updateGroupField = () => {
      if (!isInit) {
        this.groupField = '';
        this.releationCustomSelected = '';
        this.releationCustomListDatas = [];
      } else {
        if (this.groupField) {
          this.onGroupFieldChange(this.groupField);
        }
      }
    };
    this.getRsFields(
      {
        id: _rootFieldObjId,
        text: '',
        type: 'FORM',
        groupId: '',
      },
      updateGroupField
    );
  };
  /**
   * 通过资源字段，反查资源字段的表单id
   * 类型： Ebuilder，RelateBrowser
   * @param rsfieldid
   * @returns
   */
  getRsFieldFormId = (rsfieldid: string) => {
    const options = this.relationFieldOpts || [];
    let id = '';
    forEach(options, opt => {
      if (opt.id === rsfieldid) {
        if (opt.compType === EtComponentKey.Ebuilder) {
          const config = opt.config || {};
          id = config.sformId;
        }
        if (opt.compType === EtComponentKey.RelateBrowser) {
          id = opt.config?.browserType?.sformId;
        }
      }
    });
    return id;
  };

  /**
   * 请求数据仓库
   * */
  @action
  getRsFields = async (dataset: DataSetItem, callBack?: Function) => {
    getRelationBrowserFields(dataset, (fieldOpts: any[]) => {
      runInAction(() => {
        this.groupFieldOpts = fieldOpts.filter(
          i =>
            // (SelectType.includes(i.compType) || SelectType.includes(i.type)) &&
            (`${i?.multiSelect}` === 'false' || !i?.multiSelect) &&
            // 人员分组请求不到具体字段详情的 也屏蔽
            (i?.compType || i?.type) !== EtComponentKey.Employee
        );
        // 兼容groupField后期被删除导致匹配不上问题
        if (this.groupField && !this.groupFieldOpts.find(i => i.id === this.groupField)) {
          this.groupField = '';
        }
        callBack && callBack(fieldOpts);
      });
    });
  };

  @action
  onGroupFieldChange = async (value: any) => {
    this.groupField = value;
    /**
     * 根据选项值获取当前字段类型方便后面获取数据项
     */
    let targetField: AnyObj = {};
    forEach(toJS(this.groupFieldOpts), opt => {
      if (opt.id === value) {
        targetField = opt;
      }
    });
    this.relationCustomTargetField = targetField;
    this.getRelationCustomTargetFieldData(true, targetField);
  };
  /**
   * 获取自定义分组目标字段的数据
   * @param isFirst 是否是第一次渲染
   * @param targetField 目标字段
   * @param params 分页参数等
   */
  @action
  getRelationCustomTargetFieldData = async (isFirst: boolean, targetField: any, params: any = {}) => {
    const setDefault = (_listDatas: any[]) => {
      // 只作用于第一次选中
      if (!isFirst) {
        return;
      }
      // mark 默认选中列表中第一个
      if (_listDatas.length) {
        if ([EtComponentKey.Select, EtComponentKey.RadioBox].includes(targetField?.compType) || !_listDatas.find(i => i.id === this.releationCustomSelected)) {
          runInAction(() => {
            this.releationCustomSelected = _listDatas[0].id;
            // mark 初始化一次差异化分组选中的详情数据 ---> 解决选中左侧列表后 不更新数据导致分组不生效问题
            // this.updateRelationCustomTargetDataGroups(DfRelationGroupData());
          });
        }
      }
    };
    // 单独处理下拉和单选框字段
    if ([EtComponentKey.Select, EtComponentKey.RadioBox].includes(targetField?.compType)) {
      const options = targetField.config?.options || [];
      this.releationCustomListDatas = options.map((o: any) => ({
        ...o,
        id: o.value,
        content: o.name,
      }));
      this.diffListLoading = false;
      setDefault(this.releationCustomListDatas);
    } else {
      let res = {} as any;
      this.diffListLoading = true;
      if (targetField.compType === EtComponentKey.Ebuilder) {
        res = await getEbBrowserData({
          fieldId: targetField.config?.fieldId,
          formId: targetField.config?.sformId,
          ...params,
        });
      } else {
        res = await getEbRelationBrowserData(
          {
            fieldId: targetField.config?.fieldId,
            formId: targetField.config?.browserType?.sformId || targetField?.type,
            ...params,
          },
          targetField.config?.browserType?.type
        );
      }
      runInAction(() => {
        this.releationCustomListDatas = isFirst ? res.data || [] : [...this.releationCustomListDatas, ...res.data];
        this.releationCustomListPageInfo = {
          ...this.releationCustomListPageInfo,
          current: params.current || 1,
          hasMore: (res.data || []).length >= 20,
          loading: false,
        };
        this.diffListLoading = false;
        setDefault(this.releationCustomListDatas);
      });
    }
  };
  @action('新增、更新关联字段差异化分组自定义分组')
  updateRelationCustomTargetDataGroups = (data: any[]) => {
    const targetGroups = this.relationCustomGroupsArray.find((opt: GroupDataOptionItem) => `${opt.rootDataId}` === this.releationCustomSelected) || {};
    if (!isEmpty(targetGroups)) {
      // 更新
      this.relationCustomGroupsArray = this.relationCustomGroupsArray.map(cus => {
        if (cus.rootDataId === this.releationCustomSelected) {
          return { ...cus, values: data };
        }
        return cus;
      });
    } else {
      // 新增
      this.relationCustomGroupsArray = [
        ...this.relationCustomGroupsArray,
        {
          groupType: RelationGroupsType.Custom, // 数据差异化分组/自定义分组
          enableColor: false,
          values: data,
          // 需要补充rootDataId
          rootDataId: this.releationCustomSelected,
        },
      ];
    }
  };
  // @action
  // changeBoardColorStyle = () => {
  //   const { options = [] } = this.data;
  //   switch (this.relationSelected) {
  //     case RelationGroupsType.Normal: {
  //       const relationNormal = options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === RelationGroupsType.Normal);
  //       this.boardColorStyle = relationNormal?.enableColor ? ColorStyleType.HasStyle : ColorStyleType.NoStyle;
  //       break;
  //     }
  //     case RelationGroupsType.Custom: {
  //       const relationCustomFilter = this.relationCustomGroupsArray.find(a => a.rootDataId === this.releationCustomSelected);
  //       this.boardColorStyle = relationCustomFilter?.enableColor ? ColorStyleType.HasStyle : ColorStyleType.NoStyle;
  //       break;
  //     }
  //   }
  // };

  @action
  onRLRowClick = (data: ListData) => {
    this.releationCustomSelected = data.id!;
    // this.changeBoardColorStyle();
    // mark 初始化一次差异化分组选中的详情数据 ---> 解决选中左侧列表后 不更新数据导致分组不生效问题
    this.updateRelationCustomTargetDataGroups(this.relationCustomTargetDataOption);
  };
  @action
  onGroupListEndReached = (current: number) => {
    this.releationCustomListPageInfo = { ...this.releationCustomListPageInfo, loading: true };
    this.getRelationCustomTargetFieldData(false, this.relationCustomTargetField, {
      current,
    });
  };
  @action
  onRelationSelecteChange = (value: any) => {
    this.relationSelected = value;
    // this.changeBoardColorStyle();
    if (value === RelationGroupsType.Normal) {
      // 切换到默认分组 差异化分组需要重新生成
    } else {
      if (this.rootField && this.groupField) {
        this.onRootFieldChange(this.rootField, true);
      }
    }
  };
  @action
  addRelationCustomGroup = (type: string) => {
    const _tempGroup: CustomGroupItem = {
      type: '1',
      id: `temp_${Date.now()}`,
      name: '',
      color: KanBanDefaultBgColor,
    };
    if (type === 'relationNormalGroups') {
      this.relationNormalGroups = this.relationNormalGroups.concat(_tempGroup);
    } else if (type === 'relationCustomTargetDataGroups') {
      let _groups = cloneDeep(this.relationCustomTargetDataOption);
      _groups = _groups.concat(_tempGroup);
      this.updateRelationCustomTargetDataGroups(_groups);
    }
  };
  @action
  changeRelationCustomOption = (type: string) => (data: any[]) => {
    runInAction(() => {
      if (type === 'relationNormalGroups') {
        if (data.length) {
          this.relationNormalGroups = data;
        }
      } else if (type === 'relationCustomTargetDataGroups') {
        if (data.length) {
          this.updateRelationCustomTargetDataGroups(data);
        }
      }
    });
  };
}

export default DiffStore;
