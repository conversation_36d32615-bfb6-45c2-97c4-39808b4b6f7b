import { useState, useEffect, useMemo } from 'react';
import { getLabel } from '@weapp/utils';
import { Switch } from '@weapp/ui';
import { ebdBClsPrefix, isCn } from '../../../../../constants';
import { GroupType } from '../../../../../constants/common';
import { GroupRowItem } from '../Common';
import LocaleInput from '../../../../common/locale/LocaleInput';
import './index.less';

export interface BoardGroupOtherProps {
  weId?: string;
  pageId: string;
  groupType?: string;
  otherData?: {
    [x: string]: any;
  };
  titleRowSpan?: number;
  onChange: (key: string, val: any) => void;
  isYd?: boolean
}
const cls = `${ebdBClsPrefix}-board-group-other`;
const BoardGroupOther = (props: BoardGroupOtherProps) => {
  const { groupType, titleRowSpan, isYd } = props;
  const [hideWfz, setHideWfz] = useState(false);
  const [hideNoDataFz, setHideNoDataFz] = useState(false);
  const [inGroupSort, setInGroupSort] = useState(false);
  const [crossGroupSort, setCrossGroupSort] = useState(false);
  const [customWfz, setCustomWfz] = useState(getLabel('105298', '未分组'));
  useEffect(() => {
    const { hideWfz, hideNoDataFz, customWfz } = props.otherData!;
    setHideWfz(hideWfz);
    setHideNoDataFz(hideNoDataFz);
    customWfz && setCustomWfz(customWfz);
  }, []);
  const onChange = (type: string, value: any) => {
    switch (type) {
      case 'hideWfz':
        setHideWfz(value);
        break;
      case 'hideNoDataFz':
        setHideNoDataFz(value);
        break;
      case 'customWfz':
        setCustomWfz(value);
        break;
      default:
        break;
    }
    props.onChange(type, value);
  };
  const reNamaWfz = () => {
    return (
      <LocaleInput
        weId={`${props.weId || ''}_cw71qq`}
        value={customWfz}
        onChange={(data: any) => onChange('customWfz', data)}
        ebBusinessId={props.pageId}
      />
    );
  };
  const showHideWfz = useMemo(() => {
    return groupType !== GroupType.filter
  }, [groupType]);
  const showRename = useMemo(() => {
    return groupType !== GroupType.filter
  }, [groupType]);
  const _titleRowSpan = titleRowSpan ? isCn ? titleRowSpan : titleRowSpan + 2 : isCn ? 4 : 6;
  const commonConfig = {
    titleRowSpan: _titleRowSpan,
    contentRowSpan: 24 - _titleRowSpan,
  }
  return (
    <div className={`${cls}-wrap`}>
      {showHideWfz && <GroupRowItem
        {...commonConfig}
        weId={props.weId}
        title={`${getLabel('288152', '隐藏未分组')}${isYd ? getLabel('291674', '泳道') : ''}`}
        helpInfo={[`${getLabel('288804', '开启后，前台页面未分组将不再显示')}`]}
        content={<Switch weId={`${props.weId || ''}_w2sfwv`} value={hideWfz} onChange={() => onChange('hideWfz', !hideWfz)} />}
      />}
      <GroupRowItem
        {...commonConfig}
        weId={props.weId}
        title={isYd? getLabel('-1', '隐藏无数据泳道') : getLabel('288803', '隐藏无数据分组')}
        helpInfo={[
          `${isYd ? getLabel('-1', '开启后，前台页面没有数据的泳道都将不再显示') : getLabel('288805', '开启后，前台页面没有数据的分组都将不再显示')}；`,
          `${isYd ? '' : getLabel('289305', '注意：本功能开启后看板懒加载功能将失效')}；`,
        ]}
        content={
          <Switch weId={`${props.weId || ''}_w2sfwv`} value={hideNoDataFz} onChange={() => onChange('hideNoDataFz', !hideNoDataFz)} />
        }
      />
      {/* <GroupRowItem
        {...commonConfig}
        weId={props.weId}
        title={getLabel('-1', '允许卡片组内拖动')}
        content={
          <Switch weId={`${props.weId || ''}_w2sfwv`} value={hideNoDataFz} onChange={() => onChange('hideNoDataFz', !hideNoDataFz)} />
        }
      />
      <GroupRowItem
        {...commonConfig}
        weId={props.weId}
        title={getLabel('-1', '允许卡片跨组拖动')}
        content={
          <Switch weId={`${props.weId || ''}_w2sfwv`} value={hideNoDataFz} onChange={() => onChange('hideNoDataFz', !hideNoDataFz)} />
        }
      /> */}
      {showRename && <GroupRowItem {...commonConfig} weId={props.weId} title={`${getLabel('288153', '重命名未分组')}${isYd ? getLabel('291674', '泳道') : ''}`} content={reNamaWfz()} />}
    </div>
  );
};

export default BoardGroupOther;
