import React from 'react';
import { Button, Icon, BtnType } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import FieldLayout from '../../../common/field-layout';
import FieldView from '../../../common/field-view';
import { ConfigItem } from './types';
import NSettingIcon from '../../../common/n-setting-icon';

export interface CardSetProps {
  buttonType: BtnType;
  dataset: any;
  value: any;
  btnClassName?: string;
  hasIcon?: boolean;
  onChange: (key: ConfigItem, value: any) => void;
  isView?: boolean;
  appid: string;
  page?: any;
  style?: any;
  isIconBtn?: boolean;
}

export default class CardSet extends React.PureComponent<CardSetProps> {
  renderGridLayout = (config: any, showDialog: any) => {
    const { buttonType, hasIcon = true, style, isIconBtn, value } = this.props;
    if (isIconBtn) {
      const hasValue = !isEmpty(toJS(value));
      return <NSettingIcon weId={`${this.props.weId || ''}_h8l9mt`} hasValue={hasValue} onClick={showDialog} clearable={value} clearFunc={() => this.onChange({})} />
    }
    return (
      <Button weId={`${this.props.weId || ''}_h2mpwp`} className={this.props.btnClassName} type={buttonType} onClick={showDialog} style={style}>
        {hasIcon && <Icon weId={`${this.props.weId || ''}_wa8pra`} className="btn-icon-left" name="Icon-Three-columns-in-one-row-o" />}
        {/* {getLabel('94492', '卡片设置')} */}
        {getLabel('79709', '字段配置')}
      </Button>
    );
  };

  onChange = (value?: any) => {
    const { isView } = this.props;
    if (isEmpty(value?.cardLayout) && !isView) {
      this.props.onChange(ConfigItem.Card, {});
    } else {
      this.props.onChange(ConfigItem.Card, value);
    }
  };

  render() {
    const {
      // dataset = {
      //   id: '',
      //   text: '',
      //   type: 'FORM',
      //   groupId: '',
      // },
      dataset = {},
      value = {},
      isView,
      appid,
      isIconBtn,
    } = this.props;
    let _dataset = dataset?.id ? dataset : undefined;
    const config = { dataset: _dataset, toolbar: true, cardLayout: {} };
    const { field = [], ...restVal } = toJS(value) || {};
    // if (isView) {
    //   return <FieldView
    //     weId={`${this.props.weId || ''}_i7nn6e`}
    //     config={{ dataset: config.dataset }}
    //     value={{ ...toJS(restVal), field }}
    //     onChange={this.onChange}
    //   />;
    // }
    return <FieldLayout weId={`${this.props.weId || ''}_uf2367`} config={config} value={{ ...toJS(restVal), field }} onChange={this.onChange} externalElement={this.renderGridLayout} appid={appid} />;
  }
}
