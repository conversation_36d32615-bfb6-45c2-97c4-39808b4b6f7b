import { observer } from 'mobx-react';
import React from 'react';
import DataSort from '../../../common/data-sort';
import { ConfigItem } from './types';

@observer
export default class BoardOrder extends React.PureComponent<any> {
  onSave = (val: any[]) => {
    const { onChange } = this.props;
    onChange(ConfigItem.Orders, val);
  };

  render() {
    const {
      value = [], dataset, buttonType, checked,
    } = this.props;
    return (
      <DataSort
        weId={`${this.props.weId || ''}_byttyz`}
        value={value}
        dataset={dataset}
        onChange={this.onSave}
        buttonType={buttonType}
        checked={checked}
      />
    );
  }
}
