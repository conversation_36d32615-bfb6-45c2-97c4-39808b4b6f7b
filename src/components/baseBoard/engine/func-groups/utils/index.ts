import { getLabel, isEmpty } from '@weapp/utils';
import { DataSetItem } from '../../../../common/DataSet/types';
import { isEbRelationBrowser, getFields } from '../../../../../utils';
import { EtComponentKey, SelectType } from '../../../../../constants/EtComponent';
import { GroupType } from '../../../../../constants/common';

// 获取指定类型的数据源字段列表
export const getTargetFieldsByType = (fieldMap: any[], type: string | string[]) => {
  const isArray = Array.isArray(type);
  return fieldMap.filter(f => {
    // 如果是日期字段 需要判断满足EtComponentKey.DateComponent, EtComponentKey.Date 且format包含'yyyy-MM-dd' 且不是日期区间字段
    if (type === 'dateFieldRange') {
      return (
        (f.type === EtComponentKey.DateComponent || f.type === EtComponentKey.Date) &&
        f.format &&
        f.format.toLowerCase().includes('yyyy-mm-dd') &&
        f.compTypec !== 'DateInterval'
      );
    }
    if (isArray) {
      return type.includes(f.compType);
    }
    return f.compType === type;
  });
};

// 获取当前分组类型（自定义、字段）分组下分组描述的值
export const getGroupDescByType = (activeGroup: string, currentOptions: any[], selectedFieldId: string, rowData: any) => {
  let lineEndRowData = rowData;
  if (+activeGroup === +GroupType.field) {
    // 按字段分组
    // 当前选中字段需要一致
    const groupData = currentOptions.find((i: any) => `${i.groupType}` === `${activeGroup}` && i.fieldId === selectedFieldId);
    if (groupData) {
      lineEndRowData = {
        descField: groupData?.descField,
        descText: groupData?.descText,
        useStats: groupData?.useStats,
      };
    }
  }
  if (+activeGroup === +GroupType.dateRange) {
    const groupData = currentOptions.find((i: any) => `${i.groupType}` === `${activeGroup}`);
    if (groupData) {
      lineEndRowData = {
        descField: groupData?.descField,
        descText: groupData?.descText,
        useStats: groupData?.useStats,
      };
    }
  }
  return lineEndRowData;
};
// 获取关联字段下的子字段
export const getRelationBrowserFields = async (dataset: DataSetItem, callBack?: Function) => {
  try {
    const fieldData = (await getFields(dataset, '', false, false)) || [{ fields: [] }];
    const FieldOpts = fieldData[0].fields
      .filter(
        (data: any) =>
          `${data?.multiSelect}` === 'false' &&
          (isEbRelationBrowser(data) || data.compType === EtComponentKey.Ebuilder || SelectType.includes(data.compType))
      )
      .map((data: any) => ({ ...data, id: data.id, content: data.text }));
    typeof callBack === 'function' && callBack(FieldOpts);
    return FieldOpts;
  } catch (error) {}
};

export const checkRequired = (type: string, optValue: any) => {
  const defaultText = getLabel('40502', '请选择');
  // 按字段分组校验
  if (type === GroupType.field) {
    // 当选中关联ebuilder字段时 强制校验分组标题
    if (optValue.fieldType === EtComponentKey.Ebuilder && !optValue.showField) {
      return `${defaultText}${getLabel('264753', '分组标题')}`;
    }
  } else if (type === GroupType.dateRange) {
    // 日期分组需要都校验
    if (!optValue.fieldId) {
      return `${defaultText}${getLabel('56080', '日期字段')}`;
    }
    if (!optValue.dateRange) {
      return `${defaultText}${getLabel('189910', '日期范围')}`;
    }
  } else if (type === GroupType.diff) {
    // 差异化分组要校验关联字段
    if (!optValue.rootField) {
      return `${defaultText}${getLabel('186580', '关联字段')}`;
    }
  }
  return '';
};

// * 判断表单高级视图下是否有自定义分组配置
export const judgeGroupActive = (groups: any) => {
  let result = false;
  if (`${groups.active}` === GroupType.custom) {
    // 统一分组-自定义分组
    const customOptions = groups && groups.options && groups.options.find((i: any) => i.groupType === groups.active);
    const unGrouped = customOptions.values.filter((i: any) => i.name !== getLabel('105298', '未分组'));
    if (!isEmpty(unGrouped)) {
      result = true;
    }
  } else if (`${groups.active}` === GroupType.field || `${groups.active}` === GroupType.dateRange) {
    // 统一分组-按字段分组、日期分组
    result = true;
  }
  return result;
};

// 填充分组设置
// 存在部分场景返回时没有text和id的特殊场景 需要手动填充下
export const fillGroupFilter = (filter: any, relFields: any[] = []) => {
  const filterDatas = filter?.datas || []
  if (filterDatas.length > 0) {
    filter.datas = filter.datas.map((item: any) => {
      if (!item.text && !item.id) {
        const datasetIndex = relFields.findIndex((field: any) => field.name === item.fieldName);
        if (datasetIndex > -1) {
          // @ts-ignore
          item = { ...relFields[datasetIndex], ...item, parentNode: filter.key };
        }
      }
      return item;
    });
  }
  return filter;
};
