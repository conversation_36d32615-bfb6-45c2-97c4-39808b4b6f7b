// 分组设置-选择字段为关联ebuilder后才有的相关配置
// 分组设置-按数据源分组-isdstWay即为数据源分组

import React from 'react';
import { Dialog, Icon, IconNames, Button, Tag, CorsComponent, Help } from '@weapp/ui';
import { getLabel, isEqual, isEmpty } from '@weapp/utils';
import { DataSet as DataSetType } from '@weapp/ebdcoms';
import { toJS } from 'mobx';
import { ebdBClsPrefix, dlgIconName } from '../../../../../constants';
import { EtComponentKey } from '../../../../../constants/EtComponent';
import { getTargetFieldsByType } from '../utils';
import DataSort from '../../../../common/data-sort';
import { specialFieldsFilter, getRsFields, isEbRelationBrowser, isEbuilderField } from '../../../../../utils';
import { EbBrowser } from '../../../../common/eb-switch/EbBrowser';
import { DataSetItem } from '../../../../common/DataSet/types';
import { GroupRowItem } from '../Common';
import { fillGroupFilter } from '../utils';
import DataSet from '../../../../common/DataSet';
import NSelect from '../../../../common/n-select';
import { GroupDataOptionItem } from '../types';

export interface EbrAndDataSetFieldProps {
  [_: string]: any;
  fieldsMap: any[]; // 当前数据源内所有字段
  onChange: (value?: any) => void;
  value: GroupDataOptionItem;
  dataset: DataSetType;
  isdstWay?: boolean; // 是否为按数据源分组
  titleRowSpan?: number
  hideFields?: string[] // 隐藏字段
  targetFields?: boolean // 是否为自带目标数据源字段
}
export interface GroupEbuilderValueProps {
  [_: string]: any;
  showField?: string;
}
const mockDataSet = () => ({
  id: '',
  text: '',
  groupId: '',
  type: 'FORM',
});
const cls = `${ebdBClsPrefix}-board-group-common`;
const diffKey = `GroupEbuilderField`;
export default class EbrAndDataSetFieldComps extends React.PureComponent<EbrAndDataSetFieldProps> {
  state = {
    showTitle: false,
    showFilter: false,
    loading: false,
    relDataset: {} as DataSetItem,
    relFields: [],
    baseGroupFields: [], // 基础表字段列表-数据源分组需要选
    dsGroupFields: [],
    showFieldValue: '',
  };
  componentDidMount() {
    const { isdstWay, targetFields } = this.props;
    if (isdstWay) {
      this.getBaseDataSetFields();
      this.initDataSetGroup();
      return;
    }
    this.initFieldGroup();
  }
  componentWillReceiveProps(nextProps: Readonly<EbrAndDataSetFieldProps>) {
    if (nextProps.isdstWay) {
      if (!isEqual(nextProps.fieldsMap, this.props.fieldsMap)) {
        this.getBaseDataSetFields(nextProps);
      }
      if (!isEqual(nextProps.value?.dataSoueceConfig, this.props.value?.dataSoueceConfig)) {
        this.initDataSetGroup(nextProps);
      }
      return;
    }
    if (nextProps.value.fieldId !== this.props.value.fieldId || !isEqual(nextProps.fieldsMap, this.props.fieldsMap)) {
      this.initFieldGroup(nextProps);
    }
  }
  // * 按字段分组-关联ebuilder分组
  // 初始化字段-当前所选字段的关联ebuilder对应表单下的字段
  initFieldGroup = (props = this.props) => {
    const { value, fieldsMap } = props;
    if (!value.fieldId) return;
    const currentField = fieldsMap.find(item => item.id === value.fieldId);
    const relDataset = {
      id: currentField?.config?.sformId,
      text: '',
      type: 'FORM',
      groupId: '',
    };
    if (!relDataset.id) return;
    this.setState({ relDataset }, () => {
      getRsFields({ dataset: relDataset, cb: (_field: any[]) => this.setState({ relFields: _field }) });
    });
    // 填充默认值
    this.initDefaultValue();
  };
  // * 按数据源分组-初始化当前所选数据源字段列表
  getBaseDataSetFields = (props = this.props) => {
    const { fieldsMap } = props;
    if (isEmpty(fieldsMap)) return;
    // 只能是关联ebuilder,  主键和id
    const newField = fieldsMap.filter(f => isEbRelationBrowser(f) || f.mainPrimaryKey || isEbuilderField(f));
    this.setState({ baseGroupFields: newField });
  };
  initDataSetGroup = (props = this.props) => {
    const dataSetValue = this.getDataGroupDsValue(props);
    if (!dataSetValue?.id) return;
    getRsFields({
      dataset: dataSetValue,
      cb: (_field: any[]) => {
        this.setState({ dsGroupFields: _field });
      },
    });
  };
  getDataGroupDsValue = (props = this.props) => {
    const { value = {} } = props;
    const { dataSoueceConfig } = value! || {};
    return dataSoueceConfig?.dataSet || mockDataSet();
  };
  initDefaultValue = () => {
    const { value = {} } = this.props;
    if (!value.fieldId) return;
    const { showField } = value;
    this.setState({ showFieldValue: showField });
  };
  renderItem = ({ title, content, helpInfo, selfTitleRowSpan }: { title: string | React.ReactNode; content: string | React.ReactNode; helpInfo?: string[]; selfTitleRowSpan?: number }) => {
    const { titleRowSpan } = this.props;
    return <GroupRowItem weId={`${this.props.weId || ''}_9h95jf`} title={title} content={content} helpInfo={helpInfo} titleRowSpan={titleRowSpan || selfTitleRowSpan} />;
  };
  setIcon = (hasValue: boolean, clickFunc?: () => void, iconName?: IconNames) => {
    return <Icon weId={`${this.props.weId || ''}_ysn7x2`} name={`${hasValue ? 'Icon-set-up01' : 'Icon-set-up-o'}` || iconName} onClick={clickFunc} />;
  };
  // 设置按字段分组标题
  showFieldGroupTitle = () => {
    this.initDefaultValue();
    this.setState({ showTitle: true });
  };
  // 设置按字段分组数据过滤
  setFieldGroupFilter = () => {};
  // 设置按字段分组数据排序
  showFieldGroupSort = () => {
    this.setState({ showSort: true });
  };
  onChangefield = (value: any, options: any) => {
    const { onChange } = this.props;
    onChange({ fieldId: options?.id, fieldType: options?.compType });
  };
  renderTitle = () => {
    const { showTitle, relFields, showFieldValue, dsGroupFields } = this.state;
    const { onChange, isdstWay } = this.props;
    if (!showTitle) return null;
    const _fields = isdstWay ? dsGroupFields : relFields;
    const close = () => {
      this.setState({ showTitle: false });
    };
    const onConfirm = () => {
      onChange({ showField: showFieldValue });
      close();
    };
    const buttons = [
      <Button weId={`${this.props.weId || ''}_plmwds@${diffKey}`} key="save" type="primary" onClick={onConfirm}>
        {getLabel('221901', '确定')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_901za4@${diffKey}`} key="onCancel" onClick={close}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const setShowField = (field: any) => {
      this.setState({ showFieldValue: showFieldValue === field.id ? '' : field.id });
    };
    // 与后端lzh沟通 不处理id和标题
    const data = getTargetFieldsByType(_fields, EtComponentKey.Text).filter(i => `${i.id}` !== '5');
    return (
      <Dialog weId={`${this.props.weId || ''}_3xbgjz`} title={getLabel('277036', '分组标题设置')} footer={buttons} width={500} destroyOnClose visible mask closable onClose={close} icon={dlgIconName}>
        <div className={`${cls}-groupTitle`}>
          {data.map(item => {
            return (
              <div key={item.id} className={`${cls}-groupTitle-tag`} onClick={() => setShowField(item)}>
                <Tag weId={`${this.props.weId || ''}_v2jv4h@${diffKey}`} type={showFieldValue === item.id ? 'primary' : 'default'}>
                  {item.content}
                </Tag>
              </div>
            );
          })}
        </div>
      </Dialog>
    );
  };
  renderDataSort = () => {
    const { onChange, value, isdstWay } = this.props;
    const { relDataset } = this.state;
    const dataSetValue = this.getDataGroupDsValue();
    const _dataSet = isdstWay ? dataSetValue : relDataset;
    if (!_dataSet.id) return null;
    const handleChange = (value: any) => {
      onChange({ orderField: toJS(value) });
    };
    return (
      <DataSort
        weId={`${this.props.weId || ''}_18yvex`}
        value={value.orderField}
        dataset={_dataSet!}
        onChange={handleChange}
        customBtnRender={(showSortSet: () => void) => {
          return this.setIcon(!isEmpty(value.orderField), showSortSet);
        }}
      />
    );
  };
  // *按数据源分组-选择数据源
  renderDataSet = () => {
    const { value, onChange } = this.props;
    const handleChange = (value: any) => {
      const dataSoueceConfig = value?.dataSoueceConfig || {};
      onChange({ dataSoueceConfig: { ...dataSoueceConfig, dataSet: value } });
    };
    return (
      <div style={{ width: '200px' }}>
        {/* @ts-ignore */}
        <DataSet weId={`${this.props.weId || ''}_617jfu`} onChange={handleChange} value={this.getDataGroupDsValue()} showLogicData={false} />
      </div>
    );
  };
  handleFilterVisible = (visible: boolean) => {
    this.setState({ showFilter: visible });
  };
  renderFilter = () => {
    const { relDataset, showFilter, relFields } = this.state;
    const { onChange, value = {}, isdstWay } = this.props;
    const dataSetValue = this.getDataGroupDsValue();
    const _dataSet = isdstWay ? dataSetValue : relDataset;
    if (!_dataSet?.id || !showFilter) return null;
    const close = () => {
      this.handleFilterVisible(false);
    };
    const onOk = (data: any) => {
      onChange({ filter: data });
      close();
    };
    const validFilter = fillGroupFilter(value.filter, relFields);
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_b0dgq0`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        dataSet={_dataSet}
        visible
        title={getLabel('105959', '看板视图固定查询条件')}
        onCancel={close}
        onClear={onOk}
        onOk={onOk}
        fieldFilter={specialFieldsFilter}
        value={validFilter}
      />
    );
  };
  // *仅按字段分组支持-不需要单独再给数据源分组适配
  renderDefaultGroup = () => {
    const { value: propsValue, onChange } = this.props;
    const field = {
      componentKey: EtComponentKey.Ebuilder,
      title: getLabel('40502', '请选择'),
      fieldId: propsValue.fieldId,
    };
    return (
      <EbBrowser
        weId={`${this.props.weId || ''}_9z4au4`}
        field={field as any}
        value={propsValue.defaultGroup}
        onChange={(value: any) => {
          onChange({ defaultGroup: toJS(value) });
        }}
      />
    );
  };
  render() {
    const { value: propsValue, isdstWay, hideFields = [], titleRowSpan, onChange } = this.props;
    const { baseGroupFields } = this.state;
    const dataSetValue = this.getDataGroupDsValue();

    return (
      <>
        {isdstWay && dataSetValue?.id && (
          <>
            {this.renderItem({
              title: getLabel('291694', '分组数据源'),
              content: this.renderDataSet(),
            })}
            {!hideFields.includes('groupFields') && this.renderItem({
              title: getLabel('186580', '关联字段'),
              content: (
                <NSelect weId={`${this.props.weId || ''}_cc63h8`} data={baseGroupFields} value={propsValue?.fieldId} onChange={this.onChangefield} style={{ width: '200px' }} />
              ),
              helpInfo: [getLabel('291693', '选择数据源分组关联当前基础数据源的字段')],
            })}
          </>
        )}
        {!isdstWay && (
          <>
            {!hideFields.includes('showField') && this.renderItem({
              title: getLabel('264753', '分组标题'),
              content: this.setIcon(!isEmpty(propsValue.showField), this.showFieldGroupTitle),
            })}
            {/* {!hideFields.includes('showField') && <ShowTitle weId={`${this.props.weId || ''}_rwsb3p`} value={propsValue.showField} onChange={onChange} titleRowSpan={titleRowSpan} />} */}
            {!hideFields.includes('filter') && this.renderItem({
              title: getLabel('53857', '数据过滤'),
              content: this.setIcon(!isEmpty(propsValue.filter), () => this.handleFilterVisible(true)),
            })}
            {!hideFields.includes('orderField') && this.renderItem({
              title: getLabel('277037', '数据排序'),
              content: this.renderDataSort(),
            })}
            {!hideFields.includes('defaultGroup') && this.renderItem({
              title: getLabel('151785', '默认分组'),
              content: this.renderDefaultGroup(),
              helpInfo: [getLabel('278079', '删除分组的时候会把数据移动到默认该分组中')],
            })}
          </>
        )}
        {this.renderTitle()}
        {this.renderFilter()}
      </>
    );
  }
}
