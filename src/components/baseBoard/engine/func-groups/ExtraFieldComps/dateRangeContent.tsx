// 分组设置-日期分组后才有的相关配置

import React from 'react';
import { Popover, Icon, IconNames, Select, Button } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../../../constants';
import { GroupRowItem } from '../Common';
import { getTargetFieldsByType } from '../utils';
import FiveTip from '../../../../../assets/date-range-five.png';
import ThreeTip from '../../../../../assets/date-range-three.png';

export interface DateRangeFieldCompsProps {
  [_: string]: any;
  fieldsMap: any[]; // 当前数据源内所有字段
  value: any;
  onChange: (value: any) => void;
  titleRowSpan?: number;
}

export default class DateRangeFieldComps extends React.PureComponent<DateRangeFieldCompsProps> {
  state = {};
  rangeSelect = [
    {
      id: '3',
      content: `${getLabel('249224', '最近')}3${getLabel('99209', '天')}`,
    },
    {
      id: '5',
      content: `${getLabel('249224', '最近')}5${getLabel('99209', '天')}`,
    },
  ];
  setIcon = (clickFunc?: () => void, iconName?: IconNames) => {
    return <Icon weId={`${this.props.weId || ''}_as4fq0`} name={iconName || 'Icon-set-up-o'} onClick={clickFunc} />;
  };

  render() {
    const { fieldsMap, value, onChange, titleRowSpan = 4 } = this.props;
    const contentRowSpan = 24 - titleRowSpan
    const popContent = (
      <div className={`${ebdBClsPrefix}-view-group-main-field-imgTip-content ${value?.dateRange}`}>
        <div className={`${ebdBClsPrefix}-view-group-main-field-imgTip-content-title`}>
          {getLabel('249224', '最近')}
          {value?.dateRange}
          {getLabel('99209', '天')}
        </div>
        <div className={`${ebdBClsPrefix}-view-group-main-field-imgTip-content-img`}>
          <img src={value?.dateRange === '3' ? ThreeTip : FiveTip} />
        </div>
      </div>
    );
    return (
      <>
        {/* 这里只支持分组类型为日期的 */}
        <GroupRowItem
          weId={`${this.props.weId || ''}_2jic1y`}
          titleRowSpan={titleRowSpan}
          contentRowSpan={contentRowSpan}
          title={getLabel('56080', '日期字段')}
          helpInfo={[getLabel('278345', '设置以哪个日期字段作为数据分组的依据')]}
          content={
            <Select
              weId={`${this.props.weId || ''}_cc63h8`}
              className={`${ebdBClsPrefix}-view-group-main-field-select`}
              data={getTargetFieldsByType(fieldsMap, 'dateFieldRange')}
              value={value?.fieldId}
              onChange={(value: any) => onChange({ fieldId: value })}
            />
          }
        />
        <GroupRowItem
          weId={`${this.props.weId || ''}_sywl5n`}
          titleRowSpan={titleRowSpan}
          contentRowSpan={contentRowSpan}
          title={getLabel('189910', '日期范围')}
          helpInfo={[
            `${getLabel('278346', '设置看板分组显示的日期范围，例如')}：`,
            `1. ${getLabel('278347', '设置最近3天：昨天、今天、明天、其他')}`,
            `2. ${getLabel('278348', '设置最近5天：前天、昨天、今天、明天、后天、其他')}`,
          ]}
          content={
            <Select
              weId={`${this.props.weId || ''}_cc63h8`}
              className={`${ebdBClsPrefix}-view-group-main-field-select`}
              data={this.rangeSelect}
              value={value?.dateRange}
              onChange={(value: any) => onChange({ dateRange: value })}
            />
          }
          extraContent={
            <Popover
              weId={`${this.props.weId || ''}_q83ti8`}
              popup={popContent}
              placement="rightTop"
              popoverType="popover"
              triggerProps={{ popupClassName: `${ebdBClsPrefix}-view-group-main-field-imgTip-popover fade-container fade-in` }}
            >
              <div className={`${ebdBClsPrefix}-view-group-main-field-imgTip`}>
                <Icon weId={`${this.props.weId || ''}_fhg524`} name="Icon-investigation02" />
              </div>
            </Popover>
          }
        />
      </>
    );
  }
}
