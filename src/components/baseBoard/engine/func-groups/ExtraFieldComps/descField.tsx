// 分组设置-选择字段/日期分组后-描述信息

import React from 'react';
import { When } from 'react-if';
import { Icon } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { selectedFieldProps } from '../types';
import { GroupRowItem } from '../Common';


export interface FieldDescProps {
  selectedField: selectedFieldProps;
  showSetDesc: () => void;
  value?: any
}

export default class FieldTypeComps extends React.PureComponent<FieldDescProps> {
  setIcon = (hasValue: boolean, clickFunc?: () => void) => {
    return <Icon weId={`${this.props.weId || ''}_af2qds`} name={`${hasValue ? 'Icon-set-up01' : 'Icon-set-up-o'}`} onClick={clickFunc} />;
  };
  render() {
    const { selectedField, showSetDesc, value = {} } = this.props;
    const isValidField = selectedField.id && selectedField.id !== '-1';
    return (
      <>
        <When weId={`${this.props.weId || ''}_lzzq7z`} condition={isValidField}>
          <GroupRowItem weId={`${this.props.weId || ''}_y0edyy`}
            title={getLabel('253192', '分组描述')}
            content={this.setIcon(!isEmpty(value?.descField), showSetDesc)}
          />
        </When>
      </>
    );
  }
}
