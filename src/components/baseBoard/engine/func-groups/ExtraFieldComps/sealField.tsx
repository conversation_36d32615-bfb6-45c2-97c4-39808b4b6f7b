// 分组设置-选择字段后-是否封存配置

import React from 'react';
import { When } from 'react-if';
import { Switch } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { selectedFieldProps } from '../types';
import { GroupRowItem } from '../Common';
import { EtComponentKey } from '../../../../../constants/EtComponent';


export interface FieldSealProps {
  selectedField: selectedFieldProps;
  displaySealing: boolean;
  setState: (value: any) => void;
  titleRowSpan?: number;
}

export default class SealFieldComps extends React.PureComponent<FieldSealProps> {
  render() {
    const { selectedField, setState, displaySealing, titleRowSpan } = this.props;
    const { type } = selectedField || {};
    //  selectedField.id为-1时，即"请选择"
    const isValidField = [EtComponentKey.Select, EtComponentKey.RadioBox].includes(type as any) && selectedField.id !== '-1';
    return (
      <>
        <When weId={`${this.props.weId || ''}_j5h7sy`} condition={isValidField}>
          <GroupRowItem weId={`${this.props.weId || ''}_kin913`}
            title={getLabel('276643', '是否显示封存数据')}
            titleRowSpan={titleRowSpan}
            content={<Switch weId={`${this.props.weId || ''}_kftqon`} value={displaySealing} onChange={() => setState({ displaySealing: !displaySealing })} />}
          />
        </When>
      </>
    );
  }
}
