import { ComponentType } from 'react';
import Loadable from '../../../../react-loadable';
import { EbrAndDataSetFieldProps } from './ebrAndDataSetFieldComps';
import { DateRangeFieldCompsProps } from './dateRangeContent';
import { FieldSealProps } from './sealField';
import { FieldDescProps } from './descField';

const ExtraDescField = Loadable({
  name: 'ExtraDescField',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_group_ExtraDescField" */
      './descField'
    ),
}) as ComponentType<FieldDescProps>;
const ExtraSealField = Loadable({
  name: 'ExtraSealField',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_group_ExtraSealField" */
      './sealField'
    ),
}) as ComponentType<FieldSealProps>;
const EbrAndDataSetFieldComps = Loadable({
  name: 'GroupEbuilderFieldComps',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_group_GroupEbuilderFieldComps" */
      './ebrAndDataSetFieldComps'
    ),
}) as ComponentType<EbrAndDataSetFieldProps>;

const DateRangeFieldComps = Loadable({
  name: 'DateRangeFieldComps',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_group_DateRangeFieldComps" */
      './dateRangeContent'
    ),
}) as ComponentType<DateRangeFieldCompsProps>;


export { ExtraDescField, ExtraSealField, EbrAndDataSetFieldComps, DateRangeFieldComps };
