/**
 * <AUTHOR>
 * @createTime 2021-11-25
 */
import { DataSet } from '@weapp/ebdcoms';
import { Button, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, {
  Attributes, ComponentType, useEffect, useState,
} from 'react';
import { ebdBClsPrefix } from '../../../../constants';
import { specialFieldsFilter } from '../../../../utils';
import ComsFilterDlg from '../../../common/condition-set/ConditionSetDlg';
import { BtnType, ConfigItem } from './types';

export interface ParamConditionProps extends Attributes {
  value: any;
  dataset: DataSet;
  onSave: (key: ConfigItem, value: any) => void;
  btnClassName?: string;
  buttonType?: BtnType;
}

const ParamCondition: ComponentType<ParamConditionProps> = (props) => {
  const {
    dataset, onSave, value, btnClassName, buttonType = 'default',
  } = props;
  const [show, setShow] = useState<boolean>(false);
  const [val, setVal] = useState<any>(value);

  const onOk = (filters: any) => {
    setVal(filters);
    onSave(ConfigItem.Condition, filters);
    setShow(false);
  };
  useEffect(() => {
    setVal(value);
  }, [value]);

  const onCancel = () => {
    setVal(value);
    setShow(false);
  };

  const triggerShow = () => {
    const visible = !show;
    setShow(visible);
  };

  return (
    <Button
      weId={`${props.weId || ''}_7e40jq`}
      onClick={triggerShow}
      className={`${ebdBClsPrefix}-calendar-style-set-button ${btnClassName}`}
      type={buttonType}
    >
      <Icon weId={`${props.weId || ''}_y8amo8`} className="btn-icon-left" name="Icon-screen" />
      {getLabel('55481', '条件')}
      <ComsFilterDlg
        weId={`${props.weId || ''}_rlzl2e`}
        visible={show}
        dataset={dataset}
        title={getLabel('105959', '看板视图固定查询条件')}
        filters={val}
        onCancel={onCancel}
        onOk={onOk}
        filter={specialFieldsFilter}
      />
    </Button>
  );
};

export default observer(ParamCondition);
