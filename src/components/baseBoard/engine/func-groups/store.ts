import { DataSet } from '@weapp/ebdcoms';
import { AnyObj, Dialog, TypesBrowserOption, utils } from '@weapp/ui';
import { getLabel, isEmpty, forIn, trim } from '@weapp/utils';
import { action, computed, observable, runInAction, toJS } from 'mobx';
import { KanBanDefaultBgColor } from '../../../../constants';
import { GroupType } from '../../../../constants/common';
import { EtComponentKey, SelectType } from '../../../../constants/EtComponent';
import { ajax, format2FormData } from '../../../../utils/ajax';
import { deepToJS, removeDuplicateItems } from '../../../../utils';
import ebdcoms from '../../../../utils/ebdcoms';
import { CustomMenuAction } from '../../../common/board/types';
import { DataSetItem } from '../../../common/DataSet/types';
import { IdParams } from '../types';
import {
  BrowserTypes,
  ColorStyleType,
  commonBrowserTypes,
  INITIAL_GROUP_DATA,
  INITIAL_SELECTED_FIELD_DATA,
  INITIAL_DATE_GROUP_INFO,
  INITIAL_Field_GROUP_INFO,
  INITIAL_ID_PARAMS,
  INITIAL_DATASET_GROUP_INFO,
  INITIAL_DIFF_GROUP_INFO,
  GroupsTypeType,
} from './constants';
import { ConfigItem, CustomGroupItem, GroupDataOptionItem, GroupDataProps, DescItemProps, selectedFieldProps } from './types';
import { isEbFormDataV2, isUnEbDataSetCustomOptions } from '../../../common/DataSet/utils';
import { judgeGroupActive } from './utils';
import { RelationGroupsType } from './diff/constants';
import DiffStore from './diff/store';

const { isValueEmpty } = utils;

export class FuncGroupsStore {
  /** 公共 */
  @observable isLoading: boolean = false;
  @observable initLoading: boolean = true;
  @observable dataset: DataSetItem = {
    id: '',
    type: '',
    groupId: '',
    text: '',
  };

  @observable idParams: IdParams = INITIAL_ID_PARAMS();

  @observable boardId: string = '';

  @observable checkedTypes: string[] = [];

  /**
   * 外部回调函数函数变量
   * */
  @observable callbackFunc: any = {
    refresh: undefined,
  };

  /** 条件 */

  @observable condition: any = {}; // params条件参数

  @observable filter: any = {
    commonFilters: [],
    quickFilters: [],
    groupFilters: [],
    commonSearchType: '0',
  }; // params条件参数

  @observable orders: any = [];

  @observable card: any = {};

  /**  分组参数 */
  @observable data: GroupDataProps = INITIAL_GROUP_DATA();

  @observable groupType: GroupType = GroupType.custom;

  @observable optionCustom: CustomGroupItem[] = []; // 自定义选项
  @observable filterOptions: CustomGroupItem[] = []; // 按条件分组

  @observable selectedField: selectedFieldProps = INITIAL_SELECTED_FIELD_DATA();

  @observable fakeBoardData: any[] = [];

  @observable boardRerenderKey: string = '';

  @observable comServicePath: string = 'ebuilder/coms'; // ebuilder/coms映射路径

  // 权限
  @observable permissions: any = [];

  @observable showCompany: boolean = false;

  @observable showSeclevel: boolean = false;

  @observable boardColorStyle: any = 'noStyle'; // 判断看板背景色类型
  @observable customMenuActions: CustomMenuAction[] = []; // 自定义按钮事件集合
  // 分组统计数据
  @observable statistics: any = {
    openValue: false,
    statisticsItem: [],
  };
  // 是否为表单高级视图
  @observable isFromEbAdvance = false;
  // 是否显示封存
  @observable displaySealing = true;
  // 当前数据源字段集合
  @observable datasetMap = [];
  // 日期分组相关数据存储
  @observable dateGroupInfo = INITIAL_DATE_GROUP_INFO();
  // 数据源分组相关数据存储
  @observable dateSourceGroupInfo = INITIAL_DATASET_GROUP_INFO();
  // 分组设置加载中
  @observable loading = true;
  // 分组配置-其他配置
  @observable otherData = {} as AnyObj;
  // 差异化分组
  @observable diffStore = new DiffStore();
  // 前台条件分组
  @computed
  get filterShowOption() {
    const _filterOptions = (this.filterOptions || []).filter((cpt: any) => `${cpt.type}` !== '0');
    return _filterOptions && _filterOptions.length
      ? _filterOptions
      : [
          {
            type: '1',
            id: `temp_${Date.now()}`,
            name: '',
            color: KanBanDefaultBgColor,
          },
        ];
  }
  // 前台自定义分组
  @computed
  get customShowOption() {
    const normalizedOption = (this.optionCustom || []).filter((cpt: any) => `${cpt.type}` !== '0');
    return normalizedOption && normalizedOption.length
      ? normalizedOption
      : [
          {
            type: '1',
            id: `temp_${Date.now()}`,
            name: '',
            color: KanBanDefaultBgColor,
          },
        ];
  }
  // 统一分组-字段分组值
  @computed
  get groupTypeOpt() {
    return (this.data?.options || []).find((cpt: any) => `${cpt.groupType}` === GroupType.field) || INITIAL_Field_GROUP_INFO();
  }
  // 统一分组-按日期分组值
  @computed
  get groupDateRangeOpt() {
    return (this.data?.options || []).find((cpt: any) => `${cpt.groupType}` === GroupType.dateRange) || INITIAL_DATE_GROUP_INFO();
  }
  // 数据加工分组数据
  @computed
  get dstWayValue() {
    return (this.data?.options || []).find((cpt: any) => `${cpt.groupType}` === GroupType.dataset) || INITIAL_DATASET_GROUP_INFO();
  }
  // 是否为数据加工数据源
  @computed
  get isEteams() {
    return this.dataset?.type === 'ETEAMS';
  }
  // 字段分组列表
  @computed
  get optionField() {
    // *数据加工数据源排除人员类型及不校验multiSelect
    // *数仓的人员字段分组放开也选不到   能选到的人员字段必须是单选的   数仓那边的人员字段固定是多选，没法设置单选
    const eteamsFilter = (item: any) => {
      if (!this.isEteams) return true;
      return (item?.compType || item?.type) !== EtComponentKey.Employee;
    };
    const normalFilter = (item: any) => {
      return `${item?.multiSelect}` === 'false' || !item?.multiSelect;
    };
    return this.datasetMap
      .filter(
        (data: any) =>
          (SelectType.includes(data.compType) || SelectType.includes(data.type)) &&
          eteamsFilter(data) &&
          normalFilter(data) &&
          data?.compTypec !== 'ComboSelect' &&
          !['8', '9', '10', '11', '12', '13', '14', '15', '16'].includes(data?.id) //数据状态、流程状态等系统字段也是Select类型，需要屏蔽下
      )
      .map((data: any) => ({ id: data.id, content: data.text, type: data.compType || data.type }));
  }
  @action
  setComServicePath = (comServicePath: string) => {
    this.comServicePath = comServicePath || 'ebuilder/coms';
  };
  @action
  setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };
  @action('统一方法获取当前选中分组的配置项')
  getCurrentGroupValue = () => {
    if (`${this.groupType}` === GroupType.custom) {
      return this.customShowOption;
    } else if (`${this.groupType}` === GroupType.field) {
      return this.groupTypeOpt;
    } else if (`${this.groupType}` === GroupType.dateRange) {
      return this.groupDateRangeOpt;
    } else if (`${this.groupType}` === GroupType.diff) {
      // 只做校验 校验是否有选关联字段
      return { rootField: this.diffStore.rootField };
    }
  };

  /**
   * 组装 更新【分组配置】数据结构，明确接口交互的所有参数
   * @returns
   */
  @action
  buildUpData = (reset?: boolean): GroupDataProps => {
    let form: any = {
      ...this.data,
      displaySealing: this.displaySealing,
      active: `${this.groupType}` !== GroupType.diff ? `${this.groupType}` : this.diffStore.relationSelected,
      permissions: toJS(this.permissions),
      options: deepToJS(this.getBuildUpDataOptions(false, reset)),
      otherData: toJS(this.otherData),
    };
    // * 非表单高级视图才带该配置 目的是解决数据源从业务数据源切换为表单数据源 没走统一保存配置时  后端未拿到最新的数据源
    if (!this.isFromEbAdvance) {
      form.sourceType = this.dataset.type;
    }
    // * 差异化分组
    if (`${this.groupType}` === GroupType.diff) {
      // 需要带上dataGroup
      form.dataGroup = `${this.groupType}` === GroupType.diff ? GroupsTypeType.releationField : GroupsTypeType.normalCustom;
      form.rootField = this.diffStore.rootField;
      // *差异化分组自定义分组需要补充 dataOptions 不然无法同步前台显示
      if (this.diffStore.isCustomRelationGroupsType) {
        form.groupField = this.diffStore.groupField;
        form.dataOptions = this.getBuildUpDataOptions(true, reset);
      }
    } else {
      // 非差异化分组 需要清空差异化分组数据
      this.diffStore.resetDiff();
      // 默认是非差异化分组
      form.dataGroup = GroupsTypeType.normalCustom;
    }

    return form;
  };
  /**
   * @param isDataOptions 是否要创建dataOptions
   * @param reset 是否只保留当前分组信息 其余分组信息初始化 自定义分组默认保留
   */

  getBuildUpDataOptions = (isDataOptions?: boolean, reset?: boolean) => {
    const dataOptions = this.data.options || [];
    // eslint-disable-next-line max-len
    const targetActive = `${this.groupType}`;
    // eslint-disable-next-line max-len
    const fieldIndex = dataOptions.findIndex((f: any) => +f.groupType === +GroupType.field);
    const fieldOptionItem: any = dataOptions[fieldIndex] || {};
    const buildDescInfo = (currentOption: any = {}) => {
      return {
        descField: currentOption.descField || null,
        descText: currentOption.descText || '',
      };
    };
    // *自定义分组
    const normalOptions = {
      groupType: GroupType.custom,
      enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
      values: this.optionCustom
        .filter((opt: any) => trim(opt.name))
        .map((opt: any) => {
          if (opt.id && opt.id.startsWith('temp_')) {
            return {
              name: opt.name,
              type: opt.type,
              color: opt?.color || '',
              ...buildDescInfo(opt),
            };
          }
          return opt;
        }),
    };
    // 有未生成的情况 手动给一次默认值
    const _dataGroupInfo = this.groupDateRangeOpt || INITIAL_DATE_GROUP_INFO();
    // *按日期分组
    const dateGroupOptions =
      this.groupType !== GroupType.dateRange && reset
        ? INITIAL_DATE_GROUP_INFO()
        : {
            ...this.dateGroupInfo,
            ...buildDescInfo(_dataGroupInfo),
            fieldId: _dataGroupInfo.fieldId,
            dateRange: _dataGroupInfo.dateRange,
          };
    // * 差异化分组-默认分组
    const diffOptions = {
      groupType: RelationGroupsType.Normal,
      // enableColor: targetActive === RelationGroupsType.Normal ? this.boardColorStyle === ColorStyleType.HasStyle : originEnable,
      // enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
      values: this.diffStore.relationNormalGroups
        .filter((opt: any) => trim(opt.name))
        .map((opt: any) => {
          if (opt.id && opt.id.startsWith('temp_')) {
            return {
              name: opt.name,
              type: opt.type,
              color: opt?.color || '',
            };
          }
          return opt;
        }),
    };
    // *差异化分组下-自定义分组
    // 历史数据或者非明确场景存在rootDataId重复的问题 所以需要过滤一次保证唯一性
    // 处理数组内重复rootDataId 只保留一条
    const _relationCustomGroupsArray = this.diffStore.relationCustomGroupsArray.map(arr => {
      let { optionValue, ...restProps } = arr;
      // * fieldId 为关联ebuilder表单字段中关联ebuilder所指向的id
      // * dataOptions 需要传rootData 值都为左侧选中项的值
      let values = toJS(arr.values)
        .filter((opt: any) => trim(opt.name))
        .map((opt: any) => {
          // * temp_开头或者是构建dataOptions 不显示id
          if (opt.id && opt.id.startsWith('temp_')) {
            return {
              name: opt.name,
              type: opt.type,
              color: opt?.color,
            };
          }
          return opt;
        });
      if (isDataOptions) {
        restProps = { ...restProps };
        // * 找不到未分组 需要补全
        if (!values.find((i: any) => +i.type === 0)) {
          values.push({
            type: 0,
            name: getLabel('105298', '未分组'),
          });
        }
      } else {
        restProps = { ...restProps, fieldId: this.diffStore.groupField, fieldType: this.diffStore.relationCustomTargetField?.compType };
      }
      return {
        ...restProps,
        // enableColor: targetActive === RelationGroupsType.Custom ? this.boardColorStyle === ColorStyleType.HasStyle : originEnable,
        // enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
        values: toJS(values),
      };
    }).filter((item: any, index: number, self: any) =>
      index === self.findIndex((t: any) => t.rootDataId === item.rootDataId)
    );

    const cusDiffOptions = _relationCustomGroupsArray;

    // *按数据源分组
    // const dataSourceGroupOptions =
    //   this.groupType !== GroupType.dataset && reset
    //     ? INITIAL_DATASET_GROUP_INFO()
    //     : {
    //         ...this.dstWayValue,
    //         ...buildDescInfo(this.dateSourceGroupInfo),
    //       };
    // 当前是字段分组且 字段分组选中的是未分组
    if (this.selectedField.id === '-1' && targetActive === GroupType.field) {
      return [normalOptions];
    }
    const { values, ...shouldSavedInfo } = fieldOptionItem;
    // *按字段分组
    const fieldOptions =
      this.groupType !== GroupType.field && reset
        ? INITIAL_Field_GROUP_INFO()
        : {
            groupType: GroupType.field,
            enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
            ...shouldSavedInfo,
            fieldId: this.selectedField.id === '-1' ? '' : this.selectedField.id,
            fieldType: this.selectedField.type || '',
            ...buildDescInfo(fieldOptionItem),
          };
    // *按条件分组
    const filterOptions = {
      groupType: GroupType.filter,
      enableColor: this.boardColorStyle === ColorStyleType.HasStyle,
      values: this.filterOptions
        .filter((opt: any) => trim(opt.name))
        .map((opt: any) => {
          if (opt.id && opt.id.startsWith('temp_')) {
            return {
              name: opt.name,
              type: opt.type,
              color: opt?.color || '',
              filters: opt?.filters || {},
              ...buildDescInfo(opt),
            };
          }
          return opt;
        }),
    };
    let newOptions = [
      normalOptions,
      // *字段分组新增“请选择”，允许用户回到默认分组 字段分组不传values
      fieldOptions,
      diffOptions,
      ...cusDiffOptions,
      filterOptions,
      toJS(dateGroupOptions),
      // toJS(dataSourceGroupOptions),
    ];
    return newOptions.filter((f: any) => !isEmpty(f));
  };

  /**
   * 分割分组配置 数据，转化为前端比较适用的数据结构
   * @param data
   */
  @action splitData = (data: GroupDataProps = INITIAL_GROUP_DATA()) => {
    this.data = data;
    const targetEnable = (data?.options || []).find((f: any) => `${f.groupType}` === data.active)?.enableColor;
    this.setBoardColorStyle(targetEnable ? ColorStyleType.HasStyle : ColorStyleType.NoStyle);
    const { active = GroupType.custom, options = [], permissions = [], displaySealing = true, otherData = {} } = data;
    // 【统一分组】 做下数据转化，前端区分不同的分组类型
    this.groupType = (`${data?.active}` === RelationGroupsType.Normal || `${data?.active}` === RelationGroupsType.Custom ? GroupType.diff : `${active}`) as GroupType;
    // 自定义选项(默认分组)
    const custom = options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === GroupType.custom);
    this.optionCustom = custom?.values || [];
    // 系统字段
    const field = options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === GroupType.field)!;
    // * 按字段分组选中字段
    // * 老数据没有存fieldType 需要从fields里去动态查
    this.selectedField = {
      id: field?.fieldId || '',
      // @ts-ignore
      type: field?.fieldType || this.datasetMap.find((i: any) => i.id === field?.fieldId)?.compType || '',
    };
    // 按日期分组分组信息
    this.dateGroupInfo = options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === GroupType.dateRange) || INITIAL_DATE_GROUP_INFO();
    // 按条件分组
    const filterOptionValues = options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === GroupType.filter);
    this.filterOptions = filterOptionValues?.values || [];
    // 按数据源分组
    // this.dateSourceGroupInfo =
    //   options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === GroupType.dataset) || INITIAL_DATASET_GROUP_INFO();
    // 分组配置其他配置
    this.otherData = otherData;
    this.permissions = permissions;
    // 是否显示封存
    this.displaySealing = displaySealing;
    this.changeBoardColorStyle();
  };

  /** ******************************分组配置数据结构处理 end ************ */

  @action
  clearGarbage = () => {
    this.optionCustom = [];
    this.datasetMap = [];
    this.fakeBoardData = [];
    this.checkedTypes = [];
    this.selectedField = INITIAL_SELECTED_FIELD_DATA();
  };

  @action
  initGroup = async (isFromEbAdvance: boolean, idParams: IdParams, value?: GroupDataProps, comServicePath?: string, dataset?: DataSet, diffGroupCustomId?: string) => {
    this.loading = true;
    this.idParams = idParams;
    this.isFromEbAdvance = isFromEbAdvance;
    if (dataset) {
      this.dataset = dataset;
    }
    if (comServicePath) {
      this.setComServicePath(comServicePath);
    }
    if (isFromEbAdvance) {
      await this.getFields({
        id: idParams.objId,
        text: '',
        type: 'FORM',
        groupId: idParams.appId,
      });
      await this.getGroupInfo();
      runInAction(() => (this.loading = false));
    } else {
      await this.getFields(dataset);
      runInAction(() => (this.loading = false));
      if (value && !isEmpty(toJS(value))) {
        this.splitData(value);
        runInAction(() => {
          // 当前分组是差异化分组需要初始化差异化分组
          if (`${this.groupType}` === GroupType.diff) {
            this.initDiffStore(value, diffGroupCustomId);
          }
        });
      } else {
        this.resetGroup();
      }
    }
    if (idParams.appId) {
      this.companyAndSeclevel();
    }
  };
  @action
  initDiffStore = (value: GroupDataProps, diffGroupCustomId?: string) => {
    this.diffStore = new DiffStore(value, this.datasetMap);
    // 回显差异化分组自定义分组
    if (diffGroupCustomId) {
      this.diffStore.releationCustomSelected = diffGroupCustomId;
    }
  }
  // mark 如果不为表单数据源 自定义分组目前不支持（需要各个模块自己对接）
  @action resetGroup = () => {
    runInAction(() => {
      this.data = INITIAL_GROUP_DATA();
      this.optionCustom = [];
      this.datasetMap = [];
      this.groupType = GroupType.custom;
    });
  };
  @action
  initBaseParams = async (idParams: IdParams, boardId: string, refreshFn?: () => void, comServicePath?: string) => {
    this.idParams = idParams;
    this.boardId = boardId;
    this.initLoading = true;
    if (refreshFn) {
      this.callbackFunc.refresh = refreshFn;
    }
    if (comServicePath) {
      this.setComServicePath(comServicePath);
    }
    const orders = await this.getConfigByKey(ConfigItem.Orders);
    const condition = await this.getConfigByKey(ConfigItem.Condition);
    const filter = await this.getConfigByKey(ConfigItem.Filter);
    const card = await this.getConfigByKey(ConfigItem.Card);
    const comButton = await this.getConfigByKey(ConfigItem.ComButton);
    const statistics = await this.getConfigByKey(ConfigItem.Statistics);
    const groups = await this.getConfigByKey(ConfigItem.Group);
    runInAction(() => {
      this.checkedTypes = [];
      this.orders = orders || [];
      if (!isEmpty(this.orders)) {
        this.checkedTypes.push(ConfigItem.Orders);
      }
      this.condition = condition;
      if (!isEmpty(this.condition)) {
        this.checkedTypes.push(ConfigItem.Condition);
      }
      this.card = card;
      if (!isEmpty(this.card) && toJS(this.card?.field).length > 0) {
        this.checkedTypes.push(ConfigItem.Card);
      }
      this.customMenuActions = comButton;
      if (!isEmpty(comButton)) {
        this.checkedTypes.push(ConfigItem.ComButton);
      }
      this.filter = filter || {
        commonFilters: [],
        quickFilters: [],
      };
      if (!isEmpty(this.filter?.commonFilters) || !isEmpty(this.filter?.quickFilters) || !isEmpty(this.filter?.groupFilters)) {
        this.checkedTypes.push(ConfigItem.Filter);
      }
      if (!isEmpty(statistics) && Array.isArray(statistics) && statistics?.[0]?.openValue) {
        this.checkedTypes.push(ConfigItem.Statistics);
      }
      if (judgeGroupActive(toJS(groups))) {
        this.checkedTypes.push(ConfigItem.Group);
      }
      if (Array.isArray(statistics) && statistics[0]) {
        this.statistics = statistics[0];
      }
      this.boardRerenderKey = `${Date.now()}`;
      this.initLoading = false;
    });
    await this.previewBackList();
  };

  @action
  getConfigByKey = async (configName: string, viewId: string = this.idParams.viewId, appId: string = this.idParams.appId): Promise<any> => {
    if (!viewId || !appId || !configName) return;
    const res = await ajax({
      url: `/api/bs/ebuilder/form/viewdesigner/getConfigByKey?apid=${appId}`,
      method: 'GET',
      params: {
        viewId,
        apid: appId,
        key: configName,
      },
      ebBusinessId: appId,
    });
    return res;
  };

  @action
  updateViewDesignerByKey = async (key: string, value: any) => {
    this.isLoading = true;
    try {
      await ajax({
        url: `/api/bs/ebuilder/form/viewdesigner/updateViewDesignerByKey?apid=${this.idParams.appId}`,
        method: 'POST',
        params: {
          apid: this.idParams.appId,
        },
        data: {
          viewId: this.idParams.viewId,
          key,
          value: JSON.stringify(value),
        },
        ebBusinessId: this.idParams.appId,
      });
      runInAction(() => {
        Dialog.message({ type: 'success', content: getLabel('54117', '保存成功') });
        if (key === ConfigItem.Filter) {
          this.filter = value;
          if (!isEmpty(this.filter?.commonFilters) || !isEmpty(this.filter?.quickFilters) || !isEmpty(this.filter?.groupFilters)) {
            // 兼容下面判断 value数组有值就行
            value = [1];
          } else {
            value = [];
          }
          // value = value?.commonFilters || value?.quickFilters || value?.groupFilters || [];
        } else if (key === ConfigItem.Condition) {
          this.condition = value;
        } else if (key === ConfigItem.Card) {
          this.card = value;
          this.previewBackList(true);
        } else if (key === ConfigItem.Orders) {
          this.orders = value;
        } else if (key === ConfigItem.ComButton) {
          this.customMenuActions = value;
        } else if (key === ConfigItem.Statistics) {
          this.statistics = value;
        }
        const fixedItem = (check: boolean) => {
          if (check) {
            this.checkedTypes = [...this.checkedTypes, key];
          } else {
            const index = this.checkedTypes.indexOf(key);
            if (index !== -1) {
              this.checkedTypes.splice(index, 1);
            }
          }
        };
        // 高亮更新
        // 分组相关单独处理
        switch (key) {
          case ConfigItem.Group:
            fixedItem(judgeGroupActive(toJS(value)));
            break;
          case ConfigItem.Statistics:
            //分组统计单独处理高亮与否
            fixedItem(value?.openValue);
            break;
          case ConfigItem.Card:
            //分组统计单独处理高亮与否
            const grid = value?.cardLayout?.grid || [];
            const hasGrid = grid.some((item: any) => item.length);
            fixedItem(hasGrid);
            break;
          default:
            fixedItem(value && !isEmpty(value));
            break;
        }
        this.callbackFunc.refresh?.();
        this.isLoading = false;
      });
    } catch {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  };

  /** *************************** 分组相关 *********************************** */

  @action
  getGroupInfo = async () => {
    const { viewId, appId } = this.idParams;
    const res = await this.getConfigByKey(ConfigItem.Group, viewId, appId);
    runInAction(() => {
      this.data = res;
      this.splitData(this.data);
    });
  };
  @action('切换分组')
  switchGroup = (id: any) => {
    // *因为所选字段值是外置的不在options内，需要单独重置选择字段值
    this.selectedField = INITIAL_SELECTED_FIELD_DATA();
    this.groupType = id;
    this.changeBoardColorStyle();
    // this.diffStore.resetDiff();
    // 切换到日期分组且没有默认值时，初始化默认值
    if (`${id}` === GroupType.dateRange && !this.groupDateRangeOpt) {
      this.dateGroupInfo = INITIAL_DATE_GROUP_INFO();
    }
    // 切换到差异化分组时，初始化默认值
    if (`${id}` === GroupType.diff) {
      // this.dateGroupInfo = INITIAL_DIFF_GROUP_INFO();
      this.data = this.getGroupData();
      // 初始化差异化分组
      this.initDiffStore(this.data);
    }
  };

  @action
  selectGroup = (options: any) => {
    // 未初始化字段分组信息
    if (isEmpty(this.groupTypeOpt)) {
      this.data = this.getGroupData();
    }
    // 如果是字段分组
    if (`${this.groupType}` === GroupType.field) {
      // 切换后要重置设置的值（分组标题、分组排序、分组描述等）
      this.changeFieldGroup();
      let otherParams = {} as any;
      // 选择了关联ebuilder字段, 默认填充当前分组title为1
      if (options.type === EtComponentKey.Ebuilder) {
        otherParams.showField = '1';
      }
      // 字段分组都还需要 填充当前分组和字段值和其余属性
      this.changeFieldGroup({ fieldType: options.type, fieldId: options.id, ...otherParams });
    }
    this.selectedField = options;
  };

  @action
  getGroupData = (valid?: boolean): boolean | any => {
    if (valid && `${this.groupType}` === GroupType.field && !this.selectedField.id) {
      Dialog.message({ type: 'info', content: '请先选择字段！' });
      return false;
    }
    // valid为true代表要校验  其余分组信息初始化掉
    return this.buildUpData(valid);
  };
  // @action('保存自定义分组信息')
  transformRealOption = async () => {
    let saveData = this.getGroupData(true);
    if (saveData === false) return false;
    // !2024-03-21 逻辑更新：表单数据源和无数据源传入场景下，保存配置到看板配置
    // !20241201基线新增条件分组 条件分组可支持saveKanbanOption 不限定数据源
    // !20250313新增数仓、业务数据源下自定义分组保存分组支持走saveKanbanOption
    const unEbDataSetCustomOptions = isUnEbDataSetCustomOptions(this.dataset, this.groupType);
    if (isEbFormDataV2({ dataset: this.dataset }) || `${this.groupType}` === GroupType.filter || !this.dataset?.type || unEbDataSetCustomOptions) {
      if (unEbDataSetCustomOptions) {
        saveData = {
          ...saveData,
          customGroup: '1'
        }
      }
      const res = await ajax({
        url: `/api/${this.comServicePath}/kanban/saveKanbanOption`,
        method: 'post',
        data: format2FormData({
          compId: this.idParams?.compId,
          pageId: this.idParams.pageId,
          extParam: JSON.stringify(saveData),
        }),
        ebBusinessId: this.idParams.pageId,
        error: () => {},
      });
      return res.result;
    }
    // *如果是非eb数据源 直接保存配置到config的group
    return saveData;
  };

  @action
  saveGroup = async (callback: Function) => {
    const result = await this.transformRealOption();
    if (result !== false) {
      await this.updateViewDesignerByKey(ConfigItem.Group, result);
      await this.previewBackList(true);
      callback(result);
    }
  };

  /**
   * 请求数据仓库
   * */
  @action
  getFields = async (dataset?: DataSetItem) => {
    if (!dataset) {
      return;
    }
    const { dsUtils } = await ebdcoms.get();
    const fieldData = (await dsUtils.getFields(dataset, '', false, false)) || [{ fields: [] }];
    runInAction(() => {
      if (fieldData.length > 0) {
        // 存一次当前数据源字段列表
        this.datasetMap = fieldData[0].fields.map((i: any) => ({ ...i, id: i.id, content: i.text }));
      } else {
        this.datasetMap = [];
      }
    });
  };

  @action
  previewBackList = async (rerender: boolean = false) => {
    if (!this.boardId || !this.idParams.appId) return;
    const res = await ajax({
      url: `/api/bs/ebuilder/form/kanban/preview?apid=${this.idParams.appId}`,
      method: 'GET',
      params: {
        apid: this.idParams.appId,
        kanbanId: this.boardId,
      },
      ebBusinessId: this.idParams.pageId,
    });
    runInAction(() => {
      this.fakeBoardData = res.map((item: any) => ({
        id: item.optionId,
        name: item.name,
        cardDraggable: false,
        cards: item.fields,
        color: item?.color || '',
        otherConfig: item?.otherConfig || {},
      }));
      if (rerender) {
        this.boardRerenderKey = `${Date.now()}`;
      }
    });
  };

  @action
  addCustomGroup = (type: string) => () => {
    const _tempGroup: CustomGroupItem = {
      type: '1',
      id: `temp_${Date.now()}`,
      name: '',
      color: KanBanDefaultBgColor,
    };
    runInAction(() => {
      switch (type) {
        case 'optionCustom':
          this.optionCustom = this.optionCustom.concat(_tempGroup);
          break;
        case 'filterOptions':
          this.filterOptions = this.filterOptions.concat(_tempGroup);
          break;
        default:
          break;
      }
    });
  };

  @action
  changeCustomOption = (type: string) => (data: any[]) => {
    runInAction(() => {
      switch (type) {
        case 'optionCustom':
          this.optionCustom = data;
          break;
        case 'filterOptions':
          this.filterOptions = data;
          break;
        default:
          break;
      }
    });
  };

  /** *************权限*********************************** */

  @action
  companyAndSeclevel = async () => {
    try {
      const res = await ajax({
        url: `/api/bs/ebuilder/form/obj/companyAndSeclevel?apid=${this.idParams.appId}`,
        ebBusinessId: this.idParams.appId,
        error: (info: any) => {},
      });
      runInAction(() => {
        this.showCompany = res.supportCompany || false;
        this.showSeclevel = res.supportSeclevel || false;
      });
    } catch {
      runInAction(() => {
        this.showCompany = false;
        this.showSeclevel = false;
      });
    }
  };

  @computed
  get getAuthOptions() {
    const options: TypesBrowserOption[] = [...commonBrowserTypes];
    if (this.showCompany) {
      const company: TypesBrowserOption = {
        id: BrowserTypes.Company,
        content: getLabel('222259', '分部'),
      };
      options.splice(2, 0, company);
    }
    return options;
  }

  @action
  updetePermissions = (data: any[]) => {
    this.permissions = data;
  };

  @action
  changeBoardColorStyle = () => {
    const { options = [] } = this.data;
    const custom = options.find((opt: GroupDataOptionItem) => `${opt.groupType}` === `${this.groupType}`);
    this.boardColorStyle = custom?.enableColor ? ColorStyleType.HasStyle : ColorStyleType.NoStyle;
  };

  @action
  setBoardColorStyle = (style: any) => {
    runInAction(() => {
      this.boardColorStyle = style;
    });
  };
  @action('获取看板自定义按钮点击事件集合') getMenuActions = async (pageId: string, compId: string) => {
    const res = await ajax({
      url: `/api/${this.comServicePath}/button/openButtonList`,
      method: 'POST',
      ebBusinessId: pageId,
      data: format2FormData({
        pageId: pageId,
        compId: compId,
      }),
    });
    const _res = res.map((i: any) => {
      return {
        ...i,
        // buttonType: 'CUSTOM',
        // name: i.name.nameAlias ? i.name.nameAlias : i.name,
      };
    });
    return isValueEmpty(_res) ? [] : _res;
  };

  /** *************分组统计*********************************** */

  @action
  setStatistics = (key: string, value: any) => {
    this.statistics[key] = value;
  };
  @action
  getStatistics = async () => {
    const { viewId, appId } = this.idParams;
    const res = await this.getConfigByKey(ConfigItem.Statistics, viewId, appId);
    if (Array.isArray(res) && res[0]) {
      runInAction(() => {
        this.statistics = res[0];
      });
    }
  };
  @action('修改按统一分组信息')
  changeFieldGroup = (value?: DescItemProps | any) => {
    switch (this.groupType) {
      case GroupType.custom:
        // 自定义分组
        const index = this.optionCustom.findIndex(i => `${i.id}` === `${value?.id}`);
        if (index > -1) {
          this.optionCustom[index] = { ...this.optionCustom[index], ...value };
        }
        break;
      case GroupType.field:
      case GroupType.dateRange:
      case GroupType.dataset:
        // 按字段、日期、数据源分组
        const _data = this.buildUpData();
        const _index = (_data?.options || []).findIndex((f: any) => +f.groupType === +this.groupType);
        if (_index > -1) {
          if (!isEmpty(value)) {
            const { id, ...restValues } = value;
            _data.options[_index] = { ..._data.options[_index], ...restValues };
          } else {
            // * 重置
            const fieldsToExclude = ['showField', 'defaultGroup', 'filter', 'orderField', 'descField', 'descText', 'fieldType', 'fieldId'];
            if (`${this.groupType}` === GroupType.dateRange) {
              fieldsToExclude.push('dateRange');
            }
            _data.options[_index] = Object.fromEntries(Object.entries(_data.options[_index]).filter(([key]) => !fieldsToExclude.includes(key)));
          }
          this.data = _data;
        }
        break;
      case GroupType.filter:
        const filterIndex = this.filterOptions.findIndex(i => `${i.id}` === `${value?.id}`);
        if (filterIndex > -1) {
          this.filterOptions[filterIndex] = value;
        }
        break;
      default:
        break;
    }
  };
  @action('修改otherInfo')
  setOtherData = (key: string, val: any) => {
    this.otherData[key] = val;
  };
}

export default new FuncGroupsStore();
