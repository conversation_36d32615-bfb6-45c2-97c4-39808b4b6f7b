/**
 * <AUTHOR>
 * @createTime 2022 -02 -10
 */
import { DataSet } from '@weapp/ebdcoms';
import React from 'react';
import { GroupsTypeType } from './constants';
import { RelationGroupsType } from './diff/constants';
import { IdParams } from '../types';
import { BoardConfigData, groupStatisticsValue } from '../../types';
import { GroupType } from '../../../../constants/common';
import { DataSetType, DataSetTypeItem } from '../../types';


export interface ComBaseProps extends React.Attributes {
  idParams: IdParams;
}

export interface CustomGroupItem {
  id: string;
  name: string;
  type: '0' | '1'; // 0 未分组 1 自定义/系统字段
  color?: string;
}

interface GroupDataItemBase extends displaySealingProps, permissionsProps, GroupOtherDataProps {
  showPermiAdd?: boolean;
}

type GroupDataOptionItemOmitFields = 'currentGroupField' | 'otherGroupField' | 'groupType' | 'values' | 'optionValue' | 'rootDataId' | 'enableColor' | 'dateRange' | 'showField' | 'dataSoueceConfig';

export type GroupDataOptionItemField = Omit<GroupDataOptionItem, GroupDataOptionItemOmitFields> & GroupDataItemBase;
export interface GroupDataOptionItem {
  groupType?: GroupDataActiveProps;
  fieldId?: string;
  values?: CustomGroupItem[];
  optionValue?: string;
  rootDataId?: string;
  enableColor?: boolean;
  descText?: string;
  descField?: any;
  fieldType?: string // 当前字段类型
  dateRange?: string // 日期分组-日期范围
  showField?: string // 显示字段
  defaultGroup?: any[] // 默认分组
  filter?: any // 分组过滤
  orderField?: any[] // 分组排序
  dataSoueceConfig?: any // 数据源分组配置
  // 下面是前端生成 不存表
  currentGroupField?: GroupDataOptionItemField // 当前表分组字段
  otherGroupField?: GroupDataOptionItemField // 其他表分组字段
}

export type GroupOtherData = {
  hideWfz?: boolean; // 隐藏未分组
  customWfz?: any; // 未分组重命名
  hideNoDataFz?: boolean; // 隐藏无数据分组
}
 // 分组配置-其他配置
export interface GroupOtherDataProps {
  otherData?: GroupOtherData;
}
// 是否显示封
export interface displaySealingProps {
  displaySealing?: boolean;
}
// 权限
export interface permissionsProps {
  permissions?: any[];
}
export type GroupDataActiveProps = GroupType | RelationGroupsType;
export interface GroupDataProps extends displaySealingProps, permissionsProps, GroupOtherDataProps {
  active: GroupDataActiveProps;
  options: GroupDataOptionItem[];
  dataOptions?: GroupDataOptionItem[];
  dataGroup?: GroupsTypeType; // 分组类型：0-统一分组；1-按数据差异化分组
  rootField?: string; // 【新增字段】关联字段
  groupField?: string; // 【新增字段】关联字段那张表里的下拉字段
}

export interface BoardGroupProps extends ComBaseProps {
  visible: boolean;
  onClose: () => void;
  onOk: (datas: any) => void;
  onConfigChange?: (datas: any) => void;
  value?: GroupDataProps;
  comServicePath?: string;
  dataset?: DataSet;
  config?: BoardConfigData;
  fields?: any[]
  diffGroupCustomId?: string; // 差异化分组自定义分组id
  isFromEbAdvance?: boolean // 是否是表单高级视图下
}
export interface BoardGroupNewProps extends ComBaseProps {
  visible: boolean;
  onClose: () => void;
  onOk: (datas: any) => void;
  onConfigChange?: (datas: any) => void;
  value?: GroupDataProps;
  comServicePath?: string;
  dataset?: DataSet;
  config?: BoardConfigData;
  fields?: any[]
  diffGroupCustomId?: string; // 差异化分组自定义分组id
  isFromEbAdvance?: boolean // 是否是表单高级视图下
}
export interface GroupButtonBaseProps extends React.Attributes {
  idParams: IdParams;
  buttonType: BtnType;
  title?: string;
}

export interface FuncGroupsProps extends React.Attributes {
  idParams: IdParams;
  buttonType: BtnType;
  boardId: string;
  refreshFn?: () => void;
  comServicePath?: string;
}

export interface BoardGroupButtonProps extends GroupButtonBaseProps {
  hasIcon?: boolean;
  btnClassName?: string;
  optionField?: any[];
  dataset?: DataSet;
}

export enum ConfigItem {
  Group = 'group',
  Filter = 'filter',
  Condition = 'condition',
  Orders = 'orders',
  Card = 'card',
  ComButton = 'comButton',
  Statistics = 'statistics',
}

export type BtnType = 'primary' | 'default' | 'success' | 'warning' | 'danger' | 'link';

export interface ItemDomProps extends React.Attributes {
  onItemChange?: any;
  rowData?: any;
  rowIndex?: number;
  onChange?: any;
  appId: string;
  colorStyle?: string;
  setFieldDesc?: (rowData: any) => void;
  setLineFilters?: (rowData: any[]) => void; // 设置筛选信息
  isFromEbAdvance?: boolean // 是否是表单高级视图下
  isFilterGroupWay?: boolean // 是否是按条件分组
}

export interface statisticsProps {
  openValue: boolean;
  statisticsItem: groupStatisticsValue[];
}
export interface BoardGroupStatisticsProps extends React.Attributes {
  idParams: IdParams;
  buttonType: BtnType;
  comServicePath?: string;
  dataset: DataSet;
  statistics: statisticsProps;
  btnClassName?: string;
  title?: string;
  hasIcon?: boolean;
}

export interface BoardGroupStatisticsMainProps extends React.Attributes {
  idParams: IdParams;
  visible: boolean;
  onClose: () => void;
  onOk: () => void;
  dataset: DataSet;
  statistics: statisticsProps;
}

export interface StatisticsSettingProps extends React.Attributes {
  settingVisible: boolean;
  onClose: () => void;
  idParams: IdParams;
  dataset: DataSet;
  statistics: statisticsProps;
  isAdd: boolean;
  statisticsItemDetail?: any;
  store?: any;
}
export interface DescItemProps {
  descText: string;
  descField: any;
  id: string;
}
export interface DescInfoProps {
  groupType: GroupType;
  info: DescItemProps[] | DescItemProps
}

export interface selectedFieldProps {
  id?: string;
  type?: string;
}

export interface companyAndSeclevelProps {
  supportCompany: boolean;
  supportSeclevel: boolean;
}