/**
 * <AUTHOR>
 * @createTime 2022 -02 -11
 */

import { TypesBrowserOption } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { KanBanDefaultBgColor } from '../../../../constants';
import { GroupType } from '../../../../constants/common';
import { RelationGroupsType } from './diff/constants';

export const groups: any = () => [
  {
    id: GroupType.field,
    title: getLabel('-1', '按字段分组'),
    content: getLabel('-1', '按字段分组'),
  },
  {
    id: GroupType.dateRange,
    title: getLabel('277035', '按日期分组'),
    content: getLabel('277035', '按日期分组'),
  },
  {
    id: GroupType.filter,
    title: getLabel('270016', '按条件分组'),
    content: getLabel('270016', '按条件分组'),
  },
  {
    id: GroupType.custom,
    title: getLabel('-1', '按固定名称分组'),
    content: getLabel('-1', '按固定名称分组'),
  },
  {
    id: GroupType.diff,
    title: getLabel('-1', '按差异化分组'),
    content: getLabel('-1', '按差异化分组'),
  },
  // {
  //   id: GroupType.dataset,
  //   title: getLabel('291687', '按数据源分组'),
  //   content: getLabel('291687', '按数据源分组'),
  // },
];

export const groupDialogMenus = () => [
  { id: '1', content: getLabel('54220', '分组') },
  { id: '2', content: getLabel('56148', '权限') },
  { id: '3', content: getLabel('55756', '其他') },
];


/** ******************权限相关****************** */
/**
 * typeBrowser默认参数
 */
export enum BrowserTypes {
  FieldCustom = 'fieldCustom',
  InheritUpdate = 'inheritUpdate',
  Employee = 'user',
  Department = 'dept',
  Company = 'company',
  All = 'all',
  Group = 'group',
  Job = 'post',
  PartTimeJob = 'partTime', // 兼职岗位
  Grade = 'grade', // 职级
  External = 'external', // 外部人员
  ExternalAll = 'externalAll', // 所有外部人员
  Role = 'role', // 角色
  AssociateModuleField = 'associateModuleField', // 关联模块人员字段
}

export const commonBrowserTypes: TypesBrowserOption[] = [
  { id: BrowserTypes.Employee, content: getLabel('52090', '人员') },
  { id: BrowserTypes.Department, content: getLabel('222258', '部门') },
  { id: BrowserTypes.Group, content: getLabel('52094', '群组') },
  { id: BrowserTypes.All, content: getLabel('52092', '所有人') },
  { id: BrowserTypes.Job, content: getLabel('52093', '岗位') },
  { id: BrowserTypes.PartTimeJob, content: getLabel('100040', '兼职岗位') },
  { id: BrowserTypes.Grade, content: getLabel('84092', '职级') },
  { id: BrowserTypes.AssociateModuleField, content: getLabel('221910', '关联模块人员字段') },
  // { id: BrowserTypes.External, content: getLabel('100041', '外部人员') },
  // { id: BrowserTypes.ExternalAll, content: getLabel('100042', '所有外部人员') },
  { id: BrowserTypes.Role, content: getLabel('52091', '角色') },
];

/**
 * 初始化默认分组
 */
export const DfRelationGroupData = () => [
  {
    type: '1',
    id: `temp_${Date.now()}`,
    name: getLabel('117530', '未开始'),
    color: KanBanDefaultBgColor,
  },
  {
    type: '1',
    id: `temp_${Date.now() + 1}`,
    name: getLabel('54753', '进行中'),
    color: KanBanDefaultBgColor,
  },
  {
    type: '1',
    id: `temp_${Date.now() + 2}`,
    name: getLabel('54752', '已完成'),
    color: KanBanDefaultBgColor,
  },
];

export enum ColorStyleType {
  HasStyle = 'hasStyle',
  NoStyle = 'noStyle',
}

export const getColorStyleOption = [
  {
    id: ColorStyleType.NoStyle,
    iconSrc: 'Icon-bd-Dropdown1',
  },
  {
    id: ColorStyleType.HasStyle,
    iconSrc: 'Icon-bd-Dropdown2',
  },
];
export const INITIAL_GROUP_DATA = () => ({
  active: GroupType.custom,
  options: [],
  dataOptions: [],
  dataGroup: GroupsTypeType.normalCustom,
  rootField: '',
  groupField: '',
  permissions: [],
  displaySealing: true,
  otherData: {},
});

export const INITIAL_SELECTED_FIELD_DATA = () => ({
  id: '',
  type: '',
});
// 默认的自定义分组
export const INITIAL_CUSTOM_GROUP_INFO = (): any => ({
  groupType: GroupType.custom,
  enableColor: false,
  values: [],
});
// 默认的按条件
export const INITIAL_FILTER_GROUP_INFO = (): any => ({
  groupType: GroupType.filter,
  enableColor: false,
  values: [],
});
// 默认的日期分组信息
export const INITIAL_DATE_GROUP_INFO = (): any => ({
  groupType: GroupType.dateRange,
  fieldId: '',
  dateRange: '',
});
// 默认的差异化分组信息
export const INITIAL_DIFF_GROUP_INFO = (): any => ({
  active: RelationGroupsType.Normal,
  groupType: GroupType.diff,
  groupField: '',
  rootField: '',
  dataGroup: 1,
});
// 默认的按字段分组信息
export const INITIAL_Field_GROUP_INFO = (): any => ({
  groupType: GroupType.field,
  fieldId: '',
  fieldType: '',
});
// 默认的按数据源分组信息
export const INITIAL_DATASET_GROUP_INFO = (): any => ({
  groupType: GroupType.dataset,
  fieldId: '',
  fieldType: '',
  dataSoueceConfig: {
    dataSet: {}
  }
});
export const INITIAL_ID_PARAMS = () => {
  return {
    appId: '',
    objId: '',
    pageId: '',
    viewId: '',
    compId: '',
  }
}

// 看板分组类型
export enum GroupsTypeType {
  normalCustom = '0', // 统一分组---改版后可理解为非差异化分组
  releationField = '1', // 按关联数据差异化分组
}

export default {};
