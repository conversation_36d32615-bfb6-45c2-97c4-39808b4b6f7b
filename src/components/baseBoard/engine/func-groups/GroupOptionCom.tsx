import { But<PERSON>, ColorPicker, CorsComponent, Icon } from '@weapp/ui';
import { classnames, getLabel, isEmpty } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import React, { Attributes, PureComponent, useEffect, useState } from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix, KanBanDefaultBgColor } from '../../../../constants';
import { deepToJS } from '../../../../utils';
import { getIsWhiteColors } from '../../../common/board/utils';
import LocaleInput from '../../../common/locale/LocaleInput';
import { LocaleExValueDataType } from '../../../common/locale/types';
import { ColorStyleType, getColorStyleOption } from './constants';
import { ItemDomProps } from './types';


interface GroupOptionComProps extends Attributes {
  appId: string;
  boardColorStyle?: string;
  addCustomGroup: () => void;
  customShowOption: any[];
  changeCustomOption: (data: any[]) => void;
  changeBoardColorStyle?: (style: any) => void;
  setFieldDesc?: (rowData: any[]) => void; // 设置描述信息
  setLineFilters?: (rowData: any[]) => void; // 设置筛选信息
  isFromEbAdvance?: boolean; // 是否为表单高级视图
  isFilterGroupWay?: boolean; // 是否为条件分组
  disabledColor?: boolean; // 是否屏蔽颜色配置
}
export type FieldDataType = {
  id?: string;
  content?: string;
  fieledListMap?: any[];
};
const DragItem = (props: ItemDomProps) => {
  const {
    rowData: { name, color = KanBanDefaultBgColor, descField, filters },
    appId,
    colorStyle,
    setFieldDesc,
    setLineFilters,
    isFilterGroupWay,
  } = props;
  const [val, setVal] = useState<LocaleExValueDataType>('');
  const onChange = (v: LocaleExValueDataType) => {
    setVal(v);
  };
  const onBlur = (v?: LocaleExValueDataType) => {
    props.onChange({ ...props.rowData, name: v });
  };
  useEffect(() => {
    setVal(name);
  }, [name]);

  const onColorChange = (colorVal: any) => {
    const _color = `${colorVal}`.toLowerCase() === 'transparent' ? KanBanDefaultBgColor : `${colorVal}`.toLowerCase();
    props.onChange({ ...props.rowData, color: _color });
  };
  const showLineEnd = () => {
    setFieldDesc && setFieldDesc(toJS(props.rowData));
  };
  const showLineFilter = () => setLineFilters && setLineFilters(toJS(props.rowData));
  // 判断是否要显示描述
  const showDesc = () => {
    // 高级视图下看板不显示 获取配置api/bs/ebuilder/form/viewdesigner/getConfigByKey没返回保存的配置信息
    // 差异化分组不显示
    if (props.isFromEbAdvance || props.isFilterGroupWay || !setFieldDesc) {
      return false;
    }
    return true;
  };

  return (
    <>
      <LocaleInput
        weId={`${props.weId || ''}_skckgf`}
        placeholder={getLabel('105965', '输入自定义分组名称')}
        value={val}
        onBlur={onBlur}
        onSave={onBlur}
        onChange={onChange}
        ebBusinessId={appId}
      />
      {val && (
        <>
          {showDesc() && (
            <div className={`${ebdBClsPrefix}-custom-desc-set`} title={getLabel('253192', '分组描述')}>
              <Icon weId={`${props.weId || ''}_r8yyum`} name={(descField?.id || descField?.content) ? 'Icon-set-up01' : 'Icon-set-up-o'} onClick={showLineEnd} />
            </div>
          )}
          {isFilterGroupWay && (
            <div className={`${ebdBClsPrefix}-custom-desc-set`} title={getLabel('288151', '分组条件')}>
              <Icon weId={`${props.weId || ''}_8uxon3`} name={isEmpty(filters) ? 'Icon-set-up-o' : 'Icon-set-up01'} onClick={showLineFilter} />
            </div>
          )}
        </>
      )}
      <When weId={`${props.weId || ''}_yqltbb`} condition={colorStyle === ColorStyleType.HasStyle}>
        <div className={`${ebdBClsPrefix}-custom-colorpicker`}>
          <ColorPicker weId={`${props.weId || ''}_7d5qm4`} onChange={onColorChange} value={color} enableAlpha={false}>
            <div
              className={`${ebdBClsPrefix}-custom-colorpicker-button`}
              style={{
                background: color,
                borderColor: `${getIsWhiteColors(color) ? 'var(--border-color)' : color}`,
              }}
            />
          </ColorPicker>
        </div>
      </When>
    </>
  );
};

@observer
export default class GroupOptionCom extends PureComponent<GroupOptionComProps> {
  getColorStyleContainer = () => {
    const onColorStyleChange = (state: any) => (e: any) => {
      e.stopPropagation();
      this.props.changeBoardColorStyle?.(state);
    };
    return getColorStyleOption.map((item: any, index: any) => (
      <div
        className={classnames(`${ebdBClsPrefix}-colorpicker-selector-box`, {
          [`${ebdBClsPrefix}-colorpicker-selector-box-active`]: item.id === this.props.boardColorStyle,
        })}
        key={String(index + item)}
      >
        <span className={`${ebdBClsPrefix}-colorpicker-selector-icon`} onClick={onColorStyleChange(item.id)}>
          <Icon weId={`${this.props.weId || ''}_al60kb`} name={item.iconSrc} size="lg" />
        </span>
      </div>
    ));
  };
  handleChange = (data: any, type: string) => {
    const { customShowOption, changeCustomOption } = this.props;
    // if (type === 'delete') {
    //   const deleteItems = customShowOption.filter(i => data.find((k: any) => k.id !== i.id))
    //   if (deleteItems.length && deleteItems.some(i => `${i.type}` === '0')) {
    //     message({ type: 'info', content: getLabel('0', '未分组不允许删除') });
    //     return;
    //   }
    // }
    changeCustomOption?.(data)
  }

  render(): React.ReactNode {
    const { addCustomGroup, appId, customShowOption, changeCustomOption, setFieldDesc, isFromEbAdvance, isFilterGroupWay, setLineFilters, disabledColor } =
      this.props;
    const _customShowOption = deepToJS(customShowOption);
    return (
      <>
        <div className={`${ebdBClsPrefix}-view-group-main-header`}>
          <Button
            weId={`${this.props.weId || ''}_8tiroq`}
            type="link"
            className={`${ebdBClsPrefix}-view-group-button`}
            onClick={addCustomGroup}
          >
            +{getLabel('54214', '添加分组')}
          </Button>
          {!isFilterGroupWay && !disabledColor && this.getColorStyleContainer()}
        </div>
        <div className={`${ebdBClsPrefix}-view-group-main-card`}>
          <CorsComponent
            weId={`${this.props.weId || ''}_32eq30`}
            app="@weapp/ebdform"
            compName="DragList"
            sortableType="icon"
            data={_customShowOption}
            // onChange={changeCustomOption}
            onChange={this.handleChange}
            needDelete
            itemDom={
              <DragItem
                weId={`${this.props.weId || ''}_p4i8c2`}
                appId={appId}
                colorStyle={this.props.boardColorStyle}
                setFieldDesc={setFieldDesc}
                setLineFilters={setLineFilters}
                isFromEbAdvance={isFromEbAdvance}
                isFilterGroupWay={isFilterGroupWay}
              />
            }
          />
        </div>
      </>
    );
  }
}
