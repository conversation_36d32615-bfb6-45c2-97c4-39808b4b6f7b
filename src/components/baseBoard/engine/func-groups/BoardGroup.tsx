import { <PERSON><PERSON>, Cors<PERSON>omponent, Dialog, Icon, Menu, TypesBrowserOption } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import { ebdBClsPrefix } from '../../../../constants';
import { GroupType } from '../../../../constants/common';
import { EtComponentKey } from '../../../../constants/EtComponent';
import { groupDialogMenus } from './constants';
import GroupSetCom from './GroupSetCom';
import store from './store';
import { BoardGroupButtonProps, BoardGroupProps } from './types';
import { checkRequired } from './utils';
import BoardGroupOther from './BoardGroupOther';
import { isLogicData, isEteamsData } from '../../../common/DataSet/utils';
import './index.less';

const { message } = Dialog;
export const BoardGroup: React.ComponentType<BoardGroupProps> = observer(props => {
  const { visible, onClose, onOk, idParams, value, comServicePath, dataset, onConfigChange, config, fields, isFromEbAdvance, diffGroupCustomId, hideGroupWay } = props;
  const { appId } = idParams;
  const [menuValue, setMenuValue] = useState('1');
  const sourceType = dataset?.type || '';
  useEffect(() => {
    // !存在表单后台看板高级视图下无appid场景 需后端排查无appid原因 看是否能去掉限制
    if (visible) {
      // mark 表单高级视图下不清理 不然会造成列表消失
      if (!isFromEbAdvance) {
        store.clearGarbage();
      }
      store.initGroup(!!isFromEbAdvance, idParams, value, comServicePath, dataset, diffGroupCustomId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, comServicePath, dataset]);

  const handleOk = async () => {
    const { groupType, getCurrentGroupValue } = store;
    const errInfo = checkRequired(groupType, toJS(getCurrentGroupValue()));
    if (errInfo) {
      message({ type: 'info', content: errInfo });
      return;
    }
    // * 先更新config 再同步onChange 才能触发页面同步更新
    if (onConfigChange) {
      let json: any = {
        otherData: toJS(store.otherData),
      };
      if (store.diffStore.isCustomRelationGroupsType) {
        json.diffGroupCustomId = store.diffStore.releationCustomSelected;
      }
      onConfigChange(json);
    }
    if (isFromEbAdvance) {
      await store.saveGroup(onOk);
    } else {
      const data = await store.transformRealOption();
      if (data) {
        onOk(data);
      }
    }
  };

  const handleClose = () => {
    store.initGroup(!!isFromEbAdvance, idParams, value, comServicePath);
    onClose();
  };

  const getAuthColumns = () => [
    {
      dataIndex: 'selectedType',
      title: getLabel('53872', '类型'),
      width: '20%',
      bodyRender: (data: any) => {
        const item = toJS(store.getAuthOptions).find((d: TypesBrowserOption) => d.id === data.selectedType);
        return <span title={item?.content || ''}>{item?.content}</span>;
      },
    },
    {
      dataIndex: 'object',
      title: getLabel('54042', '对象'),
      width: '50%',
      bodyRender: (data: any) => {
        const { getAimContent } = window.weappEbdform.ebdformUtils?.authTableUtils;
        const { newName } = getAimContent?.(data);
        return (
          <span>
            <span className="hrm_span" dangerouslySetInnerHTML={{ __html: newName }} />
          </span>
        );
      },
    },
    {
      dataIndex: 'levelScope',
      title: getLabel('52100', '安全级别'),
      width: '30%',
      bodyRender: (data: any) => {
        const { getLevelSpan } = window.weappEbdform.ebdformUtils?.authTableUtils;
        const level = getLevelSpan?.(data.levelScope || {});
        return <span>{` ${level ? `${level}` : ''}`}</span>;
      },
    },
  ];
  const showDataPerm = () => {
    //业务数据源及数仓屏蔽权限分组
    if (isLogicData(config?.dataset) || isEteamsData(config?.dataset)) {
      return false;
    }
    //日期分组不允许设置
    if (`${store.groupType}` === GroupType.dateRange || `${store.groupType}` === GroupType.filter) {
      return false;
    }
    // 按人员分组不允许设置
    if (`${store.groupType}` === GroupType.field && store.selectedField.type === EtComponentKey.Employee) {
      return false;
    }
    return true;
  };
  return (
    <Dialog
      weId={`${props.weId || ''}_vuynwj`}
      visible={visible}
      title={getLabel('54215', '分组设置')}
      closable
      icon="Icon-e-builder-o"
      destroyOnClose
      onClose={handleClose}
      width={700}
      wrapClassName={`${ebdBClsPrefix}-board-group`}
      // height={650}
      footer={[
        <Button weId={`${props.weId || ''}_werfs45@ok`} key="ok" type="primary" onClick={handleOk} disabled={store.loading}>
          {getLabel('221901', '确定')}
        </Button>,
        <Button weId={`${props.weId || ''}_k0df4t@cancel`} key="cancel" onClick={handleClose}>
          {getLabel('53937', '取消')}
        </Button>,
      ]}
      menus={showDataPerm() ? groupDialogMenus() : groupDialogMenus().filter(i => i.id !== '2')}
      menuValue={menuValue}
      onMenuChange={setMenuValue}
    >
      <Menu.MenuContent weId={`${props.weId || ''}_emg4v2`} bindKey={`${ebdBClsPrefix}-board-group-set`} key="1" dataId="1" value={menuValue}>
        <GroupSetCom
          weId={`${props.weId || ''}_8gppe7`}
          appId={appId}
          optionField={store.optionField}
          config={config}
          fields={fields}
          value={value}
          sourceType={sourceType}
          isFromEbAdvance={isFromEbAdvance}
          hideGroupWay={hideGroupWay}
        />
      </Menu.MenuContent>
      <Menu.MenuContent weId={`${props.weId || ''}_ca2ih6`} bindKey={`${ebdBClsPrefix}-board-group-set`} key="2" dataId="2" value={menuValue}>
        <CorsComponent
          weId={`${props.weId || ''}_vv5dgx`}
          app="@weapp/ebdform"
          compName="AuthTable"
          title={getLabel('186558', '新建分组权限')}
          data={toJS(store.permissions)}
          onChange={store.updetePermissions}
          getOriginColumns={getAuthColumns}
          showSeclevel={store.showSeclevel}
          showCompany={store.showCompany}
          authAddProps={{
            needOrgVirtualType: true,
            options: toJS(store.getAuthOptions),
            associateModuleFieldObjId: store.diffStore.rootFieldObjId,
          }}
          tableProps={{
            scroll: { y: 420 },
          }}
        />
      </Menu.MenuContent>
      <Menu.MenuContent weId={`${props.weId || ''}_yu5vmw`} key="3" dataId="3" value={menuValue}>
        <BoardGroupOther weId={`${props.weId || ''}_jytawc`} otherData={store.otherData} onChange={store.setOtherData} pageId={props?.idParams?.pageId} groupType={store.groupType} />
      </Menu.MenuContent>
    </Dialog>
  );
});
// 表单高级视图下
export const BoardGroupButton: React.ComponentType<BoardGroupButtonProps> = observer(props => {
  const [show, setShow] = useState<boolean>(false);
  const { buttonType = 'default', title, hasIcon = true, btnClassName, idParams, dataset } = props;

  const triggerShow = () => {
    setShow(!show);
  };

  return (
    <>
      <Button weId={`${props.weId || ''}_cixdx9`} type={buttonType} className={btnClassName} onClick={triggerShow}>
        {hasIcon && <Icon weId={`${props.weId || ''}_j0w8m2`} name="Icon-folder" />}
        <span>{title || getLabel('54215', '分组设置')}</span>
      </Button>
      <BoardGroup
        weId={`${props.weId || ''}_ujoln6`}
        idParams={idParams}
        visible={show}
        onClose={triggerShow}
        onOk={triggerShow}
        // mark 新增表单高级视图看板标识
        isFromEbAdvance
        dataset={dataset}
        config={{dataset}}
      />
    </>
  );
});
export default BoardGroupButton;
