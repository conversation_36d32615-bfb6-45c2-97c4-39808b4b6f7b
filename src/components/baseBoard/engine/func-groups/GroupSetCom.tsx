import { Help, Layout, ListData, Radio, Select, Dialog, Button, CorsComponent, Spin, Switch } from '@weapp/ui';
import { classnames, getLabel, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import { Else, If, Then, When } from 'react-if';
import { GroupType } from '../../../../constants/common';
import { SelectType, EtComponentKey } from '../../../../constants/EtComponent';
import { groups } from './constants';
import { ebdBClsPrefix, isCn } from '../../../../constants';
import GroupOptionCom from './GroupOptionCom';
import store from './store';
import { isEbFormDataV2, isLogicData, isEteamsData } from '../../../common/DataSet/utils';
import { BoardConfigData } from '../../types';
import { GroupDataProps } from './types';
import { getGroupDescByType } from './utils';
import { EbrAndDataSetFieldComps, ExtraDescField, ExtraSealField, DateRangeFieldComps } from './ExtraFieldComps';
import { GroupRowItem, TitleText } from './Common';
import DiffGroup from './diff';

const { Row, Col } = Layout;

export interface GroupSetComProps extends Attributes {
  appId: string;
  optionField: any[];
  sourceType: string;
  config?: BoardConfigData;
  fields?: any[];
  value?: GroupDataProps;
  isFromEbAdvance?: boolean; // 是否为表单高级视图下
  hideGroupWay?: boolean;
}
const diffKey = 'GroupSetCom';
@observer
export default class GroupSetCom extends PureComponent<GroupSetComProps> {
  state = {
    lineEndVisible: false,
    lineEndRowData: {} as any,
    lineFilterData: {} as any,
  };
  customRenderItem = (item: ListData) => {
    return (
      <div key={item.id} title={item.content} className={classnames('list-item')}>
        <span style={{ marginLeft: '10px' }}>{item.content || item.name}</span>
      </div>
    );
  };

  getGroups = () => {
    const { config, isFromEbAdvance } = this.props;
    let _groups = groups();
    // 非表单数据源不显示自定义分组
    // 我们自定义分组后 我们看板的数据有拖动排序的功能  我们这边实现的话就是用临时的一张eb的中间表去实现的 数据加工没有
    // if (!isEbFormDataV2(config)) {
    //   _groups = _groups.filter((i: any) => i.id !== GroupType.custom);
    // }
    // 表单高级视图下、业务数据源和数仓不显示差异化分组
    if (isLogicData(config?.dataset) || isEteamsData(config?.dataset) || isFromEbAdvance) {
      _groups = _groups.filter((i: any) => i.id !== GroupType.diff);
    }
    return _groups;
  };
  getOptionField = () => {
    const { config, optionField = [], isFromEbAdvance } = this.props;
    //业务数据源屏蔽关联ebuilder分组
    if (isLogicData(config?.dataset)) {
      return optionField.filter(i => i.type !== EtComponentKey.Ebuilder);
    }
    if (isEbFormDataV2(config)) {
      // 表单高级视图下暂时不放开关联ebuilder
      // if (isFromEbAdvance || config?.fromEbuilder) {
      //   return optionField.filter(i => i.type !== EtComponentKey.Ebuilder)
      // }
      return optionField;
    }
    // 非eb数据源不显示自定义分组 默认按字段分组
    return [{ id: '-1', content: getLabel('40502', '请选择') }, ...optionField];
  };
  setLineFilters = (rowData?: any) => {
    this.setState({ lineFilterData: rowData });
  };
  showSetDesc = (rowData?: any) => {
    const { config } = this.props;
    this.setState({ lineEndRowData: {} }, () => {
      let lineEndRowData = rowData;
      if (+store.groupType === +GroupType.field) {
        // 按字段分组
        // 当前选中字段需要一致
        const groupData = store.data.options.find((i: any) => +i.groupType === +store.groupType && i.fieldId === store.selectedField);
        if (groupData) {
          lineEndRowData = {
            descField: groupData?.descField,
            descText: groupData?.descText,
          };
        }
      }
      // 开启统计按钮默认为true 兼容历史数据
      if (lineEndRowData.descField && !('useStats' in lineEndRowData.descField) && isLogicData(config?.dataset)) {
        lineEndRowData.descField.useStats = true;
      }
      this.setState({ lineEndVisible: true, lineEndRowData });
    });
  };
  onLineEndChange = (key: any, content: any, value: any) => {
    const { lineEndRowData } = this.state;
    if (key === 'desc') {
      const { fields, config } = this.props;
      if (value.id !== '-1') {
        const fieldItem = fields?.find(i => i.id === value.id);
        value = {
          ...value,
          fieledListMap: fieldItem ? [fieldItem] : [],
        };
      }
      if (!('useStats' in value) && isLogicData(config?.dataset)) {
        value.useStats = true;
      }
      this.setState({ lineEndRowData: { ...lineEndRowData, descField: value } });
    } else if (key === 'useStats') {
      this.setState({ lineEndRowData: { ...lineEndRowData, descField: { ...lineEndRowData.descField, useStats: value } } });
    }
  };
  onConfirmDesc = () => {
    const { lineEndRowData } = this.state;
    const { descField, id } = lineEndRowData;
    if (descField.id !== '-1') {
      const current = descField.fieledListMap.find((i: any) => i.id === descField.id) || {};
      const descText = isEmpty(current) ? '' : `${current.objId}.${current.id}`;
      store.changeFieldGroup({ id, descText, descField });
      this.onLineEndClose();
      return;
    }
    //使用时处理
    store.changeFieldGroup({ id, descText: '', descField });
    this.onLineEndClose();
  };
  onLineEndClose = () => {
    this.setState({ lineEndVisible: false });
  };
  renderDescSet = () => {
    const { lineEndVisible, lineEndRowData } = this.state;
    const { config } = this.props;
    if (!lineEndVisible) return;
    const buttons = [
      <Button weId={`${this.props.weId || ''}_785mpg@${diffKey}`} key="save" type="primary" onClick={this.onConfirmDesc}>
        {getLabel('221901', '确定')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_7nlxf7@${diffKey}`} key="onCancel" onClick={this.onLineEndClose}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const helpTip = (
      <div>
        <p>{getLabel('252900', '填写说明')}:</p>
        <p>{getLabel('256078', '页面存在数据源时，支持输入“#”，在弹出的字段列表中选择字段插入到内容中')}；</p>
        <p>{getLabel('256079', '如需在分组描述中显示数字字段/金额字段总值，需要先在卡片设置里显示该字段')}；</p>
      </div>
    );
    const customFieldHelpTip = {
      id: (
        <div>
          <div>1.{getLabel('256155', '仅支持选择数字和金额字段，选择后，会同步该分组下数字/金额总和')}</div>
          <div>2.{getLabel('256156', '显示形式：字段名称+金额（示例：商机金额：20,000元）')}</div>
        </div>
      ),
    };
    const descField = lineEndRowData.descField || {};
    let hasNumberField = false;
    if ((descField.content || descField.id !== '-1') && isLogicData(config?.dataset)) {
      // 过滤掉数据id
      hasNumberField = descField?.fieledListMap?.some((i: any) => (i.type === 'Number' || i.type === 'Money') && i.fieldId !== '5') || false;
    }
    return (
      <Dialog
        weId={`${this.props.weId || ''}_i1kb6v`}
        footer={buttons}
        width={550}
        destroyOnClose
        visible
        title={getLabel('253192', '分组描述')}
        mask
        closable
        onClose={this.onLineEndClose}
        icon={'Icon-e-builder-o'}
      >
        <CorsComponent
          weId={`${this.props.weId || ''}_ogxzpx`}
          app="@weapp/ebdgantt"
          compName="FieldSettingLayoutCom"
          config={config}
          fieldData={lineEndRowData.descField}
          onChange={(key: any, content: any, value: any) => this.onLineEndChange('desc', content, value)}
          // excludeSelectNumOption
          dataset={config?.dataset}
          // 数据源字段可选控制
          customShowFieldType={['Number']}
          // 自定义富文本help提示展示
          customTextHelpTip={helpTip}
          customFieldHelpTip={customFieldHelpTip}
        />
        {hasNumberField && (
          <div className="ui-formItem ui-formItem-module" style={{ display: 'flex', alignItems: 'center' }}>
            <Row weId={`${this.props.weId || ''}_7n4ue2`} style={{ width: '100%', alignItems: 'center' }}>
              <Col weId={`${this.props.weId || ''}_wqhkx1`} span={isCn ? 4 : 8}>
                <div className="ui-formItem-label ui-formItem-label-normal">
                  <div className="ui-formItem-label-span">
                    {getLabel('245989', '开启统计')}
                    <Help weId={`${this.props.weId || ''}_ymfm6g`} title={getLabel('277175', '开启统计后，当前选择的数字类型字段将被全部统计，并显示在分组描述中')} />
                  </div>
                </div>
              </Col>
              <Col weId={`${this.props.weId || ''}_n8ma20`} span={isCn ? 20 : 16}>
                <div className="ui-layout-col ui-layout-col-20 ui-formItem-wrapper-col">
                  <Switch weId={`${this.props.weId || ''}_tomgxs`} onChange={(checked: boolean) => this.onLineEndChange('useStats', '', checked)} value={descField.useStats} />
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Dialog>
    );
  };
  selectGroup = (value: any, options: any) => {
    this.onLineEndClose();
    store.selectGroup(options);
  };
  getSetDesc = (rowData?: any) => {
    const lineEndRowData = getGroupDescByType(store.groupType, store.data.options, store.selectedField.id!, rowData);
    return lineEndRowData;
  };
  // * 字段分组下的分组描述 和自定义分组不同
  renderFieldDesc = () => {
    const { selectedField, groupTypeOpt, groupDateRangeOpt, groupType } = store;
    let value = toJS(groupTypeOpt);
    let _selectedField = selectedField;
    if (isEmpty(value)) return;
    if (groupType === GroupType.dateRange) {
      value = toJS(groupDateRangeOpt);
      _selectedField = { id: value.fieldId } as any;
    }
    return <ExtraDescField weId={`${this.props.weId || ''}_5lk3vy`} selectedField={_selectedField} showSetDesc={() => this.showSetDesc(value)} value={this.getSetDesc(value)} />;
  };
  changeLineFilters = (type: string, value?: any) => {
    const { lineFilterData } = this.state;
    if (type === 'isOk') {
      store.changeFieldGroup({ ...lineFilterData, filters: value });
    }
    this.setState({ lineFilterData: {} });
  };
  renderFilterCmp = () => {
    const { lineFilterData } = this.state;
    const { config } = this.props;
    if (isEmpty(lineFilterData)) return null;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_7op01t`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        dataSet={config?.dataset}
        value={lineFilterData?.filters || {}}
        onOk={(value: any) => this.changeLineFilters('isOk', value)}
        onCancel={() => this.changeLineFilters('cancel')}
        visible
      />
    );
  };
  getGroupHelpInfo = () => {
    const { isEteams } = store;
    // *数据加工数据源排除按名称分组提示
    const helpInfos = [
      getLabel('278342', '按名称分组：按固定分组名称进行分组，在前端使用时可以新建分组'),
      getLabel('278343', '按字段分组：支持选择选项控件和关联e-builder控件，按选项值进行分组'),
      getLabel('278344', '按日期分组：根据系统日期和日期字段进行分组'),
      getLabel('288806', '按条件分组：根据设置的条件进行分组'),
      // getLabel('291688', '按数据源分组：根据设置的数据源以及关联其他数据源的字段进行分组'),
    ];
    return isEteams ? helpInfos.slice(1) : helpInfos;
  };
  render(): React.ReactNode {
    const { appId, config, isFromEbAdvance, hideGroupWay } = this.props;
    const {
      groupType = GroupType.custom,
      customShowOption,
      addCustomGroup,
      switchGroup,
      changeCustomOption,
      selectedField,
      boardColorStyle = 'noStyle',
      setBoardColorStyle,
      groupTypeOpt,
      displaySealing,
      setState,
      datasetMap,
      changeFieldGroup,
      loading,
      groupDateRangeOpt,
      filterShowOption,
      dstWayValue,
      diffStore,
    } = store;
    // 表单高级视图下接口实现不一样 暂时屏蔽
    const isEbForm = isEbFormDataV2(config);
    return (
      <div className={`${ebdBClsPrefix}-view-group`}>
        <Spin weId={`${this.props.weId || ''}_9asqbr`} spinning={loading}>
          {!hideGroupWay && (
            <header className={`${ebdBClsPrefix}-view-group-header`}>
              <Row weId={`${this.props.weId || ''}_ic3mr5`} className="view-group-header-row">
                <Col weId={`${this.props.weId || ''}_g5940j`} span={4} className="view-group-header-col-label">
                  <TitleText weId={`${this.props.weId || ''}_v2hg90`} title={getLabel('186579', '分组方式')} helpInfo={this.getGroupHelpInfo()} />
                </Col>
                <Col weId={`${this.props.weId || ''}_koi60v`} className="view-group-header-col-content">
                  <Select
                    weId={`${this.props.weId || ''}_kclou1`}
                    data={this.getGroups()}
                    // canReversedChoose={false}
                    value={groupType}
                    onChange={switchGroup}
                    // readOnly={!isEbForm}
                  />
                </Col>
              </Row>
            </header>
          )}
          <main className={classnames(`${ebdBClsPrefix}-view-group-main`)} style={{ marginTop: groupType === GroupType.custom || groupType === GroupType.filter ? '12px' : '0' }}>
            {/* 统一分组 */}
            <When weId={`${this.props.weId || ''}_bfnpjf`} condition={groupType === GroupType.custom}>
              <GroupOptionCom
                weId={`${this.props.weId || ''}_0lbil3`}
                appId={appId}
                boardColorStyle={boardColorStyle}
                changeBoardColorStyle={setBoardColorStyle}
                customShowOption={customShowOption}
                addCustomGroup={addCustomGroup('optionCustom')}
                changeCustomOption={changeCustomOption('optionCustom')}
                setFieldDesc={this.showSetDesc}
                isFromEbAdvance={isFromEbAdvance}
              />
            </When>
            <When weId={`${this.props.weId || ''}_3n0f93`} condition={groupType === GroupType.field}>
              <GroupRowItem
                weId={`${this.props.weId || ''}_uzgerf`}
                title={getLabel('86552', '选择字段')}
                content={
                  <Select
                    weId={`${this.props.weId || ''}_cc63h8`}
                    className={`${ebdBClsPrefix}-view-group-main-field-select`}
                    data={this.getOptionField()}
                    value={selectedField.id}
                    onChange={this.selectGroup}
                  />
                }
              />
              {/* selectedField.id为-1时，即请选择 */}
              {/* 按字段分组配置后相关选项 纯业务 不复用 */}
              <When weId={`${this.props.weId || ''}_1b3ncm`} condition={selectedField.type === EtComponentKey.Ebuilder}>
                <EbrAndDataSetFieldComps
                  weId={`${this.props.weId || ''}_tmom7h`}
                  fieldsMap={toJS(datasetMap)}
                  onChange={changeFieldGroup}
                  value={toJS(groupTypeOpt)}
                  dataset={config?.dataset!}
                  key={selectedField.id}
                />
              </When>
              {this.renderFieldDesc()}
              {isEbForm && <ExtraSealField weId={`${this.props.weId || ''}_yvlikj`} selectedField={selectedField} displaySealing={displaySealing} setState={setState} />}
            </When>
            <When weId={`${this.props.weId || ''}_3n0f93`} condition={groupType === GroupType.dateRange}>
              <DateRangeFieldComps weId={`${this.props.weId || ''}_sn29wr`} fieldsMap={datasetMap} value={toJS(groupDateRangeOpt)} onChange={changeFieldGroup} />
              {this.renderFieldDesc()}
            </When>
            <When weId={`${this.props.weId || ''}_3n0f93`} condition={groupType === GroupType.filter}>
              <GroupOptionCom
                weId={`${this.props.weId || ''}_0lbil3`}
                appId={appId}
                customShowOption={toJS(filterShowOption)}
                addCustomGroup={addCustomGroup('filterOptions')}
                changeCustomOption={changeCustomOption('filterOptions')}
                isFromEbAdvance={isFromEbAdvance}
                setLineFilters={this.setLineFilters}
                isFilterGroupWay
              />
            </When>
            <When weId={`${this.props.weId || ''}_3n0f93`} condition={groupType === GroupType.dataset}>
              <EbrAndDataSetFieldComps
                weId={`${this.props.weId || ''}_tmom7h`}
                fieldsMap={toJS(datasetMap)}
                onChange={changeFieldGroup}
                value={toJS(dstWayValue)}
                dataset={config?.dataset!}
                isdstWay
              />
            </When>
            <When weId={`${this.props.weId || ''}_3n0f93`} condition={groupType === GroupType.diff}>
              <DiffGroup weId={`${this.props.weId || ''}_0uvzgj`} {...this.props} group={store.data} store={store} diffStore={diffStore} addCustomGroup={addCustomGroup} />
            </When>
          </main>
          {this.renderDescSet()}
          {this.renderFilterCmp()}
        </Spin>
      </div>
    );
  }
}
