import React from 'react';
import { Button, Icon, BtnType, utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import ButtonSet from '../../config/ButtonSet';
import store from './store';
import { ConfigItem } from './types';

const {isEmpty} = utils
export interface BtnSetProps {
  buttonType: BtnType;
  dataset: any;
  idParams: any;
  btnClassName?: string;
  hasIcon?: boolean;
  onChange: (key: ConfigItem, value: any) => void;
  [_: string]: any;
}

@observer
export default class BtnSet extends React.PureComponent<BtnSetProps> {
  state = {
    showDialog: false,
  };
  renderGridLayout = (showModalFC: Function) => {
    const { buttonType, hasIcon = true } = this.props;
    return (
      <Button
        weId={`${this.props.weId || ''}_h2mpwp`}
        className={this.props.btnClassName}
        type={buttonType}
        onClick={() => showModalFC()}
      >
        {hasIcon && <Icon weId={`${this.props.weId || ''}_wa8pra`} className="btn-icon-left" name="Icon-Basic-settings-o" />}
        {getLabel('235054', '按钮设置')}
      </Button>
    );
  };
  onChange = (value?: any) => {
    if (isEmpty(value?.comButton)) {
      this.props.onChange(ConfigItem.ComButton, []);
    } else {
      this.props.onChange(ConfigItem.ComButton, value.comButton);
    }
  };

  render() {
    const { idParams, dataset } = this.props;
    const { customMenuActions } = store;
    const _props = {
      config: {
        comButton: toJS(customMenuActions),
        dataset
      },
      comId: idParams.compId,
      pageId: idParams.pageId,
      onConfigChange: this.onChange
    };
    return <ButtonSet weId={`${this.props.weId || ''}_7vmwnf`} externalElement={(showModalFC: Function) => this.renderGridLayout(showModalFC)} {..._props} />
  }
}
