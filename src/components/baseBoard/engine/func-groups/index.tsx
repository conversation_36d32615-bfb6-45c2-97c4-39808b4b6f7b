import { observer } from 'mobx-react';
import React from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../../constants';
import BoardFilterSet from '../../../common/field-search/FilterSetCom';
import { BoardGroupButton } from './BoardGroup';
import BoardOrder from './BoardOrder';
import CardSet from './CardSet';
import BtnSet from './BtnSet';
import ParamCondition from './ParamCondition';
import store from './store';
import { ConfigItem, FuncGroupsProps } from './types';
import BoardGroupStatistics from './BoardGroupStatistics';
import { toJS } from 'mobx';
@observer
export default class FuncGroups extends React.PureComponent<FuncGroupsProps> {
  componentDidMount() {
    this.setInfo();
  }

  componentDidUpdate(prev: FuncGroupsProps) {
    const { idParams, boardId, comServicePath } = this.props;
    if (
      prev.idParams?.viewId !== idParams?.viewId ||
      prev.idParams?.objId !== idParams?.objId ||
      prev.boardId !== boardId ||
      prev.comServicePath !== comServicePath
    ) {
      this.setInfo();
    }
  }

  setInfo = () => {
    const { idParams, boardId, refreshFn, comServicePath } = this.props;
    if (idParams.appId) {
      store.initBaseParams(idParams, boardId, refreshFn, comServicePath);
    }
  };

  componentWillUnmount() {
    store.clearGarbage();
  }

  render() {
    const { buttonType, idParams, boardId } = this.props;
    const { updateViewDesignerByKey, orders, filter, card, condition, initLoading,statistics = {} } = store;
    const dataset = {
      id: idParams.objId,
      text: '',
      type: 'FORM',
      groupId: idParams.appId,
    };
    return (
      <div className={`${ebdBClsPrefix}-func-groups`}>
        <BoardGroupStatistics weId={`${this.props.weId || ''}_08fv65`}
          idParams={idParams}
          buttonType={buttonType}
          dataset={dataset}
          statistics = {toJS(statistics)}
          btnClassName={
            store.checkedTypes.includes(ConfigItem.Statistics) ? 'engine-btn-select' : ''
          }
          />
        <BoardGroupButton
          weId={`${this.props.weId || ''}_kqkeg2`}
          idParams={idParams}
          buttonType={buttonType}
          btnClassName={
            store.checkedTypes.includes(ConfigItem.Group) ? 'engine-btn-select' : ''
          }
          dataset={dataset}
        />
        <When weId={`${this.props.weId || ''}_7vnfnp`} condition={!!dataset?.id}>
          <CardSet
            weId={`${this.props.weId || ''}_67245l`}
            dataset={dataset}
            value={card}
            buttonType={buttonType}
            onChange={updateViewDesignerByKey}
            isView
            btnClassName={store.checkedTypes.includes(ConfigItem.Card) ? 'engine-btn-select' : ''}
            appid={idParams?.appId}
          />
        </When>
        <When weId={`${this.props.weId || ''}_7vnfnp`} condition={!initLoading}>
          <BtnSet weId={`${this.props.weId || ''}_f716y3`}
            dataset={dataset}
            buttonType={buttonType}
            idParams={idParams}
            hasIcon
            onChange={updateViewDesignerByKey}
            btnClassName={store.checkedTypes.includes(ConfigItem.ComButton) ? 'engine-btn-select' : ''}
          />
        </When>
        <BoardOrder
          weId={`${this.props.weId || ''}_jhxv2m`}
          value={orders}
          onChange={updateViewDesignerByKey}
          dataset={dataset}
          btnElmType="btn"
          buttonType={buttonType}
          checked={store.checkedTypes.includes(ConfigItem.Orders)}
        />
        <BoardFilterSet
          weId={`${this.props.weId || ''}_9x2eph`}
          value={filter}
          dataset={dataset}
          buttonType={buttonType}
          onSave={updateViewDesignerByKey}
          checked={store.checkedTypes.includes(ConfigItem.Filter)}
          ebBusinessId={boardId}
        />
        <ParamCondition
          weId={`${this.props.weId || ''}_9t0sj2`}
          dataset={dataset}
          value={condition}
          onSave={updateViewDesignerByKey}
          buttonType={buttonType}
          btnClassName={store.checkedTypes.includes(ConfigItem.Condition) ? 'engine-btn-select' : ''}
        />
      </div>
    );
  }
}
