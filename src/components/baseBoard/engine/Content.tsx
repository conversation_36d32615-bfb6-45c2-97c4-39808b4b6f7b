/**
 * 表单下高级视图
 */
import React, { PureComponent } from 'react';
import { inject, observer } from 'mobx-react';
import { ebdfClsPrefix } from '../../../constants';
import FuncGroups from './func-groups';
import { IdParams } from './types';
import FakeBoard from './fake-board';

@inject('store')
@observer
class BoardEngineContent extends PureComponent<any> {
  componentDidMount() {
    const { store, data } = this.props;
    store.init(data);
  }

  render() {
    const { store, comServicePath } = this.props;
    const { idParams, boardId } = store;
    return (
      <>
        <div className={`${ebdfClsPrefix}-view-board-setting`}>
          <FuncGroups
            weId={`${this.props.weId || ''}_y44kmh`}
            comServicePath={comServicePath}
            buttonType="default"
            idParams={idParams! as IdParams}
            boardId={boardId}
          />
        </div>
        <FakeBoard weId={`${this.props.weId || ''}_nxg5v7`} />
      </>
    );
  }
}

export default BoardEngineContent;
