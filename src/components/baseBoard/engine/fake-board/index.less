@import (reference) '../../../../style/prefix.less';


.@{ebdBClsPrefix}-fake-board-wrapper {
  .ui-list-scrollview.ui-list.ui-board{
    padding: 0;
  }
}
.@{ebdBClsPrefix}-fake-board-wrapper {
  width: 100%;
  height: 100%;
  .ui-list-scrollview-wrap {
    height: 100%;
  }
  & > .ui-list-scrollview-wrap > .ui-scroller > .ui-scroller__wrap > .ui-scroller__view {
    height: 100%;
  }
}

.@{ebdBClsPrefix}-fake-board-list-empty {
  height: 100%;
  border-top: 1px solid var(--border-color);
  &-placeholder {
    height: 100%;
    .ui-board-list-cards
      > .ui-list-scrollview-wrap
      > .ui-scroller
      > .ui-scroller__wrap
      > .ui-scroller__view {
      height: 100%;
      .ui-list-body {
        background: transparent;
      }
    }
  }
  &-icon {
    position: absolute;
    top: 100px;
    left: 0;
    right: 0;
    bottom: 40px;
  }
  .ui-empty-description {
    color: inherit;
  }
}

.@{ebdBClsPrefix}-fake-board-list {
  width: 300px;
  border: 1px solid var(--border-color);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  background: transparent;
  cursor: auto;

  &-title {
    width: 100%;
    height: 40px;
    flex: 0 0 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    font-weight: bold;
    font-size: var(--font-size-14);
    flex-wrap: nowrap;
    background: transparent;
    &-name {
      display: flex;
      align-items: center;
      width:100%;
      .name-lable{
        max-width: 90%;
        cursor: pointer;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &-more {
    }
    .blur-input-wrapper {
      width: 0;
      flex: 1 1 0;
      .ui-input.is-readonly {
        color: var(--main-fc);
        font-weight: bold;
        font-size: var(--font-size-14);
      }
    }
    .ui-icon {
      color: var(--regular-fc);
    }
  }

  &-body-noMore {
    .ui-list-scrollview-content::after {
      content: getLabel('105956', '已加载全部数据');
      display: inline-block;
      text-align: center;
      width: 100%;
      padding: 8px 0;
      background: var(--bg-base);
      color: var(--placeholder-fc);
      word-break: break-word;
    }
  }

  &-body {
    width: 100%;
    height: 0;
    flex: 1 1 0;
    .ui-list-body {
      // background: var(--bg-base);
      .ui-board-drag-handle:last-of-type {
        .@{ebdBClsPrefix}-card-main {
          margin-bottom: var(--v-spacing-md);
        }
      }
    }
    .ui-list-scrollview-content {
      height: 100%;
    }
    .ebcoms-list:hover {
      background: var(--base-white);
    }
  }
  &-bottom {
    height: 40px;
    flex: 0 0 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: var(--regular-fc);
    border-top: 1px solid var(--border-color);
  }
  .ui-board-list-cards {
    width: 100%;
    padding: 0;
  }
}

.@{ebdBClsPrefix}-fake-board-card {
  background: var(--base-white);
  font-size: var(--font-size-12);
  color: var(--placeholder-fc);
  margin: 0 var(--h-spacing-sm);
  &-main {
    position: relative;
    margin-top: var(--h-spacing-sm);
    margin-bottom: var(--h-spacing-sm);
    min-height: 50px;
    background: var(--base-white);
    cursor: auto;
    .ebcoms-list {
      background: var(--base-white);
    }
    &-icon-down {
      visibility: hidden;
      position: absolute;
      right: 16px;
      top: 8px;
      z-index: 9;
      &:hover {
        visibility: visible;
      }
    }
    &:hover {
      .@{ebdBClsPrefix}-card-main-icon-down {
        visibility: visible;
        transition: all 0.3s ease-in-out;
      }
    }
  }
}
