import { Board, Empty, Icon, Help } from '@weapp/ui';
import { classnames, getLabel, flattenDeep } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../../constants';
import { getBoardTitleFontColor, getIsWhiteColors, getRgbBorderColor } from '../../../common/board/utils';
import LayoutCard from '../../../common/field-layout/LayoutCard';
import store from '../func-groups/store';
import './index.less';

export interface FakeBoardProps {
  datas?: any[];
  promptInfo?: any;
}

@observer
export default class FakeBoard extends React.PureComponent<FakeBoardProps> {
  getGroupData = (statisticsItem = []) => {
    let name = '',
      count = '';
    if (statisticsItem.length === 0) return { name: 'N', count: 'N' };
    statisticsItem.map((el: any, ind: number) => {
      if (ind === statisticsItem.length - 1) {
        name = name + el?.inputValue?.valueLocale;
        count = count + 'N';
      } else {
        name = name + el?.inputValue?.valueLocale + '/';
        count = count + 'N' + '/';
      }
    });
    return { name, count };
  };

  customListRender = (item: any, children: any) => {
    const { cards, color = '#ffffff' } = item;
    const backgroudColor = getIsWhiteColors(color) ? 'var(--bg-base)' : color;
    const borderColor = getRgbBorderColor(color);
    const fontColor = getBoardTitleFontColor(color);
    const layouts = flattenDeep(toJS(store.card?.cardLayout?.grid));
    const statistics = item?.otherConfig?.statistics?.[0];
    const helpData: any = this.getGroupData(statistics?.statisticsItem);
    return (
      <div className={`${ebdBClsPrefix}-fake-board-list`} key={item.id} style={{ background: backgroudColor, borderColor }}>
        <div className={`${ebdBClsPrefix}-fake-board-list-title`} style={{ background: backgroudColor, color: fontColor }}>
          <span className={`${ebdBClsPrefix}-fake-board-list-title-name`}>
            <span className="name-lable">{item.name}</span>
            {statistics?.openValue && (
              <>
                <span>{`（${helpData?.count}）`}</span>
                <Help weId={`${this.props.weId || ''}_s4thq5`} title={`（${helpData?.name}）`} placement="top" />
              </>
            )}
          </span>
          <span className={`${ebdBClsPrefix}-fake-board-list-title-more`}>
            <Icon weId={`${this.props.weId || ''}_83jdrm`} name="Icon-more-o" />
          </span>
        </div>
        <When weId={`${this.props.weId || ''}_eyqoc7`} condition={cards.length}>
          <div
            className={classnames({
              [`${ebdBClsPrefix}-fake-board-list-body`]: true,
              [`${ebdBClsPrefix}-fake-board-list-body-noMore`]: !item,
            })}
            id={`list_${item.id}`}
          >
            {children}
          </div>
        </When>
        <When weId={`${this.props.weId || ''}_14nm2l`} condition={!cards.length && !layouts.length}>
          <div
            className={`${ebdBClsPrefix}-fake-board-list-body ${ebdBClsPrefix}-fake-board-list-empty`}
            style={{ background: backgroudColor, color: fontColor }}
          >
            <div className={`${ebdBClsPrefix}-fake-board-list-empty-placeholder`}>{children}</div>
            <Empty
              weId={`${this.props.weId || ''}_td2kho`}
              className={`${ebdBClsPrefix}-fake-board-list-empty-icon`}
              description={getLabel('105964', '可在卡片中设置显示字段')}
              style={{ background: backgroudColor, color: fontColor }}
              image={<Icon weId={`${this.props.weId || ''}_o7r2yf`} style={{ width: 100, height: 100 }} name="Icon-empty-file" />}
            />
          </div>
        </When>
      </div>
    );
  };

  customRenderCell = (field: any, formatValue: any, originValue: any) => originValue;

  customCardRender = (item: any) => (
    <div className={`${ebdBClsPrefix}-fake-board-card`} key={item.id}>
      <div className={`${ebdBClsPrefix}-fake-board-card-main`}>
        <LayoutCard
          weId={`${this.props.weId || ''}_qaani9`}
          data={toJS(item)}
          config={store.card}
          sort={getLabel('116162', '$序号$')}
          customRenderCellRewrite={this.customRenderCell}
          useFieldCustomRender={false}
          client="PC"
          compId=""
          isEbForm // 控制内容不能编辑、点击
          dataset={{} as any}
        />
      </div>
    </div>
  );

  render() {
    const { boardRerenderKey } = store;

    return (
      <div className={`${ebdBClsPrefix}-fake-board-wrapper`}>
        <Board
          weId={`${this.props.weId || ''}_3fjzq1`}
          key={boardRerenderKey}
          data={store.fakeBoardData}
          customCardRender={this.customCardRender}
          customListRender={this.customListRender}
          draggable={false}
        />
      </div>
    );
  }
}
