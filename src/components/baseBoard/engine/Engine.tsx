import { Provider } from 'mobx-react';
import React, { PureComponent } from 'react';
import Content from './Content';
import { BoardEngineStore } from './store';

export default class BoardEngine extends PureComponent<any> {
  store = new BoardEngineStore();
  render() {
    return (
      <Provider weId={`${this.props.weId || ''}_fjts78`} store={this.store}>
        <Content weId={`${this.props.weId || ''}_n6e2tt`} {...this.props} />
      </Provider>
    );
  }
}
