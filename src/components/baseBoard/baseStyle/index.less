// 预览视图和设计视图公共样式

@import (reference) '../../../style/prefix.less';
.@{ebdBClsPrefix}-highlightSetting {
  &-popup {
    .ui-popover-content {
      padding: 0;
    }
    .@{ebdBClsPrefix}-highlightRenderer {
      display: inline-block;
      min-width: 40px;
      height: 25px;
      margin: 5px 2px;
      line-height: 25px;
      text-align: center;
      border-radius: 4px;
      padding: 0 4px;
      &-content {
        padding: 0 6px;
      }
    }
    &-content {
      width: 500px;
      height: 300px;
      background-color: var(--base-white);
      border: var(--border-solid);
      border-radius: 3px;
      padding: 0 5px;
      font-size: var(--font-size-12);
    }
    &-top {
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      font-weight: bold;
    }
    &-preset {
      padding: 4px 0;
    }
    &-bgColor,
    &-preview {
      display: flex;
      align-items: center;
      line-height: 50px;
    }
    &-bgColor {
      .ui-colorpicker {
        display: flex;
        .ui-colorpicker-inp {
          border: none;
          width: fit-content;
          .ui-input {
            display: none;
          }
        }
      }
    }
    &-footer {
      height: 40px;
      padding: 0 5px;
      border-top: 1px solid rgba(0, 0, 0, 0.08);
      text-align: center;
      .ui-btn {
        margin: 5px;
      }
    }
    &-line-title {
      margin-right: 12px;
    }
  }
}

.@{ebdBClsPrefix}-highlightRenderer {
  &-content {
    display: flex;
    align-items: center;
  }
}