import React, { Component } from 'react';
import { CorsComponent } from '@weapp/ui';
import {KanbanMockConfig} from '../../../constants/mock';

class KanbanDemo extends Component {
  render() {
    return <div style={{ height: 700 }}>
      <CorsComponent weId={`${this.props.weId || ''}_r7huhp`}
        app="@weapp/designer"
        compName="EBComView"
        data={KanbanMockConfig}
        isDoc
      />
    </div>
  }
}

export default KanbanDemo;
