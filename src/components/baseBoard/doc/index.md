---
key: Board
title: 看板组件
type: dataPresentation
person: 庞晓峰
assistant: [李劲松]
design: [设计图1]
issue: ""
demoInfo:
  - title: 基础用法
imports:
  - "./basic.tsx"
---

## API

### 一、参数说明

| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| config | 配置项 | Config | 是 | 参考【BoardConfig 配置说明】 |
| id | 组件id | string | 否 | - |
| events | 事件集合 | Event | 否 | - |
| page | 页面信息 | Object | 否 | - |

#### 1.1 BoardConfig 配置说明

| 参数        | 说明                                                         | 类型          | 是否必填 | 默认值 |
| ----------- | ------------------------------------------------------------ | ------------- | -------- | -----: |
| id          | 数据id                                                       | string     | 是       |        |
| title       | 标题                                                         | string | ReactNode     | 是       |        |
| titleEnabled       | 是否显示标题                                            | boolean     | 否       |        |
| footerEnabled       | 是否显示底部按钮                                        | boolean     | 否       |        |
| dataset | 数据源                                            | DataSet     | 否       |        |   |
| filter | 数据过滤                                            | filter[]     | 否       |        |   |
| condition | 步骤条件                                            | Condition     | 否       |        |   |
| eventGroup | 动作事件                                            | EvtMap     | 否       |        |   |
| comButton | 动作事件                                            |   Any   | 否       |        |   |
| layout | 动作事件                                            |   Any   | 否       |        |   |
| group | 分组信息                                            |   Any   | 否       |        |   |
| onRef | 获取组件实例                                            |   Any   | 否       |        |   |

#### 1.2 DataSet 配置说明

| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| groupId | 数据源分组id | string | 是 | - |
| id | 数据源id | string | 是 | - |
| text | 数据源名称 | string | 是 | - |
| type | 数据源类型 | string | 是 | - |


### 二、事件说明

> 使用方式以低代码平台支持的能力为准

```
export enum EbdBoardEventKeys {
  onDidMount = 'onDidMount', // 加载完成
  onDestroy = 'onDestroy', // 销毁
  onGroupDataUpdate = 'onGroupDataUpdate', // 看板分组数据更新回调
  onCardDragEvt = 'onCardDragEvt', // 看板卡片拖拽回调
  onCardAddEvt = 'onCardAddEvt', // 看板新建数据事件
  onCardDataUpdate = 'onCardDataUpdate', // 看板卡片数据更新回调(add/del/update/follow/unfollow)
}

```

#### 2.1 案例中的组件实例（comp）在代码块中的获取方法
```
const ebSdk = window.ebuilderSDK;  //全局SDK
const pageSdk = ebSdk.getPageSDK();  //当前页面SDK
const comp = pageSdk.getCompInstance("组件id");
//注：在事件动作中的javascript事件内不需要定义pageSdk（默认已存在直接使用即可）
```


#### 2.2 使用 events 触发看板事件
```

const ebSdk = window.ebuilderSDK;  //全局SDK
const pageSdk = ebSdk.getPageSDK();  //当前页面SDK
//注：在事件动作中的javascript事件内不需要定义pageSdk（默认已存在直接使用即可）
//1.加载完成
pageSdk.events.on('onDidMount', '组件id', () => {});
//2.销毁时
pageSdk.events.on('onDestroy', '组件id', () => {});
//3.看板分组数据更新回调
pageSdk.events.on('onGroupDataUpdate','组件id', ({type, extParam}) => {})
//4.看板卡片数据更新回调
pageSdk.events.on('onCardDataUpdate','组件id', ({type, data}) => {
  // type：add/del/update/follow/unfollow
  // data: 更新数据
})
//5.看板卡片拖拽回调
pageSdk.events.on('onCardDragEvt','组件id', (startGroupId, endGroupId, cardId, prevId) => {})
//6.看板新建数据事件
pageSdk.events.emit('onCardAddEvt','组件id', dom)
```

### 三、插件包相关
#### 3.1 插件包相关API
> 此方案Design视图不支持

> 根据具体的业务场景，可通过扩展组件插件包或者 ecode 的方式，使用组件预留的自定义 API，对组件功能做自定义复写

| 参数               | 描述                                     | 类型                                                                                                 | 最低支持版本 | 最低支持项目版本 |
| ------------------ | ---------------------------------------- | ---------------------------------------------------------------------------------------------------- | ------------ | ---------------- |
| getData  | 自定义拦截卡片列表数据并返回新处理的数据 API       | (res: BoardDataResProps & {groupId: string}) => BoardDataResProps                                                         | 10.0.2402.01 |                  |
| getCardDraggable  | 自定义卡片是否可以拖拽 API       | (config: AnyObj) => Boolean                                                         | 10.0.2411.01 |                  |
| getGroupData           | 自定义拦截看板分组数据 API             | (res: BoardGroupResProps) => BoardGroupResProps                                                                                 | 10.0.2402.01 |                  |
| getBoardAddParams | 看板自定义新建数据传参 API   | (args: {params, exParams}) => {params, exParams}                              | 10.0.2412.01 |                  |                                | 10.0.2412.01 |                  |
| getGroupEditDlgParams | 看板自定义新建/编辑分组弹窗参数 API   | (params) => params                             | 10.0.2412.01 |                  |                                | 10.0.2412.01 |                  |
| getCardEditDlgParams | 看板自定义新建/编辑数据弹窗参数 API   | (params) => params                              | 10.0.2412.01 |                  |                                | 10.0.2412.01 |                  |
| getShowEmptyVisible |  看板自定义是否显示为空占位 API   | (item) => Boolean                              | 10.0.2503.01 |                  |                                | 10.0.2503.01 |                  |
| onSetData       | 看板自定义数据(分组和分组数据) API (统一更新，不走接口获取) | (id: string, menuData: TabItemData[]) => string[]                                                    | 10.0.2312.02 |                  |
| onCardAddEvt       | 看板新建数据事件 API | (data: string, customRender: Function) => void                                                    | 10.0.2312.02 |                  |
| onGroupDataUpdate       | 看板分组数据更新回调 API | (type: string, groupExtParam: GroupExtParam) => void                                                    | 10.0.2404.02 |                  |
| onCardDataUpdate       | 看板卡片数据更新回调 API | (type: string, data: AnyObj) => void                                                    | 10.0.2404.02 |                  |
| onCustomGetExtraCardInfo  | 自定义获取单个卡片下拉菜单 API | (groupId: string, listIds: string) => void                                                    | 10.0.2411.01 |                  |
| onCustomSetExtraCardInfo  | 自定义设置单个卡片下拉菜单 API | (menus: any) => void                                                    | 10.0.2412.01 |                  |
| onLoadMore  | 看板分组点击加载更多事件 API | (groupId: string, handleFunc: Function) => void                                                    | 10.0.2412.01 |                  |
| onFilter  | 看板自定义处理筛选事件 API | (props: AnyObj) => void                                                    | 10.0.2411.01 |                  |
| onCardDel  | 看板自定义单个卡片删除事件 API | (currentGroup, data, delFunc) => void                                                    | 10.0.2412.01 |                  |
| onCardFollow  | 看板自定义单个卡片关注事件 API | (data, isFollow, followFunc) => void                                                    | 10.0.2412.01 |                  |
| renderCardContent  | 看板自定义渲染单个卡片 API                 | (preRender: Function) => () => ReactNode | 10.0.2312.02 |                  |
| renderCardBefore      | 看板自定义渲染卡片左侧区域 API             | (item: AnyObj) => () => ReactNode            | 10.0.2312.02 |                  |
| renderCardAfter | 看板自定义渲染卡片右侧区域 API   | (item: AnyObj) => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentTop | 看板自定义渲染头部区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentBottom | 看板自定义渲染底部区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentBefore | 看板自定义渲染左侧区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentAfter | 看板自定义渲染右侧区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |                                | 10.0.2408.01 |                  |
| renderMain | 看板自定义整体内容区 API   | (hook: Function, args: AnyObj) => () => ReactNode                                | 10.0.2411.01 |                  |                                | 10.0.2411.01 |                  |
| renderCardAddBtn | 看板自定义新建数据按钮 API   | (hook: Function, args: {item: AnyObj, addBtnName: string, clickFunc: React.MouseEventHandler<HTMLDivElement>}) => () => ReactNode                                | 10.0.2412.01 |                  |                                | 10.0.2412.01 |                  |
| getGroupMenus | 看板自定义获取卡片菜单 API   | (hook: Function, args: {groupId, loadMore}) => void                             | 10.0.2412.01 |                  |                                | 10.0.2412.01 |                  |
| initBoardStore | 看板自定义初始化store API   | (boardStore: any) => void                             | 10.0.2412.01 |                  |                                | 10.0.2412.01 |                  |
| renderGroupTitle | 看板自定义分组标题 API   | (hook: Function, args: {item: AnyObj, type: string, backgroundColor: string, fontColor: string) => ReactNode                             | 10.0.2402.01 |                  |                                | 10.0.2402.01 |                  |
| renderGroupMenus | 看板自定义分组菜单 API   | (hook: Function, args: {menus: AnyObj, item: AnyObj) => ReactNode                             | 10.0.2402.01 |                  |                                | 10.0.2402.01 |                  |
| renderLaneBoardStat | 看板自定义泳道统计   | (hook: Function, args: {statItem: AnyObj, item: AnyObj) => ReactNode                             | 10.0.2403.01 |                  |                                | 10.0.2403.01 |                  |
| renderLaneBoardTitle | 看板自定义泳道分组标题   | (hook: Function, args: {i: AnyObj, idx: number, groups:AnyObj, isCollapsed: Boolean) => ReactNode                             | 10.0.2403.01 |                  |                                | 10.0.2403.01 |                  |
| renderLaneBoardTitleContent | 看板自定义泳道分组标题内容   | (hook: Function, args: {item: AnyObj, index: number, groups: AnyObj) => ReactNode                             | 10.0.2403.01 |                  |                                | 10.0.2403.01 |                  |

#### 3.1.1 BoardDataRes说明
```javascript
export interface BoardDataResProps{
  list: AnyObj[]
  fieldInfo: AnyObj
  count: number
  status: boolean
  groupCount: number
  pageSize: number
}
```
#### 3.1.2 BoardGroupRes说明
```javascript
export interface BoardGroupResProps{
  options: AnyObj[] // 分组列表
  descInfo: AnyObj // 分组描述信息
  status: boolean
}
export interface BoardGroupDataProps{
  options: AnyObj[] // 分组列表
  descInfo: AnyObj // 分组描述信息
  status: boolean
}
```
#### 3.1.3 GroupExtParam说明
```javascript
export interface GroupExtParam{
  groups: AnyObj[] // 当前最新分组列表
  deleteItem?: AnyObj // 当前删除分组
  addItem?: AnyObj // 当前新增的分组
}
```
#### 3.2 声明自定义插件，及插件中需要自定义实现复写的 API
```javascript
class DemoPlugin {
  // 插件名称，唯一标识，目前版本仅支持"kan_ban_plugin"
   name = 'kan_ban_plugin'
   // 具体自定义复写的API
   renderContent = (hook) => {
    //...
   }
 }
```

#### 3.3 注册自定义实现的插件到组件中
```javascript
// 可通过组件类型针对某个类型的组件作用插件
eventEmitter.on('@weapp/designer', 'plugin_center.ready.Kanban', ({ registerPlugin }) => {
  registerPlugin(DemoPlugin);
});
// 或者通过组件id针对某个具体的组件作用插件
eventEmitter.on('@weapp/designer', 'plugin_center.ready.ea316003dc384190b1e961b6cc8b0d51', ({ registerPlugin }) => {
  registerPlugin(DemoPlugin);
});
```
#### 3.4 使用插件包复写看板事件案例

```javascript
import { eventEmitter } from '@weapp/utils';

const mockGroupData = [
  {
    id: '930558347554201602',
    name: '下拉选项1',
    type: 1,
    valueKey: '930558347554201602',
  },
  {
    id: '930558347554201603',
    name: '下拉选项2',
    type: 1,
    valueKey: '930558347554201603',
  },
  {
    id: '930558347554201604',
    name: '下拉选项3',
    type: 1,
    valueKey: '930558347554201604',
  },
  {
    id: '950217589860884494',
    name: '未分组',
    type: 0,
  },
];
const mockListData = {
  '930558347554201602': [
    {
      5: '929463324129484804',
      extra: {
        data_status: '1',
      },
      '929462834528378881': '庞6',
      id: '929463324129484804',
    },
    {
      5: '929463268471070732',
      extra: {
        data_status: '1',
      },
      '929462834528378881': '庞3',
      id: '929463268471070732',
    },
  ],
};
class ExtraPlugin {
  name = 'kan_ban_plugin';
  // 在初始化时，对基础配置进行扩展和处理，生成自己的配置
  formatConfig = (config) => {
    return { ...config };
  };
  initBoardStore = (baseBoard) => {}
  onCardAddEvt = (payload) => {
    const { data, customRender } = payload;
    if (customRender) {
      const dom = <div>自定义渲染的</div>;
      customRender(dom);
    }
  };
  // 自定义获取单个卡片下拉菜单 API
  onCustomGetExtraCardInfo = (groupId: string, listIds: string) => {}
  // 自定义设置单个卡片下拉菜单 API
  onCustomSetExtraCardInfo = (menus: any[]) => {}
  // onSetData = (hook) => {
  //   hook({ groups: mockGroupData, listData: mockListData });
  // };
  onCardDel = async (currentGroup, data, delFunc) => {
    await delFunc(currentGroup.id, data.id)
  }
  onCardFollow = async (data, isFollow, followFunc) => {
    await followFunc(data.id, isFollow)
  }
  getData = (res) => {
    let { list = [], fieldInfo = {}, count, status, groupCount, groupId } = res;
    // 更新指定分组下数据
    if (groupId === '955349469961396225') {
      list.push({
        3: '2023-11-23 14:18:47',
        4: '2023-11-23 14:18:47',
        5: '934242686629068805',
        extra: { data_status: '1' },
        id: '93424268662906880522',
      });
      count++;
    }
    /*
      1.插件机制规范，返回值放在数组内
      2.getData返回值第一个为更新后的data
    */
    return [
      {
        list,
        fieldInfo,
        count,
        status,
        groupCount,
      },
    ];
  };
  // 自定义设置列表菜单按钮数据
  getGroupMenu = (menus: any[]) => {
  	const newMenus = menus.filter(i => i.key !== 'moveData')
    return [newMenus]
  }
  // 设置卡片不允许拖拽
  getCardDraggable = (config: any) => {
    return [false]
  }
  getGroupData = (res) => {
    let { options, descInfo, status } = res;
    // 更新指定分组下数据
    options.push({
      id: 'xxxx',
      name: '新分组名',
      type: 0,
    });
    /*
      1.插件机制规范，返回值放在数组内
      2.getData返回值第一个为更新后的data
    */
    return [
      {
        options,
        descInfo,
        status,
      },
    ];
  };
  // 自定义看板请求接口参数
  getBoardAddParams = (args) => {
  	const {params, exParams} = args
  	return {params, exParams}
  }
  // 自定义看板分组新建弹窗参数
  getGroupEditDlgParams = (params) => {
  	// 比如想修改弹窗的宽度高度和居中弹窗打开
  	return [{
  		...params,
  		openMode: '5',
  		otherLinkInfo: {modalWidth: 500, modalHeight: 200}
  	}]
  }
  // 自定义看板卡片新建弹窗参数
  getCardEditDlgParams = (params) => {
  	// 比如想修改弹窗的宽度高度和居中弹窗打开
  	return [{
  		...params,
  		openMode: '5',
  		otherLinkInfo: {modalWidth: 500, modalHeight: 200}
  	}]
  }
  getGroupMenus = (hook) => {
  	// 不复写逻辑 执行原逻辑
  	// hook && hook（）
  	return (groupId, loadMore) => {
  		// 处理相关逻辑
  	}
  }
  onCardAddEvtFinished = () => {};
  //renderCardContent = (hook) => {
  //   const self = this;
  //   return (...args) => (
  //     <>
  //       <div>自定义渲染</div>
  //       {/* {hook(...args)} */}
  //     </>
  //   );
  //   // return hook;
  // };
  renderCardBefore = () => {
    return (datas, ...args) => (
      <>
        <div>自定义渲染-左边</div>
      </>
    );
  };
  renderCardAfter = () => {
    return (datas, ...args) => (
      <>
        <div>自定义渲染-右边</div>
      </>
    );
  };
  renderContentTop = () => {
    return () => (
      <>
        <div>自定义渲染-头部</div>
      </>
    );
  };
  renderContentBottom = () => {
    return () => (
      <>
        <div>自定义渲染-底部</div>
      </>
    );
  };
  renderContentBefore = () => {
    return () => (
      <>
        <div>自定义渲染-内容-左侧</div>
      </>
    );
  };
  renderContentAfter = () => {
    return () => (
      <>
        <div>自定义渲染-内容-右侧</div>
      </>
    );
  };
  // 自定义新建按钮
  renderCardAddBtn = (hook) => {
    return (item, addBtnName, clickFunc) => {
    	// 原生
      return hook('自定义新建按钮名称', clickFunc)
      // 隐藏
      // return null
    };
  };
  // 自定义分组标题
  renderGroupTitle = (hook) => {
    return (...args) => {
    	// 原生
      const {item, type, backgroundColor, fontColor} = args[0] || {}
      return hook({item, type, backgroundColor, fontColor})
      // 隐藏
      // return null
    }
  }
  // 自定义分组右上角菜单
  renderGroupMenus = (hook) => {
    return (menus, item) => {
    	// 原生
      return hook(menus, item)
      // 隐藏
      // return null
    }
  }
  renderLaneBoardTitleContent = (hook) => {
    return (...args) => (
      <>
        {hook(...args)}
      </>
    );
  };
}
eventEmitter.once('@weapp/designer', 'plugin_center.ready.Kanban', ({ registerPlugin }) => {
  // registerPlugin(DemoPlugin, 'NList') // 针对新列表组件作用的插件
  registerPlugin(ExtraPlugin); // 针对所有组件作用的插件
});
```