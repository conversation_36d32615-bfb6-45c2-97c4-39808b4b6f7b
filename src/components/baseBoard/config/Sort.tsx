/**
 * 排序
 */
import { getLabel, isEmpty } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import React from 'react';
import { ebdBClsPrefix } from '../../../constants';
import DataSort from '../../common/data-sort';
import { DesignComProps } from '../types';
import NSettingIcon from '../../common/n-setting-icon';
import './index.less';

@observer
export default class Sort extends React.PureComponent<DesignComProps & { extCls?: string }> {
  handleChange = (data: any[]) => {
    const { onChange } = this.props;
    onChange([...data]);
  };
  clearAll = () => {
    this.handleChange([])
  }
  customBtnRender = (showSortSet: () => void) => {
    const { isIconBtn, value } = this.props;
    if (!isIconBtn) return null;
    const hasValue = !isEmpty(toJS(value));
    return <NSettingIcon weId={`${this.props.weId || ''}_5y4y89`} hasValue={hasValue} onClick={showSortSet} clearable={value} clearFunc={this.clearAll} />
  };

  render() {
    const { value = [], config, extCls = '' } = this.props;
    const {
      dataset = {
        id: '',
        text: '',
        groupId: '',
        type: '',
      },
    } = config;
    const hasValue = value.length > 0;
    const btnElm = !hasValue ? getLabel('105962', '新建排序') : getLabel('105963', '编辑排序');
    return (
      <span className={`${ebdBClsPrefix}-config-sort ${extCls}`}>
        <DataSort weId={`${this.props.weId || ''}_18yvex`} value={value} dataset={dataset!} onChange={this.handleChange} buttonType="default" btnElm={btnElm} customBtnRender={this.customBtnRender} />
      </span>
    );
  }
}
