import { getLabel } from '@weapp/utils';
import { getPageLinkConfig } from './getPageLinkParams';

const eventAction = {
  events: [
    'CLICKDATA',
    {
      id: 'beforeDragEvent',
      name: get<PERSON><PERSON><PERSON>('303368', '卡片拖拽前'),
      params: [],
    },
    {
      id: 'afterDragEvent',
      name: get<PERSON><PERSON><PERSON>('303369', '卡片拖拽后'),
      params: [],
    },
  ],
  actions: [
    {
      id: 'NewPage',
      supportedEvents: 'CLICKDATA,beforeDragEvent,afterDragEvent',
      pageLinkConfig: {
        paramOptsSync: getPageLinkConfig,
      },
    },
    { id: 'RefreshComp' },
    {
      id: 'JsCode',
      name: 'JavaScript',
      jsCodePlaceholder: `  //可通过this对象获取行数据，this对象格式：{target: ..., data: {...}}
      //获取行数据，比如该行数据为{id: 1, name: Joy, age: 18}
      var data = this.data;    //data = {id: 1, name: Joy, age: 18}
      var name = data.name;    //获取行数据字段为name的值，name=Joy`,
      supportedEvents: 'CLICKDATA',
    },
  ],
};

export default eventAction;
