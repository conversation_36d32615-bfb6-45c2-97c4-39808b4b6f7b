@import (reference) '../../../style/prefix.less';

.@{ebdBClsPrefix}-board-config-btn {
  width: 100%;
  & > button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.@{ebdBClsPrefix}-config-sort {
  .ui-btn {
    width: 100%;
  }
}

.ebcoms-config-Kanban {
  .@{ebdBClsPrefix}-field-setting-btn, .ui-btn {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.@{ebdBClsPrefix}-config-card-set {
  width: 100%;
  &-item{
    margin-bottom: 10px;
    &:last-child{
      margin-bottom: 0;
    }
  }
}
.@{ebdBClsPrefix}-config{
  &-icon-set{
    display: flex;
    align-items: center;
    width: 40px;
    .ui-icon-xs{
      color: var(--secondary-fc);
      transition: all .3s;
      cursor: pointer;
      &:hover{
        color: var(--primary);
      }
    }
  }
}