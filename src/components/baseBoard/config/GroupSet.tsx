/**
 * 分组设置
 */
import { Button, Dialog } from '@weapp/ui';
import { getLabel, isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import React from 'react';
import { ebdBClsPrefix } from '../../../constants';
import { BoardGroup } from '../engine/func-groups/BoardGroup';
import { DesignComProps } from '../types';
import configStore from './store';
import './index.less';

const { message } = Dialog;

@observer
class GroupSet extends React.PureComponent<DesignComProps, any> {
  constructor(props: DesignComProps) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  componentDidMount() {
    this.init()
  }
  componentWillReceiveProps(nextProps: Readonly<DesignComProps>): void {
    const { dataset } = this.props.config;
    const { dataset: nextDataSet } = nextProps.config;
    if (!isEqual(dataset, nextDataSet)) {
      this.init(nextProps)
    }
  }
  init = (props = this.props) => {
    const { dataset } = props.config;
    if (dataset) {
      configStore.getDataFields(dataset);
    }
  }

  triggerShow = () => {
    const { config } = this.props;
    if (config.dataset?.id) {
      this.setState((prevState: any) => ({
        visible: !prevState.visible,
      }));
      return;
    }
    message({
      type: 'info',
      content: `${getLabel('261805', '请先设置数据源')}!`,
    });
  };

  onOk = (data: any) => {
    const { onChange, onConfigChange } = this.props;
    onConfigChange && onConfigChange({})
    this.setState({ visible: false }, () => {
      onChange(data);
    });
  };

  render() {
    const { appid, comId, pageId, config, value, comServicePath, onConfigChange } = this.props;
    const { dataset, diffGroupCustomId = '' } = config;
    const { visible } = this.state;
    const idParams: any = {
      pageId,
      compId: comId,
      appId: appid,
      objId: dataset?.id,
      viewId: '',
    };
    const { etFields } = configStore;
    return (
      <div>
        <Button
          weId={`${this.props.weId || ''}_geanh8`}
          type="default"
          className={`${ebdBClsPrefix}-board-config-btn`}
          onClick={this.triggerShow}
        >
          <span>{getLabel('54215', '分组设置')}</span>
        </Button>
        <BoardGroup
          weId={`${this.props.weId || ''}_8b97fb`}
          value={value}
          idParams={idParams}
          visible={visible}
          onClose={this.triggerShow}
          onOk={this.onOk}
          onConfigChange={onConfigChange}
          isFromEbAdvance={false}
          comServicePath={comServicePath}
          dataset={dataset}
          config={config}
          fields={toJS(etFields)}
          diffGroupCustomId={diffGroupCustomId}
        />
      </div>
    );
  }
}
export default GroupSet