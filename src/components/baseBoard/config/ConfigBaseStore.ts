// import { Field } from '@weapp/ebdcoms';
import { SelectOptionData } from '@weapp/ui';
import { action, computed, observable, runInAction } from 'mobx';
import { EtComponentKey as ETC, EtComponentKey } from '../../../constants/EtComponent';
import { FormFieldItem } from '../../../types/common';
import { ajax } from '../../../utils/ajax';
import ebdcoms from '../../../utils/ebdcoms';
import { getSystemFieldType } from '../../../utils';
import { DataSetField, DataSetItem } from '../../common/DataSet/types';


export type ChangeSelectOptions = {
  [key: string]: FormFieldItem[];
};
/** **
 * 页面设计config配置部分，抽取公共类
 */
class ConfigBaseStore {
  @observable dataset: DataSetItem = {
    id: '',
    type: '',
    groupId: '',
    text: '',
  };

  @observable fieldDatas: any = {}; // 缓存对应id的总etFields

  @observable fieldOptions: any = {}; // 缓存对应id的数据选项

  @observable changeSelectOptions: ChangeSelectOptions = {};

  getFormFieldData = async (dataset: DataSetItem): Promise<any[]> => {
    if (!dataset || !dataset.id) {
      return [];
    }
    const { id } = dataset;
    let { fieldDatas } = this;
    if (!fieldDatas[id]) {
      fieldDatas = await this.getFields(dataset);
    }
    const data = fieldDatas[id];
    const { fields = [] } = data[0] || { fields: [], id: '', name: '' };
    return fields;
  };

  /**
   * 请求表单相关的字段
   * */
  @action
  getFormField = async (dataset: DataSetItem): Promise<SelectOptionData[]> => {
    this.dataset = dataset;
    const fields = await this.getFormFieldData(dataset);
    const fieldOptions: SelectOptionData[] = fields.map((item: any) => ({
      id: item.id,
      content: item.text,
    }));
    // 抛出选项
    return fieldOptions;
  };

  /**
   * 表单字段
   * */
  @computed
  get fields() {
    return this.fieldDatas[this.dataset.id]?.[0]?.fields || [];
  }

  /**
   * 请求数据仓库
   * */
  @action
  getFields = async (dataset: DataSetItem) => {
    const { id } = dataset;

    const { dsUtils, transformFields } = await ebdcoms.get();
    let fieldData = await dsUtils.getFields(dataset, '', false, false);
    fieldData = fieldData.map((el: any) => ({
      ...el,
      fields: el.fields?.map((_field: any) => {
        const field = transformFields(_field, 'componentKey');
        // 系统字段类型转化表单类型
        field.compType = getSystemFieldType(field.id, field.compType as EtComponentKey);
        return field;
      }),
    }));
    runInAction(() => {
      this.fieldDatas[id] = fieldData || null;
    });
    return this.fieldDatas;
  };

  /**
   * 转换数据仓库字段为et字段
   * */
  exchange2EtComponentField = (fields: DataSetField[]): FormFieldItem[] => {
    const _fields: DataSetField[] = fields;
    return _fields.map((field: DataSetField) => ({
      config: '',
      ...field,
      componentKey: field.compType as ETC,
      fieldId: field.id,
      fieldName: field.name,
      fieldShowName: field.text,
      fieldType: field.type,
    }));
  };

  @action('标题字段(编号、标题、文本输入框、人员资源、创建者、最后更新人)')
  getTextFields = (etFields: FormFieldItem[]) => etFields.filter(
    (f: FormFieldItem) => f.fieldId === '1'
      || f.fieldId === '2'
      || f.fieldId === '6'
      || f.componentKey === ETC.Text
      || f.componentKey === ETC.String
      || f.componentKey === ETC.SerialNumber
      || f.componentKey === ETC.Employee,
  );

  @action('用于类型颜色等')
  getRadioOrSelect = (etFields: FormFieldItem[]) => etFields.filter((f: FormFieldItem) => {
    const cky = f.componentKey;
    // 屏蔽数据状态、流程状态
    if (['8', '108', '9', '109', '10', '1010', '1', '101'].includes(f.fieldId)) return false;
    return (cky && cky === ETC.RadioBox) || cky === ETC.Select;
  });

  @action('日期字段id过滤，特殊处理')
  getDateField = (etFields: FormFieldItem[]) => {
    const dateFields = etFields.filter((f: FormFieldItem) => {
      if (!f.componentKey) return false;
      const isDate: boolean = [ETC.DateComponent, ETC.Date].includes(f.componentKey);
      return isDate;
    });
    return this.getDateFields(dateFields);
  };

  @action
  getDateFields = async (dateFields: FormFieldItem[]): Promise<FormFieldItem[]> => {
    if (!dateFields.length || !this.dataset.groupId) return [];
    const dateIds = dateFields.map((f: FormFieldItem) => f.fieldId);
    const res = await ajax({
      url: `/api/bs/ebuilder/form/calendarresource/getCalendarDateField?apid=${this.dataset.groupId}`,
      method: 'post',
      data: {
        fieldIds: dateIds,
      },
      ebBusinessId: this.dataset.groupId,
    });
    const {
      changeSelectOptions: { dateTimeFields = [] },
    } = res;
    return dateTimeFields;
  };
}

export default ConfigBaseStore;
