import { action, observable } from 'mobx';
import ConfigBaseStore from './ConfigBaseStore';
import { DataSetItem } from '../../common/DataSet/types';

export class BoardConfigStore extends ConfigBaseStore {
  @observable etFields: any[] = [];

  @action
  getEtFields = () => {
    this.etFields = this.exchange2EtComponentField(this.fields);
  };
  @action reset = () => {
    this.etFields = []
  }

  @action
  getDataFields = async (dataset: DataSetItem) => {
    try {
      if (dataset?.id) {
        await this.getFormField(dataset);
        this.getEtFields();
      } else {
        this.reset()
      }
    } catch (e) {}
  };
}

export default new BoardConfigStore();
