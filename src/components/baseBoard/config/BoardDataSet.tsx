/**
 * <AUTHOR>
 * @createTime 2021-11-18
 */
import { Input } from '@weapp/ui';
import { isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import { Else, If, Then } from 'react-if';
import DataSet from '../../common/DataSet';
import { DataSetItem, DataSetProps } from '../../common/DataSet/types';
import { KanBanInitConfig } from '../../../constants';
import configStore from './store';
import { BoardConfigData } from '../types';

interface CalDataSetProps extends DataSetProps {
  config: BoardConfigData
  onConfigChange: (config: BoardConfigData) => void
  value: DataSetItem
}

// 如果只是改变权限，不清空 筛选、字段选项
export const formatDataSet = (dataset: DataSetItem) => ({
  ...dataset,
  dataPermissions: [],
});
@observer
export default class BoardDataSet extends React.PureComponent<CalDataSetProps> {
  componentDidMount() {
    const { dataset } = this.props.config;
    if (dataset) {
      configStore.getDataFields(dataset);
    }
  }

  componentDidUpdate(oldProps: CalDataSetProps) {
    const { dataset } = this.props.config;
    if (oldProps.config.dataset?.id !== dataset?.id) {
      configStore.getDataFields(dataset!);
    }
  }

  onChange = async (dataset: DataSetItem) => {
    const { value = {} as DataSetItem, onChange, onConfigChange } = this.props;
    await configStore.getDataFields(dataset);
    if (!isEqual(dataset, value)) {
      if (!(dataset.id === value.id && dataset.type === value.type)) {
        const configVal = {
          ...KanBanInitConfig(false),
          dataset,
        };
        onConfigChange(configVal);
      }
    }
    onChange?.(dataset);
  };

  render() {
    const { value, config } = this.props;
    const { fromEbuilder } = config as any;
    return (
      <If weId={`${this.props.weId || ''}_heyw1s`} condition={fromEbuilder}>
        <Then weId={`${this.props.weId || ''}_9vtk8h`}>
          <Input weId={`${this.props.weId || ''}_pg0go3`} disabled value={value?.text} />
        </Then>
        <Else weId={`${this.props.weId || ''}_ihr5u9`}>
          <DataSet
            weId={`${this.props.weId || ''}_nmgy2i`}
            {...this.props}
            value={value}
            onChange={this.onChange}
          />
        </Else>
      </If>
    );
  }
}
