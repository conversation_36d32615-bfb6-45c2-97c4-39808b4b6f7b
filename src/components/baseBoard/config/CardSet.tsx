/**
 * 卡片设置
 */
import React from 'react';
import { isEmpty } from "@weapp/utils"
import { observer } from 'mobx-react';
import CardSetCom from '../engine/func-groups/CardSet';
import { ebdBClsPrefix } from '../../../constants';
import './index.less';

const cls = `${ebdBClsPrefix}-config-card-set`;
@observer
export default class CardSet extends React.PureComponent<any> {
  handleChange = (key: string, card: any = {}) => {
    if (!isEmpty(card?.cardLayout)) {
      this.props.onChange(card);
    } else {
      this.props.onChange({});
    }
  };

  render() {
    const { config, value, appid, page, extCls = '', isView, style, isIconBtn } = this.props;
    const { dataset } = config;
    return (
      <div className={`${cls} ${extCls}`}>
        <div className={`${cls}-item`}>
          <CardSetCom
            weId={`${this.props.weId || ''}_j780pc`}
            buttonType="default"
            hasIcon={false}
            btnClassName={`${ebdBClsPrefix}-board-config-btn`}
            dataset={dataset}
            value={value}
            onChange={this.handleChange}
            isView={isView}
            appid={appid}
            page={page}
            style={style}
            isIconBtn={isIconBtn}
          />
        </div>
      </div>
    );
  }
}
