/**
 * 分组设置
 */
import { <PERSON>ton, Icon, Dialog, CorsComponent } from '@weapp/ui';
import { getLabel, cloneDeep } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import { statisticsItemProps, groupStatisticsValue } from '../../types';
import EBFilter from '../../../common/Filter';
import { specialFieldsFilter } from '../../../../utils';
import ebdcoms from '../../../../utils/ebdcoms';
import { toJS } from 'mobx';

const { LocaleEx } = ebdcoms.get();
const { confirm } = Dialog;

@observer
class StatisticsItem extends React.PureComponent<statisticsItemProps, any> {
  constructor(props: statisticsItemProps) {
    super(props);
    const { data } = this.props;
    this.state = {
      valueLocale: data?.inputValue?.valueLocale,
    };
  }

  onValueChange = (inputValue: any) => {
    this.setState({ valueLocale: inputValue });
    this.onDatasChange('inputValue',inputValue)
  };

  onChangeEBFilter = (val: any) => {
    this.onDatasChange('eBFilterData', val);
  };

  onDatasChange = <T extends keyof groupStatisticsValue>(item: T, value: any) => {
    const { allDatas = [], onChange, id } = this.props;
    let _allDatas: any = cloneDeep(toJS(allDatas));
    let { statisticsItem = [] } = _allDatas;
    let inputValue = {
      valueLocale: this.state.valueLocale,
    };
    statisticsItem.map((el: groupStatisticsValue) => {
      if (el.id === id) {
        el[item] = value;
        // el['inputValue'] = inputValue;
      }
    });
    onChange(_allDatas);
  };

  confirmDel = (data: any) => {
    const { onItemDelete } = this.props;
    confirm({
      content: getLabel('250968', '确定删除吗'),
      onOk: () => {
        onItemDelete(data);
      },
    });
  };
  onBlur = (val: any) => {
    let inputValue = {
      valueLocale: val,
    };
    this.onDatasChange('inputValue', inputValue);
  };

  render() {
    const { pageId, dataset, data, index, onItemDelete, useForm } = this.props;
    const { eBFilterData } = data!;
    const { valueLocale } = this.state;
    return (
      <div className={`groupstatistics-all`}>
        <div className={`groupstatistics-content-left`}>
          {useForm ? (
            <CorsComponent weId={`${this.props.weId || ''}_ecnvtu`} app="@weapp/ebdform" compName="LocaleInput" {...(this.props as any)} onChange={this.onBlur} ebBusinessId={pageId} value={valueLocale} />
          ) : (
            <LocaleEx
              weId={`${this.props.weId || ''}_3svcv9`}
              value={valueLocale}
              targetId={pageId}
              onChange={this.onValueChange as any}
              placeholder={getLabel('251789', '请输入统计标题')}
              onBlur={this.onBlur}
            />
          )}
          <EBFilter
            weId={`${this.props.weId || ''}_9qmdm7`}
            value={eBFilterData}
            dataset={dataset!}
            onChange={this.onChangeEBFilter}
            title={getLabel('105959', '看板视图固定查询条件')}
            filter={specialFieldsFilter}
          />
        </div>
        <div className={`groupstatistics-content-right`}>
          <Icon
            weId={`${this.props.weId || ''}_23sl32`}
            name="Icon-delete02"
            style={{ width: 14, height: 14 }}
            onClick={() => {
              this.confirmDel(index);
            }}
          />
        </div>
      </div>
    );
  }
}
export default StatisticsItem;
