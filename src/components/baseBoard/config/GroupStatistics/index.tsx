/**
 * 分组设置
 */
import { But<PERSON>, Switch, Icon } from '@weapp/ui';
import { getLabel, isEqual, isEmpty, cloneDeep } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import { ebdBClsPrefix } from '../../../../constants';
import { GroupStatisticsProps } from '../../types';
import StatisticsItem from './statisticsItem';
import ebdcoms from '../../../../utils/ebdcoms';
import { toJS } from 'mobx';
import './index.less';

const { UUID } = ebdcoms.get();

@observer
class GroupStatistics extends React.PureComponent<GroupStatisticsProps, any> {
  constructor(props: GroupStatisticsProps) {
    super(props);
    const { statistics = {} } = this.props?.config;
    const defaultStatisticsData = {
      openValue: true,
      statisticsItem: [],
    };

    this.state = {
      valueLocale: '',
      statisticsData: !isEmpty(toJS(statistics)) ? toJS(statistics) : defaultStatisticsData,
    };
  }

  componentDidUpdate(preProps: any) {
    const { value, config = {}, onChange } = this.props;
    const { dataset = {} as any } = config;
    const { statistics: preStatisticsData, dataset: preDataset } = preProps?.config;
    if (value && !isEqual(value, preStatisticsData)) {
      this.setState({ statisticsData: value });
    }
    // if (!isEqual(dataset?.id, preDataset?.id)) {
    //   onChange({
    //     openValue: true,
    //     statisticsItem: [],
    //   });
    // }
  }

  onAddClick = () => {
    const { onChange } = this.props;
    let { statisticsData = {} } = this.state;
    const id = UUID();
    const defaultStep = {
      id,
      inputValue: {
        valueLocale: '',
      },
      eBFilterData: {},
    };

    let _statisticsData = isEmpty(toJS(statisticsData)) ? { openValue: true, statisticsItem: [] } : cloneDeep(toJS(statisticsData));
    _statisticsData.statisticsItem.push(defaultStep);
    this.setState({
      statisticsData: _statisticsData,
    });

    onChange(_statisticsData);
  };

  onItemDelete = (indx: number) => {
    const { onChange } = this.props;
    let { statisticsData } = this.state;
    const _statisticsData = cloneDeep(toJS(statisticsData));
    _statisticsData?.statisticsItem.splice(indx, 1);
    this.setState({
      statisticsData: _statisticsData,
    });
    onChange(_statisticsData);
  };

  handleChange = (val: any) => {
    const { onChange } = this.props;
    let { statisticsData } = this.state;
    let _statisticsData = cloneDeep(toJS(statisticsData));
    _statisticsData.openValue = val;
    this.setState({
      statisticsData: _statisticsData,
    });
    onChange(_statisticsData);
  };

  render() {
    const { pageId, config, hideWithUnOpen } = this.props;
    const { statisticsData = {} } = this.state;
    const { dataset } = config;
    const { openValue = true, statisticsItem = [] } = statisticsData;

    const statisticsItemList = () => {
      return (
        <>
          <div>
            {statisticsItem.map((item: any, index: number) => {
              return (
                <StatisticsItem
                  weId={`${this.props.weId || ''}_4v8fw5`}
                  {...this.props}
                  key={item.id}
                  onChange={this.props.onChange}
                  allDatas={statisticsData}
                  data={item}
                  id={item.id}
                  pageId={pageId}
                  dataset={dataset}
                  index={index}
                  onItemDelete={this.onItemDelete}
                />
              );
            })}
          </div>

          <Button
            weId={`${this.props.weId || ''}_geanh8`}
            type="default"
            className={`${ebdBClsPrefix}-board-config-btn`}
            onClick={this.onAddClick}
          >
            <Icon weId={`${this.props.weId || ''}_23sl32`} name="Icon-add-to01" style={{ width: 14, height: 14 }} />
            <span>{getLabel('56032', '添加')}</span>
          </Button>
        </>
      );
    };

    return (
      <div className={`${ebdBClsPrefix}-board-config-groupstatistics`}>
        <div className={`groupstatistics-top`}>
          <div className={`groupstatistics-top-lable`}>{getLabel('245989', '开启统计')}</div>
          <Switch weId={`${this.props.weId || ''}_njzwgp`} size="sm" onChange={this.handleChange} value={openValue} />
        </div>
        {hideWithUnOpen ? openValue ? statisticsItemList() : null : statisticsItemList()}
      </div>
    );
  }
}
export default GroupStatistics;
