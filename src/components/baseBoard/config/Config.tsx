import getBaseConfig from './getConfig';
import { loadPluginClasses } from '../../plugin-packages/loader';
import eventAction from './event-action-config/eventAction';

function Config(datas: any, store: any, pluginCenter?: any, isView?: boolean) {
  const isMobile = store.clientType === 'MOBILE';
  const viewType = isMobile ? 'MConfig' : 'Config';
  let pluginPacks = store.selectedCom?.refPluginPackage || undefined;
  if (isView) {
    pluginPacks = datas._plugins?.length ? { plugins: datas._plugins } : undefined;
  }
  const baseConfig = getBaseConfig(pluginCenter);
  return loadPluginClasses(pluginPacks, viewType).then(async (plugins: any) => ({
    ...baseConfig,
    // todo 核心插件注入 支持handlePropertyChange以及handleConfigChange
    plugins: [...plugins],
    // transform,
    eventAction,
  }));
}

Config.eventAction = eventAction;
export default Config;
