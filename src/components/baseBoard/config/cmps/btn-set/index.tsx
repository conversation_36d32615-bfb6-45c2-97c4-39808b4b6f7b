/**
 * <AUTHOR>
 * @desc   : 按钮配置-new-暂时没做完-未来用这个
 */
import { CorsComponent, FormDatas } from '@weapp/ui';
import { getLabel, isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import ButtonEx from '../../../../common/buttonEx';
import { ajax } from '../../../../../utils/ajax';
import { DataSet } from '../../../../../types/common';
import { NotUseFields } from '../../../../../constants';
import { positionData, listJsPlaceholder, getDefaultButtons, rightTopKeys, lineKeys } from './constants';

const isMockDataset = (dataset?: DataSet) => dataset?.type === 'mock';

interface CustomButtonsProps extends Attributes {
  config: any;
  comId: string;
  pageId: string;
  onChange?: (data: any) => void;
  onConfigChange?: (value?: FormDatas, act?: any) => void;
  externalElement?: (showModalFC: Function) => React.ReactNode;
  [_: string]: any;
}

@observer
export default class ButtonSet extends PureComponent<CustomButtonsProps, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      datasetBtns: [],
      buttons: [],
    };
  }

  componentDidMount() {
    // this.getButtonsByDataset();
  }

  componentDidUpdate(preProps: any) {
    const { config } = this.props;
    if (!isEqual(preProps.config?.dataset, config?.dataset)) {
      // this.getButtonsByDataset();
    }
  }
  initial = () => {
    const { config, value } = this.props;
    let newButtons = [] as any[]
    if (value && value.length) {
      newButtons = value
    } else {
      newButtons = config?.comButton?.length ? config?.comButton : getDefaultButtons()
    }
    this.setState({buttons: newButtons})
  }

  getButtonsByDataset = () => {
    const { config, ebBusinessId } = this.props;
    // 模拟数据源，不继续请求接口
    const isMockData = isMockDataset(config?.dataset);
    if (config?.dataset && config?.dataset?.id && !isMockData) {
      const formData = new FormData();
      formData.append('type', 'Kanban');
      formData.append('dataset', JSON.stringify(config?.dataset));
      ajax({
        // url: '/api/ebuilder/coms/button/getButtonList',
        url: '/api/ebuilder/coms/button/getButtonListByType',
        method: 'post',
        data: formData,
        ebBusinessId,
        success: (res: any) => {
          this.setState({ datasetBtns: res || [] });
          // this.syncButtonConfig(res || []);
        },
      });
    }
  };
  /**
   * 去掉部分配置来比对
   * @param arr
   * @returns arr
   */
  filterProps = (arr: any[]) => {
    // 这块配置用看板视图自己的
    const props = ['enable', 'showPosition'];
    return arr.map(i => {
      let ret: any = {};
      for (let j in i) {
        if (!props.includes(j)) ret[j] = i[j];
      }
      return ret;
    });
  };
  /**
   * 首次请求同步数据源的按钮同步到配置里面
   * 发现配置不一致 还是需要同步
   * @param btns
   */
  syncButtonConfig = (btns: any[]) => {
    const { config } = this.props;
    // 过滤掉表单视图/表格视图内的按钮 这些都是在table那边展示的 不在设计器内进行同步
    const validBtns = btns.filter((b: any) => b.buttonType !== 'CUSTOM');
    // 如果线上已经有错误数据了 打开的时候需要去同步下当前配置重新保存再刷新即可
    const failedData = config?.comButton.length ? config?.comButton.filter((i: any) => i.buttonType === 'CUSTOM') : [];
    if ((!config?.comButton?.length && validBtns.length) || failedData.length) {
      this.onConfirm(validBtns);
    }
  };

  externalElement = (showModalFC: any) => {
    const { config, externalElement } = this.props;
    if (externalElement) return externalElement(showModalFC);
    const hasBtn = config?.comButton?.filter((b: any) => b.enable).length;
    const _showModalFC = () => {
      this.initial()
      showModalFC()
    }
    return (
      <ButtonEx weId={`${this.props.weId || ''}_oliqbd`} action={hasBtn ? 'edit' : undefined} inline={false} onClick={_showModalFC}>
        {hasBtn ? getLabel('221908', '编辑按钮') : getLabel('221909', '配置按钮')}
      </ButtonEx>
    );
  };

  onConfirm = (btns: any) => {
    this.props.onChange?.(btns);
  };

  fieldFilter = (field: any) => {
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id || field?.componentKey)) {
      return false;
    }
    return true;
  };

  render(): React.ReactNode {
    const { buttons = [] } = this.state;
    const { config, pageId, pageScope, appid, client, layoutInfo, store, comId, value = [] } = this.props;
    const page = {
      id: pageId,
      module: pageScope,
      appid,
      client,
      datasetVals: layoutInfo?.datasetVals || [], // 可能是数据集相关，PageLink组件参数相同
    };
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_73rnip`}
        app="@weapp/components"
        compName="ButtonConfig"
        dataset={config?.dataset}
        onSure={this.onConfirm} // 确定回调，两个参数
        data={buttons}
        customBtnRender={this.externalElement} // 外部定义点击按钮
        page={page}
        positionParams={{
          positionKeys: [rightTopKeys, lineKeys],
          permissionConditionKeys: lineKeys,
          needBtnOperateType: true,
          operateTypeKeys: rightTopKeys,
          operateTypeHelpTip: getLabel('222255', '仅在按钮显示在表格 “右上角、右上角更多菜单或右上角批量操作菜单” 时生效'),
          positionData: positionData(),
        }}
        actionParams={{
          /** 事件动作的传参 */
          /** 如果需要限制某种按钮类型，得包一层对象，key随便取，内部得有showPositionKeys */
          params: {
            showPositionKeys: lineKeys,
            jsCodePlaceholder: listJsPlaceholder,
          },
          /** 如果不需要限制按钮类型，就全部写在otherParams内 */
          otherParams: {
            store,
            comId,
          },
          fieldFilter: this.fieldFilter,
        }}
      />
    );
  }
}
