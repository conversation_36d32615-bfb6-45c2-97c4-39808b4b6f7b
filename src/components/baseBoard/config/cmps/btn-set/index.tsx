/**
 * <AUTHOR>
 * @desc   : 按钮配置-new-暂时没做完-未来用这个
 */
import { CorsComponent, FormDatas } from '@weapp/ui';
import { getLabel, isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import ButtonEx from '../../../../common/buttonEx';
import { ajax } from '../../../../../utils/ajax';
import { DataSet } from '../../../../../types/common';
import { NotUseFields } from '../../../../../constants';
import NSettingIcon from '../../../../common/n-setting-icon';
import { positionData, listJsPlaceholder, getDefaultButtons, rightTopKeys, lineKeys } from './constants';

const isMockDataset = (dataset?: DataSet) => dataset?.type === 'mock';

interface CustomButtonsProps extends Attributes {
  config: any;
  comId: string;
  pageId: string;
  isIconBtn?: boolean;
  onChange?: (data: any) => void;
  onConfigChange?: (value?: FormDatas, act?: any) => void;
  externalElement?: (showModalFC: Function) => React.ReactNode;
  [_: string]: any;
}

@observer
class ButtonSet extends PureComponent<CustomButtonsProps, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      datasetBtns: [],
      buttons: [],
    };
  }

  componentDidMount() {
    // this.getButtonsByDataset();
    const { value = [] } = this.props;
    this.setState({ buttons: value });
  }

  // componentDidUpdate(preProps: any) {
  //   const { config } = this.props;
  //   if (!isEqual(preProps.config?.dataset, config?.dataset)) {
  //     this.getButtonsByDataset();
  //   }
  // }
  initial = () => {
    const { value = [] } = this.props;
    this.setState({ buttons: value });
  };

  // 待废弃
  getButtonsByDataset = () => {
    const { config, ebBusinessId } = this.props;
    // 模拟数据源，不继续请求接口
    const isMockData = isMockDataset(config?.dataset);
    if (config?.dataset && config?.dataset?.id && !isMockData) {
      const formData = new FormData();
      formData.append('type', 'Kanban');
      formData.append('dataset', JSON.stringify(config?.dataset));
      ajax({
        // url: '/api/ebuilder/coms/button/getButtonList',
        url: '/api/ebuilder/coms/button/getButtonListByType',
        method: 'post',
        data: formData,
        ebBusinessId,
        success: (res: any) => {
          this.setState({ datasetBtns: res || [] });
          // this.syncButtonConfig(res || []);
        },
      });
    }
  };
  /**
   * 废弃
   * 首次请求同步数据源的按钮同步到配置里面
   * 发现配置不一致 还是需要同步
   * @param btns
   */
  syncButtonConfig = (btns: any[]) => {
    const { config } = this.props;
    // 过滤掉表单视图/表格视图内的按钮 这些都是在table那边展示的 不在设计器内进行同步
    const validBtns = btns.filter((b: any) => b.buttonType !== 'CUSTOM');
    // 如果线上已经有错误数据了 打开的时候需要去同步下当前配置重新保存再刷新即可
    const failedData = config?.comButton.length ? config?.comButton.filter((i: any) => i.buttonType === 'CUSTOM') : [];
    if ((!config?.comButton?.length && validBtns.length) || failedData.length) {
      this.onConfirm(validBtns);
    }
  };

  clearAll = () => {
    this.props.onChange?.([]);
    this.setState({ buttons: [] });
  };

  externalElement = (showModalFC: any) => {
    const { externalElement, isIconBtn } = this.props;
    const { buttons } = this.state;
    if (externalElement) return externalElement(showModalFC);
    const hasBtn = buttons.filter((b: any) => b.enable).length;
    const _showModalFC = () => {
      this.initial();
      showModalFC();
    };
    if (isIconBtn) {
      return <NSettingIcon weId={`${this.props.weId || ''}_xkjenr`} hasValue={hasBtn} onClick={_showModalFC} clearable={hasBtn} clearFunc={this.clearAll} />;
    }
    return (
      <ButtonEx weId={`${this.props.weId || ''}_oliqbd`} action={hasBtn ? 'edit' : undefined} inline={false} onClick={_showModalFC}>
        {hasBtn ? getLabel('221908', '编辑按钮') : getLabel('221909', '配置按钮')}
      </ButtonEx>
    );
  };

  onConfirm = (btns: any) => {
    this.props.onChange?.(btns);
    this.setState({ buttons: btns });
  };

  fieldFilter = (field: any) => {
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id || field?.componentKey)) {
      return false;
    }
    return true;
  };

  render(): React.ReactNode {
    const { buttons = [] } = this.state;
    const { config, pageId, pageScope, appid, client, layoutInfo, store, comId } = this.props;
    const page = {
      id: pageId,
      module: pageScope,
      appid,
      client,
      datasetVals: layoutInfo?.datasetVals || [], // 可能是数据集相关，PageLink组件参数相同
    };
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_73rnip`}
        app="@weapp/components"
        compName="ButtonConfig"
        dataset={config?.dataset}
        onSure={this.onConfirm} // 确定回调，两个参数
        data={buttons}
        customBtnRender={this.externalElement} // 外部定义点击按钮
        page={page}
        positionParams={{
          positionKeys: [rightTopKeys, lineKeys],
          permissionConditionKeys: lineKeys,
          needBtnOperateType: true,
          operateTypeKeys: rightTopKeys,
          operateTypeHelpTip: getLabel('222255', '仅在按钮显示在表格 “右上角、右上角更多菜单或右上角批量操作菜单” 时生效'),
          positionData: positionData(),
        }}
        actionParams={{
          /** 事件动作的传参 */
          /** 如果需要限制某种按钮类型，得包一层对象，key随便取，内部得有showPositionKeys */
          params: {
            showPositionKeys: lineKeys,
            jsCodePlaceholder: listJsPlaceholder,
          },
          /** 如果不需要限制按钮类型，就全部写在otherParams内 */
          otherParams: {
            store,
            comId,
          },
          fieldFilter: this.fieldFilter,
        }}
        hideGroupBtn
      />
    );
  }
}

export default ButtonSet;
