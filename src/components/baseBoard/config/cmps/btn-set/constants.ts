import { getLabel } from '@weapp/utils';
import { ButtonType, ActionPositionType, ComCustomConstBtn } from '../../../../../constants/common';
import { UUID } from '../../../../../utils';

export const positionData = () => ([
  // {
  //   id: 'rightTop',
  //   title: getLabel('222250', '右上角'),
  //   img: 'button_position/list_right_top.svg',
  // },
  {
    id: 'rightTopDropDown',
    title: getLabel('222251', '右上角更多菜单'),
    img: 'button_position/list_right_top_more.svg',
  },
  // {
  //   id: 'rightTopBatch',
  //   title: getLabel('222252', '右上角批量操作菜单'),
  //   img: 'button_position/list_right_top_batch.svg',
  // },
  // {
  //   id: 'lineEnd',
  //   title: getLabel('222253', '行上'),
  //   img: 'button_position/list_row_out.svg',
  // },
  {
    id: 'LineEndDropDown',
    title: getLabel('222254', '行末下拉'),
    img: 'button_position/list_row_inner.svg',
  },
]);

export const listJsPlaceholder = ` // 可通过this对象获取行数据以及dom对象, this对象格式: {target: ..., data: {...}}
//获取行数据，比如该行数据为{id: 1, name: Joy, age: 18}
var data = this.data;     //data = {id: 1, name: Joy, age: 18}
var name = data.name;     //获取行数据字段为name的值, name=Joy
//获取按钮dom对象
var target = this.target; //target为按钮的dom对象
target.innerText='delete' //修改当前按钮名称为'delete'`;

// 列表右上角按钮keys
export const rightTopKeys = ['boardBottom', 'rightTop', 'rightTopDropDown', 'rightTopBatch'];
//  列表行上数据按钮keys
export const lineKeys = ['lineEnd', 'LineEndDropDown'];
export const getDefaultButtons = () => [
  {
    buttonType: 'COMCUSTOM',
    buttonKey: ComCustomConstBtn.ADDBUTTON,
    system: 1,
    id: `${UUID(16)}`,
    name: getLabel('54007', '新建'),
    enable: 0,
    onlyIcon: 0,
    scope: 'PC',
    btnPositionData: rightTopKeys,
    // showPosition: ['boardBottom'],
    actions: [],
    hideAction: true,
    permissions: [
      {
        content: '',
        contentSpan: '',
        id: `-${UUID()}`,
        levelScope: { minShowlevel: '', maxShowlevel: '' },
        permissionType: 'all',
        relation: '0',
      },
    ],
  }
]
