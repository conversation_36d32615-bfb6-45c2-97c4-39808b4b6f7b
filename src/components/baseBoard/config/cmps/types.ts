export interface PageSize {
  customPage?: boolean;
  pageNum: string;
  customPageNum?: string;
}
export type PageMode = PageModeType
export enum PageModeType {
  More = 'more',
  Scroll = 'scroll',
  None = 'none',
  page = 'page',
}
export interface MarkItemProps {
  id: string
  name: string
  color: string
  position: string
  filter: MarkFilterProps
}
export interface MarkConfig {
  vertical?: MarkItemProps[];
  corner?: MarkItemProps[];
}
export interface MarkFilterProps {
  [x: string]: any
}