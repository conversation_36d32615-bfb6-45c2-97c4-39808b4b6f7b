import { Input, Select, SelectOptionData } from '@weapp/ui';
import { getLabel, cloneDeep, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { ebdBClsPrefix } from '../../../../constants';
import { INIT_PAGE_SIZE_CONFIG, pageSizeData } from './constants';
import './index.less';


const cls = `${ebdBClsPrefix}-pageConfig-size`
const CUSTOM_MIN_SIZE = 1 // 最小页码
export interface PageSizeProps extends React.Attributes {
  config: any;
  onChange: (value: string, act?: any) => void;
  onConfigChange: (value: any, act?: any) => void;
}

export default class PageSize extends PureComponent<PageSizeProps> {
  state = {
    isCustom: false,
    pageSizeInfo: INIT_PAGE_SIZE_CONFIG(),
    popVisible: false
  };

  componentDidMount() {
    const { config: configData } = this.props;
    const config = cloneDeep(toJS(configData)) as any;
    const { pageSize } = config;
    let _pageConfig = isEmpty(pageSize) ? INIT_PAGE_SIZE_CONFIG() : pageSize
    const { customPage = false, customPageNum, pageNum } = _pageConfig;
    // 兼容没有 customPageSize的历史数据
    if (customPage && customPageNum === undefined) {
      _pageConfig = {
        customPageNum: pageNum,
        pageNum: pageNum
      }
      this.onChange(_pageConfig, true);
    } else {
      this.onChange(_pageConfig, false);
    }
  }
  onChange = (params: any, syncConfig?: boolean) => {
    const { onChange } = this.props;
    if (syncConfig) {
      onChange(params)
    }
    this.setState({pageSizeInfo: params})
  }
  onPageSizeChange = (value: any) => {
    const { pageSizeInfo } = this.state;
    // 直接选中了自定义项
    if (`${value}` === '-1') {
      // 最小值为1
      const cPageNum = !pageSizeInfo.customPageNum || pageSizeInfo.customPageNum === '-1' ? CUSTOM_MIN_SIZE : pageSizeInfo.customPageNum
      this.onCustomPageChange(cPageNum, 'sync')
      return;
    }
    this.onChange({
      ...pageSizeInfo,
      customPage: false,
      pageNum: value,
    }, true);
  };

  onCustomPageChange = (val: any, syncPage?: string) => {
    this.onChange({
      customPage: true,
      customPageNum: val,
      pageNum: val,
    }, syncPage === 'sync');
  };

  onSelectBlur = () => {
    this.setState({popVisible: false})
  }
  onBlur = () => {
    const { pageSizeInfo } = this.state;
    if (pageSizeInfo.customPage) {
      const val = pageSizeInfo.customPageNum || 1;
      this.onChange({
        pageNum: val.toString(),
        customPage: true,
        customPageNum: val.toString(),
      }, true);
      this.setState({popVisible: false})
    }
  };

  customOptionRender = (option: SelectOptionData) => {
    const { id, content } = option;
    const { pageSizeInfo } = this.state;
    if (id === '-1') {
      return (
        <div className={`${cls}-custom`}>
          <span>{getLabel('54002', '自定义')}</span>
          <Input.InputNumber
            weId={`${this.props.weId || ''}_6yi6cl`}
            align="right"
            min={1}
            max={200}
            defaultValue={Number(pageSizeInfo.customPageNum || CUSTOM_MIN_SIZE)}
            onChange={this.onCustomPageChange}
            onBlur={this.onBlur}
          />
        </div>
      );
    }
    return <span>{content}</span>;
  };

  render() {
    const { pageSizeInfo, popVisible } = this.state;
    const val = pageSizeInfo.customPage ? '-1' : pageSizeInfo.pageNum;
    return (
      <div className={cls}>
        <When weId={`${this.props.weId || ''}_sqikys`} condition={pageSizeInfo.customPage}>
          <span className={`${cls}-cover`}>{pageSizeInfo.customPageNum}</span>
        </When>
        <Select
          weId={`${this.props.weId || ''}_fq243g`}
          data={pageSizeData()}
          value={String(val)}
          onChange={this.onPageSizeChange}
          onBlur={this.onSelectBlur}
          placeholder=''
          onFocus={() => this.setState({popVisible: true})}
          customOptionRender={this.customOptionRender}
          style={{ width: '100%' }}
          triggerProps={{
            popupVisible: popVisible
          }}
        />
      </div>
    );
  }
}
