/**
 * 看板-角标竖线标识设置
 */
import React from 'react';
import { <PERSON>ton, Dialog, Table, ColorPicker, Layout, Checkbox, Icon, CorsComponent, Popover } from '@weapp/ui';
import { getLabel, isEmpty, cloneDeep, isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
import { toJS } from 'mobx';
import { ebdBClsPrefix, dlgIconName } from '../../../../constants';
import { UUID } from '../../../../utils';
import { DesignComProps } from '../../types';
import LocaleInput from '../../../common/locale/LocaleInput';
import { LocaleExValueDataType } from '../../../common/locale/types';
import configStore from '../store';
import { MarkItemProps } from './types';
import { INIT_MARK_CONFIG } from './constants';
import NSettingIcon from '../../../common/n-setting-icon';
import './index.less';

type MarkType = 'cornerMarkArr' | 'lineMark';
const { Row, Col } = Layout;
const cls = `${ebdBClsPrefix}-lineMarkSet`;
const diffKey = `lineMarkSet`;
// 角标信息转css
export const cornerToCls = (corner: MarkItemProps) => {
  if (isEmpty(corner)) return {};
  const { position, color } = corner;
  let borderCls = `6px solid ${color}`;
  let cornerCls = {};
  switch (position) {
    case 'rightTop':
      cornerCls = {
        right: 0,
        top: 0,
        borderTop: borderCls,
        borderRight: borderCls,
      };
      break;
    case 'rightBottom':
      cornerCls = {
        right: 0,
        bottom: 0,
        borderBottom: borderCls,
        borderRight: borderCls,
      };
      break;
    case 'leftTop':
      cornerCls = {
        left: 0,
        top: 0,
        borderTop: borderCls,
        borderLeft: borderCls,
      };
      break;
    case 'leftBottom':
      cornerCls = {
        left: 0,
        bottom: 0,
        borderBottom: borderCls,
        borderLeft: borderCls,
      };
      break;
    default:
      break;
  }
  return { position: 'absolute', width: 0, height: 0, border: '6px solid transparent', zIndex: 2, ...cornerCls };
};
// 竖线信息转css
export const verticalToCls = (vertical: MarkItemProps) => {
  if (isEmpty(vertical)) return {};
  const { position, color } = vertical;
  const cls = { background: color, [position]: 0, top: 0 };
  return { position: 'absolute', width: '4px', height: '100%', zIndex: 2, ...cls };
};
// 角标
export const renderCorner = (corners: MarkItemProps[] = []) => {
  const validCorners = corners.filter(i => i.position);
  if (isEmpty(validCorners)) return null;
  return validCorners.map(i => {
    const pop = <div style={{ wordWrap: 'break-word' }}>{i.name}</div>;
    return (
      <Popover weId={`2ig9r@`} popup={pop} placement={'top'} popoverType="tooltip" key={i.id}>
        <div style={cornerToCls(i)} />
      </Popover>
    );
  });
};
// 竖线
export const renderVertical = (verticals: MarkItemProps[] = []) => {
  const validVerticals = verticals.filter(i => i.position);
  if (isEmpty(validVerticals)) return null;
  return validVerticals.map(i => {
    const pop = <div style={{ wordWrap: 'break-word' }}>{i.name}</div>;
    return (
      <Popover weId={`9crzmx`} popup={pop} placement={'top'} popoverType="tooltip" key={i.id}>
        <div style={verticalToCls(i)} />
      </Popover>
    );
  });
};
const INITIAL_DATA = () => ({
  id: UUID(),
  name: '',
  color: '#000000',
  position: '',
  filter: null,
});
const INITIAL_DETAIL_DATA = () => ({
  currentData: {} as MarkItemProps,
  tableData: [] as MarkItemProps[],
  uniqueKey: UUID(),
  type: '',
});
const cornerMarkArr = [
  {
    id: 'rightTop',
    content: getLabel('255176', '看板右上角'),
  },
  {
    id: 'rightBottom',
    content: getLabel('255177', '看板右下角'),
  },
  {
    id: 'leftTop',
    content: getLabel('255178', '看板左上角'),
  },
  {
    id: 'leftBottom',
    content: getLabel('255179', '看板左下角'),
  },
];
const lineMarkArr = [
  {
    id: 'left',
    content: getLabel('255180', '看板左侧'),
  },
  {
    id: 'right',
    content: getLabel('255181', '看板右侧'),
  },
];
const INITIAL_STATE = () => ({
  visible: false,
  setDetailInfo: INITIAL_DETAIL_DATA(), // 详细设置数据
  menuValue: 'cornerMark' as MarkType,
  corner: [] as MarkItemProps[],
  vertical: [] as MarkItemProps[],
  selectedRowKeys: [] as string[],
  isInitial: false,
});

@observer
class LineMarkSet extends React.PureComponent<DesignComProps> {
  state = INITIAL_STATE();

  componentWillReceiveProps(nextProps: Readonly<DesignComProps>): void {
    const { config: _config } = nextProps;
    const { config } = this.props;
    // 切换了数据源
    if (!isEqual(_config.dataset, config.dataset) && _config.dataset && config.dataset) {
      this.setState({ ...INITIAL_STATE() });
    }
  }

  init = () => {
    const { dataset, markSetting = INIT_MARK_CONFIG() } = this.props.config;
    if (dataset) {
      configStore.getDataFields(dataset);
    }
    const { corner, vertical } = cloneDeep(markSetting);
    this.setState({ corner, vertical });
  };

  triggerShow = () => {
    this.setState(
      (prevState: any) => ({
        visible: !prevState.visible,
        selectedRowKeys: [],
      }),
      () => {
        const { visible } = this.state;
        if (visible) {
          this.init();
        }
      }
    );
  };

  onCancel = () => {
    this.setState({ visible: false });
  };
  onSave = () => {
    const { corner, vertical } = this.state;
    const { onConfigChange } = this.props;
    const payload = {
      corner,
      vertical,
    };
    // 触发配置更新
    onConfigChange && onConfigChange({ markSetting: { ...payload, uniqueKey: UUID() } });
    this.onCancel();
  };
  clearAll = () => {
    const { onConfigChange, onChange } = this.props;
    const payload = {
      corner: [],
      vertical: [],
    };
    onChange(payload);
  };
  onMenuChange = (value: string) => {
    this.setState({ menuValue: value });
  };
  onItemChange = (rowData: MarkItemProps, changeType: keyof MarkItemProps, changeValue: any) => {
    const { menuValue, corner, vertical } = this.state;
    if (menuValue === 'lineMark') {
      const idx = vertical.findIndex(i => i.id === rowData.id);
      vertical[idx][changeType] = changeValue;
    } else {
      const idx = corner.findIndex(i => i.id === rowData.id);
      corner[idx][changeType] = changeValue;
    }
    this.setState({ vertical, corner, uniqueKey: UUID() });
  };
  showDetailSet = (type: 'position' | 'filter', data: MarkItemProps) => {
    const { menuValue, corner, vertical } = this.state;
    const tableData = menuValue === 'lineMark' ? vertical : corner;
    this.setState({ setDetailInfo: { currentData: data, tableData, type } });
  };
  getTableKey = () => {
    const { menuValue } = this.state;
    const key = menuValue === 'lineMark' ? 'vertical' : 'corner';
    return key;
  };
  getTableData = () => {
    return this.state[this.getTableKey()];
  };
  getPositionData = () => {
    const { menuValue } = this.state;
    return menuValue === 'lineMark' ? lineMarkArr : cornerMarkArr;
  };
  addData = () => {
    const key = this.getTableKey();
    this.setState((prevState: any) => ({
      [key]: [...prevState[key], INITIAL_DATA()],
    }));
  };
  delData = () => {
    const { selectedRowKeys } = this.state;
    const tableKey = this.getTableKey();
    const tableData = this.getTableData();
    const newData = tableData.filter((item: MarkItemProps) => !selectedRowKeys.includes(item.id));
    this.setState({ [tableKey]: newData, selectedRowKeys: [] });
  };
  onSelect = (selectedRowKeys: string[]) => {
    this.setState({ selectedRowKeys });
  };
  renderMainDialog = () => {
    const { visible, menuValue, selectedRowKeys } = this.state;
    const mainCls = `${cls}-mainDialog`;
    const ebBusinessId = '';
    const tableData = this.getTableData();
    const positionData = this.getPositionData();
    const buttons = [
      <Button weId={`${this.props.weId || ''}_j3ydqm@${diffKey}`} key="save" type="primary" onClick={this.onSave}>
        {getLabel('40496', '保存')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_dy1mds@${diffKey}`} key="onCancel" onClick={this.onCancel}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const menuData = [
      {
        id: 'cornerMark',
        content: getLabel('255182', '角标设置'),
      },
      {
        id: 'lineMark',
        content: getLabel('255183', '竖线标识设置'),
      },
    ];
    const columns = [
      {
        dataIndex: 'name',
        title: getLabel('53866', '名称'),
        bodyRender: (data: MarkItemProps) => {
          return (
            <LocaleInput
              weId={`${this.props.weId || ''}_7hc9gt@${diffKey}`}
              ebBusinessId={ebBusinessId}
              placeholder={getLabel('56234', '请输入')}
              value={data.name}
              // readOnly={isReadOnly}
              onChange={(inputValue: LocaleExValueDataType) => this.onItemChange(data, 'name', inputValue)}
              // onSave={onBlur}
              // onDialogClose={onClose}
              required
              inputProps={{
                maxLength: 50,
              }}
            />
          );
        },
      },
      {
        dataIndex: 'color',
        title: getLabel('55013', '颜色'),
        bodyRender: (data: MarkItemProps) => {
          return <ColorPicker weId={`${this.props.weId || ''}_v3zftk@${diffKey}`} onChange={(value: string) => this.onItemChange(data, 'color', value)} value={data.color} />;
        },
      },
      {
        dataIndex: 'position',
        title: getLabel('54603', '显示位置'),
        bodyRender: (data: MarkItemProps) => {
          const item = positionData.find(i => i.id === data.position);
          return (
            <div className={`${mainCls}-table-block`}>
              <Button weId={`${this.props.weId || ''}_tn9831@${diffKey}`} type="link" onClick={() => this.showDetailSet('position', data)}>
                {item?.content || getLabel('40502', '请选择')}
              </Button>
            </div>
          );
        },
      },
      {
        dataIndex: 'filter',
        title: getLabel('109838', '条件设置'),
        bodyRender: (data: MarkItemProps) => {
          return (
            <div className={`${mainCls}-table-block`}>
              <Button weId={`${this.props.weId || ''}_k2xxuh@${diffKey}`} type="link" onClick={() => this.showDetailSet('filter', data)}>
                {isEmpty(data.filter) ? getLabel('56776', '添加条件') : getLabel('56775', '编辑条件')}
              </Button>
            </div>
          );
        },
      },
    ];
    const renderTable = () => {
      return (
        <Table
          weId={`${this.props.weId || ''}_7lkws7`}
          columns={columns}
          data={tableData}
          isShowIndex={true}
          isShowIndexInCheck
          selection={{
            selectedRowKeys,
            isBatchSelect: true,
            onSelect: this.onSelect,
          }}
        />
      );
    };
    const addContent = () => {
      return (
        <div className={`${mainCls}-add`}>
          <div className={`${mainCls}-add-left`}>{menuValue === 'lineMark' ? getLabel('255183', '竖线标识设置') : getLabel('255182', '角标设置')}</div>
          <div className={`${mainCls}-right`}>
            {<Icon weId={`${this.props.weId || ''}_zan2s7`} name="Icon-add-to03" onClick={this.addData} size="md" />}
            {!isEmpty(selectedRowKeys) && <Icon weId={`${this.props.weId || ''}_bflck6`} name="Icon-Batch-delete" onClick={this.delData} size="md" />}
          </div>
        </div>
      );
    };
    return (
      <Dialog
        weId={`${this.props.weId || ''}_o5a61z`}
        footer={buttons}
        menus={menuData}
        menuValue={menuValue}
        onMenuChange={this.onMenuChange}
        width={700}
        destroyOnClose
        visible={visible}
        title={getLabel('255197', '标识设置')}
        mask
        closable
        onClose={this.onCancel}
        icon={dlgIconName}
        wrapClassName={mainCls}
      >
        {addContent()}
        {renderTable()}
      </Dialog>
    );
  };
  // 标识显示位置设置弹窗(角标+竖线)
  renderMarkDialog = () => {
    const { setDetailInfo, menuValue } = this.state;
    if (isEmpty(setDetailInfo.currentData) || setDetailInfo.type !== 'position') return null;
    let _setDetailInfo = cloneDeep(setDetailInfo);
    const { currentData, tableData } = _setDetailInfo;
    const onCancel = () => {
      this.setState({ setDetailInfo: INITIAL_DETAIL_DATA() });
    };
    const onSave = () => {
      const key = this.getTableKey();
      this.setState({ [key]: tableData });
      onCancel();
    };
    const onChange = (val: string) => {
      const idx = tableData.findIndex(i => i.id === currentData.id);
      tableData[idx].position = val;
      _setDetailInfo = {
        ...setDetailInfo,
        tableData,
        uniqueKey: UUID(),
      };
      this.setState({ setDetailInfo: _setDetailInfo });
    };
    const buttons = [
      <Button weId={`${this.props.weId || ''}_honzje@${diffKey}`} key="save" type="primary" onClick={onSave}>
        {getLabel('221901', '确定')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_20adu2@${diffKey}`} key="onCancel" onClick={onCancel}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const mapArr = menuValue === 'lineMark' ? lineMarkArr : cornerMarkArr;
    const mapKey = menuValue === 'lineMark' ? 'line' : 'corner';
    const item = tableData.find(i => i.id === currentData.id);
    if (!item) return null;

    const pCls = `${cls}-mark-dialog`;
    const renderContent = () => {
      return (
        <Row weId={`${this.props.weId || ''}_r5u39p`}>
          {mapArr.map(i => {
            const img = require(`../../../../../public/images/markAndLine/board_${mapKey}_${i.id}.png`).default || '';
            return (
              <Col weId={`${this.props.weId || ''}_qqj4nb@${diffKey}`} span={8} key={i.id} onClick={() => onChange(i.id)}>
                {
                  <div className={`${pCls}-item`}>
                    <div className={`${pCls}-item-title`}>
                      <Checkbox weId={`${this.props.weId || ''}_ury11l@${diffKey}`} value={item?.position === i.id} />
                      <div className={`${pCls}-item-title-text`}>{i.content}</div>
                    </div>
                    <img src={img} alt={''} className={`${pCls}-item-img`} />
                  </div>
                }
              </Col>
            );
          })}
        </Row>
      );
    };
    return (
      <Dialog
        weId={`${this.props.weId || ''}_soz2gj`}
        footer={buttons}
        width={552}
        destroyOnClose
        visible
        title={getLabel('56761', '按钮显示位置')}
        mask
        closable
        onClose={onCancel}
        icon={dlgIconName}
        wrapClassName={pCls}
      >
        {renderContent()}
      </Dialog>
    );
  };
  // 标识过滤条件设置弹窗(角标+竖线)
  renderFilterDialog = () => {
    const { setDetailInfo } = this.state;
    const { config } = this.props;
    const { dataset } = config;
    if (isEmpty(setDetailInfo.currentData) || setDetailInfo.type !== 'filter') return null;
    let _setDetailInfo = cloneDeep(setDetailInfo);
    const { currentData, tableData } = _setDetailInfo;
    const onCancel = () => {
      this.setState({ setDetailInfo: INITIAL_DETAIL_DATA() });
    };
    const onSave = (val: any) => {
      const idx = tableData.findIndex(i => i.id === currentData.id);
      tableData[idx].filter = val;
      const key = this.getTableKey();
      this.setState({ [key]: tableData });
      onCancel();
    };
    const item = tableData.find(i => i.id === currentData.id);
    if (!item) return null;

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_rtv1f2`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        value={item.filter}
        onOk={onSave}
        onCancel={onCancel}
        onClear={() => onSave({})}
        visible
        title={getLabel('161820', '占用标识条件')}
        dataSet={dataset}
        showFilterType // 是否展示sql和自定义接口筛选类型
      />
    );
  };

  render() {
    const { isIconBtn, value } = this.props;
    const hasValue = !isEmpty(value?.corner) || !isEmpty(value?.vertical);
    return (
      <div className={cls}>
        {isIconBtn ? (
          <NSettingIcon weId={`${this.props.weId || ''}_xlxwfc`}  hasValue={hasValue} onClick={this.triggerShow} clearable={hasValue} clearFunc={this.clearAll} />
        ) : (
          <Button weId={`${this.props.weId || ''}_geanh8`} type="default" className={`${ebdBClsPrefix}-board-config-btn`} onClick={this.triggerShow}>
            <span>{getLabel('255197', '标识设置')}</span>
          </Button>
        )}
        {this.renderMainDialog()}
        {this.renderMarkDialog()}
        {this.renderFilterDialog()}
      </div>
    );
  }
}

export default LineMarkSet;
