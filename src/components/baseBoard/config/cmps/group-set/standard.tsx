/**
 * 平铺形式的分组设置
 */
import { useState, useEffect, useMemo, useCallback } from 'react';
import { Layout, Select, Switch, Dialog } from '@weapp/ui';
import { toJS } from 'mobx';
import { getLabel, isEmpty } from '@weapp/utils';
import { TitleText } from '../../../engine/func-groups/Common';
import { ebdBClsPrefix } from '../../../../../constants';
import { GroupType } from '../../../../../constants/common';
import { EtComponentKey, SelectType } from '../../../../../constants/EtComponent';
import { RelationGroupsType } from '../../../engine/func-groups/diff/constants';
import { isEbFormDataV2, isLogicData, isEteamsData } from '../../../../common/DataSet/utils';
import FormGroupSet from './form-group-set';
import { switchGroupOptions, buildGroupOptions} from './utils';
import { saveGroupOptions, getBackBoardInfo, getFields } from './ajaxUtil';
import { DesignComProps } from '../../../types';
import { GroupDataProps } from '../../../engine/func-groups/types';
import { GroupsTypeType, groups } from '../../../engine/func-groups/constants';
import NSettingIcon from '../../../../common/n-setting-icon';
import { deepToJS } from '../../../../../utils';
import { checkGroupWayHasValue } from './form-group-set/utils';
import './style/index.less';

const { Row, Col } = Layout;
const { message } = Dialog;
const leftSpan = 9;
const cls = `${ebdBClsPrefix}-form-group-set`;

const GroupSet = (props: DesignComProps) => {
  const [isShowGroupSet, setIsShowGroupSet] = useState(false);
  const [datasetMap, setDatasetMap] = useState([]);
  // const [idParams, setIdParams] = useState({} as IdParams);
  const { config, onChange, onConfigChange, appid = '', comServicePath, pageId = '', comId, value } = props;

  const { active } = value;

  useEffect(() => {
    const _getDatasetMap = async () => {
      const res = await getFields(config?.dataset);
      setDatasetMap(res);
    };
    _getDatasetMap();
  }, [config?.dataset]);

  // useEffect(() => {
  //   const _getBackBoardInfo = async () => {
  //     const res = await getBackBoardInfo(id, appid);
  //     console.log('res', res);
  //     setIdParams(res);
  //   };
  //   _getBackBoardInfo();
  // }, [idParams]);
  const idParams = useMemo(() => {
    return {
      appId: appid!,
      pageId,
      compId: comId!,
      objId: '', // 表单高级视图才有
      viewId: pageId,
    };
  }, [appid, pageId, comId]);

  // 兼容下差异化 100 101都处理成5
  const activeGroupId = useMemo(() => {
    if (`${active}` === RelationGroupsType.Normal || `${active}` === RelationGroupsType.Custom) {
      return GroupType.diff;
    }
    return `${active}`;
  }, [active, value]);

  const isEteams = useCallback(() => {
    return props.config?.dataset?.type === 'ETEAMS';
  }, [props.config?.dataset?.type]);

  const getGroupHelpInfo = () => {
    // *数据加工数据源排除按名称分组提示
    const helpInfos = [
      getLabel('278342', '按名称分组：按固定分组名称进行分组，在前端使用时可以新建分组'),
      getLabel('278343', '按字段分组：支持选择选项控件和关联e-builder控件，按选项值进行分组'),
      getLabel('278344', '按日期分组：根据系统日期和日期字段进行分组'),
      getLabel('288806', '按条件分组：根据设置的条件进行分组'),
      // getLabel('291688', '按数据源分组：根据设置的数据源以及关联其他数据源的字段进行分组'),
    ];
    return isEteams() ? helpInfos.slice(1) : helpInfos;
  };
  const getGroups = () => {
    let _groups = groups();
    // 非表单数据源不显示自定义分组
    // 我们自定义分组后 我们看板的数据有拖动排序的功能  我们这边实现的话就是用临时的一张eb的中间表去实现的 数据加工没有
    // if (!isEbFormDataV2(config)) {
    //   _groups = _groups.filter((i: any) => i.id !== GroupType.custom);
    // }
    // 业务数据源和数仓不显示差异化分组
    if (isLogicData(config?.dataset) || isEteamsData(config?.dataset)) {
      _groups = _groups.filter((i: any) => i.id !== GroupType.diff);
    }
    return _groups;
  };
  // 非泳道才有--直接用value 不用propValue
  const switchGroup = (id: GroupType) => {
    let newValue = { ...value, options: switchGroupOptions(deepToJS(value.options), id), active: `${id}` };
    // 差异化分组没有值需要补充默认值
    if (`${id}` === GroupType.diff) {
      const originDiff = value.options.filter((i: any) => `${i.groupType}` === RelationGroupsType.Normal || `${i.groupType}` === RelationGroupsType.Custom);
      if (isEmpty(originDiff)) {
        newValue = {
          ...newValue,
          dataOptions: [],
          active: RelationGroupsType.Normal,
          rootField: '',
        };
      } else {
        const lastDiff = originDiff[originDiff.length - 1];
        if (lastDiff) {
          newValue.active = lastDiff.groupType;
        }
      }
      // 看板分组类型为差异化
      newValue.dataGroup = GroupsTypeType?.releationField;
    } else {
      // 兼容差异化 非差异化不需要dataGroup
      delete newValue.dataGroup;
    }
    onChange?.(newValue);
  };
  const showGroupSet = () => {
    if (!config?.dataset?.id) {
      message({
        type: 'info',
        content: `${getLabel('261805', '请先设置数据源')}!`,
      });
      return;
    }
    setIsShowGroupSet(true);
  };
  const closeGroupSet = useCallback(() => {
    setIsShowGroupSet(false);
  }, []);

  const confirmGroupSet = useCallback(
    async (value: (GroupDataProps & { isDiff?: boolean }), cb?: Function) => {
      try {
        const { active } = value;
        const isDiff = `${active}` === RelationGroupsType.Custom || `${active}` === RelationGroupsType.Normal;
        // 差异化分组内部组件自己处理
        const newGroupData = isDiff ? value : buildGroupOptions(config, value as GroupDataProps & { isDiff?: boolean }, false);
        const res = await saveGroupOptions(config, newGroupData, comServicePath, idParams);
        onChange && onChange(res);
        closeGroupSet();
      } catch (error) {
        console.error(error);
      } finally {
        cb && cb();
      }
    },
    [config, comServicePath, idParams, onChange, closeGroupSet]
  );

  const optionField = useMemo(() => {
    // *数据加工数据源排除人员类型及不校验multiSelect
    // *数仓的人员字段分组放开也选不到   能选到的人员字段必须是单选的   数仓那边的人员字段固定是多选，没法设置单选
    const eteamsFilter = (item: any) => {
      if (!isEteams()) return true;
      return (item?.compType || item?.type) !== EtComponentKey.Employee;
    };
    const normalFilter = (item: any) => {
      return `${item?.multiSelect}` === 'false' || !item?.multiSelect;
    };
    return datasetMap
      .filter(
        (data: any) =>
          (SelectType.includes(data.compType) || SelectType.includes(data.type)) &&
          eteamsFilter(data) &&
          normalFilter(data) &&
          data?.compTypec !== 'ComboSelect' &&
          !['8', '9', '10', '11', '12', '13', '14', '15', '16'].includes(data?.id) //数据状态、流程状态等系统字段也是Select类型，需要屏蔽下
      )
      .map((data: any) => ({ id: data.id, content: data.text, type: data.compType || data.type }));
  }, [datasetMap, isEteams]);

  // 先不介入高级视图
  const isFromEbAdvance = false;

  const renderGroupSet = useCallback(() => {
    return (
      <FormGroupSet
        weId={`${props.weId || ''}_xqf1ds`}
        {...props}
        optionField={optionField}
        fields={datasetMap}
        isFromEbAdvance={isFromEbAdvance}
        onClose={closeGroupSet}
        onOk={confirmGroupSet}
        activeGroupId={activeGroupId}
      />
    );
  }, [datasetMap, isFromEbAdvance, optionField, confirmGroupSet, props, closeGroupSet, activeGroupId]);

  return (
    <div className={cls}>
      <Row weId={`${props.weId || ''}_jexmm0`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_3v3ry7`} span={leftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_ggvyti`} title={getLabel('186579', '分组方式')} helpInfo={getGroupHelpInfo()} placement="leftTop" />
        </Col>
        <Col weId={`${props.weId || ''}_6yf3x4`} className={`${cls}-item-right`}>
          <Select weId={`${props.weId || ''}_ioes3h`} data={getGroups()} value={activeGroupId} onChange={value => switchGroup(value as GroupType)} style={{ width: 130 }} />
        </Col>
      </Row>
      <Row weId={`${props.weId || ''}_jexmm0`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_3v3ry7`} span={leftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_ggvyti`} title={getLabel('54215', '分组设置')} helpInfo={getGroupHelpInfo()} placement="leftTop" />
        </Col>
        <Col weId={`${props.weId || ''}_6yf3x4`} className={`${cls}-item-right`}>
          <NSettingIcon weId={`${props.weId || ''}_iegtlp`} onClick={showGroupSet} hasValue={checkGroupWayHasValue(value)} />
        </Col>
      </Row>
      {isShowGroupSet && renderGroupSet()}
    </div>
  );
};

export default GroupSet;
