import { isEmpty } from '@weapp/utils';
import { EtComponentKey } from '../../../../../constants/EtComponent';
import { ajax, format2FormData } from '../../../../../utils/ajax';
import { DataSetItem } from '../../../../common/DataSet/types';
import ebdcoms from '../../../../../utils/ebdcoms';
import { IdParams } from '../../../engine/types';
import { BoardConfigData } from '../../../types';
import { GroupDataProps, selectedFieldProps } from '../../../engine/func-groups/types';
import { isEbFormDataV2, isEteamsData, isLogicData } from '../../../../common/DataSet/utils';
import { GroupType } from '../../../../../constants/common';

export const getFields = async (dataset?: DataSetItem) => {
  if (!dataset) {
    return [];
  }
  const { dsUtils } = await ebdcoms.get();
  const fieldData = (await dsUtils.getFields(dataset, '', false, false)) || [{ fields: [] }];
  return isEmpty(fieldData) ? [] : fieldData[0].fields.map((i: any) => ({ ...i, id: i.id, content: i.text }));
};

export const getBackBoardInfo = async (boardId: string, appId: string) => {
  const datas: IdParams = await ajax({
    method: 'GET',
    url: `/api/bs/ebuilder/form/kanban/getInfo?apid=${appId}`,
    params: {
      kanbanId: boardId,
    },
    ebBusinessId: appId,
  });
  const { objId, pageId, viewId, compId } = datas;
  return {
    objId,
    pageId,
    viewId,
    appId,
    compId,
  };
};
export const getCompanyAndSeclevel = async (appId: string) => {
  try {
    const res = await ajax({
      url: `/api/bs/ebuilder/form/obj/companyAndSeclevel?apid=${appId}`,
      ebBusinessId: appId,
      error: (info: any) => {},
    });
    return {
      supportCompany: res.supportCompany || false,
      supportSeclevel: res.supportSeclevel || false,
    };
  } catch {
    return {
      supportCompany: false,
      supportSeclevel: false,
    };
  }
};
export const saveGroupOptions = async (config: BoardConfigData, groupData: GroupDataProps, comServicePath: string = 'ebuilder/coms', idParams: IdParams) => {
  // !2024-03-21 逻辑更新：表单数据源和无数据源传入场景下，保存配置到看板配置
  // !20241201基线新增条件分组 条件分组可支持saveKanbanOption 不限定数据源
  // !20250313新增数仓、业务数据源下自定义分组保存分组支持走saveKanbanOption
  const isScAndLogicDataSetCustomOptions = (isEteamsData(config.dataset) || isLogicData(config.dataset)) && `${groupData.active}` === GroupType.custom;
  if (isEbFormDataV2({ dataset: config.dataset }) || `${groupData.active}` === GroupType.filter || !config?.dataset?.type || isScAndLogicDataSetCustomOptions) {
    const res = await ajax({
      url: `/api/${comServicePath}/kanban/saveKanbanOption`,
      method: 'post',
      data: format2FormData({
        compId: idParams.compId,
        pageId: idParams.pageId,
        extParam: JSON.stringify(groupData),
      }),
      ebBusinessId: idParams.pageId,
      error: () => {},
    });
    return res.result;
  }
  // *如果是非eb数据源 直接保存配置到config的group
  return groupData;
};
