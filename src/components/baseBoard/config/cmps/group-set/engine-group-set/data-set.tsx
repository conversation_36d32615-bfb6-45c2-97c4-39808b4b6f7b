// 高级视图  看板数据配置
import { Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { dlgIconName } from '../../../../../../constants';

const DataSet = (props: any) => {
  const { onCancel } = props;
  return (
    <Dialog
      // footer={buttons}
      width={500}
      destroyOnClose
      visible
      title={getLabel('311430', '看板数据设置')}
      mask
      closable
      onClose={onCancel}
      icon={dlgIconName}
    >
      123
    </Dialog>
  );
};

export default DataSet;
