/**
 * 平铺形式的分组设置
 */
import { useState, useEffect, useMemo, useCallback } from 'react';
import { Layout, Select, Switch } from '@weapp/ui';
import { getLabel, isEmpty, debounce } from '@weapp/utils';
import { TitleText } from '../../../engine/func-groups/Common';
import { ebdBClsPrefix } from '../../../../../constants';
import { GroupType } from '../../../../../constants/common';
import { EtComponentKey, SelectType } from '../../../../../constants/EtComponent';
import { RelationGroupsType } from '../../../engine/func-groups/diff/constants';
import FormGroupSet from './form-group-set';
import { getFields } from './ajaxUtil';
import { DesignComProps } from '../../../types';
import NSettingIcon from '../../../../common/n-setting-icon';
import { deepToJS } from '../../../../../utils';
import { INIT_YD_OUTER_CONFIG } from '../constants';
import { LaneGroupProps } from '../yd/types';
import GroupStatistics from '../../GroupStatistics';
import { LANE_GROUP_TYPE } from '../constants';
import './style/index.less';

const { Row, Col } = Layout;
const leftSpan = 9;
const cls = `${ebdBClsPrefix}-form-group-set`;

interface LaneGroupSetProps extends DesignComProps {}
const LaneGroupSet = (props: LaneGroupSetProps) => {
  const [isShowGroupSet, setIsShowGroupSet] = useState(false);
  const [datasetMap, setDatasetMap] = useState([]);
  const { config, onChange, onConfigChange, appid = '', comServicePath, pageId = '', comId, value } = props;
  const propValue = isEmpty(value) ? INIT_YD_OUTER_CONFIG() : value;


  const { active } = propValue?.laneGroup || {};

  useEffect(() => {
    const _getDatasetMap = async () => {
      const res = await getFields(config?.dataset);
      setDatasetMap(res);
    };
    _getDatasetMap();
  }, [config?.dataset]);

  const idParams = useMemo(() => {
    return {
      appId: appid!,
      pageId,
      compId: comId!,
      objId: '', // 表单高级视图才有
      viewId: pageId,
    };
  }, [appid, pageId, comId]);

  // 兼容下差异化 100 101都处理成5
  const activeGroupId = useMemo(() => {
    if (active === RelationGroupsType.Normal || active === RelationGroupsType.Custom) {
      return GroupType.diff;
    }
    return active;
  }, [active]);

  const isEteams = useCallback(() => {
    return props.config?.dataset?.type === 'ETEAMS';
  }, [props.config?.dataset?.type]);

  const showGroupSet = () => {
    setIsShowGroupSet(true);
  };
  const closeGroupSet = useCallback(() => {
    setIsShowGroupSet(false);
  }, []);

  const confirmGroupSet = useCallback(
    async (_value: LaneGroupProps, cb?: Function) => {
      const newLaneConfig = deepToJS({
        ...value,
        laneGroup: _value,
      });
      onChange && onChange(newLaneConfig);
      cb && cb(newLaneConfig);
      closeGroupSet();
    },
    [onChange, closeGroupSet]
  );

  const optionField = useMemo(() => {
    // *数据加工数据源排除人员类型及不校验multiSelect
    // *数仓的人员字段分组放开也选不到   能选到的人员字段必须是单选的   数仓那边的人员字段固定是多选，没法设置单选
    const eteamsFilter = (item: any) => {
      if (!isEteams()) return true;
      return (item?.compType || item?.type) !== EtComponentKey.Employee;
    };
    const normalFilter = (item: any) => {
      return `${item?.multiSelect}` === 'false' || !item?.multiSelect;
    };
    return datasetMap
      .filter(
        (data: any) =>
          (SelectType.includes(data.compType) || SelectType.includes(data.type)) &&
          eteamsFilter(data) &&
          normalFilter(data) &&
          data?.compTypec !== 'ComboSelect' &&
          !['8', '9', '10', '11', '12', '13', '14', '15', '16'].includes(data?.id) //数据状态、流程状态等系统字段也是Select类型，需要屏蔽下
      )
      .map((data: any) => ({ id: data.id, content: data.text, type: data.compType || data.type }));
  }, [datasetMap, isEteams]);

  const hasYdGroupConfig = useMemo(() => {
    return value?.laneGroup?.field?.id || !isEmpty(value?.laneGroup?.conditionConfig);
  }, [value?.laneGroup]);

  // 先不介入高级视图
  const isFromEbAdvance = false;

  const renderGroupSet = useCallback(() => {
    return (
      <FormGroupSet
        weId={`${props.weId || ''}_xqf1ds`}
        {...props}
        optionField={optionField}
        fields={datasetMap}
        isFromEbAdvance={isFromEbAdvance}
        onClose={closeGroupSet}
        onOk={confirmGroupSet}
        activeGroupId={activeGroupId}
        isYd
      />
    );
  }, [datasetMap, isFromEbAdvance, optionField, confirmGroupSet, props, closeGroupSet]);
  const changeYdConfig = (type: string, value: any) => {
    if (type === 'active') {
      // 清掉已有数据
      onConfigChange?.(deepToJS({ laneConfig: { ...propValue, laneGroup: { [type]: value } } }));
    } else {
      onConfigChange?.(deepToJS({ laneConfig: { ...propValue, [type]: value } }));
    }
  };

  const debounceChangeYdConfig = debounce(changeYdConfig, 1000);

  return (
    <div className={cls}>
      <Row weId={`${props.weId || ''}_furt0n`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_2l5whh`} span={leftSpan} className={`${cls}-item-left`}>
          <TitleText
            weId={`${props.weId || ''}_ozpvb4`}
            title={getLabel('-1', '开启泳道')}
            helpInfo={[getLabel('291682', '开启后，看板组件显示二维横向泳道，移动端暂不支持显示泳道')]}
            placement="leftTop"
          />
        </Col>
        <Col weId={`${props.weId || ''}_03rhmn`} className={`${cls}-item-right`}>
          <Switch weId={`${props.weId || ''}_jpsr6i`} value={value.status} onChange={value => changeYdConfig('status', value)} />
        </Col>
      </Row>
      <Row weId={`${props.weId || ''}_yyu9z2`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_mzvowj`} span={leftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_7hsq3k`} title={getLabel('-1', '泳道分组方式')} />
        </Col>
        <Col weId={`${props.weId || ''}_guh7o5`} className={`${cls}-item-right`}>
          <Select weId={`${props.weId || ''}_xybvta`} data={LANE_GROUP_TYPE()} value={value.laneGroup?.active} onChange={value => changeYdConfig('active', value)} style={{ width: 130 }} />
        </Col>
      </Row>
      <Row weId={`${props.weId || ''}_5u7g2x`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_43po9r`} span={leftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_7hsq3k`} title={getLabel('-1', '泳道设置')} />
        </Col>
        <Col weId={`${props.weId || ''}_2t7qs2`} className={`${cls}-item-right`}>
          <NSettingIcon weId={`${props.weId || ''}_iegtlp`} onClick={showGroupSet} hasValue={hasYdGroupConfig} clearFunc={() => changeYdConfig('laneGroup', {})} />
        </Col>
      </Row>
      <GroupStatistics
        weId={`${props.weId || ''}_symvg0`}
        {...props}
        value={props.config?.laneConfig?.laneStat}
        config={{ ...props.config, statistics: props.config?.laneConfig?.laneStat }}
        onChange={value => debounceChangeYdConfig('laneStat', value)}
        statisticsData={props.config?.laneConfig?.laneStat?.statisticsItem || []}
        useForm
      />
      {isShowGroupSet && renderGroupSet()}
    </div>
  );
};

export default LaneGroupSet;
