import { isEmpty, trim, getLabel, forEach } from '@weapp/utils';
import { EtComponentKey, SelectType } from '../../../../../constants/EtComponent';
import { GroupType } from '../../../../../constants/common';
import { GroupDataProps } from '../../../engine/func-groups/types';
import { RelationGroupsType } from '../../../engine/func-groups/diff/constants';
import { DfRelationGroupData, GroupsTypeType } from '../../../engine/func-groups/constants';
import { deepToJS, isEbRelationBrowser } from '../../../../../utils';
import { BoardConfigData } from '../../../types';
import { INITIAL_DATE_GROUP_INFO, INITIAL_CUSTOM_GROUP_INFO, INITIAL_Field_GROUP_INFO, INITIAL_FILTER_GROUP_INFO, INITIAL_DIFF_GROUP_INFO } from '../../../engine/func-groups/constants';
import { isEbFormDataV2, isUnEbDataSetCustomOptions } from '../../../../common/DataSet/utils';

interface GroupOption {
  groupType: GroupType | RelationGroupsType;
  enableColor?: boolean;
  values?: any[];
  fieldId?: string;
  fieldType?: string;
  dateRange?: string;
}

type GroupOptionsMap = {
  [key in GroupType & RelationGroupsType]: GroupOption;
};

/**
 * 初始化/切换分组行为构建新分组
 * @param options 选项
 * @returns 分组选项
 */
export const switchGroupOptions = (options: GroupOption[] = [], active: GroupType) => {
  const groupOptionsMap: GroupOptionsMap = {
    [GroupType.custom]: {
      groupType: GroupType.custom,
      enableColor: false,
      values: [],
    },
    [GroupType.field]: {
      groupType: GroupType.field,
      fieldId: '',
      fieldType: '',
    },
    [GroupType.dateRange]: {
      groupType: GroupType.dateRange,
      fieldId: '',
      dateRange: '',
    },
    [GroupType.filter]: {
      groupType: GroupType.filter,
      enableColor: false,
      values: [],
    },
    [GroupType.diff]: {
      groupType: RelationGroupsType.Normal,
      enableColor: false,
      values: DfRelationGroupData().map((i: any) => {
        const { id, ...rest } = i;
        return rest;
      }),
    },
    [RelationGroupsType.Normal]: {
      groupType: RelationGroupsType.Normal,
      enableColor: false,
      values: DfRelationGroupData().map((i: any) => {
        const { id, ...rest } = i;
        return rest;
      }),
    },
    [RelationGroupsType.Custom]: {
      groupType: RelationGroupsType.Custom,
      enableColor: false,
      values: DfRelationGroupData().map((i: any) => {
        const { id, ...rest } = i;
        return rest;
      }),
    },
    [GroupType.dataset]: {
      groupType: GroupType.dataset,
      enableColor: false,
      values: [],
    },
    [GroupType.lane]: {
      groupType: GroupType.lane,
      enableColor: false,
      values: [],
    },
  };

  const isDiff = `${active}` === RelationGroupsType.Custom || `${active}` === RelationGroupsType.Normal || `${active}` === GroupType.diff;
  const hasActiveGroup = isDiff || options.some(i => `${i.groupType}` === `${active}`);
  if (!hasActiveGroup && active in groupOptionsMap) {
    return [...options, groupOptionsMap[active as keyof GroupOptionsMap]];
  }
  return options;
};
/**
 * 初始化/切换分组行为构建新分组
 * @param config 当前配置
 * @param groupData 分组数据
 * @param selectedField 下拉字段
 * @param isFromEbAdvance 是否是表单高级视图
 * @param diffOptions 差异化分组数据
 * @returns 处理后的分组数据
 */
export const buildGroupOptions = (
  config: BoardConfigData,
  groupData: GroupDataProps,
  // selectedField: selectedFieldProps = INITIAL_SELECTED_FIELD_DATA(),
  isFromEbAdvance: boolean
) => {
  const { active, options } = groupData;
  const { otherData, displaySealing, permissions = [], ...restGroupData } = groupData;
  console.log('buildGroupOptions-groupData', groupData);
  // 列出现有分组数据
  const optionCustom = options.find(i => `${i.groupType}` === GroupType.custom) || INITIAL_CUSTOM_GROUP_INFO();
  const filterOptions = options.find(i => `${i.groupType}` === GroupType.filter) || INITIAL_FILTER_GROUP_INFO();
  const dateOptions = options.find(i => `${i.groupType}` === GroupType.dateRange) || INITIAL_DATE_GROUP_INFO();
  const fieldOptions = options.find(i => `${i.groupType}` === GroupType.field) || INITIAL_Field_GROUP_INFO();
  const diffOptions = options.find(i => `${i.groupType}` === GroupType.diff || `${i.groupType}` === RelationGroupsType.Normal || `${i.groupType}` === RelationGroupsType.Custom);

  const isEbForm = isEbFormDataV2(config);

  const getBuildUpDataOptions = (isDataOptions?: boolean, reset?: boolean) => {
    const buildDescInfo = (currentOption: any = {}) => {
      return {
        descField: currentOption.descField || null,
        descText: currentOption.descText || '',
      };
    };
    const selectedField = {
      id: fieldOptions.fieldId,
      type: fieldOptions.fieldType,
    };
    // *自定义分组
    const normalOptions = {
      groupType: GroupType.custom,
      enableColor: optionCustom.enableColor,
      values: (optionCustom.values || [])
        .filter((opt: any) => trim(opt.name))
        .map((opt: any) => {
          if (opt.id && opt.id.startsWith('temp_')) {
            return {
              name: opt.name,
              type: opt.type,
              color: opt?.color || '',
              ...buildDescInfo(opt),
            };
          }
          return opt;
        }),
    };
    console.log('normalOptions', normalOptions);
    // 有未生成的情况 手动给一次默认值
    // *按日期分组
    const dateGroupOptions =
      active !== GroupType.dateRange && reset
        ? INITIAL_DATE_GROUP_INFO()
        : {
            ...dateOptions,
            ...buildDescInfo(dateOptions),
            fieldId: dateOptions.fieldId,
            dateRange: dateOptions.dateRange,
          };

    // *按条件分组
    const newFilterOptions = {
      groupType: GroupType.filter,
      enableColor: filterOptions.enableColor,
      values: (filterOptions?.values || [])
        .filter((opt: any) => trim(opt.name))
        .map((opt: any) => {
          if (opt.id && opt.id.startsWith('temp_')) {
            return {
              name: opt.name,
              type: opt.type,
              color: opt?.color || '',
              filters: opt?.filters || {},
              ...buildDescInfo(opt),
            };
          }
          return opt;
        }),
    };
    // *按数据源分组
    // const dataSourceGroupOptions =
    //   this.groupType !== GroupType.dataset && reset
    //     ? INITIAL_DATASET_GROUP_INFO()
    //     : {
    //         ...this.dstWayValue,
    //         ...buildDescInfo(this.dateSourceGroupInfo),
    //       };
    // 当前是字段分组且 字段分组选中的是未分组
    if (selectedField.id === '-1' && active === GroupType.field) {
      return [normalOptions];
    }
    const { values, ...shouldSavedInfo } = fieldOptions;
    console.log('shouldSavedInfo', shouldSavedInfo);
    // *按字段分组
    const newFieldOptions =
      active !== GroupType.field && reset
        ? INITIAL_Field_GROUP_INFO()
        : {
            groupType: GroupType.field,
            ...shouldSavedInfo,
            fieldId: shouldSavedInfo.fieldId === '-1' ? '' : shouldSavedInfo.fieldId,
            fieldType: shouldSavedInfo.fieldType || '',
            ...buildDescInfo(fieldOptions),
          };
    let newOptions = [
      normalOptions,
      // *字段分组新增"请选择"，允许用户回到默认分组 字段分组不传values
      newFieldOptions,
      newFilterOptions,
      dateGroupOptions,
      // toJS(dataSourceGroupOptions),
    ];
    if (diffOptions?.groupType) {
      newOptions = [...newOptions, diffOptions];
    }
    return newOptions.filter((f: any) => !isEmpty(f));
  };
  let form: any = {
    ...restGroupData,
    otherData,
    options: getBuildUpDataOptions(),
  };
  if (permissions.length) {
    form = {
      ...form,
      permissions,
    };
  }
  // 字段分组封存配置单独带上
  if (`${active}` === GroupType.field && isEbForm) {
    form = {
      ...form,
      displaySealing,
    };
  }
  // 差异化分组有值
  if (diffOptions?.groupType) {
    form.rootField = groupData?.rootField;
  }
  // * 非表单高级视图才带该配置 目的是解决数据源从业务数据源切换为表单数据源 没走统一保存配置时  后端未拿到最新的数据源
  if (!isFromEbAdvance) {
    form.sourceType = config?.dataset?.type;
  }
  const unEbDataSetCustomOptions = isUnEbDataSetCustomOptions(config?.dataset, `${active}`);
  if (unEbDataSetCustomOptions) {
    form = {
      ...form,
      customGroup: '1',
    };
  }
  console.log('最后的form', deepToJS(form));
  return deepToJS(form);
};

export const getRsFieldFormId = (rsfieldid: string, relationFieldOpts: any[]) => {
  let id = '';
  forEach(relationFieldOpts, opt => {
    if (opt.id === rsfieldid) {
      if (opt.compType === EtComponentKey.Ebuilder) {
        const config = opt.config || {};
        id = config.sformId;
      }
      if (opt.compType === EtComponentKey.RelateBrowser) {
        id = opt.config?.browserType?.sformId;
      }
    }
  });
  return id;
};

export const getRelationBrowserFieldOpts = (fields: any[]) => {
  if (fields.length) {
    return fields.filter((data: any) => isEbRelationBrowser(data) || data.compType === EtComponentKey.Ebuilder).map((data: any) => ({ ...data, id: data.id, content: data.text }));
  }
  return [];
};

export const getRelationFieldOpts = (fields: any[]) => {
  if (fields.length) {
    // 关联字段
    return fields
      .filter(
        (data: any) =>
          (`${data?.multiSelect}` === 'false' || !data?.multiSelect) &&
          !['8', '9', '10', '11', '12', '13', '14', '15', '16'].includes(data?.id) &&
          (isEbRelationBrowser(data) || data.compType === EtComponentKey.Ebuilder || SelectType.includes(data.compType) || SelectType.includes(data.type))
      )
      .map((data: any) => ({ ...data, id: data.id, content: data.text }));
  }
  return [];
};