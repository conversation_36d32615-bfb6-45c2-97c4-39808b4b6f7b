import { isEmpty, forIn, isEqual } from '@weapp/utils';
import { AnyObj } from '@weapp/ui';
import axios from 'axios';
import { toJS, observable, computed, runInAction, action } from 'mobx';
import { GroupType } from '../../../../../constants/common';
import {
  BrowserTypes,
  ColorStyleType,
  commonBrowserTypes,
  INITIAL_GROUP_DATA,
  INITIAL_SELECTED_FIELD_DATA,
  INITIAL_DATE_GROUP_INFO,
  INITIAL_Field_GROUP_INFO,
  INITIAL_ID_PARAMS,
  INITIAL_DATASET_GROUP_INFO,
  INITIAL_DIFF_GROUP_INFO,
  GroupsTypeType,
} from '../../../engine/func-groups/constants';

type StoreOptsType = {
  props: any;
  pluginCenter: any;
  isDesign?: boolean;
};
const baseUrl = '/api/ebuilder/coms/kanban/progress';
// 新版平铺分组store -- 后续（表单设计器内）以这里为准
class GroupSetStore {
  pluginCenter: any; // 插件中心
  isDesign = false; // 是否为设计器视图
  events = {} as any; // 事件集合
  dataset = {} as any; // 事件集合

  @observable groupType: GroupType = GroupType.custom ;
  // 是否为数据加工数据源
  @computed
  get isEteams() {
    return this.dataset?.type === 'ETEAMS';
  }
  @computed
  get isEmptyProgressConfig() {
    return 123
  }
  constructor(opts: StoreOptsType) {
    this.pluginCenter = opts.pluginCenter;
    this.isDesign = !!opts.isDesign;
    this.events = opts.props.events;
  }
  // @action('切换分组')
  // switchGroup = (id: any) => {
  //   this.groupType = id;
  //   // this.changeBoardColorStyle();
  //   // 切换到日期分组且没有默认值时，初始化默认值
  //   if (`${id}` === GroupType.dateRange && !this.groupDateRangeOpt) {
  //     this.dateGroupInfo = INITIAL_DATE_GROUP_INFO();
  //   }
  //   // 切换到差异化分组时，初始化默认值
  //   if (`${id}` === GroupType.diff) {
  //     // this.dateGroupInfo = INITIAL_DIFF_GROUP_INFO();
  //     this.data = this.getGroupData();
  //     // 初始化差异化分组
  //     this.initDiffStore(this.data);
  //   }
  // };
}

export default GroupSetStore;
