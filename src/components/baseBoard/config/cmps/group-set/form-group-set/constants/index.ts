import { RadioDataType } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { LaneGroupType } from '../../../../../../../constants/common';
import { LaneDataSource } from '../../../yd/types';
export const DataSetHelpInfo = [`${getLabel('311435', '当前表：使用看板数据源')}；`, `${getLabel('311436', '其他表：选择其他数据源')}。`];

export const GroupDataSetTypeOptions: RadioDataType = [
  {
    id: 'current',
    content: getLabel('309613', '当前表'),
  },
  {
    id: 'other',
    content: getLabel('309614', '其他表'),
  },
];

export const GroupDialogLeftSpan = 8;

// 初始化字段分组-数据源类型
export const INIT_FIELD_GROUP_VALUE = () => {
  const common = {
    fieldId: '',
    fieldType: '',
    descText: '',
    descField: null,
    permissions: [],
    otherData: undefined,
  };
  return {
    currentGroupField: {
      ...common,
    },
    otherGroupField: {
      ...common,
      fieldType: EtComponentKey.Ebuilder,
      defaultGroup: [],
      filter: null,
      orderField: [],
      dataset: undefined,
      linkField: {},
      showField: '1',
    },
  };
};
export const INIT_YD_FIELD_GROUP_VALUE = (active: LaneGroupType = LaneGroupType.field) => {
  return {
    active,
    field: {
      id: '',
      type: '',
    },
    displayNoGroupData: false,
    displayNoGroup: false,
    noGroupLaneName: null, // 重命名未分组泳道
  };
};
export const INIT_YD_FIELD_GROUP_HANDLE_VALUE = (active?: LaneGroupType) => {
  return {
    currentGroupField: {
      ...INIT_YD_FIELD_GROUP_VALUE(active),
      groupSource: LaneDataSource.current,
    },
    otherGroupField: {
      ...INIT_YD_FIELD_GROUP_VALUE(active),
      groupSource: LaneDataSource.other,
      filter: null,
      dataset: undefined,
      showField: [],
      conditionConfig: [],
    },
  };
};
