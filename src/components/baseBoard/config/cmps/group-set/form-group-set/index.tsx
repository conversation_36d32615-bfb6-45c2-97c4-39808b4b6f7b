// 表单设计器下的当前表其他表分组方式设置
import { useState, useEffect, useMemo, useCallback, useImperativeHandle, useRef, forwardRef } from 'react';
import { Dialog, Button, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { Switch, Case } from 'react-if';
import { ebdBClsPrefix } from '../../../../../../constants';
import { GroupType } from '../../../../../../constants/common';
import { DesignComProps } from '../../../../types';
import FormCustomGroupSet from './standard-group/custom-group';
import FormDateRangeGroupSet from './standard-group/date-range-group';
import FieldGroupSet from './standard-group/field-group';
import DiffGroupSet from './standard-group/diff-group';
import LaneFieldGroupSet from './lane-group/field-group';
import LaneDateRangeGroupSet from './lane-group/date-range-group';
import LaneCustomGroupSet from './lane-group/custom-group';
import { deepToJS } from '../../../../../../utils';
import { GroupDataProps } from '../../../../engine/func-groups/types';
import NSettingIcon from '../../../../../common/n-setting-icon';
import DiffStore from '../../../../engine/func-groups/diff/store';
import { LaneConfigProps } from '../../yd/types';

import './style/index.less';
import '../../../../engine/func-groups/index.less';

const diffKey = 'FORM_GROUP_SET_DIALOG';
const cls = `${ebdBClsPrefix}-form-group-set-dialog`;

export interface GroupSetProps extends DesignComProps {
  fields?: any[];
  optionField?: any[];
  isFromEbAdvance?: boolean;
  onClose: () => void;
  onOk: (value: any, cb?: Function) => void;
  activeGroupId: string;
  isIconBtn?: boolean;
  isYd?: boolean; // 是否为泳道分组
}
const FormGroupSet = (props: GroupSetProps) => {
  const [visible, setVisible] = useState(false);
  const formGroupSetRef = useRef<any>(null);
  console.log('props', props);
  const [isLoading, setIsLoading] = useState(false);

  const { onOk, onClose, isIconBtn } = props;

  useEffect(() => {
    setVisible(!isIconBtn);
  }, [isIconBtn]);

  const handleClose = () => {
    onClose && onClose();
  };

  const handleSave = () => {
    setIsLoading(true);
    formGroupSetRef.current.handleSave((value: GroupDataProps) => {
      onOk(value, () => {
        setIsLoading(false);
      });
    });
  };

  return (
    <>
      <Dialog
        weId={`${props.weId || ''}_vuynwj`}
        visible={visible}
        title={getLabel('54215', '分组设置')}
        closable
        icon="Icon-e-builder-o"
        destroyOnClose
        onClose={handleClose}
        width={600}
        wrapClassName={cls}
        buttons={[
          <Button weId={`${props.weId || ''}_werfs45@ok`} key="ok" type="primary" onClick={handleSave} disabled={isLoading}>
            {isLoading ? <Icon weId={`${props.weId || ''}_1ssbhf@${diffKey}`} style={{ width: 15, height: 15 }} name="Icon-Loading" spin /> : getLabel('-1', '保存')}
          </Button>,
        ]}
      >
        <FormGroupSetContent weId={`${props.weId || ''}_55ahqw`} {...props} ref={formGroupSetRef} />
      </Dialog>
      {isIconBtn && <NSettingIcon weId={`${props.weId || ''}_l0irqc`} onClick={() => setVisible(true)} />}
    </>
  );
};
const FormGroupSetContent = forwardRef((props: GroupSetProps, ref: any) => {
  const customOptionRef = useRef<any>(null);
  const dateRangeOptionRef = useRef<any>(null);
  const fieldOptionRef = useRef<any>(null);
  const diffOptionRef = useRef<any>(null);
  const [groupValue, setGroupValue] = useState({} as GroupDataProps | LaneConfigProps);

  useImperativeHandle(ref, () => ({
    handleSave,
  }));

  const { value, fields = [], activeGroupId, isYd } = props;

  useEffect(() => {
    setGroupValue(deepToJS(value));
  }, [value]);

  const isCustomGroup = useMemo(() => {
    return `${activeGroupId}` === GroupType.custom;
  }, [groupValue]);

  const isDateRangeGroup = useMemo(() => {
    return `${activeGroupId}` === GroupType.dateRange;
  }, [groupValue]);

  const isFieldGroup = useMemo(() => {
    return `${activeGroupId}` === GroupType.field;
  }, [groupValue]);

  const isDiffGroup = useMemo(() => {
    return `${activeGroupId}` === GroupType.diff;
  }, [groupValue]);

  const isFilterGroup = useMemo(() => {
    return `${activeGroupId}` === GroupType.filter;
  }, [groupValue]);

  const handleSave = (cb: (newGroupData: GroupDataProps | LaneConfigProps) => void) => {
    const setValue = (value: GroupDataProps) => {
      setGroupValue(value);
      cb(value);
    };
    const handleSaveYdFilterValue = (value: LaneConfigProps) => {
      customOptionRef?.current?.onOk((_value: LaneConfigProps) => {
        const newValue = { ...value, ..._value.laneGroup }
        setGroupValue(newValue);
        cb(newValue);
      });
    };
    if (isCustomGroup || isFilterGroup) {
      if (isYd) {
        fieldOptionRef?.current?.onOk(handleSaveYdFilterValue);
      } else {
        customOptionRef?.current?.onOk(setValue);
      }
    } else if (isDateRangeGroup) {
      dateRangeOptionRef?.current?.onOk(setValue);
    } else if (isFieldGroup) {
      fieldOptionRef?.current?.onOk(setValue);
    } else if (isDiffGroup) {
      diffOptionRef?.current?.onOk(setValue);
    }
  };
  const renderFieldContent = () => {
    const Cmp = isYd ? LaneFieldGroupSet : FieldGroupSet;
    return (
      <div className={`${cls}-body-wrap`}>
        <Cmp weId={`${props.weId || ''}_smatfj`} {...props} ref={fieldOptionRef} fields={fields} cls={cls} isYd={isYd} />
      </div>
    );
  };
  const renderDateRangeContent = () => {
    const Cmp = isYd ? LaneDateRangeGroupSet : FormDateRangeGroupSet;
    return (
      <div className={`${cls}-body-wrap`}>
        <Cmp weId={`${props.weId || ''}_tr1kn2`} {...props} cls={cls} ref={dateRangeOptionRef} />
      </div>
    );
  };
  const renderDiffContent = () => {
    return (
      <div className={`${cls}-body-wrap`}>
        <DiffGroupSet weId={`${props.weId || ''}_tr1kn2`} {...props} cls={cls} ref={diffOptionRef} />
      </div>
    );
  };
  // 自定义分组 | 条件分组
  const renderCustomGroup = () => {
    if (isYd) {
      return (
        <div className={`${cls}-lane-filter`}>
          <div className={`${cls}-lane-filter-top`}>
            <LaneFieldGroupSet weId={`${props.weId || ''}_gwys30`} {...props} ref={fieldOptionRef} fields={fields} cls={cls} isYdFilterWay />
          </div>
          <LaneCustomGroupSet weId={`${props.weId || ''}_um01ix`} {...props} ref={customOptionRef} fields={fields} />
        </div>
      );
    }
    return <FormCustomGroupSet weId={`${props.weId || ''}_azslg4`} {...props} ref={customOptionRef} fields={fields} />;
  };
  return (
    <div className={`${cls}-body`}>
      <Switch weId={`${props.weId || ''}_3jzx4t`}>
        <Case weId={`${props.weId || ''}_rm5poy`} condition={activeGroupId === GroupType.custom || activeGroupId === GroupType.filter}>
          {/* 自定义分组 | 条件分组 */}
          {renderCustomGroup()}
        </Case>
        <Case weId={`${props.weId || ''}_ckyydr`} condition={activeGroupId === GroupType.field}>
          {renderFieldContent()}
        </Case>
        <Case weId={`${props.weId || ''}_tkxp9f`} condition={activeGroupId === GroupType.dateRange}>
          {renderDateRangeContent()}
        </Case>
        <Case weId={`${props.weId || ''}_n39y9s`} condition={activeGroupId === GroupType.diff}>
          {renderDiffContent()}
        </Case>
      </Switch>
    </div>
  );
});
export default FormGroupSet;
