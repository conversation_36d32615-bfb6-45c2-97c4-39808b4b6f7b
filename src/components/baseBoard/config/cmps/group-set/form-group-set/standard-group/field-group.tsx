// 表单设计器下的当前表-日期分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle, useMemo } from 'react';
import { If, Then, Else } from 'react-if';
import { isEmpty } from '@weapp/utils';
import { deepToJS } from '../../../../../../../utils';
import { GroupType } from '../../../../../../../constants/common';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { GroupDataProps, GroupDataOptionItem, GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '../';
import { ColorStyleType } from '../../../../../engine/func-groups/constants';
import { CommonField, DatasetType, GroupField, AddGroup, GroupSetDataset, LinkField } from '../common';
import { DataSetType, DataSetTypeItem } from '../../../../../types';
import { isEbFormDataV2 } from '../../../../../../common/DataSet/utils';
import { getCompanyAndSeclevel, getFields } from '../../ajaxUtil';
import { companyAndSeclevelProps } from '../../../../../engine/func-groups/types';
import { fixFieldGroupData, showAddPermi, showDataSetType } from '../utils';
import '../style/index.less';

interface FormFieldGroupSetProps extends GroupSetProps {
  cls: string;
  optionField?: any[];
  isFilterWay?: boolean;
}

const FormFieldGroupSet = forwardRef((props: FormFieldGroupSetProps, ref: any) => {
  const { config, fields, value, cls, optionField, appid = '', isFilterWay } = props;

  const groupValueRef = useRef<GroupDataProps>(deepToJS(value));
  const [groupValue, setGroupValue] = useState({} as GroupDataProps);
  const [companyAndSeclevel, setCompanyAndSeclevel] = useState<companyAndSeclevelProps>({ supportCompany: false, supportSeclevel: false });
  const [boardFields, setBoardFields] = useState([] as any[]);
  const [linkFields, setLinkFields] = useState([] as any[]);
  const [linkDataset, setLinkDataset] = useState({} as any);
  const [fieldLoading, setFieldLoading] = useState(false);

  const _value = deepToJS(value);

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  // 只初始化一次
  useEffect(() => {
    let currentValue = fixFieldGroupData(groupValueRef.current as GroupDataProps);
    setGroupValue(currentValue);
  }, []);

  useEffect(() => {
    const getPermissionInfo = async () => {
      const permissionInfo = await getCompanyAndSeclevel(appid);
      setCompanyAndSeclevel(permissionInfo);
    };
    getPermissionInfo();
  }, [appid]);

  const fieldGroup = useMemo(() => {
    return groupValue.options?.find((i: any) => `${i.groupType}` === GroupType.field);
  }, [groupValue]);

  const fieldGroupValueKey = useMemo(() => {
    return fieldGroup?.fieldType === EtComponentKey.Ebuilder ? 'otherGroupField' : 'currentGroupField';
  }, [fieldGroup]);

  const fieldGroupValue = useMemo(() => {
    return fieldGroup?.[fieldGroupValueKey];
  }, [fieldGroup, fieldGroupValueKey]);

  // 获取整体看板数据源的字段
  useEffect(() => {
    setFieldLoading(true);
    const getBoardFields = async () => {
      const fields = await getFields(config?.dataset);
      setBoardFields(fields.filter((i: any) => i.compType === EtComponentKey.Ebuilder));
      setFieldLoading(false);
    };
    getBoardFields();
  }, [config?.dataset]);

  // 获取关联字段对应的数据源
  useEffect(() => {
    const handleLinkFields = async () => {
      if (!fieldGroupValue?.fieldId || isEmpty(boardFields)) {
        return;
      }
      const currentField = boardFields.find((i: any) => i.id === fieldGroupValue?.fieldId);
      const refObjId = currentField?.refObjId;
      if (!refObjId) {
        return;
      }
      const fields = await getFields({ id: refObjId, text: '', type: 'FORM', groupId: '' });
      // 设置下关联字段列表
      setLinkFields(fields);
      // 设置下关联数据源
      const linkDataset = {
        id: refObjId,
        text: currentField?.config?.formName,
        type: 'FORM',
        groupId: currentField?.config?.appId,
      };
      setLinkDataset(linkDataset);
    };
    handleLinkFields();
  }, [fieldGroupValue?.fieldId, boardFields]);

  const datasetType = useMemo(() => {
    return fieldGroup?.fieldType === EtComponentKey.Ebuilder ? DataSetType.Other : DataSetType.Current;
  }, [fieldGroup?.fieldType]);

  const showAdd = useMemo(() => {
    return showAddPermi(config?.dataset, groupValue, fieldGroupValue?.fieldType);
  }, [config?.dataset, groupValue, fieldGroupValue?.fieldType]);

  const showSealField = useMemo(() => {
    return isEbFormDataV2(config) && datasetType === DataSetType.Current;
  }, [config, datasetType]);

  const onOk = (cb: (newGroupData: GroupDataProps & { boardColorStyle: ColorStyleType }) => void) => {
    const { otherData, permissions, displaySealing, showPermiAdd, ...rest } = fieldGroupValue!;
    const newOptions = _value?.options?.map((i: any) => {
      // 只更新按字段分组
      if (`${i.groupType}` === GroupType.field) {
        if (fieldGroupValue) {
          i = { groupType: GroupType.field, ...rest };
        }
      }
      return i;
    });
    const newGroups = { ...value, options: newOptions, otherData, permissions: showPermiAdd ? permissions : [], displaySealing };
    cb && cb(newGroups);
  };

  const onGroupValueChange = (rowData: GroupDataProps) => {
    setGroupValue(rowData);
  };
  const onFieldGroupChange = (rowData: GroupDataOptionItem | GroupDataOptionItemField, changeAll?: boolean) => {
    const newOptions = (groupValue as GroupDataProps).options?.map((i: any) => {
      if (`${i.groupType}` === GroupType.field) {
        if (changeAll) {
          i = rowData;
        } else {
          // 当前操作什么数据源类型分组 就更新指定的
          i = { ...i, [fieldGroupValueKey]: rowData };
        }
      }
      return i;
    });
    onGroupValueChange({ ...groupValue, options: newOptions });
  };
  const onDatasetTypeChange = (value: DataSetTypeItem) => {
    let newJson = { ...fieldGroup };
    // 还原配置
    if (value === DataSetType.Other) {
      newJson = { ...fieldGroup, ...fieldGroup?.otherGroupField };
    } else {
      newJson = { ...fieldGroup, ...fieldGroup?.currentGroupField };
    }
    onFieldGroupChange(newJson, true);
  };

  const onChangePermissions = (showPermiAdd: boolean, value: any[]) => {
    onFieldGroupChange({ ...fieldGroupValue, permissions: value, showPermiAdd });
  };
  const showCommon = useMemo(() => {
    return !isEmpty(linkFields) || fieldGroupValue?.fieldId
  }, [linkFields, fieldGroupValue])

  const renderFilterContent = () => {
    return (
      <>
        <CommonField
          weId={`${props.weId || ''}_3fx5qk`}
          config={config}
          groupType={groupValue?.active}
          rowData={{ ...fieldGroupValue }}
          onChange={onFieldGroupChange}
          showOther
          showSealField={false}
          showDesc={false}
          showTitle={false}
          showSort={false}
          showFilter={false}
          cls={cls}
          datasetType={datasetType}
        />
      </>
    );
  };
  const renderContent = () => {
    return (
      <>
        {showDataSetType(config) && <DatasetType weId={`${props.weId || ''}_3crpns`} onChange={onDatasetTypeChange} cls={cls} value={datasetType} />}
        <If weId={`${props.weId || ''}_zzrb7n`} condition={datasetType === DataSetType.Current}>
          <Then weId={`${props.weId || ''}_kwblzb`}>
            <GroupField weId={`${props.weId || ''}_tr1kn2`} fieldGroup={fieldGroupValue!} config={config} optionField={optionField} cls={cls} onChange={onFieldGroupChange} datasetType={datasetType} />
          </Then>
          <Else weId={`${props.weId || ''}_7te42g`}>
            <LinkField weId={`${props.weId || ''}_24qgb6`} fieldGroup={fieldGroupValue!} onChange={onFieldGroupChange} cls={cls} linkFields={boardFields} loading={fieldLoading} />
            {/* 选完关联ebuilder字段后 联带出数据源 只供展示 */}
            <If weId={`${props.weId || ''}_c68a83`} condition={!isEmpty(linkDataset)}>
              <Then weId={`${props.weId || ''}_dhlwru`}>
                <GroupSetDataset weId={`${props.weId || ''}_44s46h`} value={linkDataset} onChange={onFieldGroupChange} cls={cls} disabled />
              </Then>
            </If>
          </Else>
        </If>
        <If weId={`${props.weId || ''}_zzrb7n`} condition={showCommon}>
          <Then weId={`${props.weId || ''}_kwblzb`}>
            {/* 这里数据源都 */}
            <CommonField
              weId={`${props.weId || ''}_3fx5qk`}
              config={config}
              groupType={groupValue.active}
              fields={linkFields}
              rowData={fieldGroupValue}
              onChange={onFieldGroupChange}
              showOther
              showSealField={showSealField}
              cls={cls}
              datasetType={datasetType}
              linkDataset={linkDataset}
              showDefault
            />
          </Then>
        </If>
        <If weId={`${props.weId || ''}_7wxbzy`} condition={showCommon && showAdd && !isEmpty(fields)}>
          <Then weId={`${props.weId || ''}_exg7na`}>
            <AddGroup
              weId={`${props.weId || ''}_hvge3l`}
              value={fieldGroupValue?.permissions}
              rootField={groupValue?.rootField}
              showPermiAdd={fieldGroupValue?.showPermiAdd || !isEmpty(fieldGroupValue?.permissions)}
              cls={cls}
              companyAndSeclevel={companyAndSeclevel}
              fields={fields}
              onChange={onChangePermissions}
            />
          </Then>
        </If>
      </>
    );
  };

  return <>{isFilterWay ? renderFilterContent() : renderContent()}</>;
});

export default FormFieldGroupSet;
