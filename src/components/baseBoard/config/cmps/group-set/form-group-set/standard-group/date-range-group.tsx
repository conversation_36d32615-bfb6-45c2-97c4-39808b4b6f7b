// 表单设计器下的当前表-日期分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { If, Then } from 'react-if';
import { deepToJS } from '../../../../../../../utils';
import { GroupType } from '../../../../../../../constants/common';
import { GroupOtherData, GroupDataProps, GroupDataOptionItem, GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '../';
import { ColorStyleType } from '../../../../../engine/func-groups/constants';
import { DateRangeFieldComps } from '../../../../../engine/func-groups/ExtraFieldComps';
import { GroupDialogLeftSpan } from '../constants';
import { CommonField } from '../common';
import '../style/index.less';

interface FormDateRangeGroupSetProps extends GroupSetProps {
  cls: string;
}

const FormDateRangeGroupSet = forwardRef((props: FormDateRangeGroupSetProps, ref: any) => {
  const { config, fields, value, cls } = props;
  const _value = deepToJS(value);
  const groupValueRef = useRef<GroupDataProps>(deepToJS(value));

  const [groupStates, setGroupStates] = useState<GroupDataOptionItem>(value);
  const [otherStates, setOtherStates] = useState<GroupOtherData>({});

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  useEffect(() => {
    const dateRangeGroup = groupValueRef.current?.options?.find((i: any) => `${i.groupType}` === GroupType.dateRange);
    setGroupStates(dateRangeGroup!);
    setOtherStates(groupValueRef.current?.otherData || {});
  }, []);

  // 处理确认按钮
  const onOk = (cb: (newGroupData: GroupDataProps & { boardColorStyle: ColorStyleType }) => void) => {
    const newOptions = _value?.options?.map((i: any) => {
      // 只更新条件或者自定义分组
      if (`${i.groupType}` === GroupType.dateRange) {
        i = groupStates;
      }
      return i;
    });
    cb && cb({ ...value, options: newOptions, otherData: otherStates });
  };

  const onChangeSingle = (rowData: GroupDataOptionItemField) => {
    const { otherData, ...rest } = rowData;
    setGroupStates(prev => {
      return {
        ...prev,
        ...rest,
      };
    });
    setOtherStates(otherData!);
  };

  return (
    <>
      <DateRangeFieldComps weId={`${props.weId || ''}_tr1kn2`} value={groupStates} fieldsMap={fields!} onChange={onChangeSingle} titleRowSpan={GroupDialogLeftSpan} />
      <If weId={`${props.weId || ''}_zzrb7n`} condition={groupStates.fieldId}>
        <Then weId={`${props.weId || ''}_kwblzb`}>
          <CommonField
            weId={`${props.weId || ''}_3fx5qk`}
            config={config}
            groupType={groupStates?.groupType}
            fields={fields}
            rowData={{...groupStates, otherData: otherStates}}
            onChange={onChangeSingle}
            cls={cls}
          />
        </Then>
      </If>
    </>
  );
});

export default FormDateRangeGroupSet;
