// 表单设计器下的当前表-自定义|条件分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle, useMemo, useCallback } from 'react';
import { CorsComponent } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { KanBanDefaultBgColor } from '../../../../../../../constants';
import { deepToJS } from '../../../../../../../utils';
import { GroupType } from '../../../../../../../constants/common';
import GroupOptionCom from '../../../../../engine/func-groups/GroupOptionCom';
import { CustomGroupItem, GroupDataProps } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '../';
import { isLogicData } from '../../../../../../common/DataSet/utils';
import { DescDialog } from '../common';
import { ColorStyleType, INITIAL_CUSTOM_GROUP_INFO, INITIAL_FILTER_GROUP_INFO } from '../../../../../engine/func-groups/constants';
import '../style/index.less';

const _tempGroup: CustomGroupItem = {
  type: '1',
  id: `temp_${Date.now()}`,
  name: '',
  color: KanBanDefaultBgColor,
};

interface FormCustomGroupSetProps extends GroupSetProps {}

interface GroupState {
  options: CustomGroupItem[];
  // unknownOption: CustomGroupItem;
  boardColorStyle: ColorStyleType;
}

const FormCustomGroupSet = forwardRef((props: FormCustomGroupSetProps, ref: any) => {
  const { config, fields, value, appid, isFromEbAdvance, weId } = props;
  const _value = deepToJS(value);
  const groupValueRef = useRef<GroupDataProps>(deepToJS(value));

  const [groupStates, setGroupStates] = useState<Record<string, GroupState>>({
    custom: {
      ...INITIAL_CUSTOM_GROUP_INFO(),
      options: [_tempGroup],
    },
    filter: {
      ...INITIAL_FILTER_GROUP_INFO(),
      options: [_tempGroup],
    },
  });

  const [lineEndRowData, setLineEndRowData] = useState({} as any);
  const [lineFilterRowData, setLineFilterRowData] = useState({} as any);
  const [showDesc, setShowDesc] = useState(false);

  const isFilterGroupWay = useMemo(() => {
    return groupValueRef.current.active === GroupType.filter;
  }, []);

  // 获取当前使用的分组类型
  const currentGroupType = isFilterGroupWay ? 'filter' : 'custom';

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  // 初始化分组数据
  useEffect(() => {
    const initGroupData = (groupType: GroupType) => {
      const optionItem = groupValueRef?.current?.options?.find((i: any) => `${i.groupType}` === `${groupType}`);
      if (!optionItem?.values || !optionItem.values.length) return;

      const values = optionItem.values || [];
      const type = groupType === GroupType.custom ? 'custom' : 'filter';
      const lastValue = values.filter((i: any) => `${i.type}` !== '0');

      setGroupStates(prev => ({
        ...prev,
        [type]: {
          options: isEmpty(lastValue) ? [_tempGroup] : lastValue,
          boardColorStyle: optionItem.enableColor ? ColorStyleType.HasStyle : ColorStyleType.NoStyle,
        },
      }));
    };
    if (isFilterGroupWay) {
      initGroupData(GroupType.filter);
    } else {
      initGroupData(GroupType.custom);
    }
  }, [isFilterGroupWay]);

  // 处理确认按钮
  const onOk = (cb: (newGroupData: GroupDataProps & { boardColorStyle: ColorStyleType }) => void) => {
    let newValue = _value;
    const currentOptions = isFilterGroupWay ? newValue.options?.filter((i: any) => `${i.groupType}` === GroupType.filter) : newValue.options.filter((i: any) => `${i.groupType}` === GroupType.custom);

    if (isEmpty(currentOptions)) {
      newValue.options = isFilterGroupWay ? [...newValue.options, INITIAL_FILTER_GROUP_INFO()] : [...newValue.options, INITIAL_CUSTOM_GROUP_INFO()];
    }
    const newOptions = newValue?.options?.map((i: any) => {
      // 只更新条件或者自定义分组
      if (isFilterGroupWay) {
        if (`${i.groupType}` === GroupType.filter) {
          const { options, boardColorStyle } = groupStates.filter;
          i.values = [...options].filter((i: any) => !isEmpty(i));
          i.enableColor = boardColorStyle === ColorStyleType.HasStyle;
        }
      } else {
        if (`${i.groupType}` === GroupType.custom) {
          const { options, boardColorStyle } = groupStates.custom;
          i.values = [...options].filter((i: any) => !isEmpty(i));
          i.enableColor = boardColorStyle === ColorStyleType.HasStyle;
        }
      }
      return i;
    });
    cb && cb({ ...value, options: newOptions });
  };

  // 更新单个选项
  const onChangeSingle = useCallback(
    (rowData: any) => {
      setGroupStates(prev => {
        const currentOptions = [...prev[currentGroupType].options];
        const updatedOptions = currentOptions.map(item => (item.id === rowData.id ? { ...item, ...rowData } : item));

        return {
          ...prev,
          [currentGroupType]: {
            ...prev[currentGroupType],
            options: updatedOptions,
          },
        };
      });
    },
    [currentGroupType]
  );

  // 添加自定义分组
  const addCustomGroup = () => {
    setGroupStates(prev => ({
      ...prev,
      [currentGroupType]: {
        ...prev[currentGroupType],
        options: [...prev[currentGroupType].options, { ..._tempGroup, id: `temp_${Date.now()}` }],
      },
    }));
  };

  // 更新分组选项
  const changeCustomOption = (data: any[]) => {
    setGroupStates(prev => ({
      ...prev,
      [currentGroupType]: {
        ...prev[currentGroupType],
        options: isEmpty(data) ? [_tempGroup] : data,
      },
    }));
  };

  // 设置描述
  const showSetDesc = (rowData: any) => {
    // 开启统计按钮默认为true 兼容历史数据
    if (rowData.descField && !('useStats' in rowData.descField) && isLogicData(config?.dataset)) {
      rowData.descField.useStats = true;
    }
    setLineEndRowData(rowData);
    setShowDesc(true);
  };

  // 设置筛选项
  const setLineFilters = (filters: any) => {
    console.log('filters', filters);
    setLineFilterRowData(filters);
  };
  const changeLineFilters = useCallback(
    (type: string, value: any) => {
      console.log('type', type, value);
      if (type === 'isOk') {
        onChangeSingle({ ...lineFilterRowData, filters: value });
      }
      setLineFilterRowData({});
    },
    [lineFilterRowData, onChangeSingle]
  );

  // 更新颜色样式
  const changeBoardColorStyle = (style: ColorStyleType) => {
    setGroupStates(prev => ({
      ...prev,
      [currentGroupType]: {
        ...prev[currentGroupType],
        boardColorStyle: style,
      },
    }));
  };
  const renderFilterCmp = useCallback(() => {
    if (isEmpty(lineFilterRowData)) return null;
    return (
      <CorsComponent
        weId={`${props.weId || ''}_bua7ms`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        dataSet={config?.dataset}
        value={lineFilterRowData?.filters || {}}
        onOk={(value: any) => changeLineFilters('isOk', value)}
        onCancel={() => changeLineFilters('cancel', {})}
        visible
      />
    );
  }, [lineFilterRowData, config?.dataset, props.weId, changeLineFilters]);

  const currentState = groupStates[currentGroupType];

  return (
    <>
      <GroupOptionCom
        weId={`${weId || ''}_nvxz34`}
        appId={appid!}
        boardColorStyle={currentState.boardColorStyle}
        changeBoardColorStyle={changeBoardColorStyle}
        customShowOption={currentState.options}
        addCustomGroup={addCustomGroup}
        changeCustomOption={changeCustomOption}
        setFieldDesc={showSetDesc}
        setLineFilters={setLineFilters}
        isFromEbAdvance={isFromEbAdvance}
        isFilterGroupWay={isFilterGroupWay}
      />
      {showDesc && <DescDialog weId={`${weId || ''}_nvxz34`} config={config} lineEndRowData={lineEndRowData} fields={fields!} onValueChange={onChangeSingle} onClose={() => setShowDesc(false)} />}
      {renderFilterCmp()}
    </>
  );
});

export default FormCustomGroupSet;
