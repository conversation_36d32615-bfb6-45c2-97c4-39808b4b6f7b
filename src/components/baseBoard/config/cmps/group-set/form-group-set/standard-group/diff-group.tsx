// 表单设计器下的当前表-日期分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { toJS } from 'mobx';
import { deepToJS } from '../../../../../../../utils';
import { GroupDataProps } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '../';
import { ColorStyleType, GroupsTypeType } from '../../../../../engine/func-groups/constants';
import { RelationGroupsType } from '../../../../../engine/func-groups/diff/constants';
import DiffStore from '../../../../../engine/func-groups/diff/store';
import DiffGroup from '../../../../../engine/func-groups/diff';
import { buildDiffGroupOptions } from '../../../../../engine/func-groups/diff/utils';

import '../style/index.less';

interface DiffGroupSetProps extends GroupSetProps {
  cls: string;
}

const DiffGroupSet = forwardRef((props: DiffGroupSetProps, ref: any) => {
  const { config, fields, value, cls, onConfigChange } = props;
  console.log('props', props);
  const { diffGroupCustomId } = config;

  const _value = deepToJS(value);
  // 差异化分组
  const groupValueRef = useRef<GroupDataProps>(deepToJS(value));
  const diffStoreRef = useRef(new DiffStore());

  const [groupValue, setGroupValue] = useState({} as GroupDataProps);

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  useEffect(() => {
    setGroupValue(groupValueRef.current);
  }, []);

  useEffect(() => {
    diffStoreRef.current = new DiffStore(groupValueRef.current, fields);
    // 回显差异化分组自定义分组
    if (diffGroupCustomId) {
      diffStoreRef.current.setState({ releationCustomSelected: diffGroupCustomId });
    }
  }, [diffGroupCustomId, fields]);

  // 处理确认按钮
  const onOk = (cb: (newGroupData: GroupDataProps & { boardColorStyle: ColorStyleType }) => void) => {
    // 差异化分组比较特殊 需要这里处理下diffStore的数据
    const diffStore = diffStoreRef.current;
    const diffOptions = buildDiffGroupOptions(diffStore, false);
    const unDiffOptions = _value?.options.filter((i: any) => `${i.groupType}` !== RelationGroupsType.Normal && `${i.groupType}` !== RelationGroupsType.Custom);
    let otherParams: any = {
      // 分组类型按差异化的来
      active: diffStore.relationSelected,
      dataGroup: GroupsTypeType.releationField,
      rootField: diffStore.rootField,
    };
    // *差异化分组自定义分组需要补充 dataOptions 不然无法同步前台显示
    if (diffStore.isCustomRelationGroupsType) {
      otherParams = {
        ...otherParams,
        groupField: diffStore.groupField,
        dataOptions: [...unDiffOptions, ...buildDiffGroupOptions(diffStore, true)],
      };
    }
    const newGroupData = {
      ...value,
      ...otherParams,
      options: [...unDiffOptions, ...diffOptions],
    };
    if (diffStore.isCustomRelationGroupsType) {
      onConfigChange && onConfigChange({ diffGroupCustomId: diffStore.releationCustomSelected });
    }
    cb && cb(deepToJS(newGroupData));
  };

  const addCustomGroup = () => {};
  const mockStore = () => {
    return {
      boardColorStyle: '',
      setBoardColorStyle: () => {},
    };
  };
  return (
    <>
      <DiffGroup weId={`${props.weId || ''}_wuzj6j`} {...props} group={groupValue} store={mockStore()} diffStore={diffStoreRef.current} addCustomGroup={addCustomGroup} appId={props?.appid || ''} />
    </>
  );
});

export default DiffGroupSet;
