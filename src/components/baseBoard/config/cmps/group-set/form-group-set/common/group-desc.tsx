import { useState, useMemo } from 'react';
import { Layout } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { GroupDialogLeftSpan } from '../constants';
import { isLogicData } from '../../../../../../common/DataSet/utils';
import NSettingIcon from '../../../../../../common/n-setting-icon';
import { DescDialog } from './';

const { Row, Col } = Layout;

export interface GroupDescProps {
  weId: string;
  rowData: any;
  cls: string;
  fields: any[];
  config: any;
  onChange: (value: GroupDataOptionItem) => void;
}
const GroupDesc = (props: GroupDescProps) => {
  const { cls, rowData, fields = [], config, onChange } = props;
  const [showDesc, setShowDesc] = useState(false);
  const [lineEndRowData, setLineEndRowData] = useState({} as any);

  const showSetDesc = () => {
    // 开启统计按钮默认为true 兼容历史数据
    if (rowData.descField && !('useStats' in rowData.descField) && isLogicData(config?.dataset)) {
      rowData.descField.useStats = true;
    }
    setLineEndRowData(rowData);
    setShowDesc(true);
  };

  const hasDescValue = useMemo(() => {
    if (isEmpty(rowData?.descField)) return false;
    return !!rowData?.descField?.content || rowData?.descField?.id !== '-1';
  }, [rowData]);

  return (
    <>
      <Row weId={`${props.weId || ''}_8ny9uo`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_m4h8cv`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_u22uli`} title={getLabel('-1', '分组描述')} />
        </Col>
        <Col weId={`${props.weId || ''}_shwz41`} className={`${cls}-item-right`}>
          <NSettingIcon weId={`${props.weId || ''}_6nltw7`} hasValue={hasDescValue} onClick={showSetDesc} clearable={hasDescValue} clearFunc={() => onChange({ ...rowData, descField: null, descText: '' })} />
        </Col>
      </Row>
      {showDesc && <DescDialog weId={`${props.weId || ''}_hk4jj1`} config={config} lineEndRowData={lineEndRowData} fields={fields} onValueChange={onChange} onClose={() => setShowDesc(false)} />}
    </>
  );
};

export default GroupDesc;
