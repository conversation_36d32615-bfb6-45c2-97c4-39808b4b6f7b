import { useMemo } from 'react';
import { Layout } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { GroupDialogLeftSpan } from '../constants';
import { DataSetItem } from '../../../../../../common/DataSet/types';
import NSettingIcon from '../../../../../../common/n-setting-icon';
import DataSort from '../../../../../../common/data-sort';

const { Row, Col } = Layout;

export interface GroupSetSortProps {
  weId: string;
  cls: string;
  rowData?: GroupDataOptionItem;
  onChange: (value: GroupDataOptionItem) => void;
  titleRowSpan?: number;
  dataSet?: DataSetItem;
}
const GroupSetSort = (props: GroupSetSortProps) => {
  const { cls, rowData, onChange, dataSet } = props;

  const renderDataSort = () => {
    if (!dataSet?.id) return null;
    const handleChange = (value: any) => {
      onChange({ ...rowData, orderField: toJS(value) });
    };
    return (
      <DataSort weId={`${props.weId || ''}_q3uopc`}
        value={rowData?.orderField}
        dataset={dataSet!}
        onChange={handleChange}
        customBtnRender={(showSortSet: () => void) => {
          return <NSettingIcon weId={`${props.weId || ''}_spw5je`} hasValue={hasValue} onClick={showSortSet} clearable={hasValue} clearFunc={() => onChange({ ...rowData, orderField: [] })} />
        }}
      />
    );
  };

  const hasValue = useMemo(() => {
    return !isEmpty(rowData?.orderField)
  }, [rowData])

  return (
    <>
      <Row weId={`${props.weId || ''}_gb8ds2`} className={`${cls}-item`}>
        <Col weId={`${props.weId || ''}_vh03tf`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_9j9s5o`} title={getLabel('96961', '排序设置')} placement="leftTop" />
        </Col>
        <Col weId={`${props.weId || ''}_vmg804`} className={`${cls}-item-right`}>
          {renderDataSort()}
        </Col>
      </Row>
    </>
  );
};

export default GroupSetSort;
