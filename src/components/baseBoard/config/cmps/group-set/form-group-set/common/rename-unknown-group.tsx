import { Layout, Input } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { DataSetTypeItem } from '../../../../../types';
import { GroupDialogLeftSpan } from '../constants';

const { Row, Col } = Layout;

export interface RenameUnknownGroupProps {
  weId: string;
  value?: string;
  onChange?: (value: DataSetTypeItem) => void;
  cls: string;
}
const RenameUnknownGroup = (props: RenameUnknownGroupProps) => {
  const { cls, weId, value, onChange } = props;
  return (
    <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
      <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('-1', '重命名未分组')} />
      </Col>
      <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
        <Input weId={`${props.weId || ''}_lrlvv5`} />
      </Col>
    </Row>
  );
};

export default RenameUnknownGroup;
