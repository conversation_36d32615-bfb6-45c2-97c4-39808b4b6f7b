import { use<PERSON>emo } from 'react';
import { If, Then } from 'react-if';
import { GroupDesc } from '../common';
import { GroupDataActiveProps, GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import BoardGroupOther from '../../../../../engine/func-groups/BoardGroupOther';
import { GroupDialogLeftSpan } from '../constants';
import { ExtraSealField } from '../../../../../engine/func-groups/ExtraFieldComps';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { LaneGroupType } from '../../../../../../../constants/common';
import { DataSetType } from '../../../../../types';
import { GroupSetFilter, GroupSetSort, ShowCardTitle, DefaultGroup } from './';

export interface CommonFieldProps {
  weId: string;
  rowData?: GroupDataOptionItemField;
  config?: any;
  fields?: any[];
  onChange?: (value: GroupDataOptionItemField) => void;
  cls: string;
  groupType?: GroupDataActiveProps | LaneGroupType;
  showOther?: boolean;
  showSealField?: boolean;
  datasetType?: DataSetType;
  linkDataset?: any;
  isYd?: boolean;
  showFilter?: boolean;
  showDesc?: boolean;
  showSort?: boolean;
  showTitle?: boolean;
  isCardTitle?: boolean;
  showDefault?: boolean;
}
const CommonField = (props: CommonFieldProps) => {
  const {
    cls,
    rowData,
    config,
    fields = [],
    onChange,
    groupType,
    showOther = true,
    datasetType,
    showSealField,
    linkDataset,
    isYd,
    showDesc = true,
    showSort = true,
    showTitle = true,
    showFilter = true,
    showDefault = false,
    isCardTitle,
  } = props;
  const { otherData = {}, displaySealing = true } = rowData || {};

  const onSingleFieldChange = (rowData: any) => {
    onChange && onChange(rowData);
  };

  const setOtherData = (key: string, value: boolean) => {
    onSingleFieldChange({ ...rowData, otherData: { ...otherData, [key]: value } });
  };

  const selectedField = useMemo(() => {
    return {
      id: rowData?.fieldId,
      type: rowData?.fieldType,
    };
  }, [rowData]);

  const showDefaultGroup = useMemo(() => {
    return showDefault && rowData?.fieldType === EtComponentKey.Ebuilder;
  }, [showDefault, rowData?.fieldType]);

  return (
    <>
      <If weId={`${props.weId || ''}_1c2yr5`} condition={datasetType === DataSetType.Other}>
        <Then weId={`${props.weId || ''}_yqfl3y`}>
          {showFilter && <div className={`${cls}-otherLine`}>
            <GroupSetFilter weId={`${props.weId || ''}_48bxrc`} fields={fields} dataSet={linkDataset} onChange={onSingleFieldChange} cls={cls} rowData={rowData} />
          </div>}
          {showSort && (
            <div className={`${cls}-otherLine`}>
              <GroupSetSort weId={`${props.weId || ''}_4vyfs1`} dataSet={linkDataset} onChange={onSingleFieldChange} cls={cls} rowData={rowData} />
            </div>
          )}
          {showTitle && linkDataset && (
            <div className={`${cls}-otherLine`}>
              <ShowCardTitle
                weId={`${props.weId || ''}_qokt8y`}
                dataSet={linkDataset}
                onChange={onSingleFieldChange}
                cls={cls}
                fields={fields}
                clearFunc={() => onSingleFieldChange({ ...rowData, showField: null })}
                appId={config?.appId}
                isCard={isCardTitle}
                rowData={rowData}
                required
              />
            </div>
          )}
        </Then>
      </If>
      {showDesc && <GroupDesc weId={`${props.weId || ''}_ody57c`} rowData={rowData} config={config} fields={fields} onChange={onSingleFieldChange} cls={cls} />}
      {showDefaultGroup && <DefaultGroup weId={`${props.weId || ''}_16pdw5`} rowData={rowData} onChange={onSingleFieldChange} cls={cls} titleRowSpan={GroupDialogLeftSpan} />}
      {showOther && (
        <BoardGroupOther
          weId={`${props.weId || ''}_74tj0l`}
          otherData={otherData}
          onChange={setOtherData}
          pageId={config?.pageId}
          groupType={groupType}
          titleRowSpan={GroupDialogLeftSpan}
          isYd={isYd}
        />
      )}
      {showSealField && (
        <>
          <ExtraSealField
            weId={`${props.weId || ''}_ed2y33`}
            selectedField={selectedField}
            displaySealing={!!displaySealing}
            setState={(value: any) => onSingleFieldChange({ ...rowData, ...value })}
            titleRowSpan={GroupDialogLeftSpan}
          />
        </>
      )}
    </>
  );
};

export default CommonField;
