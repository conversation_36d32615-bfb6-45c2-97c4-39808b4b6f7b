import { useState } from 'react';
import { Layout, <PERSON><PERSON>, <PERSON><PERSON>, CorsComponent, Help, Switch } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { isLogicData } from '../../../../../../common/DataSet/utils';
import { isCn } from '../../../../../../../constants';

const { Row, Col } = Layout;

export interface DescDialogProps {
  weId: string;
  config: any;
  lineEndRowData: any;
  fields: any[];
  onValueChange: (rowData: any) => void;
  onClose: () => void;
}
const DescDialog = (props: DescDialogProps) => {
  const { weId, config, lineEndRowData, fields, onValueChange, onClose } = props;
  const [rowData, setRowData] = useState(lineEndRowData);
  const onOk = () => {
    onValueChange(rowData);
    onCancel();
  };
  const onCancel = () => {
    onClose && onClose();
  };
  const onChange = (key: any, content: any, value: any) => {
    if (key === 'desc') {
      if (value.id !== '-1') {
        const fieldItem = fields?.find(i => i.id === value.id);
        value = {
          ...value,
          fieledListMap: fieldItem ? [fieldItem] : [],
        };
      }
      if (!('useStats' in value) && isLogicData(config?.dataset)) {
        value.useStats = true;
      }
      setRowData({ ...rowData, descField: value });
    } else if (key === 'useStats') {
      setRowData({ ...rowData, descField: { ...rowData.descField, useStats: value } });
    }
  };
  const buttons = [
    <Button weId={`${weId || ''}_785mpg`} key="save" type="primary" onClick={onOk}>
      {getLabel('221901', '确定')}
    </Button>,
    <Button weId={`${weId || ''}_7nlxf7`} key="onCancel" onClick={onCancel}>
      {getLabel('53937', '取消')}
    </Button>,
  ];
  const helpTip = (
    <div>
      <p>{getLabel('252900', '填写说明')}:</p>
      <p>{getLabel('256078', '页面存在数据源时，支持输入“#”，在弹出的字段列表中选择字段插入到内容中')}；</p>
      <p>{getLabel('256079', '如需在分组描述中显示数字字段/金额字段总值，需要先在卡片设置里显示该字段')}；</p>
    </div>
  );
  const customFieldHelpTip = {
    id: (
      <div>
        <div>1.{getLabel('256155', '仅支持选择数字和金额字段，选择后，会同步该分组下数字/金额总和')}</div>
        <div>2.{getLabel('256156', '显示形式：字段名称+金额（示例：商机金额：20,000元）')}</div>
      </div>
    ),
  };
  const descField = rowData.descField || {};
  let hasNumberField = false;
  if ((descField.content || descField.id !== '-1') && isLogicData(config?.dataset)) {
    // 过滤掉数据id
    hasNumberField = descField?.fieledListMap?.some((i: any) => (i.type === 'Number' || i.type === 'Money') && i.fieldId !== '5') || false;
  }
  return (
    <Dialog weId={`${weId || ''}_i1kb6v`} footer={buttons} width={550} destroyOnClose visible title={getLabel('253192', '分组描述')} mask closable onClose={onCancel} icon={'Icon-e-builder-o'}>
      <CorsComponent
        weId={`${weId || ''}_ogxzpx`}
        app="@weapp/ebdgantt"
        compName="FieldSettingLayoutCom"
        config={config}
        fieldData={rowData.descField}
        onChange={(key: any, content: any, value: any) => onChange('desc', content, value)}
        // excludeSelectNumOption
        dataset={config?.dataset}
        // 数据源字段可选控制
        customShowFieldType={['Number']}
        // 自定义富文本help提示展示
        customTextHelpTip={helpTip}
        customFieldHelpTip={customFieldHelpTip}
      />
      {hasNumberField && (
        <div className="ui-formItem ui-formItem-module" style={{ display: 'flex', alignItems: 'center' }}>
          <Row weId={`${weId || ''}_7n4ue2`} style={{ width: '100%', alignItems: 'center' }}>
            <Col weId={`${weId || ''}_wqhkx1`} span={isCn ? 4 : 8}>
              <div className="ui-formItem-label ui-formItem-label-normal">
                <div className="ui-formItem-label-span">
                  {getLabel('245989', '开启统计')}
                  <Help weId={`${weId || ''}_ymfm6g`} title={getLabel('277175', '开启统计后，当前选择的数字类型字段将被全部统计，并显示在分组描述中')} />
                </div>
              </div>
            </Col>
            <Col weId={`${weId || ''}_n8ma20`} span={isCn ? 20 : 16}>
              <div className="ui-layout-col ui-layout-col-20 ui-formItem-wrapper-col">
                <Switch weId={`${weId || ''}_tomgxs`} onChange={(checked: boolean) => onChange('useStats', '', checked)} value={descField.useStats} />
              </div>
            </Col>
          </Row>
        </div>
      )}
    </Dialog>
  );
};

export default DescDialog;
