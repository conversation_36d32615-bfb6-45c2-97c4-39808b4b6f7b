import { useMemo } from 'react';
import { Layout } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { GroupDialogLeftSpan } from '../constants';
import { isLogicData, isEbFormDataV2 } from '../../../../../../common/DataSet/utils';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { BoardConfigData } from '../../../../../../baseBoard/types';
import { DataSetType } from '../../../../../types';
import NSelect from '../../../../../../common/n-select';

const { Row, Col } = Layout;

export interface GroupFieldProps {
  weId: string;
  fieldGroup: GroupDataOptionItemField;
  onChange?: (value: GroupDataOptionItemField) => void;
  cls: string;
  optionField?: any[];
  config: BoardConfigData;
  datasetType: DataSetType;
  isYd?: boolean
}
const GroupField = (props: GroupFieldProps) => {
  const { cls, config, optionField = [], fieldGroup = {}, datasetType, isYd } = props;

  const useOptionField = useMemo(() => {
    //业务数据源屏蔽关联ebuilder分组
    let newOptionField = optionField || [];
    // 如果是当前表字段 需要过滤关联类型
    if (datasetType === DataSetType.Current) {
      newOptionField = newOptionField.filter(i => i.type !== EtComponentKey.Ebuilder && i.type !== EtComponentKey.RelateBrowser);
    }
    if (isLogicData(config?.dataset)) {
      return newOptionField.filter(i => i.type !== EtComponentKey.Ebuilder);
    }
    if (isEbFormDataV2(config)) {
      // 表单高级视图下暂时不放开关联ebuilder
      // if (isFromEbAdvance || config?.fromEbuilder) {
      //   return optionField.filter(i => i.type !== EtComponentKey.Ebuilder)
      // }
      return newOptionField;
    }
    // 非eb数据源不显示自定义分组 默认按字段分组
    return [{ id: '-1', content: getLabel('40502', '请选择') }, ...newOptionField];
  }, [optionField, config, datasetType]);

  const selectField = (value: any) => {
    const groupFiled = {
      fieldId: value,
      fieldType: useOptionField.find(i => i.id === value)?.type,
    };
    props.onChange?.({ ...fieldGroup, ...groupFiled });
  };

  return (
    <Row weId={`${props.weId || ''}_8ny9uo`} className={`${cls}-item`}>
      <Col weId={`${props.weId || ''}_m4h8cv`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText weId={`${props.weId || ''}_u22uli`} title={isYd ? getLabel('291679', '泳道字段') : getLabel('107799', '分组字段')} />
      </Col>
      <Col weId={`${props.weId || ''}_shwz41`} className={`${cls}-item-right`}>
        <NSelect weId={`${props.weId || ''}_8kmgfs`} data={useOptionField} style={{ width: 160 }} value={fieldGroup.fieldId} onChange={selectField} />
      </Col>
    </Row>
  );
};

export default GroupField;
