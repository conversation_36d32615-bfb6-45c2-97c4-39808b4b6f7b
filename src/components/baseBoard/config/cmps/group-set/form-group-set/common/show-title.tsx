// 废弃 先不使用
import { useState, useMemo } from 'react';
import { Layout, Icon, Button, Dialog, Tag } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { getTargetFieldsByType } from '../../../../../engine/func-groups/utils';
import { GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { GroupDialogLeftSpan, DataSetHelpInfo } from '../constants';
import { dlgIconName } from '../../../../../../../constants';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import NSettingIcon from '../../../../../../common/n-setting-icon';

const { Row, Col } = Layout;
const diffKey = `GroupShowTitle`;

export interface ShowTitleProps {
  weId: string;
  cls: string;
  rowData?: GroupDataOptionItem;
  onChange: (value: GroupDataOptionItem) => void;
  titleRowSpan?: number;
  fields: any[];
  clearFunc: () => void;
}
const ShowTitle = (props: ShowTitleProps) => {
  const { rowData, clearFunc, titleRowSpan, weId, cls, fields } = props;

  const [showTitle, setShowTitle] = useState(false);
  const [showFieldValue, setShowFieldValue] = useState('');

  const clickFunc = () => {
    setShowTitle(true);
  };
  const renderTitle = () => {
    const { onChange } = props;
    if (!showTitle) return null;
    const close = () => {
      setShowTitle(false);
    };
    const onConfirm = () => {
      onChange({ ...rowData, showField: showFieldValue });
      close();
    };
    const buttons = [
      <Button weId={`${props.weId || ''}_tg84pa@${diffKey}`} key="save" type="primary" onClick={onConfirm}>
        {getLabel('221901', '确定')}
      </Button>,
      <Button weId={`${props.weId || ''}_q1ftin@${diffKey}`} key="onCancel" onClick={close}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const setShowField = (field: any) => {
      setShowFieldValue(showFieldValue === field.id ? '' : field.id);
    };
    // 与后端lzh沟通 不处理id和标题
    const data = getTargetFieldsByType(fields, EtComponentKey.Text).filter(i => `${i.id}` !== '5');
    return (
      <Dialog weId={`${props.weId || ''}_9ffckz`} title={getLabel('277036', '分组标题设置')} footer={buttons} width={500} destroyOnClose visible mask closable onClose={close} icon={dlgIconName}>
        <div className={`${cls}-groupTitle`}>
          {data.map(item => {
            return (
              <div key={item.id} className={`${cls}-groupTitle-tag`} onClick={() => setShowField(item)}>
                <Tag weId={`${props.weId || ''}_62zb95@${diffKey}`} type={showFieldValue === item.id ? 'primary' : 'default'}>
                  {item.content}
                </Tag>
              </div>
            );
          })}
        </div>
      </Dialog>
    );
  };

  const hasValue = !isEmpty(rowData?.showField);

  return (
    <>
      <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
        <Col weId={`${weId || ''}_tvyw6n`} span={titleRowSpan || GroupDialogLeftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('264753', '分组标题')} helpInfo={DataSetHelpInfo} placement="leftTop" />
        </Col>
        <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
          <NSettingIcon weId={`${props.weId || ''}_c2qak2`} hasValue={!isEmpty(rowData?.showField)} onClick={clickFunc} clearable={hasValue} clearFunc={clearFunc} />
        </Col>
      </Row>
      {renderTitle()}
    </>
  );
};

export default ShowTitle;
