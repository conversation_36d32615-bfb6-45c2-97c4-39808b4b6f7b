// 标准看板---其他表--显示标题配置
import { useState, useEffect } from 'react';
import { Layout, Button, Dialog, Tag } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { getTargetFieldsByType } from '../../../../../engine/func-groups/utils';
import { GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import CardSet from '../../../../../engine/func-groups/CardSet';
import { GroupDialogLeftSpan, DataSetHelpInfo } from '../constants';
import { dlgIconName } from '../../../../../../../constants';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import NSettingIcon from '../../../../../../common/n-setting-icon';

const { Row, Col } = Layout;
const diffKey = `GroupShowTitle`;

export interface ShowTitleProps {
  weId: string;
  cls: string;
  rowData?: GroupDataOptionItem;
  dataSet?: any;
  required?: boolean;
  onChange: (value: GroupDataOptionItem) => void;
  titleRowSpan?: number;
  fields: any[];
  clearFunc: () => void;
  appId?: string;
  isCard?: boolean;
}
const ShowTitle = (props: ShowTitleProps) => {
  const { rowData, clearFunc, titleRowSpan, weId, cls, fields, dataSet, appId = '', isCard, required } = props;

  const [showTitle, setShowTitle] = useState(false);
  const [showFieldValue, setShowFieldValue] = useState('1'); // 默认填充当前分组title为1

  useEffect(() => {
    if (!isEmpty(rowData?.showField)) {
      setShowFieldValue(rowData?.showField || '');
    }
  }, [rowData?.showField]);

  const clickFunc = () => {
    setShowTitle(true);
  };
  const renderTitle = () => {
    const { onChange } = props;
    if (!showTitle || isCard) return null;
    const close = () => {
      setShowTitle(false);
    };
    const onConfirm = () => {
      onChange({ ...rowData, showField: showFieldValue });
      close();
    };
    const buttons = [
      <Button weId={`${props.weId || ''}_tg84pa@${diffKey}`} key="save" type="primary" onClick={onConfirm}>
        {getLabel('221901', '确定')}
      </Button>,
      <Button weId={`${props.weId || ''}_q1ftin@${diffKey}`} key="onCancel" onClick={close}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const setShowField = (field: any) => {
      setShowFieldValue(showFieldValue === field.id ? '' : field.id);
    };
    // 与后端lzh沟通 不处理id和标题
    const data = getTargetFieldsByType(fields, EtComponentKey.Text).filter(i => `${i.id}` !== '5');
    return (
      <Dialog weId={`${props.weId || ''}_9ffckz`} title={getLabel('277036', '分组标题设置')} footer={buttons} width={500} destroyOnClose visible mask closable onClose={close} icon={dlgIconName}>
        <div className={`${cls}-groupTitle`}>
          {data.map(item => {
            return (
              <div key={item.id} className={`${cls}-groupTitle-tag`} onClick={() => setShowField(item)}>
                <Tag weId={`${props.weId || ''}_62zb95@${diffKey}`} type={showFieldValue === item.id ? 'primary' : 'default'}>
                  {item.content}
                </Tag>
              </div>
            );
          })}
        </div>
      </Dialog>
    );
  };

  const renderSetIcon = () => {
    if (isCard) {
      const hasValue = rowData?.showField?.cardLayout?.grid.reduce((acc: boolean, subArray: any[]) => acc && subArray.length > 0, true);
      const { onChange } = props;
      const onConfirm = (type: any, value: any) => {
        onChange({ ...rowData, showField: value });
      };
      return (
        <CardSet
          weId={`${props.weId || ''}_xfuwcf`}
          dataset={dataSet}
          appid={appId}
          isIconBtn
          hasValue={hasValue}
          value={rowData?.showField}
          onChange={onConfirm}
          clearable={hasValue}
          clearFunc={clearFunc}
        />
      );
    }
    const hasValue = !isEmpty(rowData?.showField);
    return <NSettingIcon weId={`${props.weId || ''}_c2qak2`} hasValue={hasValue} onClick={clickFunc} />;
  };

  return (
    <>
      <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
        <Col weId={`${weId || ''}_tvyw6n`} span={titleRowSpan || GroupDialogLeftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('93225', '显示设置')} />
          {required && <div className={`${cls}-item-left-required`} />}
        </Col>
        <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
          {renderSetIcon()}
        </Col>
      </Row>
      {renderTitle()}
    </>
  );
};

export default ShowTitle;
