import { Layout } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import DataSet from '../../../../../../common/DataSet';
import { GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { GroupDialogLeftSpan, DataSetHelpInfo } from '../constants';

const { Row, Col } = Layout;

export interface GroupSetDatasetProps {
  weId: string;
  value: any;
  onChange?: (value: GroupDataOptionItemField) => void;
  cls: string;
}
const GroupSetDataset = (props: GroupSetDatasetProps) => {
  const { cls, weId, value, onChange } = props;
  const handleChange = (value: any) => {
    onChange?.(value)
  };

  return (
    <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
      <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('-1', '数据源')} helpInfo={DataSetHelpInfo} placement="leftTop" />
      </Col>
      <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
        <div style={{ width: 200 }}>
          <DataSet weId={`${props.weId || ''}_igekwx`} value={value} onChange={handleChange} disabled={true} />
        </div>
      </Col>
    </Row>
  );
};

export default GroupSetDataset;
