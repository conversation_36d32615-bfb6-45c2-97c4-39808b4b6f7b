// 默认分组配置
import { Layout } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { GroupDialogLeftSpan } from '../constants';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { EbBrowser } from '../../../../../../common/eb-switch/EbBrowser';

const { Row, Col } = Layout;

export interface DefaultGroupProps {
  weId: string;
  cls: string;
  rowData?: GroupDataOptionItem;
  onChange: (value: GroupDataOptionItem) => void;
  titleRowSpan?: number;
}
const DefaultGroup = (props: DefaultGroupProps) => {
  const { rowData, titleRowSpan, weId, cls, onChange } = props;


  // *仅按字段分组支持-不需要单独再给数据源分组适配
  const renderDefaultGroup = () => {
    const propsValue = rowData?.defaultGroup;
    const field = {
      componentKey: EtComponentKey.Ebuilder,
      title: getLabel('40502', '请选择'),
      fieldId: rowData?.fieldId,
    };
    return (
      <EbBrowser weId={`${props.weId || ''}_gh7qra`}
        field={field as any}
        value={propsValue}
        onChange={(value: any) => {
          onChange({ ...rowData, defaultGroup: toJS(value) });
        }}
      />
    );
  };

  return (
    <>
      <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
        <Col weId={`${weId || ''}_tvyw6n`} span={titleRowSpan || GroupDialogLeftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('151785', '默认分组')} />
        </Col>
        <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
          {renderDefaultGroup()}
        </Col>
      </Row>
    </>
  );
};

export default DefaultGroup;
