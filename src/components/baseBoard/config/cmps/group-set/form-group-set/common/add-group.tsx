/**
 * 新建分组配置
 */
import { useState, useEffect, useMemo } from 'react';
import { Layout, Switch, CorsComponent, TypesBrowserOption, Dialog, Button, Icon } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDialogLeftSpan } from '../constants';
import { companyAndSeclevelProps } from '../../../../../engine/func-groups/types';
import { commonBrowserTypes, BrowserTypes } from '../../../../../engine/func-groups/constants';
import { getRsFieldFormId, getRelationFieldOpts } from '../../utils';
import NSettingIcon from '../../../../../../common/n-setting-icon';

const { Row, Col } = Layout;

export interface AddGroupProps {
  weId: string;
  rootField?: string;
  showPermiAdd: boolean;
  value?: any[];
  fields?: any[];
  onChange?: (showPermiAdd: boolean, value: any[]) => void;
  cls: string;
  companyAndSeclevel: companyAndSeclevelProps;
}
const AddGroup = (props: AddGroupProps) => {
  const { cls, weId, value, onChange, companyAndSeclevel, rootField = '', fields = [], showPermiAdd = false } = props;

  const [isShowAdd, setIsShowAdd] = useState(false);
  const [showPermi, setShowPermi] = useState(false);
  const [rootFieldObjId, setRootFieldObjId] = useState('');

  const updetePermissions = (data: any[]) => {
    onChange?.(showPermi, data);
  };
  const getAuthColumns = () => [
    {
      dataIndex: 'selectedType',
      title: getLabel('53872', '类型'),
      width: '20%',
      bodyRender: (data: any) => {
        const item = getAuthOptions.find((d: TypesBrowserOption) => d.id === data.selectedType);
        return <span title={item?.content || ''}>{item?.content}</span>;
      },
    },
    {
      dataIndex: 'object',
      title: getLabel('54042', '对象'),
      width: '50%',
      bodyRender: (data: any) => {
        const { getAimContent } = window.weappEbdform.ebdformUtils?.authTableUtils;
        const { newName } = getAimContent?.(data);
        return (
          <span>
            <span className="hrm_span" dangerouslySetInnerHTML={{ __html: newName }} />
          </span>
        );
      },
    },
    {
      dataIndex: 'levelScope',
      title: getLabel('52100', '安全级别'),
      width: '30%',
      bodyRender: (data: any) => {
        const { getLevelSpan } = window.weappEbdform.ebdformUtils?.authTableUtils;
        const level = getLevelSpan?.(data.levelScope || {});
        return <span>{` ${level ? `${level}` : ''}`}</span>;
      },
    },
  ];
  const getAuthOptions = useMemo(() => {
    const options: TypesBrowserOption[] = [...commonBrowserTypes];
    if (companyAndSeclevel.supportCompany) {
      const company: TypesBrowserOption = {
        id: BrowserTypes.Company,
        content: getLabel('222259', '分部'),
      };
      options.splice(2, 0, company);
    }
    return options;
  }, [companyAndSeclevel]);

  const handleShowAdd = (show: boolean) => {
    onChange?.(show, value!);
  }

  useEffect(() => {
    const getRootFieldObjId = () => {
      const _relationFieldOpts = getRelationFieldOpts(fields);
      const _rootFieldObjId = getRsFieldFormId(rootField, _relationFieldOpts);
      setRootFieldObjId(_rootFieldObjId);
    };
    getRootFieldObjId();
  }, [rootField, fields]);

  useEffect(() => {
    setIsShowAdd(showPermiAdd);
  }, [showPermiAdd]);


  const renderPerm = () => {
    if (!showPermi) return null;
    return (
      <Dialog
        weId={`${props.weId || ''}_vuynwj`}
        visible
        title={getLabel('-1', '权限设置')}
        closable
        icon="Icon-e-builder-o"
        destroyOnClose
        onClose={() => setShowPermi(false)}
        width={450}
        wrapClassName={cls}
        buttons={[
          <Button weId={`${props.weId || ''}_werfs45@ok`} key="ok" type="primary" onClick={() => setShowPermi(false)}>
            {getLabel('-1', '保存')}
          </Button>,
        ]}
      >
        <CorsComponent
          weId={`${props.weId || ''}_vv5dgx`}
          app="@weapp/ebdform"
          compName="AuthTable"
          title={getLabel('186558', '新建分组权限')}
          data={value}
          onChange={updetePermissions}
          getOriginColumns={getAuthColumns}
          showSeclevel={companyAndSeclevel.supportSeclevel}
          showCompany={companyAndSeclevel.supportCompany}
          authAddProps={{
            needOrgVirtualType: true,
            options: getAuthOptions,
            associateModuleFieldObjId: rootFieldObjId,
          }}
          tableProps={{
            scroll: { y: 420 },
          }}
        />
      </Dialog>
    );
  };
  return (
    <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
      <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('-1', '新建分组')} />
      </Col>
      <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
        <Switch weId={`${props.weId || ''}_5db5h5`} value={isShowAdd} onChange={() => handleShowAdd(!isShowAdd)} />
        {isShowAdd && (
          <div className={`${cls}-item-right-permi`}>
            <span>{getLabel('-1', '权限设置')}</span>
            <NSettingIcon weId={`${props.weId || ''}_iz278e`} onClick={() => setShowPermi(true)} />
          </div>
        )}
      </Col>
      {renderPerm()}
    </Row>
  );
};

export default AddGroup;
