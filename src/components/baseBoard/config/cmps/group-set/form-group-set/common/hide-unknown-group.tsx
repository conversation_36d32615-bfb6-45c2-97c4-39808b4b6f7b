import { Layout, Switch } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDialogLeftSpan } from '../constants';

const { Row, Col } = Layout;

export interface HideUnknownGroupProps {
  weId: string;
  value?: boolean;
  onChange?: (value: boolean) => void;
  cls: string;
}
const HideUnknownGroup = (props: HideUnknownGroupProps) => {
  const { cls, weId, value, onChange } = props;
  return (
    <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
      <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('-1', '隐藏未分组')} />
      </Col>
      <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
        <Switch weId={`${props.weId || ''}_lrlvv5`} value={value} onChange={onChange} />
      </Col>
    </Row>
  );
};

export default HideUnknownGroup;
