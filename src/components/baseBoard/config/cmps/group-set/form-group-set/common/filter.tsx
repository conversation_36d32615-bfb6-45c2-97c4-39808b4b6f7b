import { useState, useMemo } from 'react';
import { Layout, CorsComponent } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { fillGroupFilter } from '../../../../../engine/func-groups/utils';
import { GroupDialogLeftSpan } from '../constants';
import { specialFieldsFilter } from '../../../../../../../utils';
import { DataSetItem } from '../../../../../../common/DataSet/types';
import NSettingIcon from '../../../../../../common/n-setting-icon';

const { Row, Col } = Layout;

export interface GroupSetFilterProps {
  weId: string;
  cls: string;
  rowData?: GroupDataOptionItem;
  onChange: (value: GroupDataOptionItem) => void;
  titleRowSpan?: number;
  fields: any[];
  dataSet?: DataSetItem;
}
const GroupSetFilter = (props: GroupSetFilterProps) => {
  const { cls, weId, rowData, onChange, dataSet, fields } = props;

  const [visible, setVisible] = useState(false);

  const renderFilter = () => {
    if (!dataSet?.id) return null;
    const close = () => {
      setVisible(false);
    };
    const onOk = (data: any) => {
      onChange({ ...rowData, filter: data });
      close();
    };
    const validFilter = fillGroupFilter(rowData?.filter, fields);
    return (
      <CorsComponent weId={`${props.weId || ''}_tlivxo`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        dataSet={dataSet}
        visible
        title={getLabel('105959', '看板视图固定查询条件')}
        onCancel={close}
        onClear={onOk}
        onOk={onOk}
        fieldFilter={specialFieldsFilter}
        value={validFilter}
      />
    );
  };

  const hasValue = useMemo(() => {
    return !isEmpty(rowData?.filter)
  }, [rowData])

  return (
    <>
      <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
        <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
          <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('53857', '数据过滤')} placement="leftTop" />
        </Col>
        <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
          <NSettingIcon weId={`${props.weId || ''}_psj0ts`} hasValue={hasValue} onClick={() => setVisible(true)} clearable={hasValue} clearFunc={() => onChange({ ...rowData, filter: {} })} />
        </Col>
      </Row>
      {visible && renderFilter()}
    </>
  );
};

export default GroupSetFilter;
