import { useEffect, useState } from 'react';
import { Layout } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { GroupDialogLeftSpan } from '../constants';
import { GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { getRelationBrowserFieldOpts } from '../../utils';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import NSelect from '../../../../../../common/n-select';

const { Row, Col } = Layout;

export interface LinkFieldProps {
  weId: string;
  fieldGroup: GroupDataOptionItemField;
  onChange?: (value: GroupDataOptionItemField) => void;
  cls: string;
  linkFields: any[];
  loading?: boolean
}
const LinkField = (props: LinkFieldProps) => {
  const { cls, weId, fieldGroup, onChange, linkFields = [], loading } = props;

  const [fields, setFields] = useState<any[]>([]);

  useEffect(() => {
    setFields(getRelationBrowserFieldOpts(linkFields));
  }, [linkFields]);

  const handleChange = (value: any) => {
    const newLinkField = fields.find(item => item.id === value);
    onChange?.({ ...fieldGroup, fieldId: newLinkField.id, fieldType: EtComponentKey.Ebuilder });
  };
  return (
    <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
      <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText
          weId={`${props.weId || ''}_88tj4c`}
          title={getLabel('-1', '关联关系')}
          helpInfo={[getLabel('-1', '设置当前数据源和看板数据源之间的关系字段，字段选择来源于【看板数据设置】-数据源中的关联e-bulider字段')]}
        />
      </Col>
      <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
        <NSelect weId={`${props.weId || ''}_kig7o7`} data={fields} style={{ width: 200 }} value={fieldGroup.fieldId} onChange={handleChange} loading={loading} />
      </Col>
    </Row>
  );
};

export default LinkField;
