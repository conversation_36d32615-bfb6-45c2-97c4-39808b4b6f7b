// 这里本质
import { Layout, Radio } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { TitleText } from '../../../../../engine/func-groups/Common';
import { DataSetTypeItem } from '../../../../../types';
import { GroupDialogLeftSpan, DataSetHelpInfo, GroupDataSetTypeOptions } from '../constants';

const { Row, Col } = Layout;

export interface DatasetTypeProps {
  weId: string;
  onChange: (value: DataSetTypeItem) => void;
  cls: string;
  value?: DataSetTypeItem;
}
const DatasetType = (props: DatasetTypeProps) => {
  const { cls, weId, onChange, value } = props;

  return (
    <Row weId={`${weId || ''}_oh1gbx`} className={`${cls}-item`}>
      <Col weId={`${weId || ''}_tvyw6n`} span={GroupDialogLeftSpan} className={`${cls}-item-left`}>
        <TitleText weId={`${props.weId || ''}_88tj4c`} title={getLabel('-1', '数据源')} helpInfo={DataSetHelpInfo} placement="leftTop" />
      </Col>
      <Col weId={`${props.weId || ''}_zenixw`} className={`${cls}-item-right`}>
        <Radio weId={`${props.weId || ''}_hkuo1p`} data={GroupDataSetTypeOptions} value={value} onChange={value => onChange(value as DataSetTypeItem)} />
      </Col>
    </Row>
  );
};

export default DatasetType;
