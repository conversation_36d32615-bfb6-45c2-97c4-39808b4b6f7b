import { ComponentType } from 'react';
import Loadable from '../../../../../../react-loadable';
import { DatasetTypeProps } from './dataset-type';
import { GroupFieldProps } from './group-field';
import { GroupDescProps } from './group-desc';
import { HideUnknownGroupProps } from './hide-unknown-group';
import { HideNoDataGroupProps } from './hide-no-data-group';
import { RenameUnknownGroupProps } from './rename-unknown-group';
import { AddGroupProps } from './add-group';
import { DescDialogProps } from './desc-dialog';
import { CommonFieldProps } from './common-field';
import { GroupSetDatasetProps } from './data-set';
import { LinkFieldProps } from './link-field';
import { GroupSetFilterProps } from './filter';
import { GroupSetSortProps } from './sort';
import { ShowTitleProps } from './show-title';
import { DefaultGroupProps } from './default-group';

const GroupField = Loadable({
  name: 'DatasetType',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_groupField" */
      './group-field'
    ),
}) as ComponentType<GroupFieldProps>;

const HideUnknownGroup = Loadable({
  name: 'HideUnknownGroup',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_hideUnknownGroup" */
      './hide-unknown-group'
    ),
}) as ComponentType<HideUnknownGroupProps>;

const GroupDesc = Loadable({
  name: 'GroupDesc',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_groupDesc" */
      './group-desc'
    ),
}) as ComponentType<GroupDescProps>;

const DatasetType = Loadable({
  name: 'DatasetType',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_datasetType" */
      './dataset-type'
    ),
}) as ComponentType<DatasetTypeProps>;

const HideNoDataGroup = Loadable({
  name: 'HideNoDataGroup',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_hideNoDataGroup" */
      './hide-no-data-group'
    ),
}) as ComponentType<HideNoDataGroupProps>;

const RenameUnknownGroup = Loadable({
  name: 'RenameUnknownGroup',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_renameUnknownGroup" */
      './rename-unknown-group'
    ),
}) as ComponentType<RenameUnknownGroupProps>;

const AddGroup = Loadable({
  name: 'AddGroup',
  loader: () =>
    import(
      /* webpackChunkName: "ebdboard_groupSet_addGroup" */
      './add-group'
    ),
}) as ComponentType<AddGroupProps>;

const DescDialog = Loadable({
  name: 'DescDialog',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_descDialog" */ './desc-dialog'),
}) as ComponentType<DescDialogProps>;

const CommonField = Loadable({
  name: 'CommonField',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_commonField" */ './common-field'),
}) as ComponentType<CommonFieldProps>;

const GroupSetDataset = Loadable({
  name: 'GroupSetDataset',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_groupSetDataset" */ './data-set'),
}) as ComponentType<GroupSetDatasetProps>;

const LinkField = Loadable({
  name: 'LinkField',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_linkField" */ './link-field'),
}) as ComponentType<LinkFieldProps>;

const GroupSetFilter = Loadable({
  name: 'GroupSetFilter',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_groupSetFilter" */ './filter'),
}) as ComponentType<GroupSetFilterProps>;

const GroupSetSort = Loadable({
  name: 'GroupSetSort',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_groupSetSort" */ './sort'),
}) as ComponentType<GroupSetSortProps>;

const ShowCardTitle = Loadable({
  name: 'ShowTitle',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_showTitle" */ './show-title'),
}) as ComponentType<ShowTitleProps>;

const DefaultGroup = Loadable({
  name: 'DefaultGroup',
  loader: () => import(/* webpackChunkName: "ebdboard_groupSet_defaultGroup" */ './default-group'),
}) as ComponentType<DefaultGroupProps>;


export { DatasetType, GroupField, GroupDesc, HideUnknownGroup, HideNoDataGroup, RenameUnknownGroup, AddGroup, DescDialog, CommonField, GroupSetDataset, LinkField, GroupSetFilter, GroupSetSort, ShowCardTitle, DefaultGroup };
