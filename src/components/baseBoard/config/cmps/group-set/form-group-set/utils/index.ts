import { isEmpty, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { GroupDataProps, GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { GroupType } from '../../../../../../../constants/common';
import { INIT_FIELD_GROUP_VALUE, INIT_YD_FIELD_GROUP_HANDLE_VALUE } from '../constants';
import { LaneConfigProps, LaneDataSource, LaneGroupProps } from '../../../yd/types';
import { RelationGroupsType } from '../../../../../engine/func-groups/diff/constants';
import { isLogicData, isEteamsData, isEbFormDataV2 } from '../../../../../../common/DataSet/utils';

// * 老数据或者新拉出的数据-按字段分组兼容
export const fixFieldGroupData = (groupData: GroupDataProps): GroupDataProps => {
  const { otherData, permissions, displaySealing } = groupData;
  const fieldGroupIdx = groupData.options?.findIndex((i: any) => `${i.groupType}` === GroupType.field);
  if (fieldGroupIdx === -1 || fieldGroupIdx === undefined) return groupData;

  let fieldGroupValue = groupData.options[fieldGroupIdx];
  if (fieldGroupValue && (!('currentGroupField' in fieldGroupValue) || !('otherGroupField' in fieldGroupValue))) {
    const newFieldGroupValue = { ...fieldGroupValue, ...INIT_FIELD_GROUP_VALUE() };
    const datasetType = fieldGroupValue.fieldType === EtComponentKey.Ebuilder ? 'otherGroupField' : 'currentGroupField';
    // 兼容新的其他、当前表
    if (newFieldGroupValue[datasetType]) {
      for (const key in newFieldGroupValue[datasetType]) {
        // @ts-ignore
        newFieldGroupValue[datasetType][key] = fieldGroupValue[key];
      }
      // @ts-ignore
      newFieldGroupValue[datasetType] = { ...newFieldGroupValue[datasetType], otherData, permissions, displaySealing, showPermiAdd: !isEmpty(permissions) };
      // 如果数据源用的其他表 则使用fieldId关联关系的字段id
      if (datasetType === 'otherGroupField' && newFieldGroupValue[datasetType].fieldId) {
        newFieldGroupValue[datasetType].linkField = {
          id: newFieldGroupValue[datasetType].fieldId,
          compType: newFieldGroupValue[datasetType].fieldType,
        };
      }
    }
    groupData.options[fieldGroupIdx] = newFieldGroupValue;
  }

  return groupData;
};

// * 前端操作当前表其他表冗余一份数据
export const fixLaneGroupData = (laneGroupData: LaneConfigProps): LaneConfigProps => {
  const { laneGroupHandle, laneGroup } = laneGroupData;
  const initialData = INIT_YD_FIELD_GROUP_HANDLE_VALUE();

  if (isEmpty(laneGroupHandle)) {
    laneGroupData.laneGroupHandle = INIT_YD_FIELD_GROUP_HANDLE_VALUE(laneGroup?.active);
    if (!isEmpty(laneGroup)) {
      const key = laneGroup.groupSource === LaneDataSource.other ? 'otherGroupField' : 'currentGroupField';
      for (let i in initialData[key]) {
        laneGroupData.laneGroupHandle[key][i] = laneGroup[i as keyof typeof laneGroup];
      }
    }
  }

  return laneGroupData;
};

export const checkRequired = (value: GroupDataProps) => {
  const _type = `${value?.active}`;
  const optValue = (value as GroupDataProps).options?.find(i => `${i.groupType}` === _type);
  const defaultText = getLabel('40502', '请选择');
  // 按字段分组校验
  if (_type === GroupType.field) {
    // 当选中关联ebuilder字段时 强制校验分组标题
    if (optValue?.fieldType === EtComponentKey.Ebuilder) {
      // if (!optValue.showField) {
      //   return `${defaultText}${getLabel('264753', '分组标题')}`;
      // }
      if (!optValue.fieldId) {
        return `${defaultText}${getLabel('310832', '关联关系')}`;
      }
    }
  } else if (_type === GroupType.dateRange) {
    // 日期分组需要都校验
    if (!optValue?.fieldId) {
      return `${defaultText}${getLabel('56080', '日期字段')}`;
    }
    if (!optValue.dateRange) {
      return `${defaultText}${getLabel('189910', '日期范围')}`;
    }
  } else if (_type === GroupType.diff || _type === RelationGroupsType.Normal || _type === RelationGroupsType.Custom) {
    // 差异化分组要校验关联字段
    if (_type === RelationGroupsType.Custom && !optValue?.rootDataId) {
      return `${defaultText}${getLabel('186580', '关联字段')}`;
    }
  }
  return '';
};

export const checkYdRequired = (value: LaneGroupProps) => {
  const _type = `${value?.active}`;
  const defaultText = getLabel('40502', '请选择');
  // 按字段分组校验
  if (_type === GroupType.field) {
    // 当选中关联ebuilder字段时 强制校验分组标题
    if (value?.groupSource === LaneDataSource.other) {
      if (!value?.field?.id) {
        return `${defaultText}${getLabel('310832', '关联关系')}`;
      }
      if (isEmpty(value?.showField)) {
        return `${defaultText}${getLabel('93225', '显示设置')}`;
      }
    } else {
      if (!value?.field?.id) {
        return `${defaultText}${getLabel('291679', '泳道字段')}`;
      }
    }
  } else if (_type === GroupType.dateRange) {
    // 日期分组需要都校验
    if (!value?.field?.id) {
      return `${defaultText}${getLabel('56080', '日期字段')}`;
    }
    if (!value?.dateRange) {
      return `${defaultText}${getLabel('189910', '日期范围')}`;
    }
  } else if (_type === GroupType.filter) {
    if (value?.groupSource === LaneDataSource.other) {
      if (!value?.dataset) {
        return `${defaultText}${getLabel('55058', '数据源')}`;
      }
      if (!value?.field?.id) {
        return `${defaultText}${getLabel('310832', '关联关系')}`;
      }
    }
    if (isEmpty(value?.conditionConfig)) {
      return `${getLabel('56234', '请输入')}${getLabel('54220', '分组')}`;
    }
  }
  return '';
};

export const checkGroupWayHasValue = (value: GroupDataProps) => {
  const _type = `${value?.active}`;
  const optValue = value.options?.find(i => `${i.groupType}` === _type);
  if (!optValue) return false;
  switch (_type) {
    case GroupType.field:
      return !!(optValue.fieldId || optValue.descText);
    case GroupType.custom:
    case GroupType.filter:
      return !isEmpty(optValue.values?.filter(i => `${i.type}` !== '0'));
    case GroupType.dateRange:
      return !!(optValue.dateRange || optValue.fieldId);
    case GroupType.diff:
    case RelationGroupsType.Normal:
    case RelationGroupsType.Custom:
      return !!value?.rootField;
    default:
      return false;
  }
};

export const showAddPermi = (dataset: any, groupValue: any, fieldType?: string) => {
  //业务数据源及数仓屏蔽权限分组
  if (isLogicData(dataset) || isEteamsData(dataset)) {
    return false;
  }
  const groupType = `${groupValue.active}`;
  //日期分组不允许设置
  if (groupType === GroupType.dateRange || groupType === GroupType.filter) {
    return false;
  }
  if (!fieldType) {
    return false;
  }
  // 字段只有为人员、下拉或者单选分组不允许设置
  const useType = [EtComponentKey.Ebuilder, EtComponentKey.RelateBrowser];
  if (!useType.includes(fieldType as any)) {
    return false;
  }
  return true;
};

// 是否显示当前表和其他表
export const showDataSetType = (config: any) => {
  return isEbFormDataV2(config);
};
