import { isEmpty } from '@weapp/utils';
import { GroupDataProps, GroupDataOptionItem } from '../../../../../engine/func-groups/types';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { GroupType } from '../../../../../../../constants/common';
import { INIT_FIELD_GROUP_VALUE, INIT_YD_FIELD_GROUP_VALUE, INIT_YD_FIELD_GROUP_HANDLE_VALUE } from '../constants';
import { LaneConfigProps, LaneDataSource } from '../../../yd/types';

// * 老数据或者新拉出的数据-按字段分组兼容
export const fixFieldGroupData = (groupData: GroupDataProps): GroupDataProps => {
  const { otherData, permissions } = groupData;
  const fieldGroupIdx = groupData.options?.findIndex((i: any) => i.groupType === GroupType.field);
  if (fieldGroupIdx === -1 || fieldGroupIdx === undefined) return groupData;

  let fieldGroupValue = groupData.options[fieldGroupIdx];
  if (fieldGroupValue && (!('currentGroupField' in fieldGroupValue) || !('otherGroupField' in fieldGroupValue))) {
    const newFieldGroupValue = { ...fieldGroupValue, ...INIT_FIELD_GROUP_VALUE() };
    const datasetType = fieldGroupValue.fieldType === EtComponentKey.Ebuilder ? 'otherGroupField' : 'currentGroupField';
    // 兼容新的其他、当前表
    if (newFieldGroupValue[datasetType]) {
      for (const key in newFieldGroupValue[datasetType]) {
        // @ts-ignore
        newFieldGroupValue[datasetType][key] = fieldGroupValue[key];
      }
      // @ts-ignore
      newFieldGroupValue[datasetType] = { ...newFieldGroupValue[datasetType], otherData, permissions };
      // 如果数据源用的其他表 则使用fieldId关联关系的字段id
      if (datasetType === 'otherGroupField' && newFieldGroupValue[datasetType].fieldId) {
        newFieldGroupValue[datasetType].linkField = {
          id: newFieldGroupValue[datasetType].fieldId,
          compType: newFieldGroupValue[datasetType].fieldType,
        };
      }
    }
    groupData.options[fieldGroupIdx] = newFieldGroupValue;
  }

  return groupData;
};

// * 前端操作当前表其他表冗余一份数据
export const fixLaneGroupData = (laneGroupData: LaneConfigProps): LaneConfigProps => {
  const { laneGroupHandle, laneGroup } = laneGroupData;
  const initialData = INIT_YD_FIELD_GROUP_HANDLE_VALUE();

  if (isEmpty(laneGroupHandle)) {
    laneGroupData.laneGroupHandle = INIT_YD_FIELD_GROUP_HANDLE_VALUE();
    if (!isEmpty(laneGroup)) {
      const key = laneGroup.groupSource === LaneDataSource.other ? 'otherGroupField' : 'currentGroupField';
      for (let i in initialData[key]) {
        laneGroupData.laneGroupHandle[key][i] = laneGroup[i as keyof typeof laneGroup];
      }
    }
  }

  return laneGroupData;
};
