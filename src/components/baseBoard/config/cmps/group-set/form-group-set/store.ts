import { isEmpty, forIn, isEqual } from '@weapp/utils';
import { AnyObj } from '@weapp/ui';
import axios from 'axios';
import { toJS, observable, computed, runInAction, action } from 'mobx';
import { GroupDataProps } from '../../../../engine/func-groups/types';
import { DataSetItem } from '../../../../types';
import { GroupType } from '../../../../../../constants/common';

type StoreOptsType = {
  props: any;
  pluginCenter: any;
  isDesign?: boolean;
};
const baseUrl = '/api/ebuilder/coms/kanban/progress';
// 进展看板store
class FormGroupSetStore {
  pluginCenter: any; // 插件中心
  isDesign = false; // 是否为设计器视图
  events = {} as any; // 事件集合
  isFromEbAdvance = false; // 是否为表单高级视图
  dataset = {} as DataSetItem;

  @observable displaySealing = false;
  @observable permissions: any = [];  // 权限
  @observable groupType: GroupType = GroupType.custom;

  @computed
  get isEmptyProjConfig() {
    return 123
  }
  constructor(opts: StoreOptsType) {
    this.isDesign = !!opts.isDesign;
    this.events = opts.props.events;
    // 【统一分组】 做下数据转化，前端区分不同的分组类型
  }
}

export default FormGroupSetStore;
