@import (reference) '../../../../../../../style/prefix.less';


.@{ebdBClsPrefix}-form-group-set-dialog {

  .ui-dialog-body {
    padding: 10px;
  }

  &-body {
    &-wrap {
      background-color: #fff;
      margin-bottom: 10px;
      padding: 16px;
      border-radius: 4px;
    }
  }

  &-item {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    &-left {
      display: flex;
      align-items: center;
    }

    &-right {
      flex: 1;
      display: flex;
      align-items: center;

      &-permi {
        display: flex;
        font-size: 12px;
        align-items: center;
        margin-left: 20px;
        height: 20px;
        line-height: 20px;
        color: var(--main-fc);

        .@{ebdBClsPrefix}-config-icon-set {
          margin-left: 10px;
        }
      }
    }
  }

  // 处理使用别的组件样式兼容问题
  .@{ebdBClsPrefix}-board-group-common-row {
    &+.@{ebdBClsPrefix}-board-group-common-row {
      margin-bottom: 6px;
    }

    .Icon-investigation02 {
      color: var(--regular-fc);

      &:hover {
        color: var(--primary);
      }
    }
  }

  .@{ebdBClsPrefix}-board-group-other-wrap {
    border: none;
    border-radius: 0;

    .@{ebdBClsPrefix}-board-group-common-row {
      border-bottom: none;
      padding-left: 0;
      height: 30px;
      line-height: 30px;

      .ui-layout-col-8 {
        padding-left: 0;
      }

      .weapp-ebdf-locale-wrapper {
        width: 200px;
      }
    }
  }

  &-lane {

    // 泳道分组-条件分组
    &-filter {
      &-top {
        background-color: #fff;
        margin-bottom: 12px;
        padding: 12px;
        border-radius: 3px;
        border: 1px solid var(--border-color);
      }
    }
  }

  &-groupTitle {
    display: flex;
    flex-wrap: wrap;

    &-tag {
      margin: 0 6px 6px 0;
      cursor: pointer;

      .ui-tag:hover {
        background: var(--tag-primary-bg-color);
        color: var(--tag-primary-font-color);
        border-color: var(--tag-primary-border-color);
      }
    }
  }
}