@import (reference) '../../../../../../../style/prefix.less';


.@{ebdBClsPrefix}-form-group-set-dialog {

  .ui-dialog-body {
    padding: 10px;
  }
  .ui-dialog-content{
      background-color: var(--modal-top-bc);
    }

  &-body {
    &-wrap {
      background-color: #fff;
      margin-bottom: 10px;
      padding: 16px;
      border-radius: 4px;
    }
  }

  &-item {
    // padding: 4px 0;
    height: 34px;
    line-height: 34px;
    margin-bottom: 4px;
    &:last-child{
      margin-bottom: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &-left {
      display: flex;
      align-items: center;
      // line-height: 30px;
      &-required{
        position: relative;
        width: 10px;
        height: 100%;
        &::before{
          content: '*';
          position: absolute;
          top: 2px;
          left: 2px;
          font-size: 16px;
          color: red;
        }
      }
    }

    &-right {
      flex: 1;
      display: flex;
      align-items: center;
      overflow: hidden;

      &-permi {
        display: flex;
        font-size: 12px;
        align-items: center;
        margin-left: 20px;
        height: 20px;
        line-height: 20px;
        color: var(--regular-fc);

        .@{ebdBClsPrefix}-n-setting-icon {
          margin-left: 5px;
        }
      }
      .ebcomponents-dataset-view{
        height: 30px;
        line-height: 30px;
      }
    }
  }

  // 处理使用别的组件样式兼容问题
  .@{ebdBClsPrefix}-board-group-common-row {
    &+.@{ebdBClsPrefix}-board-group-common-row {
      margin-bottom: 6px;
    }

    .Icon-investigation02 {
      color: var(--regular-fc);

      &:hover {
        color: var(--primary);
      }
    }
  }

  .@{ebdBClsPrefix}-board-group-other-wrap {
    border: none;
    border-radius: 0;

    .@{ebdBClsPrefix}-board-group-common-row {
      border-bottom: none;
      padding-left: 0;
      height: 30px;
      line-height: 30px;

      .ui-layout-col-8 {
        padding-left: 0;
      }

      .weapp-ebdf-locale-wrapper {
        width: 200px;
      }
    }
  }

  &-lane {

    // 泳道分组-条件分组
    &-filter {
      &-top {
        background-color: #fff;
        margin-bottom: 12px;
        padding: 12px;
        border-radius: 3px;
        border: 1px solid var(--border-color);
      }
    }
  }

  &-groupTitle {
    display: flex;
    flex-wrap: wrap;

    &-tag {
      margin: 0 6px 6px 0;
      cursor: pointer;

      .ui-tag:hover {
        background: var(--tag-primary-bg-color);
        color: var(--tag-primary-font-color);
        border-color: var(--tag-primary-border-color);
      }
    }
  }
  &-otherLine{
    // padding: 4px 0;
  }
}