// 泳道-条件分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle, useMemo, useCallback } from 'react';
import { CorsComponent } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { KanBanDefaultBgColor } from '../../../../../../../constants';
import { deepToJS } from '../../../../../../../utils';
import GroupOptionCom from '../../../../../engine/func-groups/GroupOptionCom';
import { CustomGroupItem, GroupDataProps } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '../';
import '../style/index.less';

const _tempGroup: CustomGroupItem = {
  type: '1',
  id: `temp_${Date.now()}`,
  name: '',
  color: KanBanDefaultBgColor,
};

interface LaneCustomGroupSetProps extends GroupSetProps {}

const FormCustomGroupSet = forwardRef((props: LaneCustomGroupSetProps, ref: any) => {
  const { config, value, appid, isFromEbAdvance, weId } = props;
  const optionsRef = useRef<any[]>(deepToJS(value?.laneGroup?.conditionConfig || []));

  const [options, setOptions] = useState([] as any[]);

  const [lineFilterRowData, setLineFilterRowData] = useState({} as any);

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  // 初始化分组数据
  useEffect(() => {
    let _value = optionsRef.current;
    if (isEmpty(_value)) {
      addCustomGroup();
    } else {
      const newInitValue = _value.map((item: any, index) => {
        if (!item.id) {
          return { ...item, id: `temp_${Date.now()}_${index}` };
        }
        return item;
      });
      setOptions(newInitValue);
    }
  }, []);

  // 处理确认按钮
  const onOk = (cb: (newGroupData: GroupDataProps) => void) => {
    const postOptions = options.map((item: any) => {
      if (!item.id || item.id.startsWith('temp_')) {
        delete item.id;
      }
      return item;
    });
    cb && cb({ ...value, laneGroup: { ...value.laneGroup, conditionConfig: postOptions } });
  };

  // 更新单个选项
  const onChangeSingle = useCallback(
    (rowData: any[]) => {
      let _options = [...options];
      rowData.forEach((item: any) => {
        const idx = options.findIndex((i: any) => i.id === item.id);

        if (idx > -1) {
          _options[idx] = item;
        }
      });
      setOptions(_options);
    },
    [options]
  );

  const changeCustomOption = (rowData: any[]) => {
    setOptions(isEmpty(rowData) ? [_tempGroup] : rowData);
  };
  // 添加自定义分组
  const addCustomGroup = () => {
    const newOptions = [...options, { ..._tempGroup, id: `temp_${Date.now()}` }];
    setOptions(newOptions);
  };

  // 设置筛选项
  const setLineFilters = (filters: any) => {
    console.log('filters', filters);
    setLineFilterRowData(filters);
  };
  const changeLineFilters = useCallback(
    (type: string, value: any) => {
      console.log('type', type, value);
      if (type === 'isOk') {
        onChangeSingle([{ ...lineFilterRowData, filters: value }]);
      }
      setLineFilterRowData({});
    },
    [lineFilterRowData, onChangeSingle]
  );

  const renderFilterCmp = useCallback(() => {
    if (isEmpty(lineFilterRowData)) return null;
    return (
      <CorsComponent
        weId={`${props.weId || ''}_bua7ms`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        dataSet={config?.dataset}
        value={lineFilterRowData?.filters || {}}
        onOk={(value: any) => changeLineFilters('isOk', value)}
        onCancel={() => changeLineFilters('cancel', {})}
        visible
      />
    );
  }, [lineFilterRowData, config?.dataset, props.weId, changeLineFilters]);

  return (
    <>
      <GroupOptionCom
        weId={`${weId || ''}_nvxz34`}
        appId={appid!}
        customShowOption={options}
        addCustomGroup={addCustomGroup}
        changeCustomOption={changeCustomOption}
        setLineFilters={setLineFilters}
        isFromEbAdvance={isFromEbAdvance}
        isFilterGroupWay
      />
      {renderFilterCmp()}
    </>
  );
});

export default FormCustomGroupSet;
