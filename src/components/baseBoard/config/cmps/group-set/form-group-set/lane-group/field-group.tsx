// 表单设计器下的当前表-日期分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle, useMemo } from 'react';
import { If, Then, Else } from 'react-if';
import { isEmpty } from '@weapp/utils';
import { deepToJS } from '../../../../../../../utils';
import { EtComponentKey } from '../../../../../../../constants/EtComponent';
import { GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '..';
import { CommonField, DatasetType, GroupField, GroupSetFilter, GroupSetDataset, LinkField } from '../common';
import { DataSetType, DataSetTypeItem } from '../../../../../types';
import { getFields } from '../../ajaxUtil';
import { fixLaneGroupData } from '../utils';
import { LaneConfigProps, LaneDataSource } from '../../../yd/types';
import '../style/index.less';

interface FormFieldGroupSetProps extends GroupSetProps {
  cls: string;
  optionField?: any[];
  isYdFilterWay?: boolean; // 是否为泳道按条件分组
}

const FormFieldGroupSet = forwardRef((props: FormFieldGroupSetProps, ref: any) => {
  const { config, value, cls, optionField, appid = '', isYdFilterWay } = props;

  const groupValueRef = useRef<LaneConfigProps>(deepToJS(value));
  const [groupValue, setGroupValue] = useState({} as LaneConfigProps);
  const [boardFields, setBoardFields] = useState([] as any[]);
  const [linkFields, setLinkFields] = useState([] as any[]);
  const [groupValueRefreshKey, setGroupValueRefreshKey] = useState(0);
  const [linkDataset, setLinkDataset] = useState({} as any);
  const [fieldLoading, setFieldLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  // 只初始化一次
  useEffect(() => {
    console.log('*👏👏👏***groupValueRef.current****', groupValueRef.current);
    let handleValue = fixLaneGroupData(groupValueRef.current as LaneConfigProps);
    console.log('*👏👏👏***handleValue****', handleValue);
    setGroupValue(handleValue);
  }, []);

  const fieldGroupValueKey = useMemo(() => {
    return groupValue?.laneGroup?.groupSource === LaneDataSource.other ? 'otherGroupField' : 'currentGroupField';
  }, [groupValue]);

  const fieldGroupValue = useMemo(() => {
    const _newData = groupValue.laneGroupHandle?.[fieldGroupValueKey] ?? {};
    const otherData = {
      hideNoDataFz: _newData.displayNoDataGroup,
      hideWfz: _newData.displayNoGroup,
      customWfz: _newData.noGroupLaneName,
    };
    return { ..._newData, fieldId: _newData?.field?.id, fieldType: _newData?.field?.type, otherData };
  }, [groupValue, fieldGroupValueKey, groupValueRefreshKey]);

  // 获取整体看板数据源的字段
  useEffect(() => {
    setFieldLoading(true);
    const getBoardFields = async () => {
      const fields = await getFields(config?.dataset);
      setBoardFields(fields.filter((i: any) => i.compType === EtComponentKey.Ebuilder));
      setFieldLoading(false);
    };
    getBoardFields();
  }, [config?.dataset]);

  // 获取关联字段对应的数据源
  useEffect(() => {
    const handleLinkFields = async () => {
      if (!fieldGroupValue?.fieldId || isEmpty(boardFields)) {
        return;
      }
      const currentField = boardFields.find((i: any) => i.id === fieldGroupValue?.fieldId);
      const refObjId = currentField?.refObjId;
      if (!refObjId) {
        return;
      }
      const fields = await getFields({ id: refObjId, text: '', type: 'FORM', groupId: '' });
      // 设置下关联字段列表
      setLinkFields(fields);
      // 设置下关联数据源
      const linkDataset = {
        id: refObjId,
        text: currentField?.config?.formName,
        type: 'FORM',
        groupId: currentField?.config?.appId,
      };
      setLinkDataset(linkDataset);
    };
    handleLinkFields();
  }, [fieldGroupValue?.fieldId, boardFields]);

  const datasetType = useMemo(() => {
    return groupValue?.laneGroup?.groupSource === LaneDataSource.other ? DataSetType.Other : DataSetType.Current;
  }, [groupValue?.laneGroup]);

  const onOk = (cb: (newLaneGroup: LaneConfigProps) => void) => {
    const { otherData, fieldId, fieldType, ...rest } = fieldGroupValue!;
    cb && cb(rest);
  };

  const onGroupValueChange = (rowData: LaneConfigProps) => {
    setGroupValue(rowData);
    setGroupValueRefreshKey(new Date().getTime());
  };
  const onFieldGroupChange = (rowData: GroupDataOptionItemField, changeAll?: boolean) => {
    console.log('*👏👏👏***rowData****', rowData);
    const { fieldId, fieldType, otherData, ...rest } = rowData;
    const newLaneConfig = {
      ...groupValue,
      laneGroupHandle: {
        ...groupValue.laneGroupHandle,
        [fieldGroupValueKey]: {
          ...rest,
          displayNoDataGroup: otherData?.hideNoDataFz,
          displayNoGroup: otherData?.hideWfz,
          noGroupLaneName: otherData?.customWfz,
          field: {
            id: fieldId,
            type: fieldType,
          },
        },
      },
    };
    onGroupValueChange(newLaneConfig as LaneConfigProps);
  };
  const onDatasetTypeChange = (value: DataSetTypeItem) => {
    console.log('*👏👏👏***value****', value);
    console.log('*👏👏👏***fieldGroupValue****', fieldGroupValue);
    const _fieldGroupValueKey = value === DataSetType.Other ? 'otherGroupField' : 'currentGroupField';
    let newFieldGroupValue = {
      ...groupValue.laneGroupHandle?.[_fieldGroupValueKey],
      groupSource: value === DataSetType.Other ? LaneDataSource.other : LaneDataSource.current,
    };
    const newLaneConfig = {
      ...groupValue,
      laneGroup: newFieldGroupValue,
    };
    console.log('*👏👏👏***newLaneConfig****', newLaneConfig);
    onGroupValueChange(newLaneConfig as LaneConfigProps);
  };
  console.log('****fieldGroupValue****', fieldGroupValue);

  return (
    <>
      <DatasetType weId={`${props.weId || ''}_3crpns`} onChange={onDatasetTypeChange} cls={cls} value={datasetType} />
      <If weId={`${props.weId || ''}_f3it2s`} condition={isYdFilterWay}>
        <Then weId={`${props.weId || ''}_l7cq3m`}>
          <If weId={`${props.weId || ''}_3rr5m7`} condition={datasetType === DataSetType.Current}>
            <CommonField
              weId={`${props.weId || ''}_3fx5qk`}
              config={config}
              groupType={groupValue?.laneGroup?.active}
              rowData={fieldGroupValue}
              onChange={onFieldGroupChange}
              showOther
              showSealField={false}
              showDesc={false}
              cls={cls}
              datasetType={datasetType}
              isYd
            />
          </If>
          <Else weId={`${props.weId || ''}_iyw1hy`}>
            <LinkField weId={`${props.weId || ''}_faea9x`} fieldGroup={fieldGroupValue!} onChange={onFieldGroupChange} cls={cls} linkFields={boardFields} loading={fieldLoading} />
            {/* 选完关联ebuilder字段后 联带出数据源 只供展示 */}
            <If weId={`${props.weId || ''}_c68a83`} condition={!isEmpty(linkDataset)}>
              <Then weId={`${props.weId || ''}_dhlwru`}>
                <GroupSetDataset weId={`${props.weId || ''}_44s46h`} value={linkDataset} onChange={onFieldGroupChange} cls={cls} />
              </Then>
            </If>
          </Else>
        </Then>
        <Else weId={`${props.weId || ''}_vy042u`}>
          <If weId={`${props.weId || ''}_zzrb7n`} condition={datasetType === DataSetType.Current}>
            <Then weId={`${props.weId || ''}_kwblzb`}>
              <GroupField
                weId={`${props.weId || ''}_tr1kn2`}
                isYd
                fieldGroup={fieldGroupValue!}
                config={config}
                optionField={optionField}
                cls={cls}
                onChange={onFieldGroupChange}
                datasetType={datasetType}
              />
            </Then>
            <Else weId={`${props.weId || ''}_7te42g`}>
              <LinkField weId={`${props.weId || ''}_24qgb6`} fieldGroup={fieldGroupValue!} onChange={onFieldGroupChange} cls={cls} linkFields={boardFields} loading={fieldLoading} />
              {/* 选完关联ebuilder字段后 联带出数据源 只供展示 */}
              <If weId={`${props.weId || ''}_c68a83`} condition={!isEmpty(linkDataset)}>
                <Then weId={`${props.weId || ''}_dhlwru`}>
                  <GroupSetDataset weId={`${props.weId || ''}_44s46h`} value={linkDataset} onChange={onFieldGroupChange} cls={cls} />
                </Then>
              </If>
            </Else>
          </If>
          <If weId={`${props.weId || ''}_zzrb7n`} condition={!isEmpty(linkFields) || fieldGroupValue?.field?.id}>
            <Then weId={`${props.weId || ''}_kwblzb`}>
              <CommonField
                weId={`${props.weId || ''}_3fx5qk`}
                config={config}
                groupType={groupValue?.laneGroup?.active}
                fields={linkFields}
                rowData={fieldGroupValue}
                onChange={onFieldGroupChange}
                showOther
                showSealField={false}
                showDesc={false}
                showSort={false}
                cls={cls}
                datasetType={datasetType}
                linkDataset={linkDataset}
                isYd
                showTitle
              />
            </Then>
          </If>
        </Else>
      </If>
    </>
  );
});

export default FormFieldGroupSet;
