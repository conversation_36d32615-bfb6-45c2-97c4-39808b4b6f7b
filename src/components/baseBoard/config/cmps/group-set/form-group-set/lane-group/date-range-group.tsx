// 表单设计器下的当前表-日期分组方式
import { useState, useEffect, useRef, forwardRef, useImperativeHandle, useMemo } from 'react';
import { If, Then } from 'react-if';
import { deepToJS } from '../../../../../../../utils';
import { GroupDataOptionItemField } from '../../../../../engine/func-groups/types';
import { GroupSetProps } from '../';
import { DateRangeFieldComps } from '../../../../../engine/func-groups/ExtraFieldComps';
import { GroupDialogLeftSpan } from '../constants';
import { CommonField } from '../common';
import { LaneConfigProps, LaneGroupProps } from '../../../yd/types';
import '../style/index.less';

interface LaneDateRangeGroupSetProps extends GroupSetProps {
  cls: string;
}

const LaneDateRangeGroupSet = forwardRef((props: LaneDateRangeGroupSetProps, ref: any) => {
  const { config, fields, value, cls } = props;
  const groupValueRef = useRef<LaneConfigProps>(deepToJS(value));

  const [groupStates, setGroupStates] = useState<LaneConfigProps>(value);

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  useEffect(() => {
    const _value = groupValueRef.current as LaneConfigProps;
    setGroupStates(_value)
  }, []);

  // 处理确认按钮
  const onOk = (cb: (newLaneGroup: LaneGroupProps) => void) => {
    const { displayNoDataGroup, noGroupLaneName, displayNoGroup, field, dateRange, active } = groupStates?.laneGroup!;
    cb && cb({displayNoDataGroup, noGroupLaneName, displayNoGroup, field, dateRange, active});
  };

  const onChangeSingle = (rowData: GroupDataOptionItemField) => {
    const { otherData } = rowData;
    let newLaneGroup = groupStates.laneGroup
    Object.keys(rowData).map(i => {
      if (i === 'fieldId') {
        newLaneGroup.field = {
          ...groupStates.laneGroup.field,
          id: rowData[i],
        }
      } else if (i === 'otherData') {
        newLaneGroup = {
          ...newLaneGroup,
          displayNoDataGroup: otherData?.hideNoDataFz,
          displayNoGroup: otherData?.hideWfz,
          noGroupLaneName: otherData?.customWfz,
        }
      } else {
        // @ts-ignore
        newLaneGroup[i] = rowData[i]
      }
    })
    setGroupStates({...groupStates, laneGroup: newLaneGroup} as LaneConfigProps)
  };

  const dateGroup = useMemo(() => {
    console.log('****groupStates****', groupStates);
    const { field, dateRange, displayNoDataGroup, displayNoGroup, noGroupLaneName } = groupStates?.laneGroup
    const otherData = {
      hideNoDataFz: displayNoDataGroup,
      hideWfz: displayNoGroup,
      customWfz: noGroupLaneName,
    }
    const newValue = {
      fieldId: field?.id,
      dateRange,
      otherData
    }
    return newValue
  }, [groupStates])

  return (
    <>
      <DateRangeFieldComps weId={`${props.weId || ''}_tr1kn2`} value={dateGroup} fieldsMap={fields!} onChange={onChangeSingle} titleRowSpan={GroupDialogLeftSpan} />
      <If weId={`${props.weId || ''}_zzrb7n`} condition={dateGroup.fieldId}>
        <Then weId={`${props.weId || ''}_kwblzb`}>
          <CommonField
            weId={`${props.weId || ''}_3fx5qk`}
            config={config}
            groupType={groupStates?.laneGroup?.active}
            fields={fields}
            rowData={dateGroup}
            onChange={onChangeSingle}
            cls={cls}
            showSealField={false}
            showDesc={false}
            isYd
          />
        </Then>
      </If>
    </>
  );
});

export default LaneDateRangeGroupSet;
