import React from 'react';
import { Switch, SwitchChangeEventHandler } from '@weapp/ui';
import { ebdBClsPrefix } from '../../../../../constants';
import '../index.less';

const cls = `${ebdBClsPrefix}-config-commonSwitch`;

interface Props extends React.Attributes {
  value: any;
  label: string;
  onChange: SwitchChangeEventHandler
}

const CommonSwitchConfig: React.FC<Props> = props => {
  const { onChange, value, label } = props;
  return (
    <div className={cls}>
      <div className={`${cls}-lable`}>{label}</div>
      <Switch size="sm" onChange={onChange} value={value} weId={`${props.weId || ''}_wc13ff`} />
    </div>
  );
};

export default CommonSwitchConfig;
