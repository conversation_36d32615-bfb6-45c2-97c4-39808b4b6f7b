import { statisticsProps } from '../../../engine/func-groups/types';
import { PageSize, PageMode } from '../types';
import { GroupItem } from '../../../../common/board/types';
import { LaneGroupType } from '../../../../../constants/common';

export enum LaneDataSource {
  current = '0',
  other = '1',
}
export interface LaneDataProps {
  dataSet: any;
  baseSource: any;
  baseTarget: any;
  groupTarget: any;
  groupSource: LaneDataSource;
}
export interface LaneGroupProps {
  active: LaneGroupType;
  field: any;
  filter?: any[];
  groupSource?: LaneDataSource;
  dataset?: any;
  linkField?: any;
  displayNoGroupData?: boolean;
  displayNoGroup?: boolean;
  noGroupLaneName?: any;
  dateRange?: string; // 日期分组范围
  conditionConfig?: any[]; // 条件分组数据
  showField?: boolean; // 显示字段
}
export interface LaneConfigProps {
  status: boolean;
  laneGroup: LaneGroupProps;
  laneGroupHandle?: {
    currentGroupField: any;
    otherGroupField: any;
  };
  laneStat: statisticsProps;
  laneDataSet?: LaneDataProps;
  rootField?: string;
}

export interface LanePageProps {
  pageSize: PageSize; // 这里存的是默认的页码 包括自定义的
  pageNo: number;
  pageNum: string; // 每页条数  动态的
  pageType: PageMode;
  hasMore: boolean;
  moreLoading: boolean;
  total: number;
}


export type LaneItemData = {
  id: string;
  name?: string;
  groups?: GroupItem[];
  baseTime?: string;
  stats?: LaneStatItemType[];
  filters?: any;
  archive?: string;
};

export type LaneStatItemType = {
  count: string;
  inputValue: any;
}
export type LaneStatType = {
  laneId: string;
  stageId?: string;
  stats: LaneStatItemType[]
};