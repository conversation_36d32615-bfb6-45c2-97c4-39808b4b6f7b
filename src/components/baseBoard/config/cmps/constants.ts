import { getLabel } from '@weapp/utils';
import { PageModeType, MarkConfig, PageSize } from './types';
import { KanBanDefaultPageSize } from '../../../../constants';
import { LaneGroupType, GroupType } from '../../../../constants/common';
import { LaneConfigProps, LanePageProps, LaneGroupProps } from './yd/types';

export const INIT_PAGE_SIZE_CONFIG = (): PageSize => ({
  customPage: false,
  pageNum: KanBanDefaultPageSize,
  customPageNum: '',
})
export const INIT_PAGE_MODE = PageModeType.Scroll
export const INIT_MARK_CONFIG = (): MarkConfig => ({
  vertical: [],
  corner: []
})
export const pageModeOptions = () =>[
  { id: PageModeType.More, content: getLabel('291676', '点击加载') },
  { id: PageModeType.Scroll, content: getLabel('98867', '滚动分页') },
  { id: PageModeType.page, content: getLabel('184954', '分页加载') },
  // { id: PageModeType.None, content: getLabel('98868', '隐藏分页') },
];

export const pageSizeData = () =>[
  { id: '5', content: '5' },
  { id: '10', content: '10' },
  { id: '20', content: '20' },
  { id: '50', content: '50' },
  { id: '100', content: '100' },
  { id: '-1', content: getLabel('54002', '自定义') },
];

export const INIT_YD_OUTER_CONFIG = (): LaneConfigProps => ({
  status: false,
  laneGroup: {} as LaneGroupProps,
  laneStat: {
    openValue: false,
    statisticsItem: [],
  },
})
export const INIT_OTHERDARA_CONFIG = () => ({
  hideWfz: false,
  hideNoDataFz: false,
})

export const INIT_LANE_PAGE = (): LanePageProps => ({
  pageSize: INIT_PAGE_SIZE_CONFIG(),
  pageNo: 1,
  pageNum: KanBanDefaultPageSize,
  pageType: PageModeType.More,
  hasMore: true,
  moreLoading: false,
  total: 0,
})

export const LANE_GROUP_TYPE = () =>[
  { id: LaneGroupType.field, content: getLabel('270017', '按字段分组') },
  { id: LaneGroupType.dateRange, content: getLabel('277035', '按日期分组') },
  { id: LaneGroupType.filter, content: getLabel('270016', '按条件分组') },
];