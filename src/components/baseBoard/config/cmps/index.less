@import (reference) '../../../../style/prefix.less';


// 页面配置信息相关
.@{ebdBClsPrefix}-pageConfig {
  &-size {
    position: relative;

    &-cover {
      position: absolute;
      left: 2px;
      top: 5px;
      height: 20px;
      width: 80%;
      background: #fff;
      z-index: 6;
      line-height: var(--input-line-height);
      padding-left: 7px;
      cursor: pointer;
      font-size: var(--font-size-12);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      pointer-events: none;
    }

    &-custom {
      width: 100%;
      display: flex;
      align-items: center;

      .ui-input {
        flex: 1;
        margin-left: 4px;
      }
    }
  }

  &-mode {}
}

.@{ebdBClsPrefix}-config-commonSwitch {}

// 竖线和角标配置相关
.@{ebdBClsPrefix}-lineMarkSet {
  &-mark-dialog {

    .ui-dialog-body-container,
    .ui-dialog-footer {
      background: #fff;
    }

    &-item {
      cursor: pointer;

      &-title {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: var(--font-size-12);
        line-height: 16px;
        padding: 0 10px;

        &-text {}
      }

      &-img {
        width: 163px;
        height: 134px;
      }
    }
  }

  &-mainDialog {
    &-add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--font-size-12);
      color: var(--regular-fc);
      margin-bottom: 8px;

      .ui-icon {
        margin-right: 6px;
        cursor: pointer;
        color: var(--primary);
      }
    }

    &-table {
      &-block {
        display: flex;
        align-items: center;
      }
    }
  }
}

// 自定义分组配置标题

.@{ebdBClsPrefix}-config-customTitle {
  &-titleText-help {
    p {
      margin: 0;
    }
  }

  .ui-help {
    margin-left: 4px;
    position: relative;
    top: -1px;

    .ui-icon.ui-icon-wrapper {
      transform: rotate(0) !important;
    }
  }

  &-title {
    position: relative;

    &-required {
      content: '*';
      color: var(--form-item-required);
      position: absolute;
      font-size: 20px;
      position: absolute;
      right: -10px;
      top: 1px;
    }
  }

}