import React, { useEffect, useState } from 'react';
import { Select } from '@weapp/ui';
import { OptionData } from '@weapp/ui/lib/components/select/types';
import { cloneDeep, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import { INIT_PAGE_MODE, pageModeOptions } from './constants';
import { PageModeType } from './types';

export enum ListModeType {
  TABLE = '1', // 表格 默认
  LIST = '2', // 列表
  LEFTIMG = '3', // 左图列表式
  CROSSLIST = '4', // 横向列表式
  TOPPIC = '5', // 上图式
  LEFTTABLEIMG = '6', // 左图表格式
  ODOCLISTIMG = '7', // 公文列表 正文缩略图
}


export interface PageModeProps extends React.Attributes {
  config: any;
  value: string;
  filter?: (opt: OptionData) => boolean;
  onChange: (value: string) => void;
  onConfigChange: (params: any) => void;
  needPageNumType?: boolean; // 是否需要页码分页 默认为false
}

const PageMode: React.FC<PageModeProps> = (props) => {
  const [pageMode, setPageMode] = useState(INIT_PAGE_MODE);
  const onSelectChange = (val: any) => {
    props.onChange(val);
    setPageMode(val)
  };

  useEffect(() => {
    const { config: configData } = props;
    const config = cloneDeep(toJS(configData)) as any;
    const { pageMode = INIT_PAGE_MODE} = config;
    setPageMode(pageMode)
  }, [props.config?.pageMode]);

  const getModeOption = () => {
    let modeList = pageModeOptions()
    if (!props.needPageNumType) {
      modeList = modeList.filter(i => i.id !== PageModeType.page)
    }
    return modeList
  }

  return (
    <Select
      weId={`${props.weId || ''}_h9csu6`}
      data={getModeOption()}
      value={pageMode}
      onChange={onSelectChange}
      style={{ width: '100%' }}
    />
  );
};

export default PageMode;
