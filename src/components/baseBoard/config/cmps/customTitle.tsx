import React, { useMemo } from 'react';
import { Help } from '@weapp/ui';
import { isEmpty } from '@weapp/utils';
import { ebdBClsPrefix } from '../../../../constants';
import './index.less';

const cls = `${ebdBClsPrefix}-config-customTitle`;

interface Iprops extends React.Attributes {
  required?: boolean;
  title: string;
  helpInfo?: string[];
}

const CustomConfigTitle = React.memo((props: Iprops) => {
  const { title, required, helpInfo = [] } = props;
  const helpCmp = useMemo(() => {
    return helpInfo.map(i => <p key={i}>{i}</p>);
  }, [helpInfo]);
  const helpComponent = useMemo(() => {
    if (isEmpty(helpInfo)) {
      return null;
    }
    return (
      <Help
        weId={`${props.weId || ''}_kr05d2`}
        title={<div>{helpCmp}</div>}
        placement="top"
        popoverProps={{ triggerProps: { popupClassName: `${cls}-titleText-help` } }}
      />
    );
  }, [helpInfo, helpCmp, props.weId]);
  return (
    <div className={cls}>
      <span className={`${cls}-title`}>
        {title}
        {required ? <span className={`${cls}-title-required`}>*</span> : ''}
      </span>
      {helpComponent}
    </div>
  );
});

export default CustomConfigTitle;
