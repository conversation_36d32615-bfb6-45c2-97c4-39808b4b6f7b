import React from 'react';
import { LazyType } from '../../../../types/public';

const PageMode = React.lazy(() => import('./PageMode')) as LazyType;
const PageSize = React.lazy(() => import('./PageSize')) as LazyType;
const LineMarkSet = React.lazy(() => import('./lineMarkSet')) as LazyType;
const CustomConfigTitle = React.lazy(() => import('./customTitle')) as LazyType;
export { PageMode, PageSize, LineMarkSet, CustomConfigTitle };
