import { getLabel, isEqual, isEmpty } from '@weapp/utils';
import { toJS, runInAction } from 'mobx';
import BoardDataSet from './BoardDataSet';
import { ebdBComsName } from '../../../constants';
import CardSet from './CardSet';
import ConditionSet from './ConditionSet';
import FilterSet from './FilterSet';
// import GroupSet from './GroupSet';
import Sort from './Sort';
import GroupStatistics from './GroupStatistics';
import { PageMode, PageSize, LineMarkSet, CustomConfigTitle, ButtonSet } from './cmps';
import { LANE_GROUP_TYPE } from './cmps/constants';
import GroupSet from './cmps/group-set/standard';
import LaneGroupSet from './cmps/group-set/lane';
import { isSjrEnv } from '../../../utils';

const allGroups = [
  {
    id: 'dataSet',
    title: getLabel('311430', '看板数据设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'groupSet',
    title: getLabel('311431', '看板分组设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'laneSet',
    title: getLabel('311432', '看板泳道设置'),
    visible: true,
    custom: false,
  },
];
const labelProps = {
  labelSpan: 10,
  align: 'right',
};
const dataSetItems = {
  dataset: {
    label: getLabel('55058', '数据源'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: BoardDataSet,
    required: true,
  },
  card: {
    label: getLabel('94492', '卡片设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <CardSet weId={`${props.weId || ''}_vcooiu`} {...props} clearable={false} isIconBtn />,
    required: true,
  },
  // 按钮配置
  comButton: {
    label: getLabel('235053', '按钮配置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <ButtonSet weId={`${props.weId || ''}_5hqaby`} {...props} isIconBtn />,
  },
  // 角标和竖线设置
  markSetting: {
    label: getLabel('255197', '标识设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <LineMarkSet weId={`${props.weId || ''}_q5j08q`} {...props} isIconBtn />,
  },
  // 数据过滤
  condition: {
    label: getLabel('53857', '数据过滤'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <div style={{overflow: 'hidden', maxWidth: '130px'}}><ConditionSet weId={`${props.weId || ''}_quwix5`} {...props} isIconBtn /></div>,
  },
  // 排序
  orders: {
    label: getLabel('96961', '排序设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <Sort weId={`${props.weId || ''}_m94dqp`} {...props} isIconBtn />,
  },
  // 筛选条件
  filter: {
    label: getLabel('124589', '搜索设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <FilterSet weId={`${props.weId || ''}_s5u7c7`} {...props} isIconBtn />,
  },
};
const groupSetItems = {
  group: {
    label: '',
    labelSpan: 0,
    itemType: 'CUSTOM',
    groupId: 'groupSet',
    customRender: GroupSet,
  },
  //分组统计
  statistics: {
    label: '',
    labelSpan: 0,
    itemType: 'CUSTOM',
    groupId: 'groupSet',
    customRender: (props: any) => <div style={{marginTop: '-8px'}}><GroupStatistics weId={`${props.weId || ''}_vhv7un`} {...props} /></div>,
  },
};
const laneSetItems = {
  laneConfig: {
    label: '',
    labelSpan: 0,
    itemType: 'CUSTOM',
    groupId: 'laneSet',
    customRender: LaneGroupSet,
  },
};
// 废弃 只做参考
const getGroups = (formData: any) => {
  const currentDataSet = formData?.dataset || {};
  const hasDataSet = !isEmpty(currentDataSet) && currentDataSet.id;
  const fullGroups = allGroups.filter(group => {
    let show = true;
    switch (group.id) {
      // 有数据源才支持分组、泳道配置
      case 'groupSet':
      case 'laneSet':
        if (group.id === 'laneSet') {
          show = hasDataSet && isSjrEnv();
        } else {
          show = hasDataSet;
        }
        break;
      default:
        break;
    }
    return show;
  });
  return fullGroups;
};
export default function getBaseConfig(pluginCenter?: any) {
  const config: any = {
    title: true,
    footer: false,
    fixedArea: false,
    groups: allGroups,
    items: { ...dataSetItems, ...groupSetItems, ...laneSetItems },
    customHide: function customHide(col: any) {
      const _this = this as any;
      const formDatas: any = _this.form.datas;
      const dataset = this.props.data.dataset ? JSON.parse(JSON.stringify(this.props.data.dataset)) : {};
      if (col.id === 'footerEnabled') {
        return { ...col, hide: true };
      }
      if (col.id === 'card') {
        return { ...col, hide: !dataset.id };
      }
      const _groups = getGroups(toJS(formDatas));
      if (!isEqual(_groups, toJS(_this.form.groups))) {
        this.form.setState({ groups: _groups });
      }
      return { ...col };
    },
    references: {
      dataset: ['card', 'group', 'comButton', 'condition', 'filter', 'orders', 'statistics', 'bottomBtnFuncSet', 'bottomBtnNameSet'],
    },
    onMount(configInstance: any) {
      configInstance?.props?.pluginCenter?.handleConfigMount?.(configInstance);
    },
  };

  return config;
}
