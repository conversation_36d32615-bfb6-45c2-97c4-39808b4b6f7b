import { getLabel, isEqual, isEmpty } from '@weapp/utils';
import { toJS, runInAction } from 'mobx';
import BoardDataSet from './BoardDataSet';
import { ebdBComsName } from '../../../constants';
import CardSet from './CardSet';
import ConditionSet from './ConditionSet';
import FilterSet from './FilterSet';
// import GroupSet from './GroupSet';
import Sort from './Sort';
import ButtonSet from './ButtonSet';
import GroupStatistics from './GroupStatistics';
import { PageMode, PageSize, LineMarkSet, CustomConfigTitle } from './cmps';
import { LANE_GROUP_TYPE } from './cmps/constants';
import GroupSet from './cmps/group-set';
import LaneGroupSet from './cmps/group-set/lane';
import { isEbFormDataV2 } from '../../common/DataSet/utils';

const diffKey = `${ebdBComsName}-config-group`;
const groups2 = [
  {
    id: 'datasetGroup',
    title: getLabel('55058', '数据源'),
    visible: true,
    custom: false,
  },
  {
    id: 'groupSetGroup',
    title: getLabel('54215', '分组设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'groupCountGroup',
    title: getLabel('245988', '分组统计'),
    visible: true,
    custom: false,
  },
  {
    id: 'cardSetGroup',
    title: getLabel('94492', '卡片设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'buttonSetGroup',
    title: getLabel('235053', '按钮配置'),
    visible: true,
    custom: false,
  },
  {
    id: 'lineMarkSetGroup',
    title: getLabel('255243', '角标和竖线标识设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'conditionGroup',
    title: getLabel('53857', '数据过滤'),
    visible: true,
    custom: false,
  },

  {
    id: 'ordersGroup',
    title: getLabel('54298', '排序'),
    // title: (
    //   <CustomConfigTitle
    //     weId={'17luw3'}
    //     title={getLabel('54298', '排序')}
    //     helpInfo={[getLabel('279777', '分组内拖拽排序后，该分组排序设置不生效')]}
    //   />
    // ),
    visible: true,
    custom: false,
  },
  {
    id: 'filterSearchGroup',
    title: getLabel('40540', '搜索'),
    visible: true,
    custom: false,
  },
  // {
  //   id: 'laneConfig',
  //   title: getLabel('291675', '泳道设置'),
  //   visible: true,
  //   custom: false,
  // },
  // {
  //   id: 'other',
  //   title: getLabel('55756', '其他'),
  //   visible: true,
  //   custom: false,
  // },
];
const groups = [
  {
    id: 'dataSet',
    title: getLabel('-1', '看板数据设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'groupSet',
    title: getLabel('-1', '看板分组设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'laneSet',
    title: getLabel('-1', '看板泳道设置'),
    visible: true,
    custom: false,
  },
];
const labelProps = {
  labelSpan: 10,
  align: 'right',
};
const dataSetItems = {
  dataset: {
    label: getLabel('55058', '数据源'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: BoardDataSet,
    required: true,
  },
  card: {
    label: getLabel('94492', '卡片设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <CardSet weId={`${props.weId || ''}_vcooiu`} {...props} isIconBtn />,
    required: true,
  },
  // 按钮配置
  buttonSet: {
    label: getLabel('235053', '按钮配置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <ButtonSet weId={`${props.weId || ''}_5hqaby`} {...props} isIconBtn />,
  },
  // 角标和竖线设置
  markSetting: {
    label: getLabel('-1', '标识设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <LineMarkSet weId={`${props.weId || ''}_q5j08q`} {...props} isIconBtn />,
  },
  // 数据过滤
  condition: {
    label: getLabel('53857', '数据过滤'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <ConditionSet weId={`${props.weId || ''}_quwix5`} {...props} isIconBtn />,
  },
  // 排序
  orders: {
    label: getLabel('96961', '排序设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <Sort weId={`${props.weId || ''}_m94dqp`} {...props} isIconBtn />,
  },
  // 筛选条件
  filter: {
    label: getLabel('-1', '搜索设置'),
    ...labelProps,
    itemType: 'CUSTOM',
    groupId: 'dataSet',
    customRender: (props: any) => <FilterSet weId={`${props.weId || ''}_s5u7c7`} {...props} isIconBtn />,
  },
};
const groupSetItems = {
  group: {
    label: '',
    labelSpan: 0,
    itemType: 'CUSTOM',
    groupId: 'groupSet',
    customRender: GroupSet,
  },
  //分组统计
  statistics: {
    label: '',
    labelSpan: 0,
    itemType: 'CUSTOM',
    groupId: 'groupSet',
    customRender: GroupStatistics,
  },
};
const laneSetItems = {
  laneConfig: {
    label: '',
    labelSpan: 0,
    itemType: 'CUSTOM',
    groupId: 'laneSet',
    customRender: LaneGroupSet,
  },
};
const getGroups = (formData: any) => {
  const currentDataSet = formData?.dataset || {};
  const hasDataSet = !isEmpty(currentDataSet) && currentDataSet.id;
  let allGroups = [
    {
      id: 'build-in-group',
      title: '',
      visible: true,
      custom: false,
    },
    ...groups,
  ];
  const fullGroups = allGroups.filter(group => {
    let show = true;
    switch (group.id) {
      // mark 表单数据源才支持筛选、排序、角标、按钮配置
      case 'ordersGroup':
      case 'lineMarkSetGroup':
      case 'buttonSetGroup':
      case 'yd':
        // show = hasDataSet && isEbFormDataV2(formData);
        show = hasDataSet;
        break;
      case 'filterSearchGroup':
        show = hasDataSet && isEbFormDataV2(formData);
        break;
      case 'cardSetGroup':
        show = hasDataSet;
        break;
      case 'conditionGroup':
        show = hasDataSet;
        break;
      default:
        break;
    }
    return show;
  });
  return fullGroups;
};
export default function getBaseConfig(pluginCenter?: any) {
  const config: any = {
    title: true,
    footer: false,
    fixedArea: false,
    groups,
    items: { ...dataSetItems, ...groupSetItems, ...laneSetItems },
    items2: {
      dataset: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'datasetGroup',
        customRender: BoardDataSet,
      },
      group: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'groupSetGroup',
        customRender: GroupSet,
      },
      //分组统计
      statistics: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'groupCountGroup',
        customRender: GroupStatistics,
      },
      card: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'cardSetGroup',
        customRender: CardSet,
      },
      // 按钮配置
      buttonSet: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'buttonSetGroup',
        customRender: ButtonSet,
      },
      // 角标和竖线设置
      markSetting: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'lineMarkSetGroup',
        customRender: LineMarkSet,
      },
      // 排序
      orders: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'ordersGroup',
        customRender: Sort,
      },
      // 数据过滤
      condition: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'conditionGroup',
        customRender: ConditionSet,
      },
      // 筛选条件
      filter: {
        label: '',
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'filterSearchGroup',
        customRender: FilterSet,
      },
      // 泳道配置
      // laneConfig: {
      //   label: '',
      //   labelSpan: 0,
      //   itemType: 'CUSTOM',
      //   groupId: 'laneConfig',
      //   customRender: YdConfigSet,
      // },
      // pageSize: {
      //   label: getLabel('59334', '每页行数'),
      //   itemType: 'CUSTOM',
      //   labelSpan: 7,
      //   groupId: 'other',
      //   customRender: PageSize,
      // },
      // pageMode: {
      //   label: getLabel('98869', '分页模式'),
      //   itemType: 'CUSTOM',
      //   labelSpan: 7,
      //   groupId: 'other',
      //   customRender: PageMode,
      // },
    },
    // customHide: function customHide(col: any) {
    //   const _this = this as any;
    //   const formDatas: any = _this.form.datas;
    //   const dataset = this.props.data.dataset ? JSON.parse(JSON.stringify(this.props.data.dataset)) : {};
    //   if (col.id === 'footerEnabled') {
    //     return { ...col, hide: true };
    //   }
    //   if (col.id === 'card') {
    //     return { ...col, hide: !dataset.id };
    //   }
    //   const _groups = getGroups(toJS(formDatas));
    //   if (!isEqual(_groups, toJS(_this.form.groups))) {
    //     this.form.setState({ groups: _groups });
    //   }
    //   // if (col.id === 'bottomBtnFuncSet' || col.id === 'bottomBtnNameSet') {
    //   //   if (dataset.type === SourceType.FORM) {
    //   //     return { ...col, hide: true };
    //   //   }
    //   // }
    //   return { ...col };
    // },
    references: {
      dataset: ['card', 'group', 'buttonSet', 'condition', 'filter', 'orders', 'statistics', 'bottomBtnFuncSet', 'bottomBtnNameSet'],
    },
    onMount(configInstance: any) {
      configInstance?.props?.pluginCenter?.handleConfigMount?.(configInstance);
    },
  };
  console.log('*👏👏👏***config****', config);

  return config;
}
