/**
 * 数据过滤
 */
import { observer } from 'mobx-react';
import React from 'react';
import { getLabel } from '@weapp/utils';
import EBFilter from '../../common/Filter';
import { specialFieldsFilter } from '../../../utils';
import { DesignComProps } from '../types';

@observer
export default class ConditionSet extends React.PureComponent<DesignComProps> {
  render() {
    const { onChange, value, config, isIconBtn } = this.props;
    const { dataset } = config;
    return (
      <EBFilter
        weId={`${this.props.weId || ''}_9qmdm7`}
        value={value}
        dataset={dataset!}
        onChange={onChange}
        title={getLabel('105959', '看板视图固定查询条件')}
        filter={specialFieldsFilter}
        isIconBtn={isIconBtn}
      />
    );
  }
}
