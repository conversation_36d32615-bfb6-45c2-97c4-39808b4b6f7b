/**
 * 筛选器
 */
import { Button, Icon } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import React from 'react';
import BoardFilterSet from '../../common/field-search/FilterSetCom';
import { DesignComProps } from '../types';
import NSettingIcon from '../../common/n-setting-icon';
import './index.less';

export default class FilterSet extends React.PureComponent<DesignComProps & { showMenu?: string[] }> {
  handleChange = (key: any, data: any) => {
    const { onChange } = this.props;
    onChange(data);
  };
  clearAll = () => {
    this.handleChange('', {
      commonFilters: [],
      quickFilters: [],
    })
  }

  customBtnEle = (showModalFC: any) => {
    const { value, isIconBtn } = this.props;
    const isAdd = isEmpty(value?.commonFilters) && isEmpty(value?.quickFilters);
    if (isIconBtn) {
      return <NSettingIcon weId={`${this.props.weId || ''}_v190c7`} hasValue={!isAdd} onClick={showModalFC} clearable={!isAdd} clearFunc={this.clearAll} />
    }
    return (
      <Button weId={`${this.props.weId || ''}_3flcd1`} style={{ width: '100%' }} onClick={showModalFC}>
        {isAdd && isEmpty(value?.groupFilters) ? <>+ {getLabel('55190', '添加筛选条件')}</> : <>{getLabel('93066', '编辑筛选条件')}</>}
      </Button>
    );
  };

  render() {
    const { config, value, pageId, appid, showMenu } = this.props;
    const {
      dataset = {
        id: '',
        type: 'FORM',
        groupId: '',
        text: '',
      },
    } = config;

    return (
      <BoardFilterSet
        weId={`${this.props.weId || ''}_627bo9`}
        dataset={dataset}
        value={value}
        onSave={this.handleChange}
        customBtnEle={this.customBtnEle}
        ebBusinessId={pageId! || appid!}
        showMenu={showMenu}
      />
    );
  }
}
