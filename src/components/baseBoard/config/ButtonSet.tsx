/**
 * <AUTHOR>
 * @createTime 2022-08-02
 * @desc   : 按钮配置，自定义按钮
 */
import { CorsComponent, FormDatas } from '@weapp/ui';
import { getLabel, isEqual, intersection, forEach } from '@weapp/utils';
import { computed, toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import ButtonEx from '../../common/buttonEx';
import { UUID } from '../../../utils';
import { ajax } from '../../../utils/ajax';
import { ButtonType, ActionPositionType, ComCustomConstBtn } from '../../../constants/common';
import { RightMenu, DataSet } from '../../../types/common';
import { NotUseFields } from '../../../constants';
import { isEbFormDataV2 } from '../../common/DataSet/utils';
import NSettingIcon from '../../common/n-setting-icon';

/**
 * rightTopKeys : 列表右上角按钮keys
 * lineKeys ： 列表行上数据按钮keys
 */
const rightTopKeys = ['rightTop', 'rightTopDropDown', 'rightTopBatch'];
const lineKeys = ['lineEnd', 'LineEndDropDown'];
const OPERATE_BUTTON_ID = '-99'; // 行上操作列
const OPERATE_BUTTON_NAME = 'operate_button'; // 行上操作列
const operateButtonField = {
  id: OPERATE_BUTTON_ID,
  name: OPERATE_BUTTON_NAME,
  text: getLabel('221904', '操作列'),
  content: '',
  type: 'text',
};

const positionData = [
  // {
  //   id: 'rightTop',
  //   title: getLabel('222250', '右上角'),
  //   img: 'button_position/list_right_top.svg',
  // },
  {
    id: 'rightTopDropDown',
    title: getLabel('222251', '右上角更多菜单'),
    img: 'button_position/list_right_top_more.svg',
  },
  // {
  //   id: 'rightTopBatch',
  //   title: getLabel('222252', '右上角批量操作菜单'),
  //   img: 'button_position/list_right_top_batch.svg',
  // },
  // {
  //   id: 'lineEnd',
  //   title: getLabel('222253', '行上'),
  //   img: 'button_position/list_row_out.svg',
  // },
  {
    id: 'LineEndDropDown',
    title: getLabel('222254', '行末下拉'),
    img: 'button_position/list_row_inner.svg',
  },
];
const isMockDataset = (dataset?: DataSet) => dataset?.type === 'mock';
const listJsPlaceholder = ` // 可通过this对象获取行数据以及dom对象, this对象格式: {target: ..., data: {...}}
  //获取行数据，比如该行数据为{id: 1, name: Joy, age: 18}
  var data = this.data;     //data = {id: 1, name: Joy, age: 18}
  var name = data.name;     //获取行数据字段为name的值, name=Joy
  //获取按钮dom对象
  var target = this.target; //target为按钮的dom对象
  target.innerText='delete' //修改当前按钮名称为'delete'`;

interface CustomButtonsProps extends Attributes {
  config: any;
  comId: string;
  pageId: string;
  isIconBtn?: boolean;
  onChange?: (data: any) => void;
  onConfigChange?: (value?: FormDatas, act?: any) => void;
  externalElement?: (showModalFC: Function) => React.ReactNode;
  [_: string]: any;
}

@observer
export default class ButtonSet extends PureComponent<CustomButtonsProps, any> {
  static defaultButtonconstns = {
    [ComCustomConstBtn.ExportButton]: {
      buttonType: 'COMCUSTOM',
      buttonKey: ComCustomConstBtn.ExportButton,
      system: 1,
      id: `${UUID(16)}`,
      name: getLabel('221905', '导出'),
      enable: 0,
      onlyIcon: 0,
      scope: 'PC',
      btnPositionData: rightTopKeys,
      showPosition: [rightTopKeys[1]],
      actions: [],
      hideAction: true,
      permissions: [
        {
          content: '',
          contentSpan: '',
          id: `-${UUID()}`,
          levelScope: { minShowlevel: '', maxShowlevel: '' },
          permissionType: 'all',
          relation: '0',
        },
      ],
    },
    [ButtonType.Personalization]: {
      buttonType: 'COMCUSTOM',
      buttonKey: ButtonType.Personalization,
      system: 1,
      id: `${UUID(16)}`,
      name: getLabel('189863', '个性化设置'),
      enable: 0,
      onlyIcon: 0,
      scope: 'PC',
      btnPositionData: rightTopKeys,
      showPosition: [rightTopKeys[1]],
      actions: [],
      hideAction: true,
      permissions: [
        {
          content: '',
          contentSpan: '',
          id: `-${UUID()}`,
          levelScope: { minShowlevel: '', maxShowlevel: '' },
          permissionType: 'all',
          relation: '0',
        },
      ],
    },
  };

  constructor(props: any) {
    super(props);
    this.state = {
      datasetBtns: [],
    };
  }

  componentDidMount() {
    this.getButtonsByDataset();
  }

  componentDidUpdate(preProps: any) {
    const { config } = this.props;
    if (!isEqual(preProps.config?.dataset, config?.dataset)) {
      this.getButtonsByDataset();
    }
  }

  getButtonsByDataset = () => {
    const { config, ebBusinessId } = this.props;
    // 模拟数据源，不继续请求接口
    const isMockData = isMockDataset(config?.dataset);
    if (config?.dataset && config?.dataset?.id && !isMockData) {
      const formData = new FormData();
      formData.append('type', 'Kanban');
      formData.append('dataset', JSON.stringify(config?.dataset));
      ajax({
        // url: '/api/ebuilder/coms/button/getButtonList',
        url: '/api/ebuilder/coms/button/getButtonListByType',
        method: 'post',
        data: formData,
        ebBusinessId,
        success: (res: any) => {
          this.setState({ datasetBtns: res || [] });
          this.syncButtonConfig(res || []);
        },
      });
    }
  };
  /**
   * 去掉部分配置来比对
   * @param arr
   * @returns arr
   */
  filterProps = (arr: any[]) => {
    // 这块配置用看板视图自己的
    const props = ['enable', 'showPosition'];
    return arr.map(i => {
      let ret: any = {};
      for (let j in i) {
        if (!props.includes(j)) ret[j] = i[j];
      }
      return ret;
    });
  };
  /**
   * 首次请求同步数据源的按钮同步到配置里面
   * 发现配置不一致 还是需要同步
   * @param btns
   */
  syncButtonConfig = (btns: any[]) => {
    const { config } = this.props;
    // 过滤掉表单视图/表格视图内的按钮 这些都是在table那边展示的 不在设计器内进行同步
    const validBtns = btns.filter((b: any) => b.buttonType !== 'CUSTOM');
    // 如果线上已经有错误数据了 打开的时候需要去同步下当前配置重新保存再刷新即可
    const failedData = (config?.comButton || []).length ? config?.comButton.filter((i: any) => i.buttonType === 'CUSTOM') : [];
    if ((!config?.comButton?.length && validBtns.length) || failedData.length) {
      this.onConfirm(validBtns);
    }
  };
  loadStyle = (show?: boolean) => {
    const id = 'ebdboard-dynamic-style';
    if (show) {
      let style = document.createElement('style');
      style.type = 'text/css';
      style.id = id;
      style.innerHTML = require('./cmps/fixed.less');
      document.head.appendChild(style);
      return;
    }
    const el = document.querySelector(`#${id}`);
    el && el.remove();
  };
  clearAll = () => {
    this.props.onChange?.({});
  }

  externalElement = (showModalFC: any) => {
    const { config, externalElement, isIconBtn } = this.props;
    if (externalElement) return externalElement(showModalFC);
    const hasBtn = config?.comButton?.filter((b: any) => b.enable).length;
    const _showModalFC = () => {
      showModalFC();
      this.loadStyle(true);
    };
    if (isIconBtn) {
      return <NSettingIcon weId={`${this.props.weId || ''}_xkjenr`} hasValue={hasBtn} onClick={_showModalFC} clearable={hasBtn} clearFunc={this.clearAll} />
    }
    return (
      <ButtonEx weId={`${this.props.weId || ''}_oliqbd`} action={hasBtn ? 'edit' : undefined} inline={false} onClick={_showModalFC}>
        {hasBtn ? getLabel('221908', '编辑按钮') : getLabel('221909', '配置按钮')}
      </ButtonEx>
    );
  };

  onConfirm = (btns: any) => {
    this.loadStyle(false);
    /**
     * 判断是否有行上按钮
     * 1、存在行上按钮时，更新tableField添加，无则清除操作列
     * 2、行上按钮发生数据变化时通过tableOperateBtn强制更新getdata接口
     */
    let hasRowLineButton = false;
    const enableBtns = btns.filter((b: any) => b.enable);
    forEach(enableBtns, b => {
      if (toJS(b.showPosition || []).includes(ActionPositionType.LineEnd)) {
        hasRowLineButton = true;
      }
    });
    const { config } = this.props;
    let _tableField = [...(config.tableField || [])];
    const colHasOperateButton = _tableField.some((f: any) => f.id === OPERATE_BUTTON_ID);
    // 有操作列同时有行上按钮；无操作列同时无行上按钮；考虑行上按钮数量变化
    if ((colHasOperateButton && hasRowLineButton) || (!colHasOperateButton && !hasRowLineButton)) {
      const lineEndbtnNum = enableBtns.filter(
        // eslint-disable-next-line max-len
        (btn: RightMenu) => intersection(btn.showPosition, [ActionPositionType.LineEnd]).length
      );
      const preLineEndbtnNum = this.customButtons.filter(
        // eslint-disable-next-line max-len
        (btn: RightMenu) => intersection(btn.showPosition, [ActionPositionType.LineEnd]).length
      );
      if (lineEndbtnNum !== preLineEndbtnNum) {
        this.props.onConfigChange?.({ tableOperateBtn: UUID(), comButton: btns });
        return;
      }
      this.props.onChange?.(btns);
      return;
    }
    // 存在操作列，且按钮内部无行上按钮
    if (colHasOperateButton && !hasRowLineButton) {
      _tableField = _tableField.filter(f => f.id !== OPERATE_BUTTON_ID);
    }
    // 不存在操作列，且按钮内部有行上按钮
    if (!colHasOperateButton && hasRowLineButton) {
      const operateField = {
        ...operateButtonField,
        orderType: 'DEFAULT',
        uid: UUID(),
        width: 100,
        objNmae: '',
      };
      _tableField = [..._tableField, operateField];
    }
    this.props.onConfigChange?.({
      tableField: _tableField,
      tableOperateBtn: UUID(),
      comButton: btns,
    });
  };

  @computed get customButtons() {
    const { value = [], config } = this.props;
    // 处理数据源获取按钮和配置按钮顺序
    // let _btns: any[] = [];
    // const sourceBtns = value.filter((b: any) => b.buttonType !== 'COMCUSTOM');
    // const comBtn = value.filter((b: any) => b.buttonType === 'COMCUSTOM');
    // forEach(this.state.datasetBtns, btn => {
    //   let _btn = btn;
    //   forEach(sourceBtns, sbtn => {
    //     if (btn.id === sbtn.id) {
    //       // 按钮名称取最新同步的按钮名称
    //       _btn = { ...btn, ...sbtn, name: btn.name };
    //     }
    //   });
    //   _btns.push(_btn);
    // });
    // _btns = [..._btns, ...comBtn].sort((a, b) => a.showOrder - b.showOrder);
    // /**
    //  * 针对非eb数据源固定增加组件默认按钮导出和个性化设置
    //  */
    // if (config?.dataset?.type !== SourceType.FORM && !isEbFormData(config?.dataset)) {
    //   const extroBtns = [ComCustomConstBtn.ExportButton, ButtonType.Personalization];
    //   extroBtns.forEach(buttonKey => {
    //     const hasBtn = _btns.find(btn => btn.buttonType === 'COMCUSTOM' && btn.buttonKey === buttonKey);
    //     if (!hasBtn) {
    //       _btns.push((ButtonSet.defaultButtonconstns as any)?.[buttonKey]);
    //     }
    //   });
    // }
    // // 处理历史数据中业务模块下的导出按钮
    // if (isEbFormData(config?.dataset)) {
    //   _btns = _btns.filter(b => !(b.buttonType === 'COMCUSTOM' && b.buttonKey === 'ExportButton'));
    // }
    let _btns: any[] = config?.comButton || [];
    // 处理历史数据中业务模块下的导出按钮
    if (isEbFormDataV2(config)) {
      _btns = _btns.filter(b => !(b.buttonType === 'COMCUSTOM' && b.buttonKey === 'ExportButton'));
    }
    return _btns ?? [];
  }

  fieldFilter = (field: any) => {
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id || field?.componentKey)) {
      return false;
    }
    return true;
  };

  render(): React.ReactNode {
    const { config, pageId, pageScope, appid, client, layoutInfo, store, comId } = this.props;
    const page = {
      id: pageId,
      module: pageScope,
      appid,
      client,
      datasetVals: layoutInfo?.datasetVals || [], // 可能是数据集相关，PageLink组件参数相同
    };
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_73rnip`}
        app="@weapp/components"
        compName="ButtonConfig"
        dataset={config?.dataset}
        onSure={this.onConfirm} // 确定回调，两个参数
        data={toJS(this.customButtons)}
        customBtnRender={this.externalElement} // 外部定义点击按钮
        page={page}
        positionParams={{
          positionKeys: [rightTopKeys, lineKeys],
          permissionConditionKeys: lineKeys,
          needBtnOperateType: true,
          operateTypeKeys: rightTopKeys,
          operateTypeHelpTip: getLabel('222255', '仅在按钮显示在表格 “右上角、右上角更多菜单或右上角批量操作菜单” 时生效'),
          positionData,
        }}
        actionParams={{
          /** 事件动作的传参 */
          /** 如果需要限制某种按钮类型，得包一层对象，key随便取，内部得有showPositionKeys */
          params: {
            showPositionKeys: lineKeys,
            jsCodePlaceholder: listJsPlaceholder,
          },
          /** 如果不需要限制按钮类型，就全部写在otherParams内 */
          otherParams: {
            store,
            comId,
          },
          fieldFilter: this.fieldFilter,
        }}
        hideGroupBtn
      />
    );
  }
}
