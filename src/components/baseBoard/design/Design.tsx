import React, { useEffect, useMemo, useRef, useCallback } from 'react';
import { BoardViewCom, MBoardView } from '../comp';
import { DesignProps } from '../types';
import { KanBanInitConfig } from '../../../constants';
import usePluginClasses from '../../plugin-packages/loader';
import { PluginCenterOptsType } from '../../plugin-packages/types';
import '../baseStyle/index.less';

const BoardDesign = (props: DesignProps) => {
  const refStore = useRef<any>();
  const { id, page, config, comServicePath, onRef, events, refPluginPackage, client } = props;
  const viewType = page?.client === 'MOBILE' ? 'MDesign' : 'Design';
  const pluginClasses = usePluginClasses(viewType, refPluginPackage);

  const onBeforeMount = useCallback(store => {
    refStore.current = store;
    props.onBeforeMount?.(store);
  }, []);

  useEffect(() => {
    const plugins = [...(pluginClasses || [])];
    const registerName = props?.pluginCenter?.pluginNames[0];
    // 检查是否被注册过
    if (!props?.pluginCenter?.enabledPluginRecord[registerName]) {
      props.pluginCenter?.pushPlugins(plugins);
    }
  }, [pluginClasses]);
  const pluginCenterOpts = useMemo(
    () =>
      ({
        filterEnabledPlugin: (v: any, plugin: any) => {
          const enabled1 = !plugin?.mode && v;
          const enabled2 = !!plugin?.mode && !plugin?.mode?.includes('View');
          const enabled = enabled1 || enabled2;

          if (props.pluginCenterOpts?.filterEnabledPlugin) {
            return props.pluginCenterOpts.filterEnabledPlugin(enabled, plugin);
          }

          return enabled;
        },
      } as PluginCenterOptsType),
    [props.pluginCenterOpts]
  );
  const _props = {
    onRef,
    comServicePath,
    compId: id as string,
    pageId: page.id,
    isDesign: true,
    config: config || KanBanInitConfig,
    events,
    pluginCenter: props?.pluginCenter,
    pluginCenterOpts,
    onBeforeMount,
    refPluginPackage: refPluginPackage
  };
  if (client === 'MOBILE') {
    return <MBoardView weId={`${props.weId || ''}_vm4syx`} {..._props} isMobile />;
  }
  return (
    <div style={{ height: '100%' }}>
      <BoardViewCom weId={`${props.weId || ''}_7jztx8`} {..._props} isMobile={false} />
    </div>
  );
};
BoardDesign.defaultOpts = {
  // 可选，是否有遮罩，用于控制组件设计模式下，功能的禁用，默认为false
  mask: true,

  // 可选，组件拖拽生成的布局大小
  // 如果pc端和移动端的Design一致，gl,mgl设置在Design中即可
  // 如果pc端和移动端的Design不一致，需要单独开发MDesign，则mgl需要设置在MDesign中
  layoutSizes: {
    // pc端自由布局
    gl: {
      // 页面被分为12列，默认为4
      w: 12,
      // 高度计算方式: h * rowHeight + (h - 1) * margin
      // rowHeight: 页面设置的拖拽步长，默认值为 1
      // margin: 卡片上下还是左右间距，默认值为 10
      // rowHeight和margin可以在页面设置中设置
      h: 60,
    },
  },
};
BoardDesign.defaultProps = {
  // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
  config: {
    ...KanBanInitConfig(),
  },
};
export default BoardDesign;
