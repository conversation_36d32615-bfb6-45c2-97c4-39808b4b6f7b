import React, { PureComponent } from 'react';
import { isFunction } from '@weapp/utils';
import BoardView from '../comp/board-view';
import MBoardView from '../comp/m-board-view';
import { ViewProps } from '../types';
import { EbdBoardEventName } from '../../../types/common';
import { getPluginClasses } from '../../plugin-packages/loader';
import { formatSdkRefreshData } from '../../../utils';
import { boardUrlParamsCache } from '../../../constants';
import ebcoms from '../../../utils/ebdcoms';
import '../baseStyle/index.less';

const { comEvent } = ebcoms.get();
export default class BoardViewCom extends PureComponent<ViewProps> {
  componentDidMount() {
    this.clearLocalState();
    comEvent.on('refresh.Kanban', this.props.id, this.refresh);
  }
  componentWillUnmount() {
    comEvent.off('refresh.Kanban', this.props.id, this.refresh);
  }
  clearLocalState = () => {
    sessionStorage.removeItem(boardUrlParamsCache)
  }
  componentWillReceiveProps(nextProps: Readonly<ViewProps>): void {
    if (nextProps.pluginCenter && (nextProps.pluginCenter !== this.props.pluginCenter || nextProps.refPluginPackage !== this.props.refPluginPackage)) {
      // 根据组件信息动态生成插件包
      const viewType = nextProps.page?.client === 'MOBILE' ? 'MView' : 'View';
      getPluginClasses(viewType, nextProps?.refPluginPackage, (pluginClasses: any) => {
        const plugins = [...(pluginClasses || [])];
        const registerName = nextProps?.pluginCenter?.pluginNames[0];
        // 检查是否被注册过
        if (!nextProps?.pluginCenter?.enabledPluginRecord[registerName]) {
          // 将插件包挂载到插件中心
          nextProps.pluginCenter?.pushPlugins(plugins);
          // 绑定刷新事件
          comEvent.on('refresh.Kanban', nextProps.id, this.refresh);
        }
      });
    }
  }
  refresh = async (comId: string, condition?: any, callback?: Function) => {
    callback = isFunction(condition) ? condition : callback;
    const { events = {} } = this.props;
    events.emit && events.emit(EbdBoardEventName.onBoardReload, comId, condition);
  };
  render() {
    const { id, page = {}, config, comServicePath, onRef, events, isDoc, store, pluginCenter, refPluginPackage } = this.props;
    const viewType = page?.client === 'MOBILE' ? 'MView' : 'View';
    const _props = {
      onRef,
      comServicePath,
      config,
      pluginCenter,
      isDoc,
      page,
      ebStore: store,
      compId: id as string,
      pageId: page.id,
      isDesign: false,
      isMobile: viewType === 'MView',
      events: events!,
      refPluginPackage: refPluginPackage
    };
    if (viewType === 'MView') {
      return (
        <MBoardView
          weId={`${this.props.weId || ''}_2hrrq5`}
          {..._props}
        />
      );
    }
    return <BoardView weId={`${this.props.weId || ''}_7jztx8`} {..._props} />;
  }
}
