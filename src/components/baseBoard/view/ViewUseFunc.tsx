import React, { useEffect } from 'react';
import { isFunction } from '@weapp/utils';
import BoardView from '../comp/board-view';
import MBoardView from '../comp/m-board-view';
import { ViewProps } from '../types';
import { EbdBoardEventName } from '../../../types/common';
import { formatSdkRefreshData } from '../../../utils';
import usePluginClasses from '../../plugin-packages/loader';

const BoardViewCom = React.memo((props: ViewProps) => {
  const { id, page = {}, config, comServicePath, onRef, events, isDoc, store, pluginCenter, refPluginPackage } = props;
  const viewType = page?.client === 'MOBILE' ? 'MView' : 'View';
  // 根据组件信息动态生成插件包
  const pluginClasses = usePluginClasses(viewType, refPluginPackage);
  useEffect(() => {
    const plugins = [...(pluginClasses || [])];
    const registerName = props?.pluginCenter?.pluginNames[0];
    // 检查是否被注册过
    if (!props?.pluginCenter?.enabledPluginRecord[registerName]) {
      // 将插件包挂载到插件中心
      props.pluginCenter?.pushPlugins(plugins);
      // 绑定刷新事件
      handlePageContext();
    }
  }, [pluginClasses]);
  const handlePageContext = () => {
    const { pluginCenter } = props;
    if (!pluginCenter) return;
    // @ts-ignore
    const [context]: any[] = pluginCenter?.invoke('getViewMount', {
      args: [
        {
          refresh: refresh,
        },
      ],
    }) as any[];
    return context;
  };
  const refresh = async (comId: string, condition?: any, callback?: Function) => {
    callback = isFunction(condition) ? condition : callback;
    const { config, events = {}, id } = props;
    events.emit && events.emit(EbdBoardEventName.onBoardReload, id, condition);
    // if (condition && has(condition, 'type') && has(condition, 'filter')) {
    //   const filters = (await formatSdkRefreshData(condition, config?.dataset)) || [];
    //   const filterId = condition?.comId || `${comId}_${new Date().getTime()}`;
    //   // todo 待对接
    //   return;
    // }
    // if (condition && has(condition, 'pageNo')) {
    //   // todo 待对接
    //   return;
    // }
    // this.refreshList(callback);
  };
  const _props = {
    onRef,
    comServicePath,
    config,
    pluginCenter,
    isDoc,
    page,
    ebStore: store,
    compId: id as string,
    pageId: page.id,
    isDesign: false,
    isMobile: viewType === 'MView',
    events: events!,
  }
  if (viewType === 'MView') {
    return (
      <MBoardView
        weId={`${props.weId || ''}_2hrrq5`}
        {..._props}
      />
    );
  }
  return (
    <BoardView
      weId={`${props.weId || ''}_7jztx8`}
      {..._props}
    />
  );
})
export default BoardViewCom;
