## [0.1.23](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.22...v0.1.23) (2025-05-07)


### Bug Fixes

* no.3593913 解决看板关联ebuilder分组非关联ebuilder字段新建分组无效的问题 ([f2c1db8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f2c1db877f36285ca4dfcc4f3a749656826625ea))
* no.3593913 解决看板关联ebuilder分组非关联ebuilder字段新建分组无效的问题 ([721d85f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/721d85f7132b336dd201524bab3eca87c0bfda6d))
* no.3593913 解决看板关联ebuilder分组非关联ebuilder字段新建分组无效的问题 ([c3ed63d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c3ed63de4cef600734d4641b0e93a0e53b50e28b))
* no.3593913 解决看板关联ebuilder分组非关联ebuilder字段新建分组无效的问题 ([355b244](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/355b2444f0e6179a9c0fd07dee7ebc69fac3c979))
* no.3593913 解决看板关闭ebuilder新建分组时不刷新问题0401 ([f069b42](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f069b42c5c3d5772461338daba970f4880fbf949))
* no.3593913 解决看板关闭ebuilder新建分组时不刷新问题0401 ([e9403a0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e9403a0d426ea1ef8190c15de9037528bbf22132))
* no.3593913 解决看板组件滚动分页问题 ([67bbe46](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/67bbe46ce3108cf0e58a371a4115cd6ba5417cb8))
* no.3593913 解决看板组件非表单数据源自定义分组保存异常问题 ([b77b880](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b77b88045d2130c7296a4ab488eb327adeac950f))
* no.3593913 解决看板设计器内配置差异化分组后不重新请求数据及日期分组、关联ebuilder分组后新建数据表单日期字段没默认填充的问题0401 ([d8600f7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d8600f75da62c2cb87ee6268fd4d5f991e7a5c90))
* no.3593913 解决看板静默更新后卡片右上角按钮不请求问题 ([c50ca54](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c50ca5490303ec006c5e72e48d5cea0d557ba4c6))
* no.3634026 解决看板组件，快速搜索条件，最后数据返回和筛选条件不一致【0301】 ([c5a5f5d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c5a5f5d31ca15ac018def38983777f9c704fe9c4))
* no.3646625 解决单个泳道时横向滚动失效，解决单个泳道展开收起失效的问题 ([8dfc357](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8dfc3571c4ef3f6c5d7b6a86081cad9b7a695fbd))
* no.3646625 解决看板拖拽分组提示无权限及看板高级视图下差异化分组屏蔽的问题 ([da6bf7f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/da6bf7ff372731f3e92ff93ffc718831aebf1dbf))


### Performance Improvements

* no.3636800 优化看板组件，移动端，只有一个分组时不显示左右切换箭头 ([833db67](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/833db67d45f0396b83b676ef5a6fc169b7e93c7d))
* no.3646625 优化移动端关联ebuilder分组后新建分组表单的功能 ([a52a3d7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a52a3d7e69057cd0c178dc483638705b0a0f2554))



## [0.1.22](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.21...v0.1.22) (2025-04-03)


### Bug Fixes

* no.3547927 解决看板差异化分组回显问题 ([50d8717](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/50d871706e1f8174a19cb8bcfbb9ab1d44cc1b53))
* no.3547927 解决看板非表单数据源支持自定义分组及阶段看板样式问题 ([ce5db9d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ce5db9d02dccc05ae0271aaf989b5edef3fde158))
* no.3593913 解决看板组件数据源添加报错问题 ([4586da2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4586da2cceb60ff438320c55340047ad4c991c53))
* no.3593913 解决看板组件滚动分页存在重复数据的问题 ([ff767a8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ff767a861882f61c9a35e22283c46a4241ffdaae))
* no.3593913 解决看板组件滚动分页存在重复数据的问题 ([0bcbd2b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0bcbd2bd0ac06c5585c489cc7ffa767bea47c6b1))
* no.3593913 解决看板组件筛选组件ebParams的问题 ([dc75229](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dc7522933f90538bd1cbf1e910a9df0eb3463eb6))


### Features

* no.2025005275 新增EB看板拖拽前和拖拽后事件配置 ([8b97f43](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8b97f43c5c6b653f8a2e9cff54390f8124e9230b))
* no.2025005275 新增EB看板拖拽前和拖拽后事件配置国际化 ([9f3798b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9f3798b9ca21c7844b35cd0a34541df30d921f6e))
* no.2025008136 新增EB看板数仓业务数据源排序 ([d8a2f0a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d8a2f0a488e34b9bc6114de4a4ab56c1020ea8ad))
* no.2025008136 新增EB看板数仓业务数据源排序 ([8237888](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/823788840a5196a098643d08014bcf27d904f522))
* no.2025008136 新增EB看板数仓业务数据源限制分组权限及差异化分组 ([697a2bc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/697a2bc967fbb574a25b871e37e0e9a1ae8e2e03))
* no.2025008136 新增EB看板数仓业务数据源限制自定义分组可走saveKanbanOption ([e36d816](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e36d8164e7fff23e0acc72fca8001d9c97fe80a7))
* no.2025008136 新增EB看板新增数仓、业务数据源支持自定义分组 ([5e2290a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5e2290a11814d705f6a28ca4de610875bd9f84e0))
* no.2025008772 新增看板支持静默更新 ([f00473f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f00473fc74e47e487fe47882b3e6c591e7c1eb28))
* no.3575308 新增事井然专项默认组件配置 ([a42bef9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a42bef95a9c612b124b7a71a88d1b2b56676eb99))
* no.3575308 新增事井然专项默认组件配置 ([b4442af](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b4442af8036834a27b4d4f6561dcbda5025ac8a4))


### Performance Improvements

* no.2025008136 优化卡片自定义字段事件动作点击的功能 ([5d2dc65](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5d2dc65b0ac71db4975671a4083632a4a7883e40))
* no.3559817 优化仓库css chunk数量的功能 ([c6756da](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c6756dab021a7595ce8ae3d4477ac4b6954e3115))
* no.3559817 优化仓库css chunk数量的功能 ([5cfa24d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5cfa24de9d0d13f72ca71614a5cf2af618f999c7))
* no.3579614 优化EB进展看板自定义泳道标题口子的功能 ([109762c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/109762ceb0595d6c1dfd329555cf60b2db0a91e9))
* no.3579614 优化看板差异化问题及新建分组点击失焦 ([6b50bf0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6b50bf02b269e7d501ca0d105773858daba6b9f6))
* no.3579614 优化看板拖拽提示的功能 ([30da4e4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/30da4e449af1d6346c8e1b3833606006bfb63424))
* no.3579614 优化看板组件高级视图默认样式及背景色 ([e1e6ff4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e1e6ff42066aab04abbde4d3f0a1b3d7fb4f9c41))
* no.3579614 优化看板组件高级视图默认样式及背景色 ([ffd0b3c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ffd0b3c4d6cdde6ab77b07ed5525beb5d3d40e0f))
* no.3579614 优化看板组件高级视图默认样式及背景色 ([05b4ae2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/05b4ae23dd3ee79e3b8b3d82f81e55e51207b399))
* no.3587662 优化[还原]选项卡组件嵌入多列容器，拖入看板组件，自适应宽度的功能 ([ad435d0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ad435d045b3cad3c0b7f7536c4d7118cd1739344))
* no.3587662 优化选项卡组件嵌入多列容器，拖入看板组件，自适应宽度的功能 ([8a1b7dd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8a1b7dd9839e58bb73a3d0c73b0cb3d72692e7d2))



## [0.1.21](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.20...v0.1.21) (2025-03-07)


### Bug Fixes

* no.2025003963 解决差异化分组关联类型下拉屏蔽人员类型 ([3e4f03b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3e4f03bae43edaa49b63f69931a0dde2524285e0))
* no.2025003963 解决差异化分组多分组添加失败 ([bd3cff2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bd3cff204d00c5db8cfcba596b1308f4af2155a2))
* no.2025003963 解决差异化分组多分组添加失败 ([d66bb93](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d66bb9379d9066e2828b5e74dec09f594af19bfc))
* no.3543177 解决EB看板分组隐藏未分组后移动列表数据应隐藏 ([26a46ad](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/26a46adf29971e28e6d431059fd4a0660f38bb2e))
* no.3547927 解决看板合并相关 ([6ee7961](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6ee7961bb1ca8052951710211ec3d0555d69ba60))
* no.3547927 解决看板字段筛选优化过滤判断 ([231acb3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/231acb398bfdbd21730c2e1a07ea8d4ccade23f5))
* no.3547927 解决看板差异化分组保存默认分组报错 ([f1051ce](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f1051ce98e56630ccb988e3b695df4515b5d16d7))
* no.3547927 解决看板差异化分组保存默认分组报错 ([9dcaf0a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9dcaf0a7bb56108f2b658c55fa8975a37cf90665))
* no.3547927 解决看板差异化分组关联字段无法选中 ([0621552](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/06215525f47d958d3a9d847c74439e2587aea00b))
* no.3547927 解决看板差异化分组关联字段无法选中 ([eb489f0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eb489f0441d4fb33866686dd509d081573fd5094))
* no.3547927 解决看板差异化分组关联字段无法选中 ([dd79082](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dd7908299b030fbc4ef6f05e0de1b0d1f4622263))
* no.3547927 解决看板差异化分组关联字段无法选中 ([4f8585e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4f8585eefdd41adf73dd9f8c029ee05419c9d86a))
* no.3547927 解决看板差异化分组关联字段筛选优化过滤判断 ([0bac669](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0bac669d1ed0e9bc3159530df097a5e0ab0dd717))
* no.3547927 解决看板差异化分组关联字段筛选优化过滤判断 ([b03f249](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b03f2497979cc37a878f184ab033f23278cf29bc))
* no.3547927 解决看板差异化分组切换分组后报错 ([0ce46f2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0ce46f29008257aadb63c5df7b2d6e6e73627bac))
* no.3547927 解决看板差异化分组切换分组后报错 ([3d3f1ac](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3d3f1ac6a46df5b46c60c5f2db72d68ad204d2de))
* no.3547927 解决看板差异化分组校验 ([6ce0de2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6ce0de2a5b79132dc44d4b02f8f7cf2b0fc4b7cb))
* no.3547927 解决看板差异化分组相关问题 ([7bd1449](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7bd1449319c39805ac79c1d9d7427b811e3f45a7))
* no.3547927 解决看板差异化分组相关问题 ([4ecaad0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ecaad0ad494cdb662f3ffe3eda40f5e2768071d))
* no.3547927 解决看板差异化分组相关问题 ([95ba041](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/95ba04151f06270a7b081d02f7b9d967ebeaac55))
* no.3547927 解决看板差异化分组相关问题 ([b04a0da](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b04a0dab30cb818ec69c322d55a281243f6f4e9d))
* no.3547927 解决看板差异化分组相关问题 ([886a51c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/886a51cb68a3dd36d2612db9d7c328e928b77200))
* no.3547927 解决看板差异化分组相关问题 ([63d2b19](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/63d2b193234bca86d613613cfd4aad3138f61b6f))
* no.3547927 解决看板移动端搜素后新建分组不对 ([01c2b56](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/01c2b56a7c2056f8450e8fb2c9c0552af5087028))
* no.3547927 解决看板移动端搜素后新建分组不对 ([b67e915](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b67e915133316fd2f614f3d08b8c2ee4132e1b18))
* no.3547927 解决看板组件，删除分组后，拖拽页面显示其他分组，未重新调用getdata加载数据 ([1d2f8cc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1d2f8cc6be3f86349a8bf8967c7aeb265679bebf))
* no.3547927 解决看板组件，删除分组后，拖拽页面显示其他分组，未重新调用getdata加载数据 ([8531ae8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8531ae84cd20e7134b0cf1812029439e911fae14))
* no.3547927 解决看板组件高级搜索筛选报错 ([22ac2ba](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/22ac2bafd26347aa443daa7984d6b0d2d1f700f0))
* no.3547927 解决看板隐藏无数据分组后菜单显示问题 ([ea80cd8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ea80cd8dac71116257e4fccd61141a9037096ce0))
* no.3554596 解决看板组件，getoptionmeu调用次数不正确问题 ([20eeb79](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/20eeb79fbd2c2df24cc6eddc1d35f1d9b10323e9))
* no.3560793 解决看板组件，视图字段添加打开布局未生效 ([1f6e9e7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1f6e9e7cc12872a0092eaeb5c58ca3eb12a3494a))
* no.3567016 解决【交付物进展】点击新建按钮拉出的为流程列表 ([a04513a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a04513ad0134734ff0084e99e44a33b05bc95373))


### Features

* no.2025003963 新增EB看板分组权限新增关联模块人员字段 ([ec15a13](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ec15a130077e3d202babad498670fd6c1479acd5))
* no.2025003963 新增EB看板差异化分组功能还原 ([60ee2fe](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/60ee2fea8e81ca5ddbb533c60a1bc029e9d011cb))
* no.3528499 新增显示空白占位二开口子 ([79d6896](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/79d689628163d600ba15c03da8b807f31659fc8c))


### Performance Improvements

* no.2025002621 优化项目阶段看板组件的功能 ([b660c9d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b660c9d17fe64a4105e96ba7865ac9c81c0936d2))
* no.3492279 优化看板业务数据源屏蔽关联ebuilder分组的功能 ([644bae9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/644bae9627d9506e37c98e4ad98fcdff2ae78ebf))
* no.3492279 优化看板业务数据源屏蔽关联ebuilder分组的功能 ([1214aee](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1214aeee3655dc8bd1a4a7d8d449ebeb52914980))
* no.3525848 优化项目进展看板样式问题 ([fe2cc2b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fe2cc2b0f7241597994fea93ea0742c1803fbaee))
* no.3532522 优化项目阶段看板标题，最多一行类似页签 ([0515bac](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0515bac78b4f14d9c596d361f7cb9b9fe3016be6))
* no.3532522 优化项目阶段看板标题，最多一行类似页签 ([68a2022](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/68a2022901a8b6985c7644cb2246de63199320b3))
* no.3534979 优化进展看板筛选组件监听事件 ([8914c43](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8914c43a03a2fca72df22eb42d5830cbdfa6dbb5))
* no.3534979 优化进展看板筛选组件监听事件 ([279093d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/279093dbbf0a9f1541d53d01da7a542937747105))
* no.3534979 优化进展看板筛选组件监听事件 ([b23f09e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b23f09e4663d017803d873cb8809f2cb2919e5f1))
* no.3534979 优化进展看板筛选组件监听事件 ([4283e36](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4283e36db1bd7060d3f50be65d3456ca3f477abb))



## [0.1.20](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.19...v0.1.20) (2025-02-07)


### Bug Fixes

* no.2024046043 解决进展看板相关问题 ([9a23c2b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9a23c2bd12f853e46efb97c9c4747cf86683fa82))
* no.2024046043 解决进展看板相关问题 ([176c3ba](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/176c3ba5990972d84379cca9487f952c727ce37c))
* no.2024046043 解决进展看板相关问题 ([1e8c5b2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1e8c5b2075208eb94b32a844a9c31a260b9cbac9))
* no.2024046043 解决进展看板相关问题 ([e60f9aa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e60f9aa28fcce68591f409715090e72cc76b7cd4))
* no.2024046043 解决进展看板相关问题 ([df8e570](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/df8e57006c343baff6ae4d9d63817333ab9c9a9d))
* no.2024046043 解决进展看板相关问题 ([c6af422](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c6af422342879a3331a7194b78fe91f65565c173))
* no.2024046043 解决进展看板相关问题 ([0d09412](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0d09412bbee240b581bee5913af85d4f9e48a408))
* no.2024046043 解决进展看板相关问题 ([72df51b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/72df51b5228e4a03ae0016e1c4face5c400a31b4))
* no.2024046043 解决进展看板相关问题 ([78bd223](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/78bd22375c3666ac6b27f62f25fd96674e3eccaf))
* no.3492279 解决隐藏无数据分组后分组菜单偶先不请求问题 ([1fb3a69](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1fb3a69c0705a4405c06b3690cb7d1b82e3cf4dd))
* no.3492279 解决隐藏无数据分组后分组菜单偶先不请求问题 ([1912688](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/19126883d58fb5e49abb1b19701b27b9fb986758))


### Features

* no.3477276 新增EB看板隐藏顶部标题二开及隐藏新建数据及列表右上角菜单二开 ([48cf59f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/48cf59f6ddc02e241c6651c3a40b4745f24c4991))
* no.3477276 新增EB看板隐藏顶部标题二开及隐藏新建数据及列表右上角菜单二开 ([7342719](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/734271974709fdc7033b01f15acded89789fb1cf))


### Performance Improvements

* no.2025002621 优化项目阶段看板组件的功能 ([4ed7849](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ed7849958e09819d9ee83fd140792c409733ee6))
* no.2025002621 优化项目阶段看板组件的功能 ([92c21fe](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/92c21feeca7dadd362ecf7d9419c3d753952294f))
* no.3482679 优化EB阶段看板相关的功能 ([213462b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/213462b0d513ab4d354897b205ef6aed95b97e40))
* no.3482679 优化EB阶段看板相关的功能 ([5d09176](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5d09176204fd00ade0b1e9b3c0112670e4e20df9))
* no.3482679 优化EB阶段看板相关的功能 ([3efa0ba](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3efa0ba6dc14f36d84a89e0a629abf6f353aa372))
* no.3492279 优化看板业务数据源屏蔽关联ebuilder分组的功能 ([129e772](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/129e77218fd626b30fca27b9af13a5f3eb12b694))
* no.3492279 优化看板业务数据源屏蔽关联ebuilder分组的功能 ([5c35cb6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5c35cb6a02e631250cf86983e7519bcd2275d425))
* no.3492279 优化看板业务数据源屏蔽关联ebuilder分组的功能 ([82f408d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/82f408dd0df4d567784760396cf4ba8f45d3b15c))
* no.3510697 优化阶段看板组件设计器内回显配置的功能 ([f8c2201](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f8c2201bd444052aa228dc42e42c3e2383d202b9))



## [0.1.19](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.18...v0.1.19) (2025-01-03)


### Bug Fixes

* no.2024043351 解决看板设计器内展开收起失效问题 ([61aa26c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/61aa26c172fc6d28efdf54658e4f85b0f9b0e935))
* no.2024043351 解决看板设计器内展开收起失效问题 ([c512e89](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c512e8941d60374724532da87f85d71e315dad18))
* no.2024043351 解决看板设计器内展开收起失效问题 ([6b11027](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6b11027cb48ee4789e8239d90d56e5bc0fd07025))
* no.2024043351 解决阶段看板显示空白 ([fb26815](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fb268152e7f196178e4dac95e680cd12cd8e2063))
* no.2024046043 解决二维看板国际化问题 ([c9e9a92](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c9e9a92349c605e7e7f1c134ffac147c33c7c5f1))
* no.2024046043 解决二维看板泳道分页相关 ([6ef1663](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6ef16631c677bed11824e60c40bca195966bbeed))
* no.2024046043 解决二维看板泳道分页相关 ([20607a6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/20607a69e7c5765c10a0cc8888e17e0374de9544))
* no.2024046043 解决二维看板泳道问题 ([02fac62](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/02fac620b0ae07b08c878dd38b16b7b575e5d68e))
* no.2024046043 解决二维看板泳道问题 ([c2e1656](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c2e16561911ab09ede6361922a4d8a8538d7015a))
* no.2024046043 解决二维看板相关 ([71e8788](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/71e8788d411879f758d917d6cc54fdfc3cb8df61))
* no.2024046043 解决二维看板相关 ([3e3ac17](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3e3ac17081a5664edfdabf79ce7bc36168519218))
* no.2024046043 解决二维看板相关 ([5cfc2b5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5cfc2b5a55ef46007a7afb976d5cdca5920a803d))
* no.2024046043 解决二维看板相关 ([70f5e46](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/70f5e4666c26417049235251859808f5b9a77a6c))
* no.2024046043 解决二维看板相关 ([e9c3caf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e9c3caf3579fa72a6bcc134e9868bcd28507e38b))
* no.2024046043 解决二维看板相关 ([9b138b4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9b138b4b3c90dce9e9d217cf0ec86b6a26ae7164))
* no.2024046043 解决二维看板相关 ([780bc9a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/780bc9a9167ff3482f8cfc0571f72e1e7f64a122))
* no.2024046043 解决二维看板相关 ([0e7eca8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0e7eca874efa2aa5ca1627e354f2ecba3333dc85))
* no.2024046043 解决二维看板相关 ([5ed391d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5ed391d1306f711c62ace2834d54fe96998e8b00))
* no.2024046043 解决二维看板相关 ([4ffbe5d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ffbe5df935fda6e9a4cb3d8d3c5c14198a4effd))
* no.2024046043 解决二维看板相关 ([4b33673](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4b33673b77ac613b400b023239bb2fcabce3322e))
* no.2024046043 解决二维看板相关 ([9b48456](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9b48456010a73e415a5f77b003af3f2ec5ad09f2))
* no.2024046043 解决二维看板相关 ([ecc2205](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ecc2205c4e898d988fda877f82d80c907022e6aa))
* no.2024046043 解决二维看板相关 ([9fc2e23](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9fc2e239f74a07bdc42ba9e6c7b9eaf91ac30837))
* no.2024046043 解决二维看板相关 ([75d9172](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/75d917221e87f8051aa282feb8570d8e7a8aefd3))
* no.2024046043 解决二维看板相关 ([e075252](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e075252fcc7d9603cf392e3870ba3ba7a7607d56))
* no.2024046043 解决二维看板相关 ([bb6d8de](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bb6d8dea25ffaf973ac4f1bde74c574b7161afc5))
* no.2024046043 解决二维看板相关 ([ff35bd9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ff35bd9341aeb7433e37137bfeb5097cf20a588f))
* no.2024046043 解决分页问题 ([02f612f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/02f612f67feee2cc711e52617ea8ce0b274d7d21))
* no.2024046043 解决合并相关 ([5ec4f42](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5ec4f42ea20a45a2230341d0831ed45b6e949bf6))
* no.2024046043 解决国际化 ([7b9ba7f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7b9ba7f59a88696f0e48cccaf1842da84309cb77))
* no.2024046043 解决进展看板国际化相关 ([826f474](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/826f47499efb919a58def4ee553c2c826c10a0d7))
* no.2024046043 解决进展看板相关问题 ([e30d7ff](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e30d7ff8a51b375d2bc00fb623d7a1aa76d51bdd))
* no.2024046043 解决进展看板相关问题 ([8fe9434](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8fe9434c1551d487219d3488e1180cf1bb6abff4))
* no.2024046043 解决进展看板相关问题 ([965ea0d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/965ea0d481feb8f5f18cb1a8aa4e327ee01d9781))
* no.2024046043 解决进展看板相关问题 ([40051ce](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/40051ce6804947e11afb7ec26c3d5bc743a53cb2))
* no.2024046043 解决进展看板相关问题 ([6f1803b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6f1803b95450fdea3ab37ba21409129e762069b8))
* no.2024046043 解决进展看板相关问题 ([e66c76b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e66c76b1751709ea168c9faf6d65c80018bb901e))
* no.2024046043 解决进展看板相关问题 ([dfa4bbd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dfa4bbd09447d16ab75ef0b87791978d043d6183))
* no.2024046043 解决进展看板相关问题 ([e26f031](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e26f03173a0c0d8c7b54116d929406d48dbae60b))
* no.2024046043 解决进展看板相关问题 ([488ec85](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/488ec856c98531f1916142f9fa2232d456d6609e))
* no.2024046043 解决进展看板相关问题 ([653c80b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/653c80ba0fafc65f1d0dd097b00ca95b4527a8d1))
* no.2024046043 解决进展看板相关问题 ([99a3d15](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/99a3d157cb5344e70dcd672160532e4446179da2))
* no.2024046043 解决进展看板相关问题 ([7cab1b9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7cab1b9feaa0db58a3924ce486c858605ac20f33))
* no.3298490 解决看板数据加工数据源筛选隐藏问题 ([21b45da](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/21b45da91f537207b0784a15c075e9d0e3d918ce))
* no.3298490 解决看板新建分组接口请求时序问题 ([05bbcc7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/05bbcc730764e1bf5df91c222b86c1d221cf36d8))


### Features

* no.2024046043 新增EB看板支持二维分组改造 ([e5055b4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e5055b42c6107a91ab9b8ebc04a1ba85e4c75182))
* no.2024046043 新增EB看板支持二维分组改造 ([17295dc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/17295dcd775b2a0173fb85925685b1243d8f7195))
* no.2024046043 新增EB看板支持二维分组改造 ([652eeb5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/652eeb5c843efcf77faab9e50931f935e84eebb2))
* no.2024046043 新增动态支持内部横向滚动 ([c1c1f43](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c1c1f43b0682280b2ae879736c804a3025b19d5e))
* no.2024046043 新增设计器内泳道相关配置 ([8d2cc8d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8d2cc8d7dbaf2e2c02e9529690035eb3215491ac))
* no.2024046043 新增设计器内泳道相关配置联动 ([6f6dd6e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6f6dd6ec9c787402889787049b2ba7d0413e8a49))


### Performance Improvements

*  no.3456295 优化看板组件—关联eb—删除分组后，新建分组展示的问题 ([a77ca0a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a77ca0ae305ef1850df78753bcff2ea9f48f8e6a))



## [0.1.18](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.17...v0.1.18) (2024-12-06)


### Bug Fixes

* no.2024035972 解决看板新建数据按钮显示高度 ([2d70271](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2d7027157fa71a722b45e29c3678b7f42456d2ba))
* no.2024035972 解决看板新建数据按钮显示高度 ([10a5428](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/10a542889df5269565fdfc94d1ac6b1475ba2d2e))
* no.2024035972 解决看板设计器编辑问题 ([7e89169](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7e89169c0bf4c6a54c14803cd62ba9b134789e10))
* no.2024035972 解决看板设计器编辑问题 ([e7f3d4b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e7f3d4b57011a89f91d35ec8a553760aebdc1ea4))
* no.2024035972 解决阶段看板切换项目数据源后，项目数据源内-字段配置未清空 ([9a7d583](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9a7d58374e74f9fb42ff85ff36275181d9a340b9))
* no.2024043351 解决看板修改数据后不回显 ([81e8146](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/81e81464e417c5dce03227bd971703356ffd79d9))
* no.2024043351 解决看板分组设置后回显失败的问题 ([65c7866](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/65c78661a537593f26ef10149f87e0416b3b8f59))
* no.2024043351 解决看板右上角按钮点击事件不生效 ([ec10c6c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ec10c6cf32daa915982479ec46dae74ebc19a668))
* no.2024043351 解决看板国际化问题 ([3beec28](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3beec284b86d982ffa180e449ea52bd107314b18))
* no.2024043351 解决看板容器内高度样式自定义不生效问题 ([303814a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/303814a23b1fb1af936caf9aa554316b1f7d293f))
* no.2024043351 解决看板按人员分组测试问题 ([4ff6ab4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ff6ab4cda1bf7344b3e1bbee8efcd701c6825df))
* no.2024043351 解决看板按人员分组测试问题 ([c2ffd44](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c2ffd44d000c8f3b51bdd417a2078ba97d42e486))
* no.2024043351 解决看板条件分组相关问题 ([5783be6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5783be6305c9719bc8c0df1c6743375f752209d8))
* no.2024043351 解决看板移动端按钮点击不生效 ([3e59fb7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3e59fb7e6fc6d346da79b88510298ff84a8063dd))
* no.2024043351 解决看板移动端点击不生效 ([166dd7f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/166dd7f5156a7edfcffc84d42157bbf68073e483))
* no.2024043351 解决自定义分组新建数据不关联问题 ([13b92e7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/13b92e727c4409513603ef5ac9d37ed2a891466e))
* no.2024043351 解决自定义分组新建数据不关联问题 ([302d055](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/302d05580bd52b33820c17363a3c4ce53ac0ae4f))
* no.3298490 解决看板删除卡片前置二开支持 ([5d99048](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5d9904846b262d28dd71179f40c37f1ba83281cd))
* no.3364283 解决左侧树刷新导致看板加载两次问题 ([753239d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/753239d5a397b2bc162d3ffc1a26486d69ea41a0))
* no.3364283 解决左侧树刷新导致看板加载两次问题 ([87b5fb8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/87b5fb8e7f6e4eb2af0a9052fd2074e84625c6ae))
* no.3364283 解决左侧树刷新导致看板加载两次问题 ([77b1a42](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/77b1a42ce4483876355acb75b79d9e0546bb116c))
* no.3364283 解决移动端数据不请求问题 ([277cba5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/277cba5b4d30086bc6ac31b649c33918f02faeb5))
* no.3364283 解决移动端数据不请求问题 ([859ad93](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/859ad930f00e18c6b9cf03c29c195bd0799b985f))
* no.3364283 解决移动端数据路由变化时导致阶段看板重新获取问题 ([ad0f258](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ad0f2588de036fe33003ed33039a9149408741fd))
* no.3364283 解决移动端数据路由变化时导致阶段看板重新获取问题 ([741e8bc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/741e8bc43b587add5b0c5ba728e28e020575bf02))
* no.3364398 解决阶段看板刷新未触发刷新问题 ([338512c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/338512c226be564234bc2a91f537db931b0637b3))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([2f18b25](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2f18b25a25e551aabdcb2856be8c9f6e510329e4))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([2bb6ca0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2bb6ca0055f1be400fcee1259a0b8536d48b5ced))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([10a899f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/10a899fb0ead85a52caed4c10e611e9e829fad80))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([6de356c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6de356ccedc0f7ba82f8bc842fdb8be8828c3d5d))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([25eff48](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/25eff482f242d16346a32073c9299631553e82d2))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([0f1f2ce](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0f1f2cec4fec723dce7a98bfeb1e9db1c5a2491e))
* no.3364398 解决阶段看板树组件刷新参数传参值问题 ([11ecd3b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/11ecd3b23254bf72aa9a1cbe84ceb51b9b978427))
* no.3364432 解决阶段看板——滚动分页已触发，切换分页模式到加载更多，依旧是滚动分页 ([f443da1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f443da1963cab8e692b2309f412d88a153e6d9cc))
* no.3364573 解决阶段看板——切换项目类型数据源，关联类型字段未清空 ([ea6ee5d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ea6ee5de61cbdb1b0bba649ae1f148661b57ea3f))
* no.3364573 解决阶段看板——切换项目类型数据源，关联类型字段未清空 ([f893845](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f893845e521445b0e16d59263be11b8c41fd4204))
* no.3371550 解决看板组件——已封存数据，添加不能移入提示 ([285b284](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/285b284bbcbe7860627c381c0496f28663543cbc))
* no.3371550 解决看板组件——已封存数据，添加不能移入提示 ([20b0cac](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/20b0cac3d8dcefcf6df616fef81c5e7433d4dbcc))
* no.3371550 解决看板组件——已封存数据，添加不能移入提示 ([0313419](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0313419ef1ab668e2b03191c232461244bff1592))
* no.3371550 解决看板组件——已封存数据，添加不能移入提示 ([c86e3ec](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c86e3ec8fbd14d556ba056c8af330ca67df01322))
* no.3371550 解决看板组件自定义loading二开口子问题 ([b72bcaf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b72bcaf8bfa511daa96174c999c859df5353dcf5))
* no.3371550 解决看板组件设计器页面分组按钮问题 ([f88803f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f88803fce98ce7dc84bf609c3e22ebc88ea3434c))
* no.3371550 解决看板组件设计器页面分组按钮问题 ([5f00e26](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5f00e26db58f730bb91a1095a15b4a52b5c0a76b))
* no.3371618 解决阶段看板——页面拖入两个组件报错 ([39a0aab](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/39a0aabd23ab84804d1f933895468d03acf9085e))


### Features

* no.2024043351 新增EB看板支持按照任务负责人分组以及按条件分组 ([e523a78](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e523a78f21bab7a09d41e1da79cf0ae05e1fc8b0))
* no.2024043351 新增EB看板支持按照任务负责人分组以及按条件分组【1201】 ([df1ba37](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/df1ba3733e9bab17c59cd1016d4541fa096a6c02))
* no.2024043351 新增EB看板支持按照任务负责人分组以及按条件分组【1201】 ([ab65c9f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ab65c9f46e37e36948446f6521f70ee20695727c))
* no.2024043351 新增EB看板支持预览页隐藏未分组 ([038eadf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/038eadf0816ece957346a74e6fb8d95acf4e53e8))
* no.2024043351 新增EB看板支持预览页隐藏未分组 ([de52adb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/de52adbe0366dd1736128df1daed7eb538e9f547))
* no.2024043351 新增人员分组拖拽分组的数据为空的时候，重新请求分组 ([6868d52](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6868d526fe1788cd8324ec06d0562b980bad7feb))
* no.2024043351 新增对接隐藏无数据分组功能 ([c210eba](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c210eba1c26e919fecd180cbae827826239588ea))
* no.2024043351 新增放开数据加工数据源及人员下拉 ([cd18518](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cd18518a3e275188a390457593a069b7506f9a8a))
* no.2024043351 新增放开数据加工数据源及人员下拉 ([af41fe2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/af41fe248baa3fcb4c6e9268c07941c6533bd1c3))
* no.2024043351 新增数据加工数据源按日期分组 ([6044110](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/60441107fc432879febda83c1da8b072bb0e6bf0))
* no.2024043351 新增看板对接数据加工数据源 ([cbabdad](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cbabdadc72524a743a83b158b2c63e7d0196b882))
* no.2024043351 新增看板帮助文案 ([7babde3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7babde3213100f002e6c5e57c67a3a7536763643))
* no.2024043351 新增看板描述文案及条件配置有值时图标提示 ([2b8ba2d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2b8ba2dd56947c46debdc0063a295cf4ef6ba57a))
* no.2024043351 新增看板条件分组限制卡片拖拽及放开saveKanbanOption ([a846467](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a846467ebad29de117995ff4220b8e9b07bb8cc0))
* no.3364283 新增单个分组菜单数据下拉二开 ([d777f18](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d777f1835ec35ed73eb5b19953b1d94c12149f0e))
* no.3364283 新增阶段看板兼容项目任务树组件视图切换 ([e5a7664](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e5a7664ee49d358cd753060cd598dba02e43ef08))
* no.3364283 新增阶段看板兼容项目任务树组件视图切换 ([e222cd8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e222cd8050c2c86ad44567ad851900f90eb34a67))
* no.3364283 新增阶段看板兼容项目任务树组件视图切换 ([4e06fca](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4e06fcac9ca55792cc546adad3f4228354de7cf2))


### Performance Improvements

* no.3298490 优化看板分组二开为空数据请求的功能 ([2825c65](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2825c65260a1a662636dce7e1a362275862beaaf))
* no.3298490 优化看板分组二开为空数据请求的功能 ([9c16b48](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9c16b48c5fd673ddc5dafd4702e39a69afe25571))
* no.3298490 优化看板分页加载功能 ([fb22145](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fb22145219f9bf1ae22190637e7b9488e0f8cc25))
* no.3298490 优化看板刷新逻辑 ([bd823eb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bd823eb6744552ff793d88da42108624dec1cd0c))
* no.3298490 优化看板刷新逻辑 ([c6adc7e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c6adc7e561d715e6041cad78aedca29ad43c3978))
* no.3298490 优化看板刷新逻辑 ([3926a75](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3926a750f1bf66b3bc2c02d136cf340a0526e780))
* no.3298490 优化看板刷新逻辑 ([ebf40f1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ebf40f1850f5cc63224a2739999f9e299c7668db))
* no.3298490 优化看板手动调用组件刷新重复请求竞态的功能 ([884e913](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/884e913586502dbd2a45a39faae96140df18b73d))
* no.3298490 优化看板手动调用组件刷新重复请求竞态的功能 ([3e51d45](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3e51d4594d960d88fda50ea1bd11d05049fbbfc4))
* no.3298490 优化看板新建数据后重复请求问题 ([a6a4ac3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a6a4ac301848c84f594954120ebfe1205d167cf9))
* no.3298490 优化看板本地新建表单逻辑 ([5b88c48](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5b88c489e34db6ef6d9480a218feb7b0b39f13b6))
* no.3298490 优化看板本地新建表单逻辑 ([2199a68](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2199a68d38fd87bb18d63964a59784cb47911ec0))
* no.3298490 优化看板移除差异化分组逻辑 ([50607bd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/50607bd1fea1556410a95e9ec883d0fdf775a657))
* no.3298490 优化看板移除差异化分组逻辑 ([cfef7fe](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cfef7fee83d92e905f5aa2275a7aa941908e2482))
* no.3364283 优化看板数据请求逻辑 ([6814ff2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6814ff26f78f6f4c77fef29563e8db0e6747e535))



## [0.1.17](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.16...v0.1.17) (2024-11-08)


### Bug Fixes

* no.2024035972 解决看板同组排序失效问题 ([201de39](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/201de3943edc3123d73474764578c40d7a7c0482))
* no.2024035972 解决阶段看板分页问题 ([d42e11b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d42e11b8c1af95839586cceddd1a280eae24d036))
* no.2024035972 解决阶段看板分页问题 ([38911f3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/38911f39408176c619055987b08eb63573d39858))
* no.2024035972 解决阶段看板切换类型不更新值的问题 ([a4b9e30](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a4b9e3079380388abd4575c2442612b888753314))
* no.3162993 解决全部数据权限数据源放开 ([6e3d9fc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6e3d9fc65acac4bf916e736a97b7418bfd9bc363))
* no.3281908 解决看板左侧树筛选条件混淆的问题 ([437f90d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/437f90d6b621daeaae31a3eed66e8966c1a3eab2))
* no.3291384 解决看板滚动加载失效 ([cb9d0fc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cb9d0fc54ad3cadbe7efdc2db1041e3ab2d19473))
* no.3291384 解决阶段看板统计配置不生效 ([9aabde5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9aabde5808bef01adfe648bbfa299809161288a1))
* no.3291384 解决阶段看板统计配置位置 ([bcc2125](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bcc21257579170dc37b5c1810bb30bb042781e28))
* no.3291384 解决项目阶段看板单击每一条事件id不生效 ([1408fc9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1408fc99c1ce5521c84df885d1f75e4255242f27))
* no.3291384 解决项目阶段看板数据重复请求问题 ([3bf47ca](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3bf47ca5a9a600d613fe497e19bbac16da13ba0c))
* no.3291384 解决项目阶段看板测试问题 ([8298f3e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8298f3eec4d2af8ba8d202bdd4d3b8142e500c85))
* no.3291384 解决项目阶段看板测试问题 ([0822cd6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0822cd6ba5e59ce2ef42b5ddd5ec4c478a67891a))
* no.3291384 解决项目阶段看板统计配置保存失效的问题 ([1ee3b29](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1ee3b29784bcd2ef08b1649b2f1646db058d2f83))
* no.3291384 解决项目阶段看板问题 ([0e00477](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0e00477c298d089c5dcbae05db472c7c869d1115))
* no.3291384 解决项目阶段看板问题 ([1e1fad2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1e1fad241601922aa269fbdd430201591e71a6d5))
* no.3291384 解决项目阶段看板问题 ([6aceba8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6aceba8436df692013e63053d64f21ba4944ea37))
* no.3291384 解决项目阶段看板问题 ([2028815](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/20288158f619a7478fce1c3f881b94c266f5271b))
* no.3291384 解决项目阶段看板问题 ([3f8a40f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3f8a40f7c4baf8c38c2e66247e301bc6eaf5088d))
* no.3291384 解决项目阶段看板问题 ([972e3a1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/972e3a11dddb975a2cf33149c1fed922dbd89f9a))
* no.3291384 解决项目阶段看板问题 ([c13ffcb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c13ffcb308eb3ac118044f720415ed038392a1d4))
* no.3291384 解决项目阶段看板问题 ([2860a05](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2860a0546c83c4165b804b4ba9bd3a0da377933a))
* no.3291384 解决项目阶段看板问题 ([874a55d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/874a55d5182d5be7e702b09b347f1616a13f7ec8))
* no.3291384 解决项目阶段筛选问题 ([33c0ad4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/33c0ad458686ce7b8702606273a3876a9cc6c966))
* no.3298490 解决看板为空数据兼容 ([4147571](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4147571a87d37b40241de4c0793f73a308664aaa))
* no.3298490 解决看板构建问题 ([d75a4d9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d75a4d92e3440aef0a4f26dfbc83564458a5ca14))
* no.3298490 解决门户下看板无appid场景分组设置一直显示loading以及看板初次进入无筛选条件预制问题 ([53078a8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/53078a8bfb6d15856569be217ec1a2b3e59c3bd8))
* no.3323093 解决看板拖拽数据有偶先数据对不上的问题 ([a61cc05](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a61cc058f1dc11b5b6c37ff6f1eb1f99e450dbfc))


### Features

* no.2024035972 新增EB看板项目阶段看板插件包支持 ([b7008c4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b7008c4831e80c13c32c4e25517c35bb2d4cbeab))
* no.2024035972 新增EB看板项目阶段看板插件包支持 ([f4ed10a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f4ed10ae454f38184238e0d3260621485145cf2b))
* no.2024035972 新增EB看板项目阶段看板插件包支持 ([130b6a8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/130b6a8d8ba67eed4c114f5e80b154372dc2d8fe))
* no.2024035972 新增兼容lodash改造 ([3bd4cca](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3bd4cca345f8d1c1d7ca272c4df5c0b752429703))
* no.2024035972 新增处理自定义获取信息接口 ([b222e53](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b222e5364b91657131d24d16448ae4572eebabc3))
* no.2024035972 新增阶段看板默认空数据 ([c7c0627](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c7c06275dd6270f345ad18b7e381f2588d77dbdb))
* no.3162993 新增自定义新建数据二开口子 ([77fe1ae](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/77fe1aea3815a7930c6ee687f6e2ac185fcbc71e))
* no.3291384 新增数据源权限放开 ([b358269](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b35826993b6df711feb1fe6a38ffdfa877489d83))
* no.3291384 新增数据源权限放开后对接 ([b4c1f04](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b4c1f04f43e2a4d8f50cb5d969a8e8e5d809ff8e))
* no.3291384 新增移动端快捷搜索 ([a354817](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a354817e8a6a9b41b8fe95baeda56108e3037325))
* no.3291384 新增阶段看板快捷筛选 ([d761d84](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d761d84a8335269e33c98869f6ea73d03a92f2da))
* no.3291384 新增阶段看板设计器内右侧配置默认全部放开 ([cbcba82](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cbcba827f0e325d26505d9ce4640d4c7d7d4d30b))
* no.3323093 新增看板日期分组要做改造-前端 ([2e67041](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2e670412a69041c333271216d93a60c735a057f9))
* no.3323093 新增看板日期分组要做改造-前端 ([8ffb0fb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8ffb0fb99af019f555cb09b26a6b8dd21808c114))
* no.3323093 新增看板日期分组要做改造-前端 ([9f5cc3c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9f5cc3c5c9a413f8d6454e55aa33a1c589cc12ec))


### Performance Improvements

*  no.3240930 优化仓库eslint校验规则（borad仓库） ([66d5f87](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/66d5f8712288f7e73905a8a60171eedc3c3807e0))
* no.2024035972 优化阶段看板数据请求逻辑 ([d6bbdb2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d6bbdb296a1aa41aa9c171d06440c93828ec187a))
* no.3225154 优化前端仓库开启资源多版本的功能 ([abcc73d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/abcc73d687ceddfb19bd77f074456926fee6d8d5))
* no.3237987 优化EB组件仓库使用lodash引用调整到底层工具库上的功能 ([d6a764d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d6a764d63fc987e6eb9c3faaaaeb092ee2b4676c))
* no.3237987 优化EB组件仓库使用lodash引用调整到底层工具库上的功能 ([ac839d0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ac839d0d9c37def7b3c8d6a77a05ae2f07536aec))
* no.3291384 优化阶段看板快捷搜索在设计器视图内展示效果 ([e41ac92](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e41ac92f83236c3224d44d9f562a7af7a880f4a6))
* no.3298490 优化看板分组设置差异化分组显示逻辑 ([54d639d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/54d639dcc576459068a5f5ca8626d84e8147c943))
* no.3298490 优化看板拖拽偶先报错及新增自定义获取卡片菜单下拉二开 ([875900c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/875900cc4ffc1ca868d552bf76e8c3d156454a9d))
* no.3298490 优化看板新建数据刷新逻辑以及路由参数过滤处理 ([eab3e70](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eab3e703fc37b1a78798056054a89a5b2cb3a8ad))
* no.3298490 优化看板显示样式及loading效果 ([2cbb482](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2cbb48237f30324686fdd79e143a830d255995c0))
* no.3298490 优化看板显示样式及loading效果 ([3c89a29](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3c89a29af258440e0f00a33116bdc236ad65dbae))
* no.3298490 优化看板显示样式及loading效果 ([e4d0158](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e4d0158ae63bdac7638f84a88d6ac11935aef77c))
* no.3298490 优化看板请求数据过滤field_字段及支持新建数据传参二开口子 ([fea2a93](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fea2a935b901fcdfbeb69983748732645784226f))
* no.3298490 优化项目看板问题反馈处理 ([27f3f0c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/27f3f0caa1a7296ef2281f83c86280518a89c17b))
* no.3298490 优化项目看板问题反馈处理 ([2fecae9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2fecae993434c52bb192ce52b0ccceec9208d1ad))
* no.3298490 优化项目看板问题反馈处理 ([7c34cdd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7c34cdd38a62f4adf9fced5f78e45e520751065f))
* no.3298490 优化项目看板问题反馈处理 ([6dec1fe](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6dec1fef7a644145e9153cdb34149fb383d9b36f))
* no.3298490 优化项目看板问题反馈处理 ([d559d86](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d559d860f1d2192579f006c59b8522b9be314943))
* no.3298490 优化项目看板问题反馈处理 ([8982476](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8982476805162dd64d82eedd3b05885b12ff35ee))
* no.3298490 优化项目看板问题反馈处理 ([0331239](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/03312393d8655dd3144e4aa654a7aa78062cd918))
* no.3298490 优化项目看板问题反馈处理 ([e21ac7f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e21ac7f9991ebc92b94070f703c747a4f1da34d0))
* no.3300480 优化看板组件—选中卡片时，取消拖拽光标效果 ([1a388cc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1a388ccbf715ec25c2ffe75a71cd15acbe1c71ae))



## [0.1.16](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.15...v0.1.16) (2024-10-11)


### Bug Fixes

* no.3162993 解决eb看板关联ebuilder分组切换字段后标题不更新问题 ([3d829bb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3d829bbb3634a0c91c08886ea5a7d2a60a74f752))
* no.3162993 解决eb看板拖拽无权限提示 ([c97e96e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c97e96eb25e95c8c2128a508048bf0b285686e59))
* no.3162993 解决eb看板新建分组后拖拽数据到新分组下报错 ([b5736f0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b5736f0338862e3baa469985774f7702523afff2))
* no.3162993 解决关联ebuilder字段分组表单高级视图下放开 ([944730c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/944730c967566ee5c33086785033e0a7a39ac224))
* no.3162993 解决非表单数据源屏蔽封存开关 ([15fdc4d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/15fdc4d4213f1b8e7d19cebaf6fa470eabc6133c))
* no.3281908 解决看板组件，筛选配的多选，无法选中多个 ([764f376](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/764f3762dfe9d34018b1257187da827169683fd5))
* no.3281908 解决看板组件，筛选配的多选，无法选中多个 ([bca01e6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bca01e62be76b22424d4837e15707f26ce3a6b02))



## [0.1.15](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.14...v0.1.15) (2024-09-06)


### Bug Fixes

* no.2969927 解决看板高级视图-删除字段配置内容，字段配置依然高亮 ([9bc7c70](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9bc7c70c6eafea80ee766f0db5617ab7dc29dcf1))
* no.2990475 解决看板视图分组排序报错问题 ([6a73d93](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6a73d93074ccaec40b6afb3cb8498e6c8efada2d))
* no.3044873 解决看板组件，数据过滤无法取路径值 ([eda5809](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eda58095153ae454704577b79b1632087018bdc7))
* no.3059456 解决看板组件不支持源码传参问题 ([8a3f940](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8a3f94015f5097db2414160249d9344d3e3bb666))
* no.3092519 解决看板分组字段ebuilder的问题 ([1ad9a68](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1ad9a68cb6e10a0dd4a58a6578fab2bb50e76186))
* no.3092519 解决看板分组字段ebuilder的问题 ([96ffb60](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/96ffb606e0af3aebe530e230ebe746cde552a1a5))
* no.3092519 解决看板分组字段ebuilder的问题 ([c802d96](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c802d9647c5a582e78c69ced9b6b103c75aa55cf))
* no.3157060 解决关联ebuilder相关问题 ([ef9a5f4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ef9a5f4b810c608c24db797dc2731b7f9320764a))
* no.3157060 解决放开关联ebuilder筛选配置 ([6236468](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/62364689f9bc2b2e57526a2a16a1edf0ab97de84))
* no.3162993 解决关联ebuilder下国际化问题 ([55c182d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/55c182db265398b1b9e7d29275c2adb9dbe8ab82))
* no.3162993 解决关联ebuilder字段分组新建数据时分组名称不回显问题 ([dad53ef](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dad53ef7a15d1b38e96d9c7284901b34790cfff9))
* no.3162993 解决关联ebuilder字段分组筛选回显异常 ([fd8a9b0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fd8a9b01b4404890e736716c8870777cd9dbe110))
* no.3162993 解决关联ebuilder字段后复测问题 ([be664fa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/be664fa576167ba4dc0afd4331773974ceca9497))
* no.3162993 解决关联ebuilder字段后非表单数据源不显示卡片设置 ([625ad64](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/625ad64018b5f8d4ba20dc4ec77e6425f0ab89d3))
* no.3162993 解决分组过滤无值时点击报错问题 ([5568bdc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5568bdc2df6804c83e72a168c31f8fd787743842))
* no.3162993 解决删除分组名称国际化后报错问题 ([7888626](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7888626fdd0175f5c87aad27e86f18b0818c6d19))
* no.3162993 解决删除分组未分组未刷新问题 ([67b132a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/67b132aa3d643d8a0d003c6531b45d30b86e3a3f))
* no.3162993 解决删除分组未分组未刷新问题 ([24545b8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/24545b8a3c15ca33567de514902c341d93aa3b66))
* no.3162993 解决屏蔽数据加工数据源 ([c03a308](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c03a3081de38d0dd6898270569202915a3171bd9))
* no.3162993 解决差异化分组保存报错 ([55c3c27](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/55c3c27fe5eba86616a518772bc3a3494a1d9a3b))
* no.3162993 解决看板删除分组报错 ([1226898](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/122689887e5854de7094b095c4d747dedb303efe))
* no.3162993 解决看板按钮设置无效 ([02ed68f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/02ed68f6315bf1e7c80885bc021d9b7c056894ae))
* no.3162993 解决看板按钮设置无效 ([729b6e8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/729b6e88bf1f4e10180ca7a80c96958240d01788))
* no.3162993 解决看板按钮设置无效 ([764b400](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/764b400891104f356850099797709775871f2a1b))
* no.3162993 解决看板样式优化 ([afb8e5e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/afb8e5e4aa28db8b68b5faeb469ce658009daad6))
* no.3162993 解决看板样式优化 ([d90faba](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d90faba9b336b0ab188485c3a14429affef4000b))
* no.3162993 解决看板样式优化 ([6a1e313](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6a1e313c39ba8c5bfe6e2146a3b82168cffa3da3))
* no.3162993 解决看板样式优化 ([7eb7954](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7eb7954c2d10520d2c0605bbf542de46fcad507f))
* no.3162993 解决看板样式优化 ([36ae407](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/36ae407cf3f41e92ee4e33a2c74195af5b3fa804))
* no.3162993 解决看板测试关联ebuilder新建 ([a66321a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a66321a648e38cdb7a02aab0b8c78a17a085d075))
* no.3162993 解决看板测试关联ebuilder新建 ([6f6a88a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6f6a88a0f90a0a1b33ff39b9256106490b34384b))
* no.3162993 解决看板设计器内分组筛选不传路由参数问题 ([311f24c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/311f24cfd5b0e0fb17ff826cee7380f8a0abdd83))
* no.3176574 解决懒加载相关问题 ([3914dcc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3914dccfa806f5c6cf5272238219fdd981a50244))
* no.3176574 解决日期分组描述不生效 ([70ee9f6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/70ee9f6eb07bffb71488176bbe7d8c376feb5af5))
* no.3176574 解决看板关联ebuilder字段分组相关问题 ([6ad4296](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6ad42962272bc42748aba8a548e738721d8ebd11))
* no.3176574 解决看板高度问题 ([518fbbd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/518fbbdb8d92887a420894d6cee171920ad16f2a))
* no.3176574 解决移动端懒加载未更新 ([161fcb2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/161fcb274d9e335a0edfecf680abd7f2825953e2))
* no.3176574 解决选中关联ebuilder后支持配置过滤条件去过滤分组 ([5efdbb4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5efdbb461ee8c3d10a5997fe8ad2df6aa448cdae))
* no.3176574 解决选中关联ebuilder后新建数据不回显分组名问题 ([2485d19](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2485d196699f0235e3babe6f68816cd8dc33ff32))
* no.3176574 解决选中关联ebuilder后新建数据不回显分组名问题 ([1efd492](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1efd4922f72ce7637d74dcfd0ec0dddf9f44797c))
* no.3176574 解决选中关联ebuilder后新建数据不回显分组名问题 ([1741b58](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1741b58488cd35a5e4bb96d89a7be4244c419d97))
* no.3176574 解决选中关联ebuilder后相关测试问题 ([ef653fb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ef653fbfd0ee250c6a7c9a787f79da292a16fb63))
* no.3176574 解决选中关联ebuilder后相关测试问题 ([1e0f781](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1e0f781dfbabfd290342493d0ed0d93301e49bb7))
* no.3176574 解决选中关联ebuilder后相关测试问题 ([dfac8f9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dfac8f902101e40468a36af55eac48853b446747))
* no.3176576 解决日期分组新建数据默认数据填充失效 ([aacceb5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/aacceb5e40ca671507ba711fc928f9b6b123f6b1))
* no.3176576 解决日期分组新建数据默认数据填充失效 ([631107a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/631107a8c701e85713fd74a0f9fed55adef242a1))
* **统一调整:** no.2947660 解决前端代码存在中文词条与数据库不匹配的问题 ([e021638](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e021638e3f44918deb88454ddcfea731298fc459))


### Features

* no.2024030302 新增EB看板字段分组支持关联Ebuilder ([454afef](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/454afef9e5cce08f42cc5c6a3a34a0aa4186bc7c))
* no.3059456 新增看板筛选逻辑重构 ([f3a0afb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f3a0afb23ebf4957c6a1fe2cd6716f3d54f7a6b4))
* no.3059456 新增看板筛选逻辑重构 ([c086fd8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c086fd8aa062329f83ff47d3f99e28de86946533))
* no.3072727 新增项目阶段看板需求处理 ([268ef21](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/268ef21d2700b1d43693f25301ef5e229e916ac0))
* no.3072727 新增项目阶段看板需求处理 ([b035381](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b035381ba9d3c90a9f1daa0532c5885d4202214a))
* no.3072727 新增项目阶段看板需求处理（导出lib组件用于组件申请） ([3ab7d8a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3ab7d8a0122fd48814b2296020acee948ed45926))
* no.3138630 新增EB看板(pc+移动)支持多看板分组下懒加载看板数据 ([cc3bd6c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cc3bd6c28fa26ea662d0beb0b9fd33b30667bc1e))
* no.3140432 新增【21834】独立部署ebuilder服务动态路由改造 ([3a43179](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3a4317952bb8e3cbc1c5b6cd2dff7d5daa0e9517))
* no.3157060 新增【23794】看板分组支持过滤封存数据 ([2a05850](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2a058503c693634c2c92b7fcb519be6d530d3451))
* no.3157060 新增看板支持选中关联ebuilder ([46b9097](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/46b9097b70fd1ba6c6e5d2d1d21afd9fd9e6e6ee))
* no.3162993 新增按日期分组+差异化+字段分组 ，点保存配置后，清掉别的分组配置过的数据 ([bd794f5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bd794f5decbedb081bfca3b22e03ad7f29b255f5))
* no.3162993 新增本地标志位开放各数据源测试功能 ([ff017a5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ff017a5a407089881bef7470b675789be5f1f91b))
* no.3162993 新增移动端看板样式优化 ([2df35fe](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2df35fe2c342bb58a39f23d073451445c2006ce2))
* no.3162993 新增自定义分组新建分组后默认定位到第一个看板 ([aa7bcfa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/aa7bcfaead86e516360d8eaa4be690ed558ff51f))
* no.3162993 新增表单高级视图后台暂时屏蔽按日期分组及关联ebuilder ([6ff7e41](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6ff7e41f0f659a4c96074af3f8d5b8a0ffbb04eb))
* no.3162993 新增表单高级视图后台暂时屏蔽按日期分组及关联ebuilder ([1987a24](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1987a24e4a5e2b0ced9f0c0cc5f5cf711d34f25c))
* no.3176574 新增分组帮助信息更新 ([2c35f13](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2c35f1303a0e9fda2b9a25770d40a9a7770e4a82))
* no.3176576 新增日期范围分组，支持选择日期字段后新建数据填充当前字段 ([77dfb65](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/77dfb650106d0d3549953d458d65a05c8034400e))
* no.3176576 新增日期范围分组，支持选择日期字段后新建数据填充当前字段 ([4759d8d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4759d8d8d9a12a7059fb09503ba2a050d33c85d3))


### Performance Improvements

* no.3092519 优化看板放入选项卡高度处理 ([6c06fd2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6c06fd29cb264e0442350eaf4ab254f011c5de29))
* no.3092519 优化看板放入选项卡高度处理 ([f581d5d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f581d5dd06c3326d388e502f5bc7a6042974e354))
* no.3092519 优化看板放入选项卡高度处理（还原处理） ([0ce0737](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0ce0737ff5171a3074a61366045123fffbef39f4))
* no.3122616 优化EB仓库组件打包体积的结果的功能 ([60ac058](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/60ac05895f879fcf6d3bece1af3a8a7f11acace1))



## [0.1.14](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.13...v0.1.14) (2024-08-09)


### Bug Fixes

* no.3073734 解决EB看板组件支持描述统计问题 ([425b3dd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/425b3dd5cf5f9eb254bba64c980c839bed490b54))
* no.3120701 解决看板组件—卡片设置链接字段不生效的问题 ([af03ed2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/af03ed206c7c0f66bd1415cb570835884b32b7e3))
* no.3122517 解决看板组件—行末下拉菜单，位置固定在行末尾 ([abf0773](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/abf07738f99b580bf076dae92058a639ffd7ef72))
* no.3139487 解决【0601】【0701】解决看板组件无法动态获取单条数据的字段值的问题 ([1a8e72e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1a8e72e7267542bf5c5cf42c0431b20d64a4fa44))
* no.3148773 解决看板组件，新建高级视图，字段分组下新建数据—分组选项未带入 ([066afdd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/066afdd80c870ceca350cf98286e6dc968e08ff9))
* no.3148977 解决【0601】【0701】解决看板组件，新增数据未即时回显，需刷新 ([3a271b3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3a271b33e6647615f6e76f4992d005661e370fab))
* no.3148993 解决【0601】【0701】解决看板组件，按钮配置前后端不同步 ([acf0e2c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/acf0e2cbf839e13520ffe87e1da8b678a3cc3644))
* no.3150410 解决【0601】【0701】解决看板组件，设置单行点击事件，数据ID未直接带出 ([0b15e58](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0b15e58df93ebf79f2c23cb8f4a84ba80cc516d6))
* no.3162993 解决看板组件，字段分组下的分组描述，切换到自定义分组，描述内容也带入了 ([0f984f1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0f984f12714f9ca378b237b82e3f34a62936515c))
* no.3162993 解决看板组件”开启统计“按钮业务数据源下开启 ([2689daa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2689daa71d9bb7b4d26505de94b4ce98cfd469fa))
* no.3162993 解决看板组件”开启统计“按钮保存位置问题 ([fb67cc4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fb67cc48a478719c479f683394096dbf1fdf413d))
* no.3162993 解决看板组件”开启统计“按钮关闭后下次还是开启不生效问题 ([38d56de](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/38d56de5f39a0aca2e135c821f92e3b0b62ea9c0))
* no.3163119 解决看板组件，分组描述，未开启统计时，显示数据ID.字段名称 ([f14b77e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f14b77e6530906373a5214f4e48eceaf8444fd4f))
* no.3163331 解决看板组件，首次进入分组设置，自定义分组，设置描述，保存未成功 ([6000086](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6000086619d17600bd93b16ad255530a681e4991))
* no.3165794 解决看板组件—切换到英文后，分组描述弹窗多余横向滚动条 ([8a6dc3a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8a6dc3a9a2c9879a17366f7ab37597638e0f092e))
* no.3165811 解决看板组件，切换到国际化后，填充字段与开关位置不在一条竖线 ([477c027](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/477c0279c8f83196b798e10c5e7d377f9aeeafb5))


### Features

* no.3073734 新增EB看板组件支持描述统计及统计单位需求 ([711ff74](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/711ff747749c01e3815dd49f8f64de5b3937f381))
* no.3073734 新增EB看板组件支持描述统计及统计单位需求 ([7e9c077](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7e9c0777a8e60c7e614463e2d0a79640a807c2f6))
* no.3073734 新增EB看板组件支持描述统计及统计单位需求开关 ([b39cd9a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b39cd9ad2aeb3b3e1821f9e9c156a8420a550056))
* no.3140432 新增【21834】独立部署ebuilder服务动态路由改造 ([b1f44f7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b1f44f7b28fdb6c3e21a2466af1d70d2a165240e))



## [0.1.13](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.12...v0.1.13) (2024-07-15)


### Bug Fixes

* no.3059456 解决看板组件不支持源码传参问题 ([3121032](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/31210325367a1cfd54c6887df1c9c400ea29e75b))
* no.3059456 解决看板组件不支持源码传参问题 ([59d9970](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/59d9970be2ec5500903dd4a22df366e58f36d4f4))
* no.3060754 解决看板组件-业务数据源--卡片设置--链接地址保存后再次选择报错 ([f5fb5e5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f5fb5e51b22ba89675bb01c23679829f609f3dba))



## [0.1.12](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.11...v0.1.12) (2024-06-14)


### Bug Fixes

* no.3027814 解决分组选择字段multiSelect为空字符串兼容 ([f31a9cf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f31a9cf3554a2d6dc997141f0cf3c1671e2541e2))
* no.3027814 解决看板组件—角标和竖线标识，手动输入颜色，保存为生效 ([588b24f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/588b24ff2901a6656855a18557807992cbe5f188))
* no.3027814 解决看板组件—角标和竖线标识，手动输入颜色，保存未生效 ([a0115be](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a0115be24d9700376b87aa7764000d429dde3804))
* no.3044873 解决看板组件，数据过滤无法取路径值 ([5b1a4ef](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5b1a4ef622655efa65748f5ae651341589ca7e8a))
* no.3044873 解决看板组件，数据过滤无法取路径值(计算ebparams) ([36a5395](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/36a539508c8140d9f072d901a6168f6d99bc4695))


### Features

* no.3044873 新增自定义渲染卡片二开口子新增抛出当前卡片Props、点击事件及当前分组信息 ([e7a6d0b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e7a6d0bed9f72247b2678146f896d70f7df4fe97))



## [0.1.11](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.10...v0.1.11) (2024-05-17)


### Bug Fixes

* no.2023057004 解决数据源字段选择后报错问题 ([ce7bee1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ce7bee15660d8cac2b1c26d77c9f352983e2d123))
* no.2023057004 解决直接选数据源字段不显示问题 ([ab9bc39](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ab9bc39db13354c3b70344ffdcb4c42d1555804b))
* no.2692422 解决看板分组描述相关问题 ([2d24f9e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2d24f9eb39063df5ef0f0d794a7ddc332b3fd3cc))
* no.2692422 解决看板行末描述相关问题 ([bae9af1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bae9af16f137c45d499e3476b2c57f9d7b89ad0d))
* no.2698074 解决合并test相关 ([fd28ef2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fd28ef276da15df67436a62b3daf7f2782b714ac))
* no.2725361 解决分组描述填充字段 ([2d892fc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2d892fcc83ea2fa618ad5944d04a9cb035229d9b))
* no.2725361 解决按字段分组描述信息不显示问题 ([d7c1d2e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d7c1d2e9bf675ccab55684848ac90e1ff7f75cc8))
* no.2725361 解决预览时分组描述信息不显示问题 ([ce94bb2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ce94bb24623c7e773264f9633255e8b422787133))
* no.2725368 解决合并test ([8d15f8f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8d15f8fa5c3913c725f84e15abe2ba4d85ae3d8f))
* no.2725368 解决筛选设置相关 ([50e60b2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/50e60b2a0545a917e4de7c01cdccdfb31d9696de))
* no.2734013 解决标识反馈相关问题 ([690c0ec](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/690c0ecd33a59e8b6b6c5d461d35d5d45aa65e1f))
* no.2734013 解决标识设置数据源切换后不重置数据问题 ([44efe73](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/44efe73112cad1c927666b8033b23b18d1e61f20))
* no.2734013 解决标识设置移动端不显示问题 ([9f01cfb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9f01cfb2de31b3819816af50d404628f6b100431))
* no.2734013 解决看板业务数据源内eb表单数据源新增groupId判断 ([cf7565f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cf7565f11f7afc788f2fe5344adfedfa44e95575))
* no.2734013 解决看板分组描述相关问题 ([82b10bb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/82b10bbb7e7707efa3aeaf0a24cf103406512f20))
* no.2734013 解决看板分组描述相关问题 ([b569b4e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b569b4e84e0b4149567fa373799bf6525d94efa7))
* no.2734013 解决看板分组描述相关问题 ([ae8b992](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ae8b992ef27d7067b781163821d746eed0b52c55))
* no.2734013 解决看板分组描述相关问题 ([817e529](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/817e5299ed3948f2a52dc5a40c5230e30fee6b5c))
* no.2734013 解决看板分组描述相关问题 ([1ee0277](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1ee0277e85dddfee26ef7211f12d6715c4021377))
* no.2734013 解决看板分组描述相关问题 ([1313be9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1313be9ef0468cd9dc130fdda366e9efb66edc99))
* no.2734013 解决看板移动端显示问题 ([6dfecf9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6dfecf97de78b183088d2ccd14fa57d0447541d8))
* no.2734013 解决看板竖线标识设置未选择位置显示问题 ([5ec5b69](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5ec5b69b65fb7e98259b7cf7c9876e7d638a26aa))
* no.2734013 解决看板组件支持角标国际化问题 ([0fc1b99](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0fc1b9989fdc2e71f9b07213d8366747d689c6f3))
* no.2734013 解决看板高级视图下分组保存问题 ([dd1be58](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dd1be586c43119f5e373e0fff4228bf7e409897c))
* no.2734013 解决看板高级视图下分组保存问题 ([6dcabb1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6dcabb15e07df09384e8355213649ad18536ab4c))
* no.2800304 解决差异化分组兼容optionValue取值 ([7549a92](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7549a92ccedb1f2078e9b46da93ed0fe2cc661e5))
* no.2800304 解决差异化分组相关问题 ([21eb71d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/21eb71d7c8c7282dc11c34ef799333929f3a8c10))
* no.2800304 解决差异化分组相关问题 ([f6a6a0a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f6a6a0a95042266600518f4c5e7fa669a2f534ee))
* no.2800304 解决差异化分组相关问题 ([0d6ce1f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0d6ce1f958cbf85c44de6c69154230b30e32f83a))
* no.2800304 解决差异化分组自定义分组相关问题 ([b1736b9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b1736b987c0102df4472ddd66cb9a39be43ecf4d))
* no.2800304 解决差异化分组配置更新问题 ([bc6c692](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bc6c692deff92aa3da17128e98784ef0e39d976d))
* no.2800304 解决差异化分组问题 ([09c15e5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/09c15e50c721c9a53c33b0558a9b0d338c888a79))
* no.2800304 解决差异化分组问题 ([48c9c88](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/48c9c888490ee6cc616b880f152d2cb5dd80cbb4))
* no.2800304 解决差异化分组默认分组不渲染（切换数据源未重置分组信息） ([b25c26b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b25c26bff7c854a75060062b642ba4bdb7755cfb))
* no.2800304 解决看板默认分组不显示数据 ([b17a4c1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b17a4c1c722d43c617adf735790ff5477af6a0fc))
* no.2801185 解决ebdmindmap,ebdboard步骤条多语言静态变量修修改(看板0326) ([88cb240](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/88cb24030d61bfea3d487fc27cedab01a3f4de3b))
* no.2801185 解决ebdmindmap,ebdboard步骤条多语言静态变量修修改（看板realease） ([8120091](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8120091af7f6dbc953db382736c6c292b64feb38))
* no.2833886 解决差异化分组自定义分组设置保存后未生效 ([7744f83](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7744f835e7f75dec9fcf9e64f88ca1dc44bdc3d4))
* no.2933665 解决看板合并相关 ([86a582d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/86a582de206994b296592dee801805fda24619c3))
* no.2933665 解决看板差异化分组-预览页改变页面分组后，设计器内，拖动分组顺序，所有数据恢复到未分组下 ([0507d52](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0507d52723396bedd3c937ca84d05776fe6d1cd7))
* no.2933665 解决看板移动端配置颜色后分组空白问题处理 ([a6b0d00](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a6b0d00cbb9fa52d9e6641ab7df922b4f60116a1))
* no.2933702 解决多看板下，数据新建/编辑/删除会触发别的看板更新的问题 ([c921818](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c921818789f6c189f78d2a5e2426060c0c08a471))
* no.2933702 解决看板差异化分组-切换到未设置过的默认分组，未分组未返回数据 ([0e7e700](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0e7e700b725c28ab050328ab7cbf9f9ccd113493))
* no.2933702 解决看板高级视图设计器内标题为空 ([84339ff](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/84339ff406bc9797ca2ef1a8e98102304c9fe8dc))
* no.2933702 解决看板高级视图设计器内标题为空 ([8830682](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8830682b46e2f55a7fdd04704eda91eca4ed9cfe))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([bc1c560](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bc1c560bd2d334d30fb5e7dc20393059201313e8))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([5032a37](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5032a370875f1906fac3bea479287399606b9825))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([c1ea5ff](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c1ea5ff01811e7f3d90f6f1e583f69123ed04123))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([820cf97](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/820cf97859cf995e61b93e8fc9b732cdcf455b72))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([1c0d6a0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1c0d6a03ce978e0bdd1e5733d5fc2b5d8d43249d))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([3042c6e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3042c6e825a93e25e1296348ed90c508a4401137))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([4ac6eb7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ac6eb7c19dab7a490153d61e356a75f18865c58))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([10485ac](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/10485ac6d0796b0dcc8190fed44f0cfde9531313))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字设置问题合集 ([7642c9b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7642c9b3c22432463cf97327263f7d06ce6188e9))
* no.2949161 解决EB组件(通用)：看板组件支持分组描述文字高级视图屏蔽 ([21e946c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/21e946c1627716f6020f10a37f252cb131cf996e))
* no.2949161 解决暂时屏蔽业务模块,0601基线发 ([3e702c4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3e702c443dddad6a851f92a7340c10b1b6c1a38b))
* no.2951241 解决看板高级视图-切换到分组设置后，页面分组列表未显示 ([9644c39](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9644c39634f22b71a3ae70fdd2704451588a05e9))
* no.2951306 解决看板业务数据源部分场景屏蔽 ([938bb2b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/938bb2be8bc888fa33fc05d6d21f014c5cea3ff1))
* no.2951306 解决看板高级视图-已设置分组设置后，未保持高亮 ([4f3a986](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4f3a9863ac6ad0297b60d923344588119c9d3c88))
* no.2951306 解决看板高级视图-已设置分组设置后，未保持高亮 ([8cd3b24](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8cd3b24a8a62435f468f5d56fddd95e8f696a36a))
* no.2951416 解决看板高级视图-预览后，数据无法直接拖动到其他分组 ([bc93ba9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bc93ba9cc28a4d6dee47d87cf573fbe6bb2eab93))
* no.2954125 解决看板支持业务模块数据源——添加分组前端报错 ([06236c4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/06236c47db14f63fb92b3dc54456133d31a9a0a8))
* no.2954356 解决看板支持业务模块数据源--预览提示表单不存在 ([739964a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/739964acda08e8ae37b4c2784ef489888fafd241))
* no.2955121 解决看板API拓展-新建数据，onCardDataUpdate重复触发 ([a5ea671](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a5ea67162c56d4c2b21a8973cfc0a62939ab33aa))
* no.2955121 解决看板API拓展-新建数据，onCardDataUpdate重复触发 ([0a3839c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0a3839cce77a07b4ecbae71749a391a4c47c46b8))
* no.2960801 解决看板支持角标设置——切换数据源后，角标条件设置内，目标字段还是前数据源的内容 ([7e5a271](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7e5a27110804fea77097e8ef4c3ee65ed33f7928))
* no.2965048 解决看板高级视图-分组设置-已设置差异化分组，再打开分组设置，页面回显在统一分组 ([f396a39](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f396a398a5fcc625ae556128a5997845af1bbb27))
* no.2965048 解决看板高级视图-分组设置-已设置差异化分组，再打开分组设置，页面回显在统一分组 ([73bb54e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/73bb54ee1015c842cf06c944c0fa1fa80f6c4ab8))
* no.2965048 解决看板高级视图-分组设置-已设置差异化分组，再打开分组设置，页面回显在统一分组 ([5b076a8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5b076a8aecb0aefd5da317cecdb5d3345eb802df))
* no.2965672 解决看板差异化分组切换默认分组时修复分组下数据不显示 ([7a6dc15](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7a6dc15c20502333a66f5b6f8536cc74ba74e035))
* no.2965672 解决看板支持角标设置--批量删除角标设置，未保存，重新打开标识设置，未回显出 ([f314a1d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f314a1d16dd9d8052b284b32b88d7350763c149c))
* no.2965672 解决看板支持角标设置--批量删除角标设置，未保存，重新打开标识设置，未回显出 ([440e148](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/440e148d0eb5ab716a6034898c0d2b44a12bc1bb))
* no.2965672 解决看板高级视图下屏蔽差异化分组 ([2565853](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/25658531e6280b9184af89c475909ea17679966e))
* no.2967047 解决新拖动的看板组件无法--分组设置无法删除，新建分组 ([a46bc11](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a46bc11d681e1a3876508e43eaf225f40df3fe24))
* no.2967056 解决看板高级视图，搜索--添加分组--多选条件预览后是单选 ([0fd221c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0fd221cbbae8057b9101669c456d405ece0eec40))
* no.2967056 解决看板高级视图，搜索--添加分组--多选条件预览后是单选 ([05e8e78](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/05e8e784fade3102db95b9154b1c116711f93927))
* no.2967208 解决看板组件--同时添加多个分组统计，只设置一个统计信息，未生效 ([536a9c0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/536a9c075114f13e109ef579d5f2beb40a1003ef))
* no.2967208 解决看板组件--同时添加多个分组统计，只设置一个统计信息，未生效 ([aa4ad05](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/aa4ad0581cc2781d025c980453af1eaa92391c0c))
* no.2967208 解决看板组件--高级视图分组统计高亮显示处理 ([87a1104](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/87a1104f4018f3c0635b3a4f28223b8b5e1ffc03))
* no.2967208 解决看板组件--高级试图为空显示undefinend ([2360504](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/236050441cb47b2a262b1759b52ea31a27bd7b4f))
* no.2967208 解决看板组件--高级试图取消高亮 ([0634952](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/06349529c65139b57ca5fbbf097b97ce3880c9fe))
* no.2969239 解决看板组件高级视图——自定义分组后，分组设置未保持高亮 ([ea6349c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ea6349cc750b2bab92a2b08c8ce2eb66c1f4c9fd))
* no.2969239 解决看板组件高级视图——自定义分组后，分组设置未保持高亮 ([d2c75c9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d2c75c9f1d5ccc6aff6c7170f40b3f502680b0ff))
* no.2969927 解决看板高级视图-删除字段配置内容，字段配置依然高亮 ([0816dda](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0816ddae93bfc7854ac5303e240fdfa396e3c366))
* no.2990475 解决看板视图分组排序报错问题 ([52dd3d8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/52dd3d8522ff3a3a16c4bdd31eac7d897dfca88c))
* **统一调整:** no.2947660 解决前端代码存在中文词条与数据库不匹配的问题 ([89efa21](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/89efa21e3923102e793c0bfdb960921ca61cbf1e))


### Features

* no.2023057004 新增行末配置 ([4588e7a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4588e7a0d5f1117171524564ca0ddad9a0341531))
* no.2023057004 新增行末配置限制数据源字段只能选数字类型且显示为卡片内金额相加之和 ([4403cdb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4403cdb7c0d99975d40f5857dac0a836a6ddf271))
* no.2692422 新增看板移动端描述文字 ([27dc568](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/27dc5682bbc62b1e2767512b3c28ba42005d6734))
* no.2698074 新增看板支持行末描述配置,接口待提供 ([1f42c92](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1f42c92d64c00eebf9118a7af071e4df4aee05d4))
* no.2698074 新增行末配置相关 ([de96114](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/de961143c33c87f008b82f3d85a3731fc5723b4e))
* no.2698074 新增角标和左侧竖线样式(待对接配置) ([aa9b305](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/aa9b3057c53d5035a465faf22bfea3ed9aff76e9))
* no.2698074 新增角标和左侧竖线样式(待对接配置) ([eeed762](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eeed76264f9cd608d94a5593962d8d9297d67fdb))
* no.2734013 新增角标和竖线标识 ([2a56514](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2a56514841281cccc46bbf6c8636c068187e9b77))
* no.2734013 新增角标竖线标识设置 ([5061202](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5061202dc2738c57c26bdbbbf80147a9868f4c10))
* no.2734013 新增角标竖线标识设置后端格式对接 ([d805066](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d80506630828a211a5f5f0153330bc73b6b0e97f))
* no.2734013 新增角标竖线标识设置国际化处理 ([976d6fd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/976d6fd7f10e28f9d773c04441ff38effe1181c4))
* no.2800304 新增看板数据源为空时分组设置提示 ([ba10d7a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ba10d7aee949fb7a692c14b45ab119743903993b))


### Performance Improvements

* no.2734013 优化看板业务数据源按钮设置显示 ([2e2e953](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2e2e953755d85f8b06a68518833caf037d15a765))
* no.2734013 优化看板业务数据源按钮设置显示 ([9e88c67](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9e88c67c068baf127cea7f96580819b5dc3bb7c3))
* no.2800304 优化表单高级视图下看板对数据源的判断 ([73f1ad6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/73f1ad6c2a795f796c7760bb4f7f7ec3123a408e))
* no.2833886 优化看板回归问题处理 ([5e5b886](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5e5b8868672fa8f530707dfc2b33b8a51fa956c8))
* no.2833886 优化看板回归问题处理(切换数据源修改title) ([0b4afe4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0b4afe41c75ab49ecca63c6b5a8e20d3172c95e8))



## [0.1.10](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.9...v0.1.10) (2024-03-22)


### Bug Fixes

*  no.2796240 解决修复分类搜索-添加分类-默认选中条件未生效 ([3dc2c0f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3dc2c0f79eb18b64e9bc53fd15744a61da62b5cf))
* no.2796240 解决修复分类搜索-添加分类-默认选中条件未生效 ([48db78f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/48db78f84a7ef972c3f8819c59b7f749c725bcb8))
* no.2796240 解决数据源下屏蔽设置项 ([60247bd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/60247bd218558e3eb099d5076de373108fd5f32e))
* no.2796240 解决无数据分组—无法拖动数据进入该分组 ([a48c355](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a48c3556ad5c7ced108ec33ff5521b42fc260f5a))
* no.2796240 解决移动端-超长分组名，移动分组时，显示名称重叠 ([87efb0c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/87efb0c780651a637049e710f7cd4b8ba41af746))
* no.2796240 解决移动端多级下拉字段显示异常 ([755c718](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/755c71806be6e583fbea04c7a416bf40fcf9373c))
* no.2796240 解决移动端展示的高级搜索字段是同页面其他看板字段数据 ([9579779](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/957977997d00fd771823657df270ac9035cddea3))
* no.2796240 解决顶部和左侧都有分类条件时默认选中不生效问题 ([a81bf13](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a81bf1334f61bfa5ee7d77e144a36db3211813b0))
* no.2796240 解决顶部和左侧都有分类条件时默认选中不生效问题 ([e19e392](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e19e3925222501bd19100b989e0a68be1f7705d2))
* no.2800304 解决显示字段添加‘序号’报错问题处理 ([c481e4a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c481e4aa07d9d6e1e6a6da057e7c866814cdee22))
* no.2800304 解决未选择数据源看板预览时新增mock数据替代 ([a077b4c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a077b4c51462a54f0a0cf98ffcc3088d45f05304))
* no.2800304 解决表单高级视图下看板字段分组数据源判断问题 ([bb57290](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/bb572902878d83765029472190942dbc2a96f170))
* no.2800304 解决表单高级视图下看板字段分组数据源判断问题 ([782c7da](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/782c7da43c3b0e3c4ac2c9d5a727ec3059539f46))
* no.2833886 解决分类筛选屏蔽相关 ([d62a602](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d62a6029e28e62d0055d9dee0efe009b5230a6f6))
* no.2833886 解决差异化分组切换类型时不能拉出新数据问题 ([8dae116](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8dae116aafe5f436cfc0208032d512ccc86bc3e1))
* no.2833886 解决新拉出看板刷新异常 ([08828fd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/08828fd7369a91f9e43d0fc10404a08a3b35fa2e))


### Features

* no.2698074 新增二开口子文档 ([fc89677](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fc89677f3f9ef79c2344ec5b2fa72ef9931ab43e))
* no.2698074 新增项目看板插件包 ([2434c76](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2434c76791132a443e4dafb8e4dc848322936582))


### Performance Improvements

* no.2698074 优化插件包文档 ([69f4d19](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/69f4d19184e8260c04ace1e7ecac5807463dcb16))
* no.2698074 优化组件文档 ([2cb35db](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2cb35db5fbd396722953833f7393bc07950b6091))
* no.2824945 优化常用搜索里面添加分类，需展示在这个高级搜索内部 ([342c13b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/342c13b07c23f0289e80dc5a35f6c53d880508f5))
* no.2833886 优化看板回归问题处理 ([d6402e1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d6402e1bc0295c2974866cdbe6996f995f0a9b49))
* no.2833886 优化看板回归问题处理 ([fb7fe2b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fb7fe2bb0424079cdb738e3dc05821a3316a6daa))
* no.2833886 优化看板回归问题处理 ([1f2e983](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1f2e983e269383eee9ea4b199cd9193775f25bdc))
* no.2833886 优化看板回归问题处理(design页面基础加上config配置) ([e028220](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e028220ce7301df2ace11c199e81a6e08f7e81d7))
* no.2833886 优化看板回归问题处理(ie不显示) ([e0f09e1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e0f09e1a969d32ed7d70beadbf262162160b041c))
* no.2833886 优化看板回归问题处理（lib.css问题处理） ([2ecbc94](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2ecbc94e8bf167375addd58cd68ea7f7f81f64b7))
* no.2833886 优化看板回归问题处理(realease 分组统计) ([2213c84](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2213c84a524216c7ef430c3a30280112973e2bf2))
* no.2833886 优化看板回归问题处理(功能缺陷61) ([29df7f2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/29df7f2723f2940d6c62f39c5eff366ec5638289))
* no.2833886 优化看板回归问题处理（功能缺陷问题60） ([88de6f6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/88de6f608e85d32bcf70ff734dc945f2ee702bf2))
* no.2833886 优化看板回归问题处理(处理空数据时候默认最低高度) ([6371aa9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6371aa984803f9e2ee4aea9ac61b318a4b7e3690))
* no.2833886 优化看板回归问题处理(差异化分组-关联字段-回显了其他看板组件的关联eb字段) ([354d741](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/354d741fe54ed2402cdbb131ee29e93c503d2454))
* no.2833886 优化看板回归问题处理(数据源为空屏蔽筛选条件) ([cca2acc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cca2acc5f905420d935d49d3cd00520f643486a2))
* no.2833886 优化看板回归问题处理(滚动刷新滚动条移到顶部) ([9362d56](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9362d56ff6a772468219887b8a21044e72694ff5))
* no.2833886 优化看板回归问题处理（看板ie 容器内部高度无法撑开） ([0379f43](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0379f4339115d5b3243145956c8e6df5c7102f7a))
* no.2833886 优化看板回归问题处理(移动端暂无数据高度处理) ([f68305d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f68305d0d385356a83def0741ef231765b57103e))
* no.2833886 优化看板回归问题处理（配置页面视图错位问题处理） ([0b3728a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0b3728aede9e040f4314bdb351e5c2087e62938d))



## [0.1.9](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.8...v0.1.9) (2024-02-02)


### Bug Fixes

* no.0 解决任务数据源对接相关 ([ed22d5e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ed22d5ec0b7c2a8a2f3462f07b1a23594bcc7456))
* no.0 解决任务数据源相关 ([6531d81](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6531d81547f005c820ae57fae3a7561f1816ee8d))
* no.0 解决分组设置差异化分组未自动带出关联字段的问题 ([17dbe31](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/17dbe3196cdc624efc169326963c49f63d4c9a5d))
* no.0 解决加载分页的问题 ([4ff4f0d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ff4f0d4a30be51e8effe928f76e72439e86eeb9))
* no.0 解决加载分页的问题 ([14edaba](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/14edaba3e1352caf6e56b24eecc8a43ee3949897))
* no.0 解决历史遗留问题处理 ([0f326d4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0f326d462d7740fdeb16b92a2bfa9e90a02852c0))
* no.0 解决引入文档方式 ([7cedd13](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7cedd13360c784d7a992db842ef017d557bc3f67))
* no.0 解决看板事件相关 ([e2d11e2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e2d11e29674857d4650e78d3eb4e1b2cb0407680))
* no.0 解决看板事件相关 ([e575354](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e57535469c8e38e7da4ef5c51eae1bb7fbeb3e3e))
* no.0 解决看板任务数据源相关 ([4ad94ec](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4ad94ec8aebb3d416987f221c034dfee917f454c))
* no.0 解决看板任务数据源相关 ([310d136](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/310d136d3f28be23a4fca371b23a4551430b970e))
* no.2598096 解决看板文档渲染方式 ([178301e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/178301e6be9afa6eb5e01260fb6342c2620be5d5))
* no.2692422 解决看板刷新功能合并测试环境 ([ae1956c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ae1956c39b1849d5813aab79482326418d84590a))
* no.2692422 解决看板新建数据移动端打开问题 ([c30596a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c30596aa934c83de235878f221d099002fee55bf))
* no.2692422 解决看板新建数据移动端打开问题 ([e0fe350](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e0fe35093d82daed17073b27fe40b4bae0914b36))
* no.2692422 解决看板移动端标识重复显示 ([0dc3a61](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0dc3a61cbd8de443f5d413878d28bb94885a5aa6))
* no.2692422 解决看板设计器视图高度未自适应的问题 ([af6db35](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/af6db358435701751cca864333d8ea7cc03583c1))
* no.2692422 解决获取新建任务权限数据源类型判断 ([ae653c4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ae653c443ef0183497df9b434e5594a3c0165af4))
* no.2698074 解决config相关 ([0e9d9e8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0e9d9e8634a9271f028981297542207ef9abe466))
* no.2698074 解决H5-A分组下新建数据，数据展示在未分组下 ([0fff885](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0fff885462e7786daa09b616a8daaaba4515460e))
* no.2698074 解决分页自定义页码保存失效的问题 ([9ffe622](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9ffe6222de04f5cb02f0bdcef2fb53350cbecbe5))
* no.2698074 解决按关联数据差异化分组选中ebuilder报错 ([eb3417f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eb3417fb48dc062526e3ec68ce4d0b49017d4099))
* no.2698074 解决新建/编辑卡片后从新路由打开无法拿到id问题(h5卡片详情新增弹窗打开方式) ([18be028](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/18be0282b18277c07f2a717b7e533812cb346b75))
* no.2698074 解决注释分页相关 ([981fee1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/981fee1e74303baf73f0c23bb26246b96048a5d1))
* no.2698074 解决统一设置项的折叠效果 ([c77760d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c77760d902aa2a1766751005d3a2fbd2d3a555aa))
* no.2725368 解决筛选组件传参 ([e2463f2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e2463f277603cf1c239c8ea574a71ed7f8fcdc84))
* no.2725369 解决字段分组配置相关 ([658b548](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/658b548b16fc7ffe733917ae3d3cc5377611ca82))
* no.2790192 解决项目按字段分组屏蔽“数据状态“问题 ([90b56fc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/90b56fcbff4645ae433b7fec4679b2a64c7164c9))
* no.2790192 解决项目按字段分组屏蔽“数据状态“问题 ([84161bf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/84161bfe89d2221cf77245ece227b1f41105e491))
* no.2796222 解决切换数据源后，应清空上个数据源的分组统计设置（现在不清空数据且统计不正确） ([07d2b20](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/07d2b2013697639467414e0fb07f251a4c0f9548))
* no.2796240 解决ie11兼容性相关 ([c7e0255](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c7e025519237487afd84f60fdb9da0e19f80158d))
* no.2796240 解决ie11兼容性相关 ([61aa1d7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/61aa1d70da431a468a77d1aa5631d51ed9a6e605))
* no.2796240 解决ie11兼容性相关 ([dc935d4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dc935d4fe397a7ecbd4b98db63de8b62c05538d8))
* no.2796240 解决ie11兼容性相关 ([f8cc09c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f8cc09ce6702f004f6de2b88f1b911d3de563383))
* no.2796240 解决ie11兼容性相关 ([5777ece](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5777ecec4649e5a1b506510250f6c4f2f0dac489))
* no.2796240 解决差异化分组-默认分组-分组背景色未生效 ([29dea75](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/29dea756d2de88008dac8105813cbf6a58521a59))
* no.2796240 解决移动分组-分组名字超长时，提示信息显示需优化 ([cd4e043](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/cd4e0437bcabc54ac38d6396addf81cb361b3336))
* no.2796240 解决移动分组下数据，图标建议优化为绿色 ([7fb423e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7fb423e95e30b110a81db698e9f5b9b5d43a0c92))
* no.2796240 解决编辑按钮-仅显示图标未生效 ([068b5af](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/068b5af53117b46488ffd6509a50bf013c6771ed))
* no.2796271 解决加载完成页面触发了单击行事件 ([ea8cf16](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ea8cf16284181dfdb6f4ca38441721f643473f26))
* no.2796280 解决选择有关联事项字段的数据源，按关联事项差异化分组，应回显数据源中的所有关联事项 ([e47d030](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e47d03078f301c3606a831ea4d5f74724900f09a))
* no.2796354 解决统一设置项的折叠效果 ([422ea7c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/422ea7cc7f6cc12f50d26ab73238ea384d73b0b1))
* no.2796361 解决切换数据源后，上一个数据源的自定义分组设置未清空 ([dc0c1ea](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/dc0c1ea7c5d045b57ce227e4bedc428fafe61bea))
* no.2796369 解决看板选择有下拉按钮的数据源，再拖动一个新的看板组件，字段分组中回显另一个看板组件的下拉按钮 ([39af80f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/39af80f10d0bec8cb8b08a312fab1d956cc8c010))
* no.2796538 解决搜索-常用搜索/快捷搜索-预览页面未展示 ([587d05e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/587d05ef7fd80424d0d673cc7d139ba82a78349e))
* no.2797408 解决卡片设置-左右均拓展宽度区域，中间字段被折叠不展示 ([d4d1094](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d4d10949d5cd55a964772822998b42c472232178))
* no.2797799 解决按钮配置-显示位置：右上角更多菜单-数据过滤未生效，所有分组均展示该按钮 ([13c6b7e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/13c6b7e82e45567a70f729633616e23e8cbece6a))
* no.2797799 解决搜索-分类搜索-添加分类报错 ([194f000](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/194f000161b079b8f81fa5e1a8b842b9cc951f1b))
* no.2797799 解决未添加数据源，打开卡片设置-系统报错 ([167e734](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/167e7343d8271c13f7efc799a9ab9d2dbd75977a))
* no.2797799 解决移动端-编辑分组无响应 ([691daa7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/691daa782e43fe3a373d2a9d0e8c3cc7e6c41e85))
* no.2797802 解决修改数据源设置-数据权限设置-冲掉了卡片设置 ([e0a8bf2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e0a8bf293649a3f75b8d409f053ddd12b096da2f))
* no.2798965 解决看板移动端-分组无数据，新增数据按钮的位置优化 ([5537899](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/55378997666dddf0a8bbe68732617705c2b61ade))
* no.2799232 解决显示字段配置明细表字段，显示不正确 ([e38e755](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e38e755bd0abdf3e7234ef46da4d7243973de41e))
* no.2799232 解决显示字段配置明细表字段，显示不正确 ([c81699b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c81699b9c4d99fda6dc1768025f45019c30e3fb2))
* no.2799232 解决显示字段配置明细表字段，显示不正确 ([7708dc9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7708dc9f00cc3d15f3b0e600f478553a6751a048))
* no.2800178 解决移动端-无数据源看板-预览展示-应显示暂无数据效果 ([8e8b10b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8e8b10b3deed261f0743d58ebc9e214062fcc459))
* no.2800178 解决移动端-无数据源看板-预览展示-应显示暂无数据效果 ([2f8621d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2f8621db72343e175510504ddb7bb38556709857))
* no.2800178 解决移动端-无数据源看板-预览展示-应显示暂无数据效果 ([0cb192c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0cb192c630fee7796572091cfda9460e46b75b0f))
* no.2801185 解决ebdmindmap,ebdboard步骤条多语言静态变量修修改 ([74235d4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/74235d4ad2f31cef409a635f4d9d74ad3d4de7cf))


### Features

* no.0 新增数据源放开限制 ([c5fe5d0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c5fe5d04dff654a4b773f9b186ebfabf2859c348))
* no.19218 新增看板分页配置支持 ([5d463dc](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5d463dcd78f4f6afca44108718703a47b90db1c6))
* no.2598096 新增eb看板组件文档书写 ([c759570](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c759570001ab0667cbfddde7cc1d3160b4da60e7))
* no.2598096 新增eb看板组件文档书写 ([8534610](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/85346109a483703f2f04cce4f4f7530be15d3e13))
* no.2598096 新增eb看板组件文档书写 ([33d789a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/33d789a9ea65201012bc1acb27cb8c69f827ed19))
* no.2598096 新增eb看板组件文档书写 ([eca28d3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eca28d39ea009874fee6a991f3138ec5d5921eef))
* no.2662487 新增Eb看板视图组件支持自定义底部按钮名称与点击事件 ([a33bc8e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a33bc8e245e005122b2925b950d092e0efd50e8c))
* no.2692422 新增看板卡片数据更新回调以及卡片拖拽回调sdk事件支持 ([a14462e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a14462e9a7cab71cc45aa24910a1b161f033d7d4))
* no.2692422 新增看板视图组件刷新动作支持组件自定义配置对接 ([c93fe71](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c93fe71ed7aaefb90594faa9d870e6d9b5f3d3c6))
* no.2698074 新增二开sdk以插件包形式能力补足 ([216fcd0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/216fcd0c0a78466dccae0e69c6e0e3e0b0ad784b))
* no.2698074 新增二开sdk命名规范 ([2189084](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/21890846c2016d6dd588964e2b1ed0fc4264955f))
* no.2698074 新增二开口子文档 ([f05c1aa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f05c1aa436e11e10648177f209bb46a5ee98ee06))
* no.2698074 新增分页配置国际化 ([569e49f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/569e49f140b425fcf936a2a5e170e66c30f9371c))
* no.2698074 新增功能配置 ([341a2c6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/341a2c66dd7517a7e2d1d67969f695dfa1110121))
* no.2698074 新增引入插件包 ([92041f1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/92041f1f94e0413bd2ebe68d258d0768180f5601))
* no.2698074 新增插件包机制 ([d73b92d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d73b92db1a1614ed79dc0a7687df056da1e4719c))
* no.2698074 新增项目看板插件包 ([9031bec](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9031bec7fbf6a1d7a57747005d84ffb4aafcbbc3))
* no.2725368 新增看板二开事件 ([074152c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/074152c5a032d92a31a81d66de74db43d7ba5c27))
* no.2725375 新增拖拽开关配置 ([909aac5](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/909aac59bece6b02e56aa4179e38f42f1420bbb1))


### Performance Improvements

* no.2588752 优化步骤条新增样式配置(优化) ([1f08135](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1f08135d3d222d93e38924f300cc9de9a442b8dc))
* no.2614937 优化分组统计配置 ([0255c7e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0255c7e761f6009dcf92b2971cb661040728584b))
* no.2614937 优化分组统计配置 ([3301302](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3301302fb34a489e58642b6e6196e2142e76597c))
* no.2614937 优化分组统计配置 ([857b095](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/857b09533ae8712b845030cf41fef1cd64000519))
* no.2614937 优化分组统计配置--(多语言标签处理) ([54ba4dd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/54ba4dd7fc750a6a8b9c20459aba202ce16aad4e))
* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([b26f164](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b26f164dd3e29436550b3ff27f1185d39e0e2e2b))
* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([5658666](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/565866665cb9dc50ee18f80ff770d46543ec7e00))
* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([9542c0a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9542c0a161c05085affa105f5fd22e8095b3736e))
* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([75da21d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/75da21dfca7267d7604913c9a079c1459941fa56))
* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([04c68d9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/04c68d93861437d10f62beb5c571938de147495f))
* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([e845d0f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e845d0f80c9a6ae9ba6ed9f2378c1e54e112a4f5))
* no.2614937 优化分组统计配置(优化 多语言) ([688dbd3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/688dbd3ebc44baf819994005bc44873d9fef2669))
* no.2614937 优化分组统计配置(优化) ([2680101](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/26801016ed5bc73808b6e9c146d2ffb12452419b))
* no.2614937 优化分组统计配置(优化多语言test) ([fefa385](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fefa3851868681b3bcf48885fc2738fbbeea2132))
* no.2700080  优化看板 思维导图 显示字段、条件设计器、动作屏蔽标签字段（自建） ([e719610](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e71961059afab84bf753026a7923c8defe110c76))
* no.2700080  优化看板 思维导图 显示字段、条件设计器屏蔽标签字段（自建） ([6dfd88a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6dfd88a56d7b0a22c88df7bf06ee4f3ff5d2cb63))
* no.2806572  优化未分组-屏蔽删除分组和编辑分组 ([4429e1d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4429e1d51025da41b2d0ce817135c3b4f71dfbdd))
* no.2807705 优化分组名称过长，更多操作按钮超出显示框 ([9ac48d6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9ac48d6b3f84c03920ee420750a9868f4a9ca1f7))



## [0.1.8](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.7...v0.1.8) (2024-01-12)



## [0.1.7](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.6...v0.1.7) (2024-01-05)


### Bug Fixes

* no.2692422 解决看板视图刷新每一条事件不生效 ([a821ade](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a821ade40b0f817de4fdcf80167fe3fa4b8ac7f4))
* no.2692422 解决看板视图刷新每一条事件不生效 ([b81a5ef](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b81a5ef6dcd4ec3f87f827850b54d553cd757db4))
* no.2692422 解决看板移动端标识重复显示 ([7e19a83](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7e19a8395cf6126d9a3eb717450dc8bb089f23c4))


### Features

* no.2692422 新增看板视图组件刷新动作支持组件自定义配置对接 ([f7a5d1e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f7a5d1ec888a2f20d0c899e93943a5d44d18e98d))


### Performance Improvements

* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([e8d2fec](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e8d2fecd4c77cf4ed1272dde83568e3ff80905c4))
* no.2614937 优化分组统计配置(优化 多语言) ([4c7b94b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4c7b94b2b160447147ebd7b4a19fea16adb2bc6b))
* no.2614937 优化分组统计配置(优化) ([61dc0fd](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/61dc0fd3157b6275f2e414f895fc0564b99bae3d))
* no.2614937 优化分组统计配置(优化多语言) ([92f5fcf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/92f5fcf13225bf27775a1a71a15851ee0a494f2d))
* no.2700080  优化看板 思维导图 显示字段、条件设计器、动作屏蔽标签字段（自建） ([6f994a1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6f994a176a82e6ab1b65afbfc60806ad524b9613))
* no.2700080  优化看板 思维导图 显示字段、条件设计器屏蔽标签字段（自建） ([125000a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/125000af1e3865646de624fb324a74e7bc3a0aec))



## [0.1.6](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.5...v0.1.6) (2023-12-01)


### Bug Fixes

* no.0 修复按钮设置偶先报错 ([0e386f4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0e386f45b95788398fddb18c359eac6fbbf820e5))
* no.0 解决分组设置差异化分组未自动带出关联字段的问题 ([2629647](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/262964731d203ed9eb5d68a68b272f9d30a2e0da))
* no.0 解决引入文档方式 ([47dbd68](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/47dbd68dbebac318f1cb1d47e9be477052ef1a40))
* no.0 解决看板分组设置后标题错位导致数据源区域显示异常 ([9106a5e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9106a5ea084f3378515aa2486d0a54a896eef15f))
* no.0 解决看板新建自定义分组默认显示背景色 ([2737578](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/273757838184b78391b864045a442ec90358c53c))
* no.0 解决看板新建自定义分组默认显示背景色 ([5ba1c3d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5ba1c3d199f39f9aeb65dade28fb541544ff9404))
* no.2598096 解决看板文档渲染方式 ([e392542](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e3925424d68a70276bf468e9fbeefd0b16d7f1c0))
* no.2634809 解决看板视图添加数据后，删除提示异常，无法删除数据 ([b04f271](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b04f2712ab67801987f615de88accd77eafb0695))


### Features

* no.2598096 新增eb看板组件文档书写 ([fb43002](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fb4300224060c3ef7db830ac345bc68db9ea6fe3))
* no.2598096 新增eb看板组件文档书写 ([9b67000](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9b670009743e04c210214e263736f612fe4087a8))
* no.2598096 新增eb看板组件文档书写 ([1264e9a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1264e9a805478a383ed4305b746b14819abb916d))
* no.2598096 新增eb看板组件文档书写 ([845bb55](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/845bb55f613d8d4273eb9c4506b11b652ac05bc5))


### Performance Improvements

* no.2614937 优化分组统计配置--支持各分组设置筛选条件 ([5f1eaed](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5f1eaed6f6993807559c417da8e326dc000e1dd0))



## [0.1.5](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.4...v0.1.5) (2023-08-17)


### Bug Fixes

* no.0 修复pdf导出时不显示名字 ([556b750](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/556b7507e0063e4f30282688949be3663a614b81))
* no.0 修复动作设置“刷新组件”、”显示隐藏组件”选不到组件问题 ([86a0ce4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/86a0ce4c06f8f384ed1214e1c5bb9fd1343e4d28))
* no.0 修复按钮设置在高级视图下部分配置会造成页面轮询死循环的问题 ([ac2cd38](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ac2cd388fb2c986506c3bc567e6d7908482bac06))
* no.0 修复按钮设置点击不生效 ([4fbdb12](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4fbdb12f3eb0044667573fe2665783cba9961522))
* no.0 修复按钮配置报错 ([d3a4951](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d3a49513e4011d4eaa2fefd77891e38143c30132))
* no.0 修复搜索显示条件关系不生效 ([1818d8b](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1818d8b0f20f56c930cba1eabbff077306dbef7a))
* no.0 修复显示条件关系不生效 ([644b276](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/644b276b6ed02cb6fd82db957d069ab486958002))
* no.0 修复看板国际化标签失效问题 ([fcf32c7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fcf32c7f7493548f0ab39d48cacde0003dcc49b6))
* no.0 修复看板视图下配置搜索条件关闭显示条件关系后重新打开，还是打开的状态 ([4f5657c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4f5657c347a29ec9eb4d796ec4b83e931764b403))
* no.0 修复看板视图下配置搜索条件关闭显示条件关系后重新打开，还是打开的状态 ([89c1850](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/89c18509fb028f751a22e18dfdb9f2d976e3f480))
* no.0 修复看板配置触发更新校验 ([94bed46](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/94bed46dacc260e701643f2e26064bf061979b5f))
* no.0 修复配置的动作事件，导出PDF、条件控制按钮点击后无反应 ([eb64bfe](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/eb64bfe247fe6e0c365c0841a36e9ae9edae1ca9))
* no.2023010416 修复【1951805】eb看板支持按钮设置相关问题 ([44b43de](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/44b43deb6de80c1b0bb03418db9d846f24147515))
* no.2023010416 修复推送测试 ([b8006eb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b8006eb8c61847d6f3c7b2c5b4e3c539b697096c))
* no.2023010416 修复看板卡片不展示 ([1464092](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/14640926a70b421564808d9079e8b400d71a87a5))
* no.2023010416 修复看板点击按钮不生效 ([e6039e0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e6039e0256314873943b9769d53e116167341f48))
* no.2023010416 修复看板点击按钮不生效 ([c1899e6](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c1899e6e20b8d963571dfdad82e4dca11a43a3f9))


### Features

* no.0 新增【15467】表格视图、高级视图增加开关控制是否显示条件关系切换 ([2c709ca](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/2c709ca2a7ec845d7161e35ef92ce89c02e1051f))
* no.0 新增【15467】表格视图、高级视图增加开关控制是否显示条件关系切换 ([ed5c5a7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ed5c5a7820f666e9d4063a0cab1c81ed694bfb37))
* no.0 新增暂时屏蔽【15467】表格视图、高级视图增加开关控制是否显示条件关系切换 ([07ebb8a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/07ebb8ae74a078178233dd2a4f4e7de9122dc01d))
* no.2023010416 新增【1951805】eb看板支持按钮设置 ([d336ecf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d336ecf17b97032fa0b96d60efe798c795301196))
* no.2023010416 新增看板支持按钮设置 ([287817a](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/287817ad76d60e979cf50510826065f8d7bddfcd))
* no.2023010416 新增表单高级视图按钮设置 ([a95c4ad](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/a95c4ad8401d834f97d33a449f19d50d7e88acf1))
* no.2023010416 新增表单高级视图按钮设置 ([f6ac338](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f6ac338c7b796d096c19354010f5482e3fe80943))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图迁移 ([83b44fa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/83b44fac9e853ac93a51338c44c0265b24a6d1f0))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图迁移 ([0e60094](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0e60094686fd1c07ed692a562c77fd299c6e3b69))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图迁移 ([da7086f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/da7086f05c7e554737b9d5ab8eae331d6834d6c4))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图迁移 ([8f4bea9](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8f4bea9c43156891462fe33d81c0588e2854e773))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图迁移 ([fe492ea](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fe492eab1d913137060ccc5c0b90f762c222e5ab))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([8224dce](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8224dce3520088288d7f10712a54a05eae1f61aa))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([8e52bbf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8e52bbf835caa979e717e5a1878221867e0edfc9))



## [0.1.4](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.3...v0.1.4) (2023-06-21)


### Bug Fixes

* no.0 修复看板设计器视图搜索条件区域不显示 ([c0c2a18](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c0c2a180466b1d32b7198e51d050e702a5168f46))



## [0.1.3](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.2...v0.1.3) (2023-06-20)


### Bug Fixes

* no.0 修复接口请求屏蔽 ([c500fbb](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c500fbb880f7b8c8fc2351508c484fb3fc2a1eb6))



## [0.1.2](http://10.12.101.12/FRONTEND/weapp-ebdboard/compare/v0.1.1...v0.1.2) (2023-06-17)


### Features

* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([9456d7f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/9456d7f973ee13ed9fbb4a73de0c21f8241a02dd))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([b9e6a07](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b9e6a07bc98c5dddd382844798a8fb9c9de34bbc))



## 0.1.1 (2023-06-16)


### Bug Fixes

* no.0 修复ebboard 样式 ([fb1af3f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fb1af3faa27ab5cf676d857257bd77c1c505e15e))
* no.0 修复eb看板样式 ([78c090c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/78c090c91d82a724f91478d6df463094de05e8f6))
* no.0 修复filterSearch ([8f510e1](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/8f510e18cbe878448c9d23ee9d665a0475bc2c42))
* no.0 修复分组显隐异常 ([1f1191c](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1f1191c38ad9ce70e17f8a991926fab5675309fe))
* no.0 修复国际化标签处理 ([30a7c13](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/30a7c13e5f69b9bc90d6b5e1b46eba7314028ac9))
* no.0 修复文件清理 ([1dbc7b2](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/1dbc7b26ef1e93f3072a2856163452feef7a0ffa))
* no.0 修复新建 ([f796e04](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f796e042659b9c53e3334edf54e4b602fb516c39))
* no.0 修复看板事件注册不生效 ([0a5c81e](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0a5c81ec9b7fef78715cb237dd6e80c16b366652))
* no.0 修复看板事件注册不生效 ([762a8d8](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/762a8d8c5b3ebba0e5e36dc15df509d5a95c4abf))
* no.0 修复看板样式异常 ([5ded8d3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/5ded8d3e07ce5017d163ebae75f5fc8993f3b3fa))
* no.0 修复看板相关问题 ([82b98aa](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/82b98aa3d3d83b7098269437352033116ccd1864))
* no.0 修复看板组件迁移 ([d27cc48](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d27cc48dc82f2e4715b06f9454804b88e212f173))
* no.0 修复看板自定义按钮不能触发点击 ([105fa20](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/105fa20f1ebfb6fb3eb955e6613c9e33b8b3ae08))
* no.0 修复获取分类搜索配置isPhysical传参判定 ([6549620](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/6549620b40064905ba14832f6ea11796f31f7185))
* no.0 修复迁移 ([936f127](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/936f12785666d9f5fa940ff438aa12944208288c))
* no.2023007317 修复eb看板组件card渲染问题 ([482c702](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/482c70266b0d88fac99ab6fbfb07952f72789a54))
* no.2023007317 修复eb看板组件迁移 ([4874fdf](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4874fdfd5cad74d9689228426861846e531aada8))
* no.2023007317 修复eb看板组件迁移 ([e1de3d7](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e1de3d7d9a4ff6fda98b0e7fe48ff8d1d061f9bc))
* no.2023007317 修复eb看板组件迁移 ([35a32af](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/35a32afab43ed80c3d2337022ab3d7b5af4c1012))
* no.2023007317 修复eb看板组件迁移 ([c9bc311](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/c9bc3116ed0e7dc6f84078a0eaf9daab4a4735a2))
* no.2023007317 修复国际化标签处理 ([ca3d400](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/ca3d40078b6ec0b8e66db77d906bc37c5b0e78f7))
* no.2023007317 修复看板自定义文本只配置文本不显示 ([f5aa1b3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/f5aa1b33d7e9831868784d2db74b19c15e2fb82d))
* no.2174499 修复页面设计器中看板组件设置分组点击确定前端报错 ([d9e06d3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d9e06d352c6045f4bb8db53b7e8f620a23b507ea))
* no.2174499 修复页面设计器中看板组件设置分组点击确定前端报错 ([61a3de0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/61a3de0e3d9976c141d737b51bc0dfe516cbb4e9))
* no.2177891 修复新建给表单传参问题 ([040cd11](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/040cd11f8222d43ff59b3616a64738842c22cb38))
* no.2177891 修复看板打开详情无数据，修复新建给表单传参问题 ([4c84215](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/4c842153001c749c2d0ce33ad594e0bccbe31e53))


### Features

* no.0 新增看板迁移 ([fc11402](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/fc11402838398e38c0399df91d3577c3b15c41bc))
* no.0 新增看板迁移 ([3333df0](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/3333df05351546fa66362d5e52629c9e6fb268b4))
* no.0 新增看板迁移 ([b00a7d3](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/b00a7d3a7cc24dc4aeab6a16c140c5e360652738))
* no.2023007317 新增eb看板组件国际化处理 ([578aa0d](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/578aa0d1330716e6e0a756ed505eaa7f69825846))
* no.2023007317 新增eb看板组件本地调试 ([7f03f49](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/7f03f49794a8a37e1e89144ae59d8ca254079acb))
* no.2023007317 新增eb看板组件移动端迁移 ([d0c2912](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/d0c2912bd75e135fab143bc759dc002099eb68dd))
* no.2023007317 新增eb看板组件迁移 ([086ad6f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/086ad6f2ec2df0bb9fa91d8467301f0fc1605554))
* no.2023007317 新增部分eb看板组件迁移 ([e99b74f](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/e99b74f61c43a91314d49f43e8bdc64820e46b94))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图 ([0509cd4](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/0509cd46a5377823f5963fb7f31ef5a9567fdb4b))
* no.2224090 新增事件动作刷新组件支持排行榜、时间轴等数据展示类组件 - 看板视图 ([825c736](http://10.12.101.12/FRONTEND/weapp-ebdboard/commits/825c7364748fca55f22e01b586d7b462824975d4))



