let size = 0;
let smallSize = 0;
module.exports = {
  optimization: {
    splitChunks: {
      cacheGroups: {
        bigCss: {
          name: 'big-minilizer-css',
          chunks: 'all',
          minSize: 0,
          reuseExistingChunk: true,
          priority: 200,
          enforce: true,
          test(module, chunks) {
            const test = /[\/\\]src[\/\\](components|utils)[\/\\].*\.less$/;
            if (
              module.nameForCondition &&
              test.test(module.nameForCondition()) &&
              module.size() < 1024 * 50 &&
              module.size() > 1024 * 3
            ) {
              size += module.size();
              if (size > 1024 * 200) {
                return false;
              } else {
                return true;
              }
            }
            for (const chunk of module.chunksIterable) {
              if (chunk.name && test.test(chunk.name)) {
                return true;
              }
            }
            return false;
          },
        },
        smallCss: {
          name: 'small-minilizer-css',
          chunks: 'all',
          minSize: 0,
          reuseExistingChunk: true,
          priority: 1000,
          enforce: true,
          test(module, chunks) {
            const test = /[\/\\]src[\/\\](components|utils)[\/\\].*\.less$/;
            if (module.nameForCondition && test.test(module.nameForCondition()) && module.size() < 1024 * 3) {
              smallSize += module.size();
              if (smallSize > 1024 * 150) {
                return false;
              } else {
                return true;
              }
            }
            for (const chunk of module.chunksIterable) {
              if (chunk.name && test.test(chunk.name)) {
                return true;
              }
            }
            return false;
          },
        },
      },
    },
  },
};
