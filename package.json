{"name": "@weapp/ebdboard", "version": "0.1.24", "dependencies": {"@types/lodash-es": "4.17.3", "@weapp/designer": "^0.x", "@weapp/designer-demo": "^0.x", "http-proxy-middleware": "^2.0.6", "lodash-es": "^4.17.21", "react-if": "^4.0.1", "react-sortable-hoc": "^2.0.0", "web-vitals": "^0.2.4"}, "main": "build/ebdboard/static/js/lib.js", "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-angular": "^11.0.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/axios": "^0.14.0", "@types/form-data": "^2.5.0", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/qs": "^6.9.5", "@types/react": "^17.0.10", "@types/react-dom": "^17.0.10", "@types/react-router-dom": "^5.1.7", "@weapp/base-css-vars": "^0.x", "@weapp/ecodesdk": "^0.x", "@weapp/layout": "^0.x", "@weapp/scripts": "^0.x", "@weapp/ui": "^0.x", "@weapp/utils": "^0.x", "@weapp/vendor": "^0.x", "axios": "^0.21.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.3", "css-vars-ponyfill": "^2.4.3", "eslint-plugin-weapp": "^0.0.12", "form-data": "^3.0.0", "husky": "^4.3.8", "loadjs": "^4.2.0", "mobx": "^4.15.7", "mobx-react": "^6.3.1", "postcss-less-weapp": "^0.0.5", "qs": "^6.9.6", "react": "^17.0.1", "react-dom": "^17.0.1", "react-router-dom": "^5.2.0", "stylelint-plugin-weapp": "^0.0.4", "typescript": "^4.0.3"}, "scripts": {"start": "cross-env PORT=7777 weapp-scripts start", "build": "weapp-scripts build", "test": "weapp-scripts test --watchAll=false --passWithNoTests", "test:watch": "weapp-scripts test", "analyzer": "weapp-scripts build --analyzer", "upwedep": "weapp-scripts upwedep", "lib": "weapp-scripts lib", "pub": "weapp-scripts pub", "version": "conventional-changelog -p angular -i CHANGELOG.md -s", "props": "weapp-scripts props", "pub-kb": "weapp-scripts pub-kb", "postbuild": "weapp-scripts props", "prebuild": "npm run lint:style", "lint:style": "stylelint src/**/*.{css,less} --customSyntax postcss-less-weapp", "deps-analysis": "weapp-scripts deps-analysis"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"@babel/preset-env": "7.12.1", "@babel/plugin-transform-regenerator": "7.12.1"}, "weappScriptConfig": {"pubContentHash": true, "buildContentHash": true}}